{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7091941d51debda0144e911b5677e2b4", "packages": [{"name": "activecampaign/api-php", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/ActiveCampaign/activecampaign-api-php.git", "reference": "2bf5b72ba8668de8dcb27e194948028276e748c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ActiveCampaign/activecampaign-api-php/zipball/2bf5b72ba8668de8dcb27e194948028276e748c5", "reference": "2bf5b72ba8668de8dcb27e194948028276e748c5", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["includes/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Official PHP wrapper for the ActiveCampaign API.", "homepage": "https://github.com/ActiveCampaign/activecampaign-api-php", "keywords": ["Forms", "activecampaign", "automation", "email-marketing", "emails", "marketing-automation", "newsletter", "subscribe"], "support": {"email": "<EMAIL>", "forum": "http://feedback.activecampaign.com/forums/238014-feedback-ideas/category/78647-api", "issues": "https://github.com/ActiveCampaign/activecampaign-api-php/issues", "source": "https://github.com/ActiveCampaign/activecampaign-api-php/tree/master"}, "time": "2017-04-26T00:00:00+00:00"}, {"name": "appwrite/php-clamav", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/appwrite/php-clamav.git", "reference": "f3897169f5c1f365312238a516ae9465f804634f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/appwrite/php-clamav/zipball/f3897169f5c1f365312238a516ae9465f804634f", "reference": "f3897169f5c1f365312238a516ae9465f804634f", "shasum": ""}, "require": {"ext-sockets": "*", "php": ">=8.0"}, "require-dev": {"phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Appwrite\\ClamAV\\": "src/ClamAV"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eldad Fu<PERSON>", "email": "el<PERSON>@appwrite.io"}], "description": "ClamAV network and pipe client for PHP", "keywords": ["anti virus", "appwrite", "clamav", "php"], "support": {"issues": "https://github.com/appwrite/php-clamav/issues", "source": "https://github.com/appwrite/php-clamav/tree/2.0.0"}, "time": "2023-02-24T09:50:42+00:00"}, {"name": "async-aws/core", "version": "1.26.0", "source": {"type": "git", "url": "https://github.com/async-aws/core.git", "reference": "58ab79116d990e7053b2e31162f47df4223148c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/async-aws/core/zipball/58ab79116d990e7053b2e31162f47df4223148c5", "reference": "58ab79116d990e7053b2e31162f47df4223148c5", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-simplexml": "*", "php": "^7.2.5 || ^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/http-client": "^4.4.16 || ^5.1.7 || ^6.0 || ^7.0", "symfony/http-client-contracts": "^1.1.8 || ^2.0 || ^3.0", "symfony/service-contracts": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"async-aws/s3": "<1.1", "symfony/http-client": "5.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.26-dev"}}, "autoload": {"psr-4": {"AsyncAws\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Core package to integrate with AWS. This is a lightweight AWS SDK provider by AsyncAws.", "keywords": ["amazon", "async-aws", "aws", "sdk", "sts"], "support": {"source": "https://github.com/async-aws/core/tree/1.26.0"}, "funding": [{"url": "https://github.com/jderusse", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2025-05-12T09:35:01+00:00"}, {"name": "async-aws/ses", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/async-aws/ses.git", "reference": "904ee7b5c07d865c20db4c06c3c0b97e7035673d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/async-aws/ses/zipball/904ee7b5c07d865c20db4c06c3c0b97e7035673d", "reference": "904ee7b5c07d865c20db4c06c3c0b97e7035673d", "shasum": ""}, "require": {"async-aws/core": "^1.9", "ext-json": "*", "php": "^7.2.5 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"psr-4": {"AsyncAws\\Ses\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "SES client, part of the AWS SDK provided by AsyncAws.", "keywords": ["amazon", "async-aws", "aws", "sdk", "ses"], "support": {"source": "https://github.com/async-aws/ses/tree/1.12.0"}, "funding": [{"url": "https://github.com/jderusse", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2025-05-12T09:35:01+00:00"}, {"name": "aweber/aweber", "version": "v1.1.18", "source": {"type": "git", "url": "https://github.com/aweber/AWeber-API-PHP-Library.git", "reference": "2ee27a5ed68e477c4cedfed3ae1daf310ae65845"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aweber/AWeber-API-PHP-Library/zipball/2ee27a5ed68e477c4cedfed3ae1daf310ae65845", "reference": "2ee27a5ed68e477c4cedfed3ae1daf310ae65845", "shasum": ""}, "require-dev": {"bamboohr/phpcs": "^0.1.4", "mayflower/php-codebrowser": "^1.1", "pdepend/pdepend": "^2.2", "php-di/phpdoc-reader": "^2.0", "phpdocumentor/phpdocumentor": "^2.9", "phploc/phploc": "^3.0", "phpmd/phpmd": "^2.4", "phpunit/phpunit": "^5.6", "sebastian/phpcpd": "^2.0", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "autoload": {"classmap": ["aweber_api/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "description": "The official AWeber API client library.", "homepage": "https://www.aweber.com", "keywords": ["api", "client", "email"], "support": {"email": "<EMAIL>", "issues": "https://github.com/aweber/AWeber-API-PHP-Library", "source": "https://github.com/aweber/AWeber-API-PHP-Library"}, "abandoned": true, "time": "2017-09-13T17:29:18+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/d71d9906c7bb63a28295447ba12e74723bd3730e", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.7"}, "time": "2024-10-18T22:15:13+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.344.1", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "7ff4b73f2d6550949be14b8822e100963e068705"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/7ff4b73f2d6550949be14b8822e100963e068705", "reference": "7ff4b73f2d6550949be14b8822e100963e068705", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/promises": "^2.0", "guzzlehttp/psr7": "^2.4.5", "mtdowling/jmespath.php": "^2.8.0", "php": ">=8.1", "psr/http-message": "^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^2.7.8", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^2.0 || ^3.0", "psr/simple-cache": "^2.0 || ^3.0", "sebastian/comparator": "^1.2.3 || ^4.0 || ^5.0", "symfony/filesystem": "^v6.4.0 || ^v7.1.0", "yoast/phpunit-polyfills": "^2.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://github.com/aws/aws-sdk-php/discussions", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.344.1"}, "time": "2025-06-05T18:09:40+00:00"}, {"name": "bacon/bacon-qr-code", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/8674e51bb65af933a5ffaf1c308a660387c35c22", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.8"}, "time": "2022-12-07T17:46:57+00:00"}, {"name": "beberlei/doctrineextensions", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/beberlei/DoctrineExtensions.git", "reference": "281f1650641c2f438b0a54d8eaa7ba50ac7e3eb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/DoctrineExtensions/zipball/281f1650641c2f438b0a54d8eaa7ba50ac7e3eb6", "reference": "281f1650641c2f438b0a54d8eaa7ba50ac7e3eb6", "shasum": ""}, "require": {"doctrine/orm": "^2.19 || ^3.0", "php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^1.14 || ^2", "doctrine/coding-standard": "^9.0.2 || ^12.0", "nesbot/carbon": "^2.72 || ^3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5 || ^9.6", "squizlabs/php_codesniffer": "^3.8", "symfony/cache": "^5.4 || ^6.4 || ^7.0", "symfony/yaml": "^5.4 || ^6.4 || ^7.0", "vimeo/psalm": "^3.18 || ^5.22", "zf1/zend-date": "^1.12", "zf1/zend-registry": "^1.12"}, "type": "library", "autoload": {"psr-4": {"DoctrineExtensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A set of extensions to Doctrine 2 that add support for additional query functions available in MySQL, Oracle, PostgreSQL and SQLite.", "keywords": ["database", "doctrine", "orm"], "support": {"source": "https://github.com/beberlei/DoctrineExtensions/tree/v1.5.0"}, "time": "2024-03-03T17:55:15+00:00"}, {"name": "behat/transliterator", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/baac5873bac3749887d28ab68e2f74db3a4408af", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^8.5.25 || ^9.5.19"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "support": {"issues": "https://github.com/Behat/Transliterator/issues", "source": "https://github.com/Behat/Transliterator/tree/v1.5.0"}, "abandoned": true, "time": "2022-03-30T09:27:43+00:00"}, {"name": "braintree/braintree_php", "version": "6.26.0", "source": {"type": "git", "url": "https://github.com/braintree/braintree_php.git", "reference": "602bfe7876cc2f6c6c24bd68fef4c6de8c3a8195"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/braintree/braintree_php/zipball/602bfe7876cc2f6c6c24bd68fef4c6de8c3a8195", "reference": "602bfe7876cc2f6c6c24bd68fef4c6de8c3a8195", "shasum": ""}, "require": {"ext-curl": "*", "ext-dom": "*", "ext-hash": "*", "ext-openssl": "*", "ext-xmlwriter": "*", "php": ">=7.3.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Braintree\\": "lib/Braintree"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Braintree", "homepage": "https://www.braintreepayments.com"}], "description": "Braintree PHP Client Library", "support": {"issues": "https://github.com/braintree/braintree_php/issues", "source": "https://github.com/braintree/braintree_php/tree/6.26.0"}, "time": "2025-05-28T19:18:19+00:00"}, {"name": "brick/math", "version": "0.13.1", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/fc7ed316430118cc7836bf45faff18d5dfc8de04", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-03-29T13:50:30+00:00"}, {"name": "cakephp/core", "version": "4.6.1", "source": {"type": "git", "url": "https://github.com/cakephp/core.git", "reference": "c2f4dff110d41e475d1041f2abe236f1c62d0cd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/core/zipball/c2f4dff110d41e475d1041f2abe236f1c62d0cd0", "reference": "c2f4dff110d41e475d1041f2abe236f1c62d0cd0", "shasum": ""}, "require": {"cakephp/utility": "^4.0", "php": ">=7.4.0"}, "provide": {"psr/container-implementation": "^1.0 || ^2.0"}, "suggest": {"cakephp/cache": "To use Configure::store() and restore().", "cakephp/event": "To use PluginApplicationInterface or plugin applications.", "league/container": "To use Container and ServiceProvider classes"}, "type": "library", "autoload": {"files": ["functions.php"], "psr-4": {"Cake\\Core\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "description": "CakePHP Framework Core classes", "homepage": "https://cakephp.org", "keywords": ["cakephp", "core", "framework"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/core"}, "time": "2023-10-21T13:30:46+00:00"}, {"name": "cakephp/database", "version": "4.6.1", "source": {"type": "git", "url": "https://github.com/cakephp/database.git", "reference": "36599acf67c4416ccf7e009e69aaecc5e10ae75a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/database/zipball/36599acf67c4416ccf7e009e69aaecc5e10ae75a", "reference": "36599acf67c4416ccf7e009e69aaecc5e10ae75a", "shasum": ""}, "require": {"cakephp/core": "^4.0", "cakephp/datasource": "^4.0", "php": ">=7.4.0"}, "suggest": {"cakephp/i18n": "If you are using locale-aware datetime formats or Chronos types.", "cakephp/log": "If you want to use query logging without providing a logger yourself."}, "type": "library", "autoload": {"psr-4": {"Cake\\Database\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/database/graphs/contributors"}], "description": "Flexible and powerful Database abstraction library with a familiar PDO-like API", "homepage": "https://cakephp.org", "keywords": ["abstraction", "cakephp", "database", "database abstraction", "pdo"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/database"}, "time": "2025-04-01T11:56:48+00:00"}, {"name": "cakephp/datasource", "version": "4.6.1", "source": {"type": "git", "url": "https://github.com/cakephp/datasource.git", "reference": "073bb5f3b082b87c20309c1b25b1ac2a238d97b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/datasource/zipball/073bb5f3b082b87c20309c1b25b1ac2a238d97b3", "reference": "073bb5f3b082b87c20309c1b25b1ac2a238d97b3", "shasum": ""}, "require": {"cakephp/core": "^4.0", "php": ">=7.4.0", "psr/log": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0"}, "suggest": {"cakephp/cache": "If you decide to use Query caching.", "cakephp/collection": "If you decide to use ResultSetInterface.", "cakephp/utility": "If you decide to use EntityTrait."}, "type": "library", "autoload": {"psr-4": {"Cake\\Datasource\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/datasource/graphs/contributors"}], "description": "Provides connection managing and traits for Entities and Queries that can be reused for different datastores", "homepage": "https://cakephp.org", "keywords": ["cakephp", "connection management", "datasource", "entity", "query"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/datasource"}, "time": "2024-11-19T19:41:00+00:00"}, {"name": "cakephp/utility", "version": "4.6.1", "source": {"type": "git", "url": "https://github.com/cakephp/utility.git", "reference": "708929115e5b400e1b5b76d8120ca2e51e2de199"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/utility/zipball/708929115e5b400e1b5b76d8120ca2e51e2de199", "reference": "708929115e5b400e1b5b76d8120ca2e51e2de199", "shasum": ""}, "require": {"cakephp/core": "^4.0", "php": ">=7.4.0"}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "type": "library", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Cake\\Utility\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "homepage": "https://cakephp.org", "keywords": ["cakephp", "hash", "inflector", "security", "string", "utility"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/utility"}, "time": "2024-06-23T00:11:14+00:00"}, {"name": "campaignmonitor/createsend-php", "version": "v6.1.2", "source": {"type": "git", "url": "https://github.com/campaignmonitor/createsend-php.git", "reference": "922cec7fbc9da1508c18156db7693a6ddad7194e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/campaignmonitor/createsend-php/zipball/922cec7fbc9da1508c18156db7693a6ddad7194e", "reference": "922cec7fbc9da1508c18156db7693a6ddad7194e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"simpletest/simpletest": "~1.1.3"}, "type": "library", "autoload": {"classmap": ["csrest_administrators.php", "csrest_campaigns.php", "csrest_clients.php", "csrest_general.php", "csrest_events.php", "csrest_lists.php", "csrest_people.php", "csrest_segments.php", "csrest_subscribers.php", "csrest_templates.php", "csrest_transactional_classicemail.php", "csrest_transactional_smartemail.php", "csrest_transactional_timeline.php", "csrest_journeys.php", "csrest_journey_emails.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A php library which implements the complete functionality of the Campaign Monitor API.", "homepage": "http://campaignmonitor.github.io/createsend-php/", "keywords": ["api", "campaign", "monitor"], "support": {"issues": "https://github.com/campaignmonitor/createsend-php/issues", "source": "https://github.com/campaignmonitor/createsend-php/tree/v6.1.2"}, "time": "2021-10-02T04:57:43+00:00"}, {"name": "chrome-php/chrome", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/chrome-php/chrome.git", "reference": "9dce1e484a6703e487da5e219d39a8d2fb3d8afa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chrome-php/chrome/zipball/9dce1e484a6703e487da5e219d39a8d2fb3d8afa", "reference": "9dce1e484a6703e487da5e219d39a8d2fb3d8afa", "shasum": ""}, "require": {"chrome-php/wrench": "^1.7", "evenement/evenement": "^3.0.1", "monolog/monolog": "^1.27.1 || ^2.8 || ^3.2", "php": "^7.4.15 || ^8.0.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/filesystem": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-mbstring": "^1.26", "symfony/process": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^9.6.3 || ^10.0.12", "symfony/var-dumper": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"HeadlessChromium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/enricodias"}], "description": "Instrument headless chrome/chromium instances from PHP", "keywords": ["browser", "chrome", "chromium", "crawl", "headless", "pdf", "puppeteer", "screenshot"], "support": {"issues": "https://github.com/chrome-php/chrome/issues", "source": "https://github.com/chrome-php/chrome/tree/v1.14.0"}, "time": "2025-05-28T11:31:22+00:00"}, {"name": "chrome-php/wrench", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/chrome-php/wrench.git", "reference": "f82965181bd325ffb02be247676831a1f3e71912"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chrome-php/wrench/zipball/f82965181bd325ffb02be247676831a1f3e71912", "reference": "f82965181bd325ffb02be247676831a1f3e71912", "shasum": ""}, "require": {"ext-sockets": "*", "php": "^7.4.15 || ^8.0.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/polyfill-php80": "^1.26"}, "conflict": {"wrench/wrench": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^9.6.3 || ^10.0.12"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"Wrench\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "A simple PHP WebSocket implementation", "keywords": ["WebSockets", "hybi", "websocket"], "support": {"issues": "https://github.com/chrome-php/wrench/issues", "source": "https://github.com/chrome-php/wrench/tree/v1.7.1"}, "time": "2025-05-01T11:18:17+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d665d22c417056996c59019579f1967dfe5c1e82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82", "reference": "d665d22c417056996c59019579f1967dfe5c1e82", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-26T15:08:54+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.6"}, "time": "2024-08-09T14:30:48+00:00"}, {"name": "doctrine/annotations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/901c2ee5d26eb64ff43c47976e114bf00843acf7", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.2"}, "time": "2024-09-05T10:17:24+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/common", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/d9ea4a54ca2586db781f0265d36bea731ac66ec5", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5", "shasum": ""}, "require": {"doctrine/persistence": "^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "doctrine/collections": "^1", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^6.1", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2025-01-01T22:12:03+00:00"}, {"name": "doctrine/dbal", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/ec16c82f20be1a7224e65ac67144a29199f87959", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "9.6.22", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.9.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-01-16T08:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.14.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "ca6a7350b421baf7fbdefbf9f4993292ed18effb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/ca6a7350b421baf7fbdefbf9f4993292ed18effb", "reference": "ca6a7350b421baf7fbdefbf9f4993292ed18effb", "shasum": ""}, "require": {"doctrine/dbal": "^3.7.0 || ^4.0", "doctrine/persistence": "^3.1 || ^4", "doctrine/sql-formatter": "^1.0.1", "php": "^8.1", "symfony/cache": "^6.4 || ^7.0", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/doctrine-bridge": "^6.4.3 || ^7.0.3", "symfony/framework-bundle": "^6.4 || ^7.0", "symfony/service-contracts": "^2.5 || ^3"}, "conflict": {"doctrine/annotations": ">=3.0", "doctrine/cache": "< 1.11", "doctrine/orm": "<2.17 || >=4.0", "symfony/var-exporter": "< 6.4.1 || 7.0.0", "twig/twig": "<2.13 || >=3.0 <3.0.4"}, "require-dev": {"doctrine/annotations": "^1 || ^2", "doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^12", "doctrine/deprecations": "^1.0", "doctrine/orm": "^2.17 || ^3.0", "friendsofphp/proxy-manager-lts": "^1.0", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-phpunit": "2.0.3", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^9.6.22", "psr/log": "^1.1.4 || ^2.0 || ^3.0", "symfony/doctrine-messenger": "^6.4 || ^7.0", "symfony/messenger": "^6.4 || ^7.0", "symfony/phpunit-bridge": "^7.2", "symfony/property-info": "^6.4 || ^7.0", "symfony/security-bundle": "^6.4 || ^7.0", "symfony/stopwatch": "^6.4 || ^7.0", "symfony/string": "^6.4 || ^7.0", "symfony/twig-bridge": "^6.4 || ^7.0", "symfony/validator": "^6.4 || ^7.0", "symfony/var-exporter": "^6.4.1 || ^7.0.1", "symfony/web-profiler-bundle": "^6.4 || ^7.0", "symfony/yaml": "^6.4 || ^7.0", "twig/twig": "^2.13 || ^3.0.4"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "ext-pdo": "*", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/2.14.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2025-03-22T17:28:21+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "5a6ac7120c2924c4c070a869d08b11ccf9e277b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/5a6ac7120c2924c4c070a869d08b11ccf9e277b9", "reference": "5a6ac7120c2924c4c070a869d08b11ccf9e277b9", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "^2.4", "doctrine/migrations": "^3.2", "php": "^7.2 || ^8.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/framework-bundle": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"composer/semver": "^3.0", "doctrine/coding-standard": "^12", "doctrine/orm": "^2.6 || ^3", "phpstan/phpstan": "^1.4 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpstan/phpstan-symfony": "^1.3 || ^2", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/phpunit-bridge": "^6.3 || ^7", "symfony/var-exporter": "^5.4 || ^6 || ^7"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.4.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-migrations-bundle", "type": "tidelift"}], "time": "2025-03-11T17:36:26+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "doctrine/migrations", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/dbal": "^3.6 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2.0", "php": "^8.1", "psr/log": "^1.1.3 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.2 || ^7.0"}, "conflict": {"doctrine/orm": "<2.12 || >=4"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/orm": "^2.13 || ^3", "doctrine/persistence": "^2 || ^3 || ^4", "doctrine/sql-formatter": "^1.0", "ext-pdo_sqlite": "*", "fig/log-test": "^1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.4", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^10.3", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "autoload": {"psr-4": {"Doctrine\\Migrations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.9.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2025-03-26T06:48:45+00:00"}, {"name": "doctrine/orm", "version": "2.20.3", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "17d28b5c4cac212ca92730d9a26e32a0b1516126"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/17d28b5c4cac212ca92730d9a26e32a0b1516126", "reference": "17d28b5c4cac212ca92730d9a26e32a0b1516126", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.12.1 || ^2.1.1", "doctrine/collections": "^1.5 || ^2.1", "doctrine/common": "^3.0.3", "doctrine/dbal": "^2.13.1 || ^3.2", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2", "doctrine/inflector": "^1.4 || ^2.0", "doctrine/instantiator": "^1.3 || ^2", "doctrine/lexer": "^2 || ^3", "doctrine/persistence": "^2.4 || ^3", "ext-ctype": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3", "symfony/console": "^4.2 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php72": "^1.23", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.13 || >= 3.0"}, "require-dev": {"doctrine/annotations": "^1.13 || ^2", "doctrine/coding-standard": "^9.0.2 || ^13.0", "phpbench/phpbench": "^0.16.10 || ^1.0", "phpstan/extension-installer": "~1.1.0 || ^1.4", "phpstan/phpstan": "~1.4.10 || 2.0.3", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psr/log": "^1 || ^2 || ^3", "squizlabs/php_codesniffer": "3.12.0", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7.0", "symfony/var-exporter": "^4.4 || ^5.4 || ^6.2 || ^7.0", "symfony/yaml": "^3.4 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"ext-dom": "Provides support for XSD validation for XML mapping files", "symfony/cache": "Provides cache support for Setup Tool with doctrine/cache 2.0", "symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "autoload": {"psr-4": {"Doctrine\\ORM\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.20.3"}, "time": "2025-05-02T17:07:53+00:00"}, {"name": "doctrine/persistence", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/0ea965320cec355dba75031c1b23d4c78362e3ff", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^7.2 || ^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/common": "^3.0", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5.38 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-10-30T19:48:12+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "ergebnis/phpunit-slow-test-detector": "^2.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.5.2"}, "time": "2025-01-24T11:45:48+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00"}, {"name": "drewm/mailchimp-api", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/drewm/mailchimp-api.git", "reference": "c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drewm/mailchimp-api/zipball/c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a", "reference": "c6cdfab4ca6ddbc3b260913470bd0a4a5cb84c7a", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "7.0.*", "vlucas/phpdotenv": "^2.0"}, "type": "library", "autoload": {"psr-4": {"DrewM\\MailChimp\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://allinthehead.com/"}], "description": "Super-simple, minimum abstraction MailChimp API v3 wrapper", "homepage": "https://github.com/drewm/mailchimp-api", "support": {"issues": "https://github.com/drewm/mailchimp-api/issues", "source": "https://github.com/drewm/mailchimp-api/tree/master"}, "time": "2019-08-06T09:24:58+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "endroid/qr-code", "version": "4.8.5", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "0db25b506a8411a5e1644ebaa67123a6eb7b6a77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/0db25b506a8411a5e1644ebaa67123a6eb7b6a77", "reference": "0db25b506a8411a5e1644ebaa67123a6eb7b6a77", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0.5", "php": "^8.1"}, "conflict": {"khanamiryan/qrcode-detector-decoder": "^1.0.6"}, "require-dev": {"endroid/quality": "dev-master", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^1.0.4||^2.0.2", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["code", "endroid", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/endroid/qr-code/issues", "source": "https://github.com/endroid/qr-code/tree/4.8.5"}, "funding": [{"url": "https://github.com/endroid", "type": "github"}], "time": "2023-09-29T14:03:20+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "facebook/php-business-sdk", "version": "17.0.2", "source": {"type": "git", "url": "https://github.com/facebook/facebook-php-business-sdk.git", "reference": "7df79347120112b21b632e7d080406ea628b1758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/facebook-php-business-sdk/zipball/7df79347120112b21b632e7d080406ea628b1758", "reference": "7df79347120112b21b632e7d080406ea628b1758", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.5 || ^7.0", "php": ">=8.0"}, "require-dev": {"mockery/mockery": "1.3.3", "phpunit/phpunit": "~9", "symfony/finder": "~2.6"}, "type": "library", "autoload": {"psr-4": {"FacebookAds\\": "src/FacebookAds/"}}, "notification-url": "https://packagist.org/downloads/", "description": "PHP SDK for Facebook Business", "homepage": "https://developers.facebook.com/", "keywords": ["ads", "business", "facebook", "instagram", "page", "sdk"], "support": {"issues": "https://github.com/facebook/facebook-php-business-sdk/issues", "source": "https://github.com/facebook/facebook-php-business-sdk/tree/17.0.2"}, "time": "2023-08-08T16:33:40+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "gedmo/doctrine-extensions", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/doctrine-extensions/DoctrineExtensions.git", "reference": "ea1d37586b8e4bae2a815feb38b177894b12c44c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine-extensions/DoctrineExtensions/zipball/ea1d37586b8e4bae2a815feb38b177894b12c44c", "reference": "ea1d37586b8e4bae2a815feb38b177894b12c44c", "shasum": ""}, "require": {"behat/transliterator": "^1.2", "doctrine/collections": "^1.2 || ^2.0", "doctrine/deprecations": "^1.0", "doctrine/event-manager": "^1.2 || ^2.0", "doctrine/persistence": "^2.2 || ^3.0 || ^4.0", "php": "^7.4 || ^8.0", "psr/cache": "^1 || ^2 || ^3", "psr/clock": "^1", "symfony/cache": "^5.4 || ^6.0 || ^7.0"}, "conflict": {"doctrine/annotations": "<1.13 || >=3.0", "doctrine/common": "<2.13 || >=4.0", "doctrine/dbal": "<3.7 || >=5.0", "doctrine/mongodb-odm": "<2.3 || >=3.0", "doctrine/orm": "<2.20 || >=3.0 <3.3 || >=4.0"}, "require-dev": {"doctrine/annotations": "^1.13 || ^2.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/common": "^2.13 || ^3.0", "doctrine/dbal": "^3.7 || ^4.0", "doctrine/doctrine-bundle": "^2.3", "doctrine/mongodb-odm": "^2.3", "doctrine/orm": "^2.20 || ^3.3", "friendsofphp/php-cs-fixer": "^3.70", "nesbot/carbon": "^2.71 || ^3.0", "phpstan/phpstan": "^2.1.1", "phpstan/phpstan-doctrine": "^2.0.1", "phpstan/phpstan-phpunit": "^2.0.3", "phpunit/phpunit": "^9.6", "rector/rector": "^2.0.6", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/doctrine-bridge": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.0 || ^7.0", "symfony/uid": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/mongodb-odm": "to use the extensions with the MongoDB ODM", "doctrine/orm": "to use the extensions with the ORM"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Gedmo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine behavioral extensions", "homepage": "http://gediminasm.org/", "keywords": ["Blameable", "behaviors", "doctrine", "extensions", "gedmo", "loggable", "nestedset", "odm", "orm", "sluggable", "sortable", "timestampable", "translatable", "tree", "uploadable"], "support": {"docs": "https://github.com/doctrine-extensions/DoctrineExtensions/tree/main/doc", "issues": "https://github.com/doctrine-extensions/DoctrineExtensions/issues", "source": "https://github.com/doctrine-extensions/DoctrineExtensions/tree/v3.20.0"}, "funding": [{"url": "https://github.com/l3pp4rd", "type": "github"}, {"url": "https://github.com/mbabker", "type": "github"}, {"url": "https://github.com/phansys", "type": "github"}, {"url": "https://github.com/stof", "type": "github"}], "time": "2025-04-04T17:19:27+00:00"}, {"name": "getbrevo/brevo-php", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/getbrevo/brevo-php.git", "reference": "6c3286e62327277fd8445cddb057d44e850722c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getbrevo/brevo-php/zipball/6c3286e62327277fd8445cddb057d44e850722c0", "reference": "6c3286e62327277fd8445cddb057d44e850722c0", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^7.4.0", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.12", "phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "~2.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x.x-dev"}}, "autoload": {"psr-4": {"Brevo\\Client\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Brevo Developers", "email": "<EMAIL>", "homepage": "https://www.brevo.com/"}], "description": "Official Brevo provided RESTFul API V3 php library", "homepage": "https://github.com/getbrevo/brevo-php", "keywords": ["api", "brevo", "php", "sdk", "swagger"], "support": {"issues": "https://github.com/getbrevo/brevo-php/issues", "source": "https://github.com/getbrevo/brevo-php/tree/v1.0.2"}, "time": "2023-07-14T10:00:50+00:00"}, {"name": "getresponse/sdk-php", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/GetResponse/sdk-php.git", "reference": "a255de23ede4f9cf83b893e63e0770975f9ca4ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GetResponse/sdk-php/zipball/a255de23ede4f9cf83b893e63e0770975f9ca4ce", "reference": "a255de23ede4f9cf83b893e63e0770975f9ca4ce", "shasum": ""}, "require": {"getresponse/sdk-php-client": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Getresponse\\Sdk\\": "src/", "Getresponse\\Sdk\\Test\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "GetResponse DevTeam"}], "description": "SDK for all public GetResponse products", "support": {"email": "<EMAIL>", "issues": "https://github.com/GetResponse/sdk-php/issues", "source": "https://github.com/GetResponse/sdk-php/tree/3.0.0"}, "time": "2023-08-22T11:22:52+00:00"}, {"name": "getresponse/sdk-php-client", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/GetResponse/sdk-php-client.git", "reference": "363323335cb558837856db8238a10999a6215fbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GetResponse/sdk-php-client/zipball/363323335cb558837856db8238a10999a6215fbd", "reference": "363323335cb558837856db8238a10999a6215fbd", "shasum": ""}, "require": {"guzzlehttp/psr7": "^2.1", "php": ">=7.3", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"php-mock/php-mock": "^1.0 || ^2.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Getresponse\\Sdk\\Client\\": "src/", "Getresponse\\Sdk\\Client\\Test\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "GetResponse DevTeam"}], "description": "GetResponse SDK client for all public GetResponse product", "support": {"email": "<EMAIL>", "issues": "https://github.com/GetResponse/sdk-php-client/issues", "source": "https://github.com/GetResponse/sdk-php-client/tree/2.0.1"}, "time": "2023-08-22T11:21:15+00:00"}, {"name": "gettext/gettext", "version": "v5.7.3", "source": {"type": "git", "url": "https://github.com/php-gettext/Gettext.git", "reference": "95820f020e4f2f05e0bbaa5603e4c6ec3edc50f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Gettext/zipball/95820f020e4f2f05e0bbaa5603e4c6ec3edc50f1", "reference": "95820f020e4f2f05e0bbaa5603e4c6ec3edc50f1", "shasum": ""}, "require": {"gettext/languages": "^2.3", "php": "^7.2|^8.0"}, "require-dev": {"brick/varexporter": "^0.3.5", "friendsofphp/php-cs-fixer": "^3.2", "oscarotero/php-cs-fixer-config": "^2.0", "phpunit/phpunit": "^8.0|^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP gettext manager", "homepage": "https://github.com/php-gettext/Gettext", "keywords": ["JS", "gettext", "i18n", "mo", "po", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/php-gettext/Gettext/issues", "source": "https://github.com/php-gettext/Gettext/tree/v5.7.3"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "time": "2024-12-01T10:18:08+00:00"}, {"name": "gettext/js-scanner", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/php-gettext/JS-Scanner.git", "reference": "371bddca27b29eea5d553aade324c8e631ac3444"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/JS-Scanner/zipball/371bddca27b29eea5d553aade324c8e631ac3444", "reference": "371bddca27b29eea5d553aade324c8e631ac3444", "shasum": ""}, "require": {"gettext/gettext": "^5.5.0", "mck89/peast": "^1.9", "php": "^7.2|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "oscarotero/php-cs-fixer-config": "^1.0", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Gettext\\Scanner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Javascript scanner for gettext", "homepage": "https://github.com/php-gettext/JS-Scanner", "keywords": ["gettext", "i18n", "javascript", "scanner", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/php-gettext/JS-Scanner/issues", "source": "https://github.com/php-gettext/JS-Scanner/tree/v1.1.2"}, "time": "2022-02-14T21:40:09+00:00"}, {"name": "gettext/languages", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/php-gettext/Languages.git", "reference": "0b0b0851c55168e1dfb14305735c64019732b5f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Languages/zipball/0b0b0851c55168e1dfb14305735c64019732b5f1", "reference": "0b0b0851c55168e1dfb14305735c64019732b5f1", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "bin": ["bin/export-plural-rules", "bin/import-cldr-data"], "type": "library", "autoload": {"psr-4": {"Gettext\\Languages\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "gettext languages with plural rules", "homepage": "https://github.com/php-gettext/Languages", "keywords": ["cldr", "i18n", "internationalization", "l10n", "language", "languages", "localization", "php", "plural", "plural rules", "plurals", "translate", "translations", "unicode"], "support": {"issues": "https://github.com/php-gettext/Languages/issues", "source": "https://github.com/php-gettext/Languages/tree/2.12.1"}, "funding": [{"url": "https://paypal.me/mlocati", "type": "custom"}, {"url": "https://github.com/mlocati", "type": "github"}], "time": "2025-03-19T11:14:02+00:00"}, {"name": "gettext/php-scanner", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/php-gettext/PHP-Scanner.git", "reference": "989a2cffa1d0f43d13b14c83a50429119b5eb8e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/PHP-Scanner/zipball/989a2cffa1d0f43d13b14c83a50429119b5eb8e4", "reference": "989a2cffa1d0f43d13b14c83a50429119b5eb8e4", "shasum": ""}, "require": {"gettext/gettext": "^5.5.0", "nikic/php-parser": "^4.2", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "oscarotero/php-cs-fixer-config": "^1.0", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Gettext\\Scanner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP scanner for gettext", "homepage": "https://github.com/php-gettext/PHP-Scanner", "keywords": ["gettext", "i18n", "php", "scanner", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/php-gettext/PHP-Scanner/issues", "source": "https://github.com/php-gettext/PHP-Scanner/tree/v1.3.1"}, "time": "2022-03-18T11:47:55+00:00"}, {"name": "gettext/translator", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/php-gettext/Translator.git", "reference": "8ae0ac79053bcb732a6c584cd86f7a82ef183161"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Translator/zipball/8ae0ac79053bcb732a6c584cd86f7a82ef183161", "reference": "8ae0ac79053bcb732a6c584cd86f7a82ef183161", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "gettext/gettext": "^5.0.0", "oscarotero/php-cs-fixer-config": "^1.0", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "^3.0"}, "suggest": {"gettext/gettext": "Is necessary to load and generate array files used by the translator"}, "type": "library", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Gettext translator functions", "homepage": "https://github.com/php-gettext/Translator", "keywords": ["gettext", "i18n", "php", "translator"], "support": {"email": "<EMAIL>", "issues": "https://github.com/php-gettext/Translator/issues", "source": "https://github.com/php-gettext/Translator/tree/v1.2.1"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "time": "2025-01-09T09:20:22+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.55", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "shasum": ""}, "require": {"giggsey/locale": "^2.0", "php": "^7.4|^8.0", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.7", "phing/phing": "^3.0", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^9.6", "symfony/console": "^v5.2", "symfony/var-exporter": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2025-02-14T08:14:08+00:00"}, {"name": "giggsey/locale", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.66", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.17.4", "php-coveralls/php-coveralls": "^2.7", "phpunit/phpunit": "^10.5.45", "symfony/console": "^6.4", "symfony/filesystem": "6.4", "symfony/finder": "^6.4", "symfony/process": "^6.4", "symfony/var-exporter": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.8.0"}, "time": "2025-03-20T14:25:27+00:00"}, {"name": "google/apiclient", "version": "v2.18.3", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "4eee42d201eff054428a4836ec132944d271f051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/4eee42d201eff054428a4836ec132944d271f051", "reference": "4eee42d201eff054428a4836ec132944d271f051", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "google/apiclient-services": "~0.350", "google/auth": "^1.37", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.6", "monolog/monolog": "^2.9||^3.0", "php": "^8.0", "phpseclib/phpseclib": "^3.0.36"}, "require-dev": {"cache/filesystem-adapter": "^1.1", "composer/composer": "^1.10.23", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.8", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.18.3"}, "time": "2025-04-08T21:59:36+00:00"}, {"name": "google/apiclient-services", "version": "v0.397.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "8366037e450b62ffc1c5489459f207640acca2b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/8366037e450b62ffc1c5489459f207640acca2b4", "reference": "8366037e450b62ffc1c5489459f207640acca2b4", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.397.1"}, "time": "2025-06-04T17:28:44+00:00"}, {"name": "google/auth", "version": "v1.44.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "5670e56307d7a2eac931f677c0e59a4f8abb2e43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/5670e56307d7a2eac931f677c0e59a4f8abb2e43", "reference": "5670e56307d7a2eac931f677c0e59a4f8abb2e43", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.4.5", "php": "^8.1", "psr/cache": "^2.0||^3.0", "psr/http-message": "^1.1||^2.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "kelvinmo/simplejwt": "0.7.1", "phpseclib/phpseclib": "^3.0.35", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^6.0||^7.0", "webmozart/assert": "^1.11"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.44.0"}, "time": "2024-12-04T15:34:58+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "html2text/html2text", "version": "4.3.2", "source": {"type": "git", "url": "https://github.com/mtibben/html2text.git", "reference": "3b443cbe302b52eb5806a21a9dbd79524203970a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mtibben/html2text/zipball/3b443cbe302b52eb5806a21a9dbd79524203970a", "reference": "3b443cbe302b52eb5806a21a9dbd79524203970a", "shasum": ""}, "require-dev": {"phpunit/phpunit": "~4|^9.0"}, "suggest": {"ext-mbstring": "For best performance", "symfony/polyfill-mbstring": "If you can't install ext-mbstring"}, "type": "library", "autoload": {"psr-4": {"Html2Text\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Converts HTML to formatted plain text", "support": {"issues": "https://github.com/mtibben/html2text/issues", "source": "https://github.com/mtibben/html2text/tree/4.3.2"}, "time": "2024-08-20T02:43:29+00:00"}, {"name": "ilovepdf/ilovepdf-php", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/ilovepdf/ilovepdf-php.git", "reference": "b9ab2bcb17f187a6a48b862deeb1e1f96794345c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ilovepdf/ilovepdf-php/zipball/b9ab2bcb17f187a6a48b862deeb1e1f96794345c", "reference": "b9ab2bcb17f187a6a48b862deeb1e1f96794345c", "shasum": ""}, "require": {"ext-json": "*", "firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.4", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.21"}, "type": "library", "autoload": {"psr-4": {"Ilovepdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ilovepdf", "email": "<EMAIL>"}], "description": "iLovePDF Php Api", "homepage": "https://ilovepdf.com/", "support": {"issues": "https://github.com/ilovepdf/ilovepdf-php/issues", "source": "https://github.com/ilovepdf/ilovepdf-php/tree/v1.2.6"}, "time": "2024-06-20T12:42:02+00:00"}, {"name": "karel<PERSON>tersky/monolog-pdo-handler", "version": "0.6", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>intersky/monolog-pdo-handler.git", "reference": "0ca6cb85aa4ab91d5c0ab2053993ab0c54164e91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON><PERSON>/monolog-pdo-handler/zipball/0ca6cb85aa4ab91d5c0ab2053993ab0c54164e91", "reference": "0ca6cb85aa4ab91d5c0ab2053993ab0c54164e91", "shasum": ""}, "require": {"ext-pdo": "*", "monolog/monolog": ">1.4.0", "php": ">=7.1"}, "type": "library", "extra": {"class": "KarelWintersky\\Monolog\\KWPDOHandler"}, "autoload": {"psr-4": {"KarelWintersky\\Monolog\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A handler for Monolog that stores data via PDO driver to Database", "homepage": "https://github.com/<PERSON><PERSON>intersky/monolog-pdo-handler", "keywords": ["database", "log", "logging", "monolog", "pdo"], "support": {"issues": "https://github.com/<PERSON><PERSON>intersky/monolog-pdo-handler/issues", "source": "https://github.com/<PERSON><PERSON>intersky/monolog-pdo-handler/tree/0.6"}, "time": "2023-08-06T17:33:05+00:00"}, {"name": "knplabs/knp-menu", "version": "v3.7.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenu.git", "reference": "328bb430a0afad0b6eae2bd4bfea8e15db616fd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenu/zipball/328bb430a0afad0b6eae2bd4bfea8e15db616fd6", "reference": "328bb430a0afad0b6eae2bd4bfea8e15db616fd6", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"twig/twig": "<1.42.3 || >=2,<2.9"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6", "psr/container": "^1.0 || ^2.0", "symfony/http-foundation": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^7.0", "symfony/routing": "^5.4 || ^6.0 || ^7.0", "twig/twig": "^2.16 || ^3.0"}, "suggest": {"twig/twig": "for the TwigRenderer and the integration with your templates"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Knp\\Menu\\": "src/Knp/Menu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs", "homepage": "https://knplabs.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "The Community", "homepage": "https://github.com/KnpLabs/KnpMenu/contributors"}], "description": "An object oriented menu library", "homepage": "https://knplabs.com", "keywords": ["menu", "tree"], "support": {"issues": "https://github.com/KnpLabs/KnpMenu/issues", "source": "https://github.com/KnpLabs/KnpMenu/tree/v3.7.0"}, "time": "2025-02-28T08:21:46+00:00"}, {"name": "laravel/serializable-closure", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"illuminate/support": "^10.0|^11.0|^12.0", "nesbot/carbon": "^2.67|^3.0", "pestphp/pest": "^2.36|^3.0", "phpstan/phpstan": "^2.0", "symfony/var-dumper": "^6.2.0|^7.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2025-03-19T13:51:03+00:00"}, {"name": "league/oauth2-client", "version": "2.8.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "9df2924ca644736c835fc60466a3a60390d334f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/9df2924ca644736c835fc60466a3a60390d334f9", "reference": "9df2924ca644736c835fc60466a3a60390d334f9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "php": "^7.1 || >=8.0.0 <8.5.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.11"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.8.1"}, "time": "2025-02-26T04:37:30+00:00"}, {"name": "lstrojny/fxmlrpc", "version": "0.22.0", "source": {"type": "git", "url": "https://github.com/lstrojny/fxmlrpc.git", "reference": "866ae7835beb0d0e36ec2ae183fb86f4dafd1b34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lstrojny/fxmlrpc/zipball/866ae7835beb0d0e36ec2ae183fb86f4dafd1b34", "reference": "866ae7835beb0d0e36ec2ae183fb86f4dafd1b34", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "php-http/discovery": "^1.0"}, "require-dev": {"lstrojny/hmmmath": "^0.8", "monolog/monolog": "~1", "php-http/guzzle7-adapter": "^0.1", "php-http/httplug": "^1.0 || ^2.0", "php-http/message": "^1.2", "php-http/message-factory": "^1.0", "phpunit/phpunit": "^8.5", "psr/http-factory": "*", "psr/log": "*", "symfony/process": "~2.1", "zendframework/zend-diactoros": "^1.3", "zendframework/zend-log": "~2", "zendframework/zendframework1": "~1"}, "suggest": {"monolog/monolog": "To integrate monolog as logger", "php-http/curl-client": "A simple PSR-7 compatible HTTP client", "php-http/guzzle5-adapter": "Guzzle 5 PSR-7 adapter", "php-http/guzzle6-adapter": "Guzzle 6 PSR-7 adapter", "php-http/guzzle7-adapter": "Guzzle 7 PSR-7 adapter", "php-http/message": "For PSR-7 message factories", "zendframework/zend-log": "To use ZF2 Zend\\Log\\Logger as a logger", "zendframework/zendframework1": "To use ZF1 Log"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"fXmlRpc\\": "src/fXmlRpc"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://usrportage.de"}], "description": "Fast and tiny XML/RPC client with bridges for various HTTP clients", "keywords": ["api", "performance", "rpc", "xml", "xmlrpc"], "support": {"issues": "https://github.com/lstrojny/fxmlrpc/issues", "source": "https://github.com/lstrojny/fxmlrpc/tree/0.22.0"}, "time": "2021-06-21T09:30:29+00:00"}, {"name": "matgyver1/monolog-firehose-handler", "version": "dev-master", "source": {"type": "git", "url": "**************:matgyver1/monolog-firehose-handler.git", "reference": "9c4a37da59b54725121d45ebf6bd86c3246c8c79"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/matgyver1%2Fmonolog-firehose-handler/repository/archive.zip?sha=9c4a37da59b54725121d45ebf6bd86c3246c8c79", "reference": "9c4a37da59b54725121d45ebf6bd86c3246c8c79", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.2", "monolog/monolog": "^3.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"MatGyver\\Monolog\\": "src"}}, "license": ["MIT"], "description": "Firehose handler for Monolog", "support": {"source": "https://gitlab.com/matgyver1/monolog-firehose-handler/-/tree/master", "issues": "https://gitlab.com/matgyver1/monolog-firehose-handler/-/issues"}, "time": "2024-07-11T12:00:51+02:00"}, {"name": "matgyver1/symfony-maker", "version": "dev-master", "source": {"type": "git", "url": "**************:matgyver1/symfony-maker.git", "reference": "aaf22e0d9adb756bdad50eca7e65cd7a32b73c14"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/matgyver1%2Fsymfony-maker/repository/archive.zip?sha=aaf22e0d9adb756bdad50eca7e65cd7a32b73c14", "reference": "aaf22e0d9adb756bdad50eca7e65cd7a32b73c14", "shasum": ""}, "require": {"doctrine/annotations": "^2.0", "doctrine/doctrine-bundle": "^2.0", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^2.0", "ext-ctype": "*", "ext-iconv": "*", "php": "^8.1.0", "symfony/console": "^6.0", "symfony/dotenv": "^6.0", "symfony/flex": "^2.0", "symfony/framework-bundle": "^6.0", "symfony/maker-bundle": "1.43", "symfony/yaml": "^6.0"}, "conflict": {"symfony/symfony": "*"}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php56": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php71": "*"}, "default-branch": true, "type": "project", "extra": {"symfony": {"allow-contrib": false, "require": "^6.0"}}, "autoload": {"psr-4": {"MatGyver\\": "src/"}}, "autoload-dev": {"psr-4": {"MatGyver\\Tests\\": "tests/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "license": ["proprietary"], "support": {"source": "https://gitlab.com/matgyver1/symfony-maker/-/tree/master", "issues": "https://gitlab.com/matgyver1/symfony-maker/-/issues"}, "time": "2023-09-14T15:28:06+02:00"}, {"name": "maximebf/debugbar", "version": "v1.23.6", "source": {"type": "git", "url": "https://github.com/php-debugbar/php-debugbar.git", "reference": "4b3d5f1afe09a7db5a9d3282890f49f6176d6542"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-debugbar/php-debugbar/zipball/4b3d5f1afe09a7db5a9d3282890f49f6176d6542", "reference": "4b3d5f1afe09a7db5a9d3282890f49f6176d6542", "shasum": ""}, "require": {"php": "^7.2|^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4|^5|^6|^7"}, "require-dev": {"dbrekelmans/bdi": "^1", "phpunit/phpunit": "^8|^9", "symfony/panther": "^1|^2.1", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.23-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "support": {"issues": "https://github.com/php-debugbar/php-debugbar/issues", "source": "https://github.com/php-debugbar/php-debugbar/tree/v1.23.6"}, "abandoned": "php-debugbar/php-debugbar", "time": "2025-02-13T12:22:36+00:00"}, {"name": "mck89/peast", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5", "reference": "3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17.0-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.17.0"}, "time": "2025-03-07T19:44:14+00:00"}, {"name": "merci-facteur/merci-facteur-api", "version": "master", "source": {"type": "git", "url": "https://github.com/MerciFacteur/Merci-facteur-API.git", "reference": "master"}, "type": "library"}, {"name": "microsoft/kiota-abstractions", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-abstractions-php.git", "reference": "53beaf41d810cd757a89f55152aa686aa74565bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-abstractions-php/zipball/53beaf41d810cd757a89f55152aa686aa74565bb", "reference": "53beaf41d810cd757a89f55152aa686aa74565bb", "shasum": ""}, "require": {"doctrine/annotations": "^1.13 || ^2.0", "open-telemetry/sdk": "^1.0.0", "php": "^7.4 || ^8.0", "php-http/promise": "~1.2.0", "psr/http-message": "^1.1 || ^2.0", "ramsey/uuid": "^4.2.3", "stduritemplate/stduritemplate": "^0.0.53 || ^0.0.54 || ^0.0.55 || ^0.0.56 || ^0.0.57 || ^0.0.59 || ^1.0.0 || ^2.0.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Abstractions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "Abstractions for Ki<PERSON>", "support": {"source": "https://github.com/microsoft/kiota-abstractions-php/tree/1.5.0"}, "time": "2025-02-17T16:44:43+00:00"}, {"name": "microsoft/kiota-authentication-phpleague", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-authentication-phpleague-php.git", "reference": "2d8e1e200ead2d883f494d767d6a0a57eff62a8b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-authentication-phpleague-php/zipball/2d8e1e200ead2d883f494d767d6a0a57eff62a8b", "reference": "2d8e1e200ead2d883f494d767d6a0a57eff62a8b", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "firebase/php-jwt": "^v6.0.0", "league/oauth2-client": "^2.6.1", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0", "ramsey/uuid": "^4.2.3"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Authentication\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>"}], "description": "Authentication provider for Kiota using the PHP League OAuth 2.0 client to authenticate against the Microsoft Identity platform", "support": {"source": "https://github.com/microsoft/kiota-authentication-phpleague-php/tree/1.5.0"}, "time": "2025-02-19T06:24:55+00:00"}, {"name": "microsoft/kiota-http-guzzle", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-http-guzzle-php.git", "reference": "4a9c4b69819712af5c62c2b978a0123ecf8f1208"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-http-guzzle-php/zipball/4a9c4b69819712af5c62c2b978a0123ecf8f1208", "reference": "4a9c4b69819712af5c62c2b978a0123ecf8f1208", "shasum": ""}, "require": {"ext-json": "*", "ext-zlib": "*", "guzzlehttp/guzzle": "^7.4.5", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Http\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>"}], "description": "Kiota HTTP Request Adapter implementation", "support": {"source": "https://github.com/microsoft/kiota-http-guzzle-php/tree/1.5.0"}, "time": "2025-02-19T06:27:54+00:00"}, {"name": "microsoft/kiota-serialization-form", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-serialization-form-php.git", "reference": "d26a199a2a5ca5cae3654a6106ff4f9560809277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-serialization-form-php/zipball/d26a199a2a5ca5cae3654a6106ff4f9560809277", "reference": "d26a199a2a5ca5cae3654a6106ff4f9560809277", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/psr7": "^1.6 || ^2", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22", "roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Serialization\\Form\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "Form implementation of Kiota abstractions serialization.", "support": {"source": "https://github.com/microsoft/kiota-serialization-form-php/tree/1.5.0"}, "time": "2025-02-19T06:29:35+00:00"}, {"name": "microsoft/kiota-serialization-json", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-serialization-json-php.git", "reference": "ec99e2c22c5229b1b39f5957abcb430f28cc448c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-serialization-json-php/zipball/ec99e2c22c5229b1b39f5957abcb430f28cc448c", "reference": "ec99e2c22c5229b1b39f5957abcb430f28cc448c", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/psr7": "^1.6 || ^2", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22", "roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Serialization\\Json\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "Implementation of Kiota serialization abstractions using Json.", "support": {"source": "https://github.com/microsoft/kiota-serialization-json-php/tree/1.5.0"}, "time": "2025-02-19T06:28:58+00:00"}, {"name": "microsoft/kiota-serialization-multipart", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-serialization-multipart-php.git", "reference": "6668768223cf0760a5cfab76232883eae85e7c15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-serialization-multipart-php/zipball/6668768223cf0760a5cfab76232883eae85e7c15", "reference": "6668768223cf0760a5cfab76232883eae85e7c15", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/psr7": "^1.6 || ^2", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22", "roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Serialization\\Multipart\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "Multipart implementation of Kiota abstractions serialization.", "support": {"source": "https://github.com/microsoft/kiota-serialization-multipart-php/tree/1.5.0"}, "time": "2025-02-19T06:29:47+00:00"}, {"name": "microsoft/kiota-serialization-text", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/microsoft/kiota-serialization-text-php.git", "reference": "341ae866e50341f63f1b0320cb6c08582ae6709a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoft/kiota-serialization-text-php/zipball/341ae866e50341f63f1b0320cb6c08582ae6709a", "reference": "341ae866e50341f63f1b0320cb6c08582ae6709a", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.6 || ^2", "microsoft/kiota-abstractions": "^1.5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.12.16", "phpunit/phpunit": "^9.6.22"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Kiota\\Serialization\\Text\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>"}], "description": "Implementation of Serialization Abstractions for text/plain content", "support": {"source": "https://github.com/microsoft/kiota-serialization-text-php/tree/1.5.0"}, "time": "2025-02-19T06:29:21+00:00"}, {"name": "microsoft/microsoft-graph", "version": "v2.36.0", "source": {"type": "git", "url": "https://github.com/microsoftgraph/msgraph-sdk-php.git", "reference": "744bcbd446ea64434f1f075500668d8c5b626320"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoftgraph/msgraph-sdk-php/zipball/744bcbd446ea64434f1f075500668d8c5b626320", "reference": "744bcbd446ea64434f1f075500668d8c5b626320", "shasum": ""}, "require": {"microsoft/microsoft-graph-core": "^2.2.1", "php": "^8.0 || ^7.4"}, "require-dev": {"phpstan/phpstan": "^0.12.90 || ^1.0.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Graph\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "The Microsoft Graph SDK for PHP", "homepage": "https://developer.microsoft.com/en-us/graph", "support": {"issues": "https://github.com/microsoftgraph/msgraph-sdk-php/issues", "source": "https://github.com/microsoftgraph/msgraph-sdk-php/tree/v2.36.0"}, "time": "2025-06-03T16:17:28+00:00"}, {"name": "microsoft/microsoft-graph-core", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/microsoftgraph/msgraph-sdk-php-core.git", "reference": "783111f9e81db9da20cc2dbd48aa1876b500ba15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/microsoftgraph/msgraph-sdk-php-core/zipball/783111f9e81db9da20cc2dbd48aa1876b500ba15", "reference": "783111f9e81db9da20cc2dbd48aa1876b500ba15", "shasum": ""}, "require": {"ext-json": "*", "microsoft/kiota-authentication-phpleague": "^1.5.0", "microsoft/kiota-http-guzzle": "^1.5.0", "microsoft/kiota-serialization-form": "^1.5.0", "microsoft/kiota-serialization-json": "^1.5.0", "microsoft/kiota-serialization-multipart": "^1.5.0", "microsoft/kiota-serialization-text": "^1.5.0", "php": "^8.0 || ^7.4"}, "require-dev": {"mikey179/vfsstream": "^1.2", "phpstan/phpstan": "^0.12.90 || ^1.0.0", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\Graph\\Core\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Microsoft Graph Client Tooling", "email": "<EMAIL>", "role": "Developer"}], "description": "The Microsoft Graph Core SDK for PHP", "homepage": "https://developer.microsoft.com/en-us/graph", "support": {"issues": "https://github.com/microsoftgraph/msgraph-sdk-php-core/issues", "source": "https://github.com/microsoftgraph/msgraph-sdk-php-core/tree/v2.3.1"}, "time": "2025-03-18T14:27:59+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "3.74.3", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "39582ab62f86b40e4edb698159f895929a29c346"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/39582ab62f86b40e4edb698159f895929a29c346", "reference": "39582ab62f86b40e4edb698159f895929a29c346", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Detection\\": "src/"}, "classmap": ["src/MobileDetect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "https://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/3.74.3"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-10-27T16:28:04+00:00"}, {"name": "mollie/mollie-api-php", "version": "v2.79.1", "source": {"type": "git", "url": "https://github.com/mollie/mollie-api-php.git", "reference": "4c1cf5f603178dd15bdf60b5e3999f91bb59f5b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mollie/mollie-api-php/zipball/4c1cf5f603178dd15bdf60b5e3999f91bb59f5b0", "reference": "4c1cf5f603178dd15bdf60b5e3999f91bb59f5b0", "shasum": ""}, "require": {"composer/ca-bundle": "^1.2", "ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "php": "^7.2|^8.0"}, "require-dev": {"eloquent/liberator": "^2.0||^3.0", "friendsofphp/php-cs-fixer": "^3.0", "guzzlehttp/guzzle": "^6.3 || ^7.0", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5 || ^9.5"}, "suggest": {"mollie/oauth2-mollie-php": "Use OAuth to authenticate with the Mollie API. This is needed for some endpoints. Visit https://docs.mollie.com/ for more information."}, "type": "library", "autoload": {"psr-4": {"Mollie\\Api\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "Mollie B.V.", "email": "<EMAIL>"}], "description": "Mollie API client library for PHP. Mollie is a European Payment Service provider and offers international payment methods such as Mastercard, VISA, American Express and PayPal, and local payment methods such as iDEAL, Bancontact, SOFORT Banking, SEPA direct debit, Belfius Direct Net, KBC Payment Button and various gift cards such as Podiumcadeaukaart and fashioncheque.", "homepage": "https://www.mollie.com/en/developers", "keywords": ["Apple Pay", "CBC", "Przelewy24", "api", "bancontact", "banktransfer", "belfius", "belfius direct net", "charges", "creditcard", "direct debit", "fashioncheque", "gateway", "gift cards", "ideal", "ing<PERSON>ep<PERSON>", "intersolve", "kbc", "klarna", "mister<PERSON>h", "mollie", "paylater", "payment", "payments", "paypal", "paysafecard", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recurring", "refunds", "sepa", "service", "sliceit", "sofort", "sofortbanking", "subscriptions"], "support": {"issues": "https://github.com/mollie/mollie-api-php/issues", "source": "https://github.com/mollie/mollie-api-php/tree/v2.79.1"}, "time": "2025-05-06T10:55:09+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "time": "2024-09-04T18:46:31+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "nyholm/psr7-server", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7-server.git", "reference": "4335801d851f554ca43fa6e7d2602141538854dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7-server/zipball/4335801d851f554ca43fa6e7d2602141538854dc", "reference": "4335801d851f554ca43fa6e7d2602141538854dc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"nyholm/nsa": "^1.1", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^7.0 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Nyholm\\Psr7Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "Helper classes to handle PSR-7 server requests", "homepage": "http://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7-server/issues", "source": "https://github.com/Nyholm/psr7-server/tree/1.1.0"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-08T09:30:43+00:00"}, {"name": "open-telemetry/api", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/api.git", "reference": "4e3bb38e069876fb73c2ce85c89583bf2b28cd86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/api/zipball/4e3bb38e069876fb73c2ce85c89583bf2b28cd86", "reference": "4e3bb38e069876fb73c2ce85c89583bf2b28cd86", "shasum": ""}, "require": {"open-telemetry/context": "^1.0", "php": "^8.1", "psr/log": "^1.1|^2.0|^3.0", "symfony/polyfill-php82": "^1.26"}, "conflict": {"open-telemetry/sdk": "<=1.0.8"}, "type": "library", "extra": {"spi": {"OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\HookManagerInterface": ["OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\ExtensionHookManager"]}, "branch-alias": {"dev-main": "1.1.x-dev"}}, "autoload": {"files": ["Trace/functions.php"], "psr-4": {"OpenTelemetry\\API\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "API for OpenTelemetry PHP.", "keywords": ["Metrics", "api", "apm", "logging", "opentelemetry", "otel", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-05-07T12:32:21+00:00"}, {"name": "open-telemetry/context", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/context.git", "reference": "1eb2b837ee9362db064a6b65d5ecce15a9f9f020"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/context/zipball/1eb2b837ee9362db064a6b65d5ecce15a9f9f020", "reference": "1eb2b837ee9362db064a6b65d5ecce15a9f9f020", "shasum": ""}, "require": {"php": "^8.1", "symfony/polyfill-php82": "^1.26"}, "suggest": {"ext-ffi": "To allow context switching in Fibers"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["fiber/initialize_fiber_handler.php"], "psr-4": {"OpenTelemetry\\Context\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "Context implementation for OpenTelemetry PHP.", "keywords": ["Context", "opentelemetry", "otel"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-05-07T23:36:50+00:00"}, {"name": "open-telemetry/sdk", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/sdk.git", "reference": "cd0d7367599717fc29e04eb8838ec061e6c2c657"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/sdk/zipball/cd0d7367599717fc29e04eb8838ec061e6c2c657", "reference": "cd0d7367599717fc29e04eb8838ec061e6c2c657", "shasum": ""}, "require": {"ext-json": "*", "nyholm/psr7-server": "^1.1", "open-telemetry/api": "~1.0 || ~1.1", "open-telemetry/context": "^1.0", "open-telemetry/sem-conv": "^1.0", "php": "^8.1", "php-http/discovery": "^1.14", "psr/http-client": "^1.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0.1|^2.0", "psr/log": "^1.1|^2.0|^3.0", "ramsey/uuid": "^3.0 || ^4.0", "symfony/polyfill-mbstring": "^1.23", "symfony/polyfill-php82": "^1.26", "tbachert/spi": "^1.0.1"}, "suggest": {"ext-gmp": "To support unlimited number of synchronous metric readers", "ext-mbstring": "To increase performance of string operations", "open-telemetry/sdk-configuration": "File-based OpenTelemetry SDK configuration"}, "type": "library", "extra": {"spi": {"OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\HookManagerInterface": ["OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\ExtensionHookManager"]}, "branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["Common/Util/functions.php", "Logs/Exporter/_register.php", "Metrics/MetricExporter/_register.php", "Propagation/_register.php", "Trace/SpanExporter/_register.php", "Common/Dev/Compatibility/_load.php", "_autoload.php"], "psr-4": {"OpenTelemetry\\SDK\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "SDK for OpenTelemetry PHP.", "keywords": ["Metrics", "apm", "logging", "opentelemetry", "otel", "sdk", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-05-22T02:33:34+00:00"}, {"name": "open-telemetry/sem-conv", "version": "1.32.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/sem-conv.git", "reference": "16585cc0dbc3032a318e274043454679430d2ebf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/sem-conv/zipball/16585cc0dbc3032a318e274043454679430d2ebf", "reference": "16585cc0dbc3032a318e274043454679430d2ebf", "shasum": ""}, "require": {"php": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"OpenTelemetry\\SemConv\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "Semantic conventions for OpenTelemetry PHP.", "keywords": ["Metrics", "apm", "logging", "opentelemetry", "otel", "semantic conventions", "semconv", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-05-05T03:58:53+00:00"}, {"name": "openai-php/client", "version": "v0.15.0", "source": {"type": "git", "url": "https://github.com/openai-php/client.git", "reference": "4db080738104be6a1144fa9f203c428ea4e58128"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openai-php/client/zipball/4db080738104be6a1144fa9f203c428ea4e58128", "reference": "4db080738104be6a1144fa9f203c428ea4e58128", "shasum": ""}, "require": {"php": "^8.2.0", "php-http/discovery": "^1.20.0", "php-http/multipart-stream-builder": "^1.4.2", "psr/http-client": "^1.0.3", "psr/http-client-implementation": "^1.0.1", "psr/http-factory-implementation": "*", "psr/http-message": "^1.1.0|^2.0.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.9.3", "guzzlehttp/psr7": "^2.7.1", "laravel/pint": "^1.24.0", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8.0", "pestphp/pest": "^3.8.2|^4.0.0", "pestphp/pest-plugin-arch": "^3.1.1|^4.0.0", "pestphp/pest-plugin-type-coverage": "^3.5.1|^4.0.0", "phpstan/phpstan": "^1.12.25", "symfony/var-dumper": "^7.2.6"}, "type": "library", "autoload": {"files": ["src/OpenAI.php"], "psr-4": {"OpenAI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}], "description": "OpenAI PHP is a supercharged PHP API client that allows you to interact with the Open AI API", "keywords": ["GPT-3", "api", "client", "codex", "dall-e", "language", "natural", "openai", "php", "processing", "sdk"], "support": {"issues": "https://github.com/openai-php/client/issues", "source": "https://github.com/openai-php/client/tree/v0.15.0"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/gehrisandro", "type": "github"}, {"url": "https://github.com/nunomaduro", "type": "github"}], "time": "2025-08-04T17:44:21+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:36:18+00:00"}, {"name": "php-di/invoker", "version": "2.3.6", "source": {"type": "git", "url": "https://github.com/PHP-DI/Invoker.git", "reference": "59f15608528d8a8838d69b422a919fd6b16aa576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/59f15608528d8a8838d69b422a919fd6b16aa576", "reference": "59f15608528d8a8838d69b422a919fd6b16aa576", "shasum": ""}, "require": {"php": ">=7.3", "psr/container": "^1.0|^2.0"}, "require-dev": {"athletic/athletic": "~0.1.8", "mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"Invoker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Generic and extensible callable invoker", "homepage": "https://github.com/PHP-DI/Invoker", "keywords": ["callable", "dependency", "dependency-injection", "injection", "invoke", "invoker"], "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.6"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2025-01-17T12:49:27+00:00"}, {"name": "php-di/php-di", "version": "7.0.11", "source": {"type": "git", "url": "https://github.com/PHP-DI/PHP-DI.git", "reference": "32f111a6d214564520a57831d397263e8946c1d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/32f111a6d214564520a57831d397263e8946c1d2", "reference": "32f111a6d214564520a57831d397263e8946c1d2", "shasum": ""}, "require": {"laravel/serializable-closure": "^1.0 || ^2.0", "php": ">=8.0", "php-di/invoker": "^2.0", "psr/container": "^1.1 || ^2.0"}, "provide": {"psr/container-implementation": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3", "friendsofphp/proxy-manager-lts": "^1", "mnapoli/phpunit-easymock": "^1.3", "phpunit/phpunit": "^9.6 || ^10 || ^11", "vimeo/psalm": "^5|^6"}, "suggest": {"friendsofphp/proxy-manager-lts": "Install it if you want to use lazy injection (version ^1)"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"DI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The dependency injection container for humans", "homepage": "https://php-di.org/", "keywords": ["PSR-11", "container", "container-interop", "dependency injection", "di", "ioc", "psr11"], "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.11"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/php-di/php-di", "type": "tidelift"}], "time": "2025-06-03T07:45:57+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/10086e6de6f53489cca5ecc45b6f468604d3460e", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.4.2"}, "time": "2024-09-04T13:22:54+00:00"}, {"name": "php-http/promise", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/44a67cb59f708f826f3bec35f22030b3edb90119", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.2.1"}, "time": "2023-11-08T12:57:08+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpoffice/math", "version": "0.3.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "fc31c8f57a7a81f962cbf389fd89f4d9d06fc99a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/fc31c8f57a7a81f962cbf389fd89f4d9d06fc99a", "reference": "fc31c8f57a7a81f962cbf389fd89f4d9d06fc99a", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "support": {"issues": "https://github.com/PHPOffice/Math/issues", "source": "https://github.com/PHPOffice/Math/tree/0.3.0"}, "time": "2025-05-29T08:31:49+00:00"}, {"name": "phpoffice/phpword", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "6d75328229bc93790b37e93741adf70646cea958"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/6d75328229bc93790b37e93741adf70646cea958", "reference": "6d75328229bc93790b37e93741adf70646cea958", "shasum": ""}, "require": {"ext-dom": "*", "ext-gd": "*", "ext-json": "*", "ext-xml": "*", "ext-zip": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.3"}, "require-dev": {"dompdf/dompdf": "^2.0 || ^3.0", "ext-libxml": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^7.0 || ^8.0", "phpmd/phpmd": "^2.13", "phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpstan/phpstan-phpunit": "^1.0 || ^2.0", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "support": {"issues": "https://github.com/PHPOffice/PHPWord/issues", "source": "https://github.com/PHPOffice/PHPWord/tree/1.4.0"}, "time": "2025-06-05T10:32:36+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "phpxmlrpc/phpxmlrpc", "version": "4.11.1", "source": {"type": "git", "url": "https://github.com/gggeek/phpxmlrpc.git", "reference": "06b9d7275d637f6859527091a54a5edfe8a16749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gggeek/phpxmlrpc/zipball/06b9d7275d637f6859527091a54a5edfe8a16749", "reference": "06b9d7275d637f6859527091a54a5edfe8a16749", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.4.0 || ^7.0 || ^8.0"}, "conflict": {"phpxmlrpc/extras": "<= 1.0.0-beta2", "phpxmlrpc/jsonrpc": "<= 1.0.0-beta1"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-mbstring": "*", "phpunit/phpunit": "^4.8 || ^5.0 || ^8.5.14", "phpunit/phpunit-selenium": "*", "yoast/phpunit-polyfills": "*"}, "suggest": {"ext-curl": "Needed for HTTPS, HTTP2 and HTTP 1.1 support, NTLM Auth etc...", "ext-mbstring": "Needed to allow reception of requests/responses in character sets other than ASCII,LATIN-1,UTF-8", "ext-zlib": "Needed for sending compressed requests and receiving compressed responses, if cURL is not available", "phpxmlrpc/extras": "Adds more featured Server classes, including self-documenting and ajax-enabled servers", "phpxmlrpc/jsonrpc": "Adds support for the JSON-RPC protocol"}, "type": "library", "autoload": {"psr-4": {"PhpXmlRpc\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A php library for building xmlrpc clients and servers", "homepage": "https://gggeek.github.io/phpxmlrpc/", "keywords": ["webservices", "xml-rpc", "xmlrpc"], "support": {"issues": "https://github.com/gggeek/phpxmlrpc/issues", "source": "https://github.com/gggeek/phpxmlrpc/tree/4.11.1"}, "time": "2025-01-17T17:04:37+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/ef29f6d262798707a9edd554e2b82517ef3a9376", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/2.0.0"}, "time": "2021-07-14T16:41:46+00:00"}, {"name": "psr/simple-cache", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/8707bf3cea6f710bf6ef05491234e3ab06f6432a", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/2.0.0"}, "time": "2021-10-29T13:22:09+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.8.1", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.8.1"}, "time": "2025-06-01T06:28:46+00:00"}, {"name": "robmorgan/phinx", "version": "0.13.4", "source": {"type": "git", "url": "https://github.com/cakephp/phinx.git", "reference": "18e06e4a2b18947663438afd2f467e17c62e867d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/phinx/zipball/18e06e4a2b18947663438afd2f467e17c62e867d", "reference": "18e06e4a2b18947663438afd2f467e17c62e867d", "shasum": ""}, "require": {"cakephp/database": "^4.0", "php": ">=7.2", "psr/container": "^1.0 || ^2.0", "symfony/config": "^3.4|^4.0|^5.0|^6.0", "symfony/console": "^3.4|^4.0|^5.0|^6.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^4.0", "ext-json": "*", "ext-pdo": "*", "phpunit/phpunit": "^8.5|^9.3", "sebastian/comparator": ">=1.2.3", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"ext-json": "Install if using JSON configuration format", "ext-pdo": "PDO extension is needed", "symfony/yaml": "Install if using YAML configuration format"}, "bin": ["bin/phinx"], "type": "library", "autoload": {"psr-4": {"Phinx\\": "src/Phinx/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://robmorgan.id.au", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://shadowhand.me", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "CakePHP Community", "homepage": "https://github.com/cakephp/phinx/graphs/contributors", "role": "Developer"}], "description": "Phinx makes it ridiculously easy to manage the database migrations for your PHP app.", "homepage": "https://phinx.org", "keywords": ["database", "database migrations", "db", "migrations", "phinx"], "support": {"issues": "https://github.com/cakephp/phinx/issues", "source": "https://github.com/cakephp/phinx/tree/0.13.4"}, "time": "2023-01-07T00:42:55+00:00"}, {"name": "rob<PERSON><PERSON>/twofactor<PERSON>h", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/RobThree/TwoFactorAuth.git", "reference": "ab93dd41ced7818ecda83a37c2741ee38bb72fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobThree/TwoFactorAuth/zipball/ab93dd41ced7818ecda83a37c2741ee38bb72fb7", "reference": "ab93dd41ced7818ecda83a37c2741ee38bb72fb7", "shasum": ""}, "require": {"php": ">=8.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.13", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9"}, "suggest": {"bacon/bacon-qr-code": "Needed for BaconQrCodeProvider provider", "endroid/qr-code": "Needed for EndroidQrCodeProvider"}, "type": "library", "autoload": {"psr-4": {"RobThree\\Auth\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://robiii.me", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/NicolasCARPi", "role": "Developer"}, {"name": "Will Power", "homepage": "https://github.com/willpower232", "role": "Developer"}], "description": "Two Factor Authentication", "homepage": "https://github.com/RobThree/TwoFactorAuth", "keywords": ["Authentication", "MFA", "Multi Factor Authentication", "Two Factor Authentication", "authenticator", "authy", "php", "tfa"], "support": {"issues": "https://github.com/RobThree/TwoFactorAuth/issues", "source": "https://github.com/RobThree/TwoFactorAuth"}, "funding": [{"url": "https://paypal.me/robiii", "type": "custom"}, {"url": "https://github.com/RobThree", "type": "github"}], "time": "2023-11-14T12:50:27+00:00"}, {"name": "seld/signal-handler", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/Seldaek/signal-handler.git", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"phpstan/phpstan": "^1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^7.5.20 || ^8.5.23", "psr/log": "^1 || ^2 || ^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "support": {"issues": "https://github.com/Seldaek/signal-handler/issues", "source": "https://github.com/Seldaek/signal-handler/tree/2.0.2"}, "time": "2023-09-03T09:24:00+00:00"}, {"name": "setasign/fpdf", "version": "1.8.6", "source": {"type": "git", "url": "https://github.com/Setasign/FPDF.git", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDF/zipball/0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "shasum": ""}, "require": {"ext-gd": "*", "ext-zlib": "*"}, "type": "library", "autoload": {"classmap": ["fpdf.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://fpdf.org/"}], "description": "FPDF is a PHP class which allows to generate PDF files with pure PHP. F from FPDF stands for Free: you may use it for any kind of usage and modify it to suit your needs.", "homepage": "http://www.fpdf.org", "keywords": ["fpdf", "pdf"], "support": {"source": "https://github.com/Setasign/FPDF/tree/1.8.6"}, "time": "2023-06-26T14:44:25+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/67c31f5e50c93c20579ca9e23035d8c540b51941", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2025-02-05T13:22:35+00:00"}, {"name": "setasign/fpdi-fpdf", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI-FPDF.git", "reference": "f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI-FPDF/zipball/f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357", "reference": "f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357", "shasum": ""}, "require": {"setasign/fpdf": "^1.8.2", "setasign/fpdi": "^2.3"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "Kind of metadata package for dependencies of the latest versions of FPDI and FPDF.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"source": "https://github.com/Setasign/FPDI-FPDF/tree/v2.3.0"}, "abandoned": true, "time": "2020-02-19T12:21:53+00:00"}, {"name": "setasign/fpdi_pdf-parser", "version": "2.1.4", "dist": {"type": "zip", "url": "https://www.setasign.com/downloads/6913817/FPDI_PDF-Parser-2.1.4.zip"}, "require": {"setasign/fpdi": "^2.6.0"}, "type": "library", "autoload": {"psr-4": {"setasign\\FpdiPdfParser\\": "src/"}}, "license": ["proprietary"], "support": {"email": "<EMAIL>"}, "time": "2024-12-10T00:00:00+00:00"}, {"name": "smalot/pdfparser", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/8440edbf58c8596074e78ada38dcb0bd041a5948", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.12.0"}, "time": "2025-03-31T13:16:09+00:00"}, {"name": "spipu/html2pdf", "version": "v5.2.8", "source": {"type": "git", "url": "https://github.com/spipu/html2pdf.git", "reference": "6c94dcd48c94c6c73f206629839c1ebd81e8c726"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spipu/html2pdf/zipball/6c94dcd48c94c6c73f206629839c1ebd81e8c726", "reference": "6c94dcd48c94c6c73f206629839c1ebd81e8c726", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "php": "^5.6 || ^7.0 || ^8.0", "tecnickcom/tcpdf": "^6.3"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^9.0"}, "suggest": {"ext-gd": "Allows to embed images into the PDF", "fagundes/zff-html2pdf": "if you need to integrate Html2Pdf with Zend Framework 2 (zf2)"}, "type": "library", "autoload": {"psr-4": {"Spipu\\Html2Pdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/spipu", "role": "Developer"}], "description": "Html2Pdf is a HTML to PDF converter written in PHP5 (it uses TCPDF). OFFICIAL PACKAGE", "homepage": "http://html2pdf.fr/", "keywords": ["html", "html2pdf", "pdf"], "support": {"issues": "https://github.com/spipu/html2pdf/issues", "source": "https://github.com/spipu/html2pdf/tree/v5.2.8"}, "time": "2023-07-18T14:52:59+00:00"}, {"name": "stduritemplate/stduritemplate", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/std-uritemplate/std-uritemplate-php.git", "reference": "57f10976540d2fba3df5475c84dad6243eb3f36e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/std-uritemplate/std-uritemplate-php/zipball/57f10976540d2fba3df5475c84dad6243eb3f36e", "reference": "57f10976540d2fba3df5475c84dad6243eb3f36e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"StdUriTemplate\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Std UriTemplate, RFC 6570 implementation", "support": {"source": "https://github.com/std-uritemplate/std-uritemplate-php/tree/2.0.5"}, "time": "2025-05-14T13:10:06+00:00"}, {"name": "stevenmaguire/oauth2-microsoft", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/stevenmaguire/oauth2-microsoft.git", "reference": "f24f79d8c47224d24a1240270ca3b0a4c1521ed4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stevenmaguire/oauth2-microsoft/zipball/f24f79d8c47224d24a1240270ca3b0a4c1521ed4", "reference": "f24f79d8c47224d24a1240270ca3b0a4c1521ed4", "shasum": ""}, "require": {"league/oauth2-client": "^2.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"Stevenmaguire\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "stevenmagu<PERSON>@gmail.com", "homepage": "https://github.com/stevenmaguire"}], "description": "Microsoft OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["authorisation", "authorization", "client", "microsoft", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/stevenmaguire/oauth2-microsoft/issues", "source": "https://github.com/stevenmaguire/oauth2-microsoft/tree/master"}, "time": "2017-06-07T13:42:47+00:00"}, {"name": "stripe/stripe-php", "version": "v12.8.0", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "6b6f4a775ad46fee4b1df2df4fdfa574365b1621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/6b6f4a775ad46fee4b1df2df4fdfa574365b1621", "reference": "6b6f4a775ad46fee4b1df2df4fdfa574365b1621", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v12.8.0"}, "time": "2023-10-16T18:04:12+00:00"}, {"name": "supervisorphp/supervisor", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/supervisorphp/supervisor.git", "reference": "e8a5ac306748c5fc6f53ada6a68648eef5a1a286"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/supervisorphp/supervisor/zipball/e8a5ac306748c5fc6f53ada6a68648eef5a1a286", "reference": "e8a5ac306748c5fc6f53ada6a68648eef5a1a286", "shasum": ""}, "require": {"lstrojny/fxmlrpc": ">=0.12", "php": ">=8.1", "psr/log": ">=1"}, "require-dev": {"behat/behat": "^3.0", "ext-pcntl": "*", "ext-posix": "*", "guzzlehttp/guzzle": "^7", "php-http/httplug": "^2.1", "php-http/message": "^1.8", "php-parallel-lint/php-console-highlighter": "^1", "php-parallel-lint/php-parallel-lint": "^1.3", "phpspec/phpspec": "^7", "phpstan/phpstan": "^1", "phpstan/phpstan-strict-rules": "^1", "roave/security-advisories": "dev-latest", "supervisorphp/configuration": "^0.3"}, "type": "library", "autoload": {"psr-4": {"Supervisor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP library for managing Supervisor through XML-RPC API", "homepage": "http://supervisorphp.com", "keywords": ["process manager", "supervisor"], "support": {"issues": "https://github.com/supervisorphp/supervisor/issues", "source": "https://github.com/supervisorphp/supervisor/tree/5.1.0"}, "time": "2022-11-21T11:48:01+00:00"}, {"name": "symfony/amazon-mailer", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/amazon-mailer.git", "reference": "9f02a352ceefdb89b318fd98230a6f9f6ae0ba95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amazon-mailer/zipball/9f02a352ceefdb89b318fd98230a6f9f6ae0ba95", "reference": "9f02a352ceefdb89b318fd98230a6f9f6ae0ba95", "shasum": ""}, "require": {"async-aws/ses": "^1.0", "php": ">=8.1", "symfony/mailer": "^5.4.21|^6.2.7|^7.0"}, "require-dev": {"symfony/http-client": "^5.4|^6.0|^7.0"}, "type": "symfony-mailer-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\Bridge\\Amazon\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Amazon Mailer Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/amazon-mailer/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/cache", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-08T08:21:20+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/config", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/af5917a3b1571f54689e56677a3f06440d2fe4c7", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T06:00:01+00:00"}, {"name": "symfony/console", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-07T07:05:04+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9", "reference": "8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-17T07:35:26+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "1df0cb5ce77ddfa0bdbca410009e3822567a6a19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/1df0cb5ce77ddfa0bdbca410009e3822567a6a19", "reference": "1df0cb5ce77ddfa0bdbca410009e3822567a6a19", "shasum": ""}, "require": {"doctrine/event-manager": "^2", "doctrine/persistence": "^3.1|^4", "php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/collections": "<1.8", "doctrine/dbal": "<3.6", "doctrine/lexer": "<1.1", "doctrine/orm": "<2.15", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/form": "<6.4.6|>=7,<7.0.6", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/lock": "<6.4", "symfony/messenger": "<6.4", "symfony/property-info": "<6.4", "symfony/security-bundle": "<6.4", "symfony/security-core": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"doctrine/collections": "^1.8|^2.0", "doctrine/data-fixtures": "^1.1|^2", "doctrine/dbal": "^3.6|^4", "doctrine/orm": "^2.15|^3", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/doctrine-messenger": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4.6|^7.0.6", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-25T10:32:38+00:00"}, {"name": "symfony/dotenv", "version": "v6.4.16", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "1ac5e7e7e862d4d574258daf08bd569ba926e4a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/1ac5e7e7e862d4d574258daf08bd569ba926e4a5", "reference": "1ac5e7e7e862d4d574258daf08bd569ba926e4a5", "shasum": ""}, "require": {"php": ">=8.1"}, "conflict": {"symfony/console": "<5.4", "symfony/process": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T11:08:19+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "ce765a2d28b3cce61de1fb916e207767a73171d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/ce765a2d28b3cce61de1fb916e207767a73171d1", "reference": "ce765a2d28b3cce61de1fb916e207767a73171d1", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-28T12:00:15+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/flex", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "4ae50d368415a06820739e54d38a4a29d6df9155"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/4ae50d368415a06820739e54d38a4a29d6df9155", "reference": "4ae50d368415a06820739e54d38a4a29d6df9155", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.7.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-28T14:22:54+00:00"}, {"name": "symfony/framework-bundle", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "b1de19b2083484d0ce945977f6c6484e9e493a2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/b1de19b2083484d0ce945977f6c6484e9e493a2e", "reference": "b1de19b2083484d0ce945977f6c6484e9e493a2e", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^6.1|^7.0", "symfony/dependency-injection": "^6.4.12|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.1|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/annotations": "<1.13.1", "doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<5.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.3", "symfony/console": "<5.4|>=7.0", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<6.3", "symfony/lock": "<5.4", "symfony/mailer": "<5.4", "symfony/messenger": "<6.3", "symfony/mime": "<6.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4", "symfony/runtime": "<5.4.45|>=6.0,<6.4.13|>=7.0,<7.1.6", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<5.4", "symfony/security-csrf": "<5.4", "symfony/serializer": "<6.4", "symfony/stopwatch": "<5.4", "symfony/translation": "<6.4", "symfony/twig-bridge": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"doctrine/annotations": "^1.13.1|^2", "doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^5.4|^6.0|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/console": "^5.4.9|^6.0.9|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/http-client": "^6.3|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/mailer": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.3|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^5.4|^6.0|^7.0", "symfony/semaphore": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/string": "^5.4|^6.0|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bundle": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^5.4|^6.0|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0", "twig/twig": "^2.10|^3.0.4"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T07:14:36+00:00"}, {"name": "symfony/http-client", "version": "v6.4.19", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/3294a433fc9d12ae58128174896b5b1822c28dad", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T09:55:13+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c", "reference": "75d7043853a42837e68111812f4d964b01e5101c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-29T11:18:49+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-11T15:36:20+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "15c105b839a7cfa1bc0989c091bfb6477f23b673"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/15c105b839a7cfa1bc0989c091bfb6477f23b673", "reference": "15c105b839a7cfa1bc0989c091bfb6477f23b673", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:23:40+00:00"}, {"name": "symfony/intl", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "aaecb52f18a6f95766a239ca0a6cc0df983d92cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/aaecb52f18a6f95766a239ca0a6cc0df983d92cc", "reference": "aaecb52f18a6f95766a239ca0a6cc0df983d92cc", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides access to the localization data of the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-04T12:02:38+00:00"}, {"name": "symfony/mailer", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/ada2809ccd4ec27aba9fc344e3efdaec624c6438", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-26T23:47:35+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.43.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "e3f9a1d9e0f4968f68454403e820dffc7db38a59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/e3f9a1d9e0f4968f68454403e820dffc7db38a59", "reference": "e3f9a1d9e0f4968f68454403e820dffc7db38a59", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "nikic/php-parser": "^4.11", "php": ">=7.2.5", "symfony/config": "^5.4.7|^6.0", "symfony/console": "^5.4.7|^6.0", "symfony/dependency-injection": "^5.4.7|^6.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^5.4.7|^6.0", "symfony/finder": "^5.4.3|^6.0", "symfony/framework-bundle": "^5.4.7|^6.0", "symfony/http-kernel": "^5.4.7|^6.0"}, "conflict": {"doctrine/orm": "<2.10"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^2.4", "doctrine/orm": "^2.10.0", "symfony/http-client": "^5.4.7|^6.0", "symfony/phpunit-bridge": "^5.4.7|^6.0", "symfony/polyfill-php80": "^1.16.0", "symfony/process": "^5.4.7|^6.0", "symfony/security-core": "^5.4.7|^6.0", "symfony/yaml": "^5.4.3|^6.0", "twig/twig": "^2.0|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.43.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-17T15:46:50+00:00"}, {"name": "symfony/mime", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:26+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php82", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php82.git", "reference": "5d2ed36f7734637dacc025f179698031951b1692"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php82/zipball/5d2ed36f7734637dacc025f179698031951b1692", "reference": "5d2ed36f7734637dacc025f179698031951b1692", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php82\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php82/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "000df7860439609837bbe28670b0be15783b7fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/000df7860439609837bbe28670b0be15783b7fbf", "reference": "000df7860439609837bbe28670b0be15783b7fbf", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-20T12:04:08+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/e2a61c16af36c9a07e5c9906498b73e091949a20", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/property-info", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "94d18e5cc11a37fd92856d38b61d9cdf72536a1e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/94d18e5cc11a37fd92856d38b61d9cdf72536a1e", "reference": "94d18e5cc11a37fd92856d38b61d9cdf72536a1e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<5.4", "symfony/dependency-injection": "<5.4|>=6.0,<6.4", "symfony/serializer": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-21T10:52:27+00:00"}, {"name": "symfony/routing", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/1f5234e8457164a3a0038a4c0a4ba27876a9c670", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T16:08:38+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/stopwatch", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T10:49:57+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/translation", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/7e3b3b7146c6fab36ddff304a8041174bf6e17ad", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:06:44+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/twig-bridge", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "04ab306a2f2c9dbd46f4363383812954f704af9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/04ab306a2f2c9dbd46f4363383812954f704af9d", "reference": "04ab306a2f2c9dbd46f4363383812954f704af9d", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<5.4", "symfony/form": "<6.3", "symfony/http-foundation": "<5.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.2", "symfony/serializer": "<6.4", "symfony/translation": "<5.4", "symfony/workflow": "<5.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^5.4|^6.0|^7.0", "symfony/asset-mapper": "^6.3|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/form": "^6.4.20|^7.2.5", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^5.4|^6.0|^7.0", "symfony/security-csrf": "^5.4|^6.0|^7.0", "symfony/security-http": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^6.1|^7.0", "symfony/web-link": "^5.4|^6.0|^7.0", "symfony/workflow": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-16T08:23:44+00:00"}, {"name": "symfony/validator", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "4c5fbccb2d8f64017c8dada6473701a5c8539716"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/4c5fbccb2d8f64017c8dada6473701a5c8539716", "reference": "4c5fbccb2d8f64017c8dada6473701a5c8539716", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:03:46+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "22560f80c0c5cd58cc0bcaf73455ffd81eb380d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/22560f80c0c5cd58cc0bcaf73455ffd81eb380d5", "reference": "22560f80c0c5cd58cc0bcaf73455ffd81eb380d5", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T07:34:50+00:00"}, {"name": "symfony/var-exporter", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c9a1168891b5aaadfd6332ef44393330b3498c4c", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/yaml", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/f01987f45676778b474468aa266fe2eda1f2bc7e", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:48:44+00:00"}, {"name": "tbachert/spi", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/Nevay/spi.git", "reference": "506a79c98e1a51522e76ee921ccb6c62d52faf3a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nevay/spi/zipball/506a79c98e1a51522e76ee921ccb6c62d52faf3a", "reference": "506a79c98e1a51522e76ee921ccb6c62d52faf3a", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "composer/semver": "^1.0 || ^2.0 || ^3.0", "php": "^8.1"}, "require-dev": {"composer/composer": "^2.0", "infection/infection": "^0.27.9", "phpunit/phpunit": "^10.5", "psalm/phar": "^5.18"}, "type": "composer-plugin", "extra": {"class": "Nevay\\SPI\\Composer\\Plugin", "branch-alias": {"dev-main": "0.2.x-dev"}, "plugin-optional": true}, "autoload": {"psr-4": {"Nevay\\SPI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Service provider loading facility", "keywords": ["service provider"], "support": {"issues": "https://github.com/Nevay/spi/issues", "source": "https://github.com/Nevay/spi/tree/v1.0.3"}, "time": "2025-04-02T19:38:14+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.10.0", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "ca5b6de294512145db96bcbc94e61696599c391d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/ca5b6de294512145db96bcbc94e61696599c391d", "reference": "ca5b6de294512145db96bcbc94e61696599c391d", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.1.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.10.0"}, "funding": [{"url": "https://www.paypal.com/donate/?hosted_button_id=NZUEC5XS8MFBJ", "type": "custom"}], "time": "2025-05-27T18:02:28+00:00"}, {"name": "theiconic/php-ga-measurement-protocol", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/theiconic/php-ga-measurement-protocol.git", "reference": "6136c2f2ef159045402ef985843db0ad0f136125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theiconic/php-ga-measurement-protocol/zipball/6136c2f2ef159045402ef985843db0ad0f136125", "reference": "6136c2f2ef159045402ef985843db0ad0f136125", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "4.7.*", "satooshi/php-coveralls": "1.0.1"}, "type": "library", "autoload": {"psr-4": {"TheIconic\\Tracking\\GoogleAnalytics\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "THE ICONIC ENGINEERING TEAM", "email": "<EMAIL>"}], "description": "Send data to Google Analytics from the server using PHP. This library fully implements GA measurement protocol.", "support": {"issues": "https://github.com/theiconic/php-ga-measurement-protocol/issues", "source": "https://github.com/theiconic/php-ga-measurement-protocol/tree/v2.9.0"}, "time": "2020-09-24T23:37:47+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d", "reference": "0d72ac1c00084279c1816675284073c5a337c20d", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.4 || ^8.0", "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"}, "time": "2024-12-21T16:25:41+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "phpstan/phpstan", "version": "1.12.27", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/3a6e423c076ab39dfedc307e2ac627ef579db162", "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-05-21T20:51:45+00:00"}, {"name": "rector/rector", "version": "1.2.10", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "40f9cf38c05296bd32f444121336a521a293fa61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/40f9cf38c05296bd32f444121336a521a293fa61", "reference": "40f9cf38c05296bd32f444121336a521a293fa61", "shasum": ""}, "require": {"php": "^7.2|^8.0", "phpstan/phpstan": "^1.12.5"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "suggest": {"ext-dom": "To manipulate phpunit.xml via the custom-rule command"}, "bin": ["bin/rector"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/1.2.10"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2024-11-08T13:59:10+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"matgyver1/monolog-firehose-handler": 20, "merci-facteur/merci-facteur-api": 20, "matgyver1/symfony-maker": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.2", "ext-json": "*", "ext-curl": "*", "ext-iconv": "*", "ext-simplexml": "*", "ext-openssl": "*", "ext-soap": "*", "ext-gd": "*", "ext-exif": "*"}, "platform-dev": [], "platform-overrides": {"php": "8.2"}, "plugin-api-version": "2.6.0"}