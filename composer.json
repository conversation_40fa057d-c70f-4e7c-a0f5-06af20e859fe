{"name": "matgyver/base-saas", "license": "proprietary", "type": "project", "version": "2.0.0", "autoload": {"classmap": ["src/Controllers/", "src/Components/"], "psr-4": {"MatGyver\\": "src/"}}, "repositories": [{"type": "vcs", "url": "**************:matgyver1/monolog-firehose-handler.git"}, {"type": "vcs", "url": "**************:matgyver1/symfony-maker.git"}, {"type": "package", "package": {"name": "merci-facteur/merci-facteur-api", "version": "master", "source": {"url": "https://github.com/MerciFacteur/Merci-facteur-API.git", "type": "git", "reference": "master"}}}, {"type": "composer", "url": "https://www.setasign.com/downloads/"}], "require": {"php": ">=8.2", "ext-json": "*", "ext-curl": "*", "ext-iconv": "*", "ext-simplexml": "*", "ext-openssl": "*", "ext-soap": "*", "ext-gd": "*", "ext-exif": "*", "ext-imagick": "*", "symfony/var-dumper": "^6.0", "braintree/braintree_php": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.0", "spipu/html2pdf": "5.2.8", "html2text/html2text": "^4.0", "giggsey/libphonenumber-for-php": "^8.8", "stripe/stripe-php": "^12.0", "robmorgan/phinx": "^0.13.0", "maximebf/debugbar": "^1.15", "mobiledetect/mobiledetectlib": "^3.74", "monolog/monolog": "^3.0", "aws/aws-sdk-php": "^3.0", "dragonmantank/cron-expression": "^3.3", "theiconic/php-ga-measurement-protocol": "^2.0", "symfony/process": "^6.0", "supervisorphp/supervisor": "^5.0", "lstrojny/fxmlrpc": "^0.22.0", "php-http/message": "^1.6", "guzzlehttp/guzzle": "^7.0", "php-di/php-di": "^7.0", "seld/signal-handler": "^2.0", "doctrine/annotations": "^2.0", "symfony/routing": "^6.0", "symfony/http-foundation": "^6.0", "symfony/finder": "^6.0", "doctrine/collections": "^2.0", "symfony/yaml": "^6.0", "symfony/cache": "^6.0", "doctrine/orm": "^2.7", "doctrine/migrations": "^3.0", "gedmo/doctrine-extensions": "^3.0", "symfony/validator": "^6.0", "symfony/translation": "^6.0", "symfony/event-dispatcher": "^6.0", "gettext/gettext": "^5.0", "knplabs/knp-menu": "^3.0", "twig/twig": "^3.0", "symfony/twig-bridge": "^6.0", "aweber/aweber": "^1.1", "phpxmlrpc/phpxmlrpc": "^4.4", "drewm/mailchimp-api": "^2.5", "campaignmonitor/createsend-php": "^6.1", "activecampaign/api-php": "^2.0", "symfony/http-client": "^6.0", "matgyver1/monolog-firehose-handler": "dev-master", "facebook/php-business-sdk": "^17.0", "symfony/intl": "^6.0", "symfony/property-info": "^6.0", "mollie/mollie-api-php": "^2.40", "setasign/fpdf": "^1.8", "chrome-php/chrome": "^1.9", "phpoffice/phpword": "^1.0", "merci-facteur/merci-facteur-api": "master@dev", "ilovepdf/ilovepdf-php": "^1.2", "symfony/error-handler": "^6.0", "psr/http-factory": "^1.0", "getbrevo/brevo-php": "^1.0", "symfony/mailer": "^6.0", "gettext/translator": "^1.1", "getresponse/sdk-php": "^3.0", "gettext/php-scanner": "^1.3", "gettext/js-scanner": "^1.1", "phpdocumentor/reflection-docblock": "^5.3", "robthree/twofactorauth": "^2.0", "endroid/qr-code": "^4.0", "symfony/amazon-mailer": "^6.3", "beberlei/doctrineextensions": "^1.3", "google/apiclient": "^2.14", "appwrite/php-clamav": "^2.0", "smalot/pdfparser": "^2.11", "openai-php/client": "^0.15", "karelwintersky/monolog-pdo-handler": "^0.6.0", "league/oauth2-client": "^2.8", "stevenmaguire/oauth2-microsoft": "^2.2", "microsoft/microsoft-graph": "^2.36", "setasign/fpdi-fpdf": "^2.3", "setasign/fpdi_pdf-parser": "^2.1"}, "config": {"allow-plugins": {"symfony/flex": true, "php-http/discovery": true, "tbachert/spi": true}, "platform": {"php": "8.2"}}, "require-dev": {"matgyver1/symfony-maker": "dev-master", "phpstan/phpstan": "^1.12", "rector/rector": "^1.2"}}