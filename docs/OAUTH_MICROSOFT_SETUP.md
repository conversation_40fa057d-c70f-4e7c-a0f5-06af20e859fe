# Configuration OAuth Microsoft pour l'envoi d'emails

Ce guide vous explique comment configurer l'authentification OAuth avec Microsoft pour envoyer des emails via Outlook/Microsoft 365.

## Pourquoi utiliser OAuth ?

L'authentification OAuth présente plusieurs avantages par rapport au SMTP traditionnel :

- **Plus sécurisé** : Pas besoin de stocker de mots de passe
- **Compatible avec 2FA** : Fonctionne avec l'authentification à deux facteurs
- **Tokens temporaires** : Les tokens d'accès expirent automatiquement
- **Permissions granulaires** : Accès limité aux fonctionnalités nécessaires

## Prérequis

1. Un compte Microsoft (personnel ou professionnel)
2. Accès au portail Azure AD (pour les comptes professionnels) ou au portail des applications Microsoft

## Étapes de configuration

### 1. Créer une application dans Azure AD

1. Connectez-vous au [portail Azure](https://portal.azure.com)
2. Allez dans **Azure Active Directory** > **Inscriptions d'applications**
3. Cliquez sur **Nouvelle inscription**
4. Remplissez les informations :
   - **Nom** : Nom de votre application (ex: "MonApp Email Sender")
   - **Types de comptes pris en charge** : Choisissez selon vos besoins
   - **URI de redirection** : `https://votre-domaine.com/app/oauth/microsoft/callback`

### 2. Configurer les permissions

1. Dans votre application, allez dans **Autorisations API**
2. Cliquez sur **Ajouter une autorisation**
3. Sélectionnez **Microsoft Graph**
4. Choisissez **Autorisations déléguées**
5. Ajoutez les permissions suivantes :
   - `Mail.Send` : Pour envoyer des emails
   - `User.Read` : Pour lire les informations de base de l'utilisateur

### 3. Créer un secret client

1. Allez dans **Certificats et secrets**
2. Cliquez sur **Nouveau secret client**
3. Donnez une description et choisissez une durée d'expiration
4. **Important** : Copiez immédiatement la valeur du secret, elle ne sera plus visible après

### 4. Récupérer les informations nécessaires

Notez les informations suivantes depuis la page **Vue d'ensemble** de votre application :

- **ID d'application (client)** : Utilisé comme Client ID
- **ID de l'annuaire (locataire)** : Utilisé comme Tenant ID (ou "common" pour les comptes personnels)
- **Secret client** : Créé à l'étape précédente

### 5. Configuration dans l'application

1. Allez dans **Paramètres** > **Envoi d'emails**
2. Ouvrez la section **Configuration OAuth**
3. Remplissez les champs :
   - **Client ID** : ID d'application depuis Azure
   - **Client Secret** : Secret créé précédemment
   - **Tenant ID** : ID de l'annuaire (ou "common")
4. Cliquez sur **Sauvegarder**
5. Cliquez sur **Se connecter avec Microsoft**
6. Autorisez l'application à accéder à votre compte

## Test de la configuration

Une fois connecté, vous pouvez :

1. **Tester la connexion** : Vérifie que les tokens sont valides
2. **Envoyer un email test** : Envoie un email de test via OAuth
3. **Choisir OAuth comme méthode d'envoi** : Dans les paramètres principaux

## Dépannage

### Erreur "Invalid redirect URI"
- Vérifiez que l'URI de redirection dans Azure correspond exactement à votre domaine
- L'URI doit être en HTTPS en production

### Erreur "Insufficient privileges"
- Vérifiez que les permissions `Mail.Send` et `User.Read` sont accordées
- Un administrateur peut devoir approuver les permissions pour les comptes professionnels

### Token expiré
- Les tokens sont automatiquement renouvelés
- Si le refresh token expire, reconnectez-vous via "Se connecter avec Microsoft"

### Erreur "Application not found"
- Vérifiez le Client ID et Tenant ID
- Assurez-vous que l'application n'a pas été supprimée d'Azure

## Sécurité

- **Ne partagez jamais** votre Client Secret
- **Utilisez HTTPS** en production
- **Surveillez** les logs d'accès dans Azure AD
- **Renouvelez** régulièrement les secrets clients

## Support

Pour plus d'informations, consultez :
- [Documentation Microsoft Graph](https://docs.microsoft.com/en-us/graph/)
- [Guide OAuth 2.0 Microsoft](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow)
