<svg width="388" height="134" viewBox="0 0 388 134" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="388" height="134" fill="white"/>
<mask id="mask0_2963_43361" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="388" height="134">
<path d="M0 0H388V134H0V0Z" fill="url(#paint0_radial_2963_43361)"/>
</mask>
<g mask="url(#mask0_2963_43361)">
<path opacity="0.1" d="M0 0H388V134H0V0Z" fill="url(#paint1_radial_2963_43361)"/>
<g opacity="0.3">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M326.354 185.968C321.011 195.733 306.932 199.753 295.648 205.422C285.148 210.697 274.946 215.961 263.068 217.761C249.085 219.881 232.097 223.968 222.184 216.559C212.199 209.095 219.96 194.718 217.418 183.639C215.118 173.612 202.382 164.578 208.045 154.934C213.769 145.187 231.467 146.254 242.786 140.436C253.224 135.073 259.464 123.554 271.401 122.378C283.517 121.184 293.139 128.687 302.227 134.578C311.098 140.328 318.748 147.015 322.832 155.714C327.306 165.244 331.747 176.108 326.354 185.968Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M348.731 190.28C341.421 203.648 322.165 209.158 306.733 216.922C292.373 224.15 278.422 231.361 262.182 233.83C243.063 236.739 219.834 242.342 206.289 232.205C192.646 221.994 203.27 202.311 199.805 187.146C196.669 173.423 179.267 161.064 187.018 147.86C194.852 134.515 219.046 135.966 234.526 127.999C248.802 120.651 257.343 104.882 273.663 103.266C290.229 101.625 303.375 111.89 315.795 119.949C327.918 127.815 338.369 136.965 343.945 148.871C350.051 161.912 356.112 176.78 348.731 190.28Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M368.941 194.175C359.852 210.799 335.924 217.654 316.745 227.312C298.899 236.298 281.562 245.268 261.381 248.346C237.623 251.965 208.757 258.939 191.931 246.337C174.984 233.644 188.194 209.169 183.895 190.315C180.006 173.253 158.388 157.89 168.024 141.471C177.763 124.877 207.826 126.674 227.064 116.764C244.804 107.626 255.426 88.017 275.704 86.0031C296.29 83.9575 312.62 96.7186 328.05 106.736C343.11 116.513 356.092 127.887 363.015 142.69C370.595 158.903 378.119 177.388 368.941 194.175Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M387.709 197.791C376.966 217.435 348.701 225.539 326.043 236.958C304.96 247.584 284.475 258.182 260.638 261.823C232.572 266.105 198.474 274.348 178.601 259.458C158.585 244.463 174.197 215.537 169.124 193.256C164.534 173.094 139.002 154.943 150.39 135.538C161.898 115.926 197.411 118.046 220.139 106.331C241.097 95.531 253.648 72.355 277.604 69.9721C301.919 67.5536 321.207 82.6303 339.43 94.4658C357.216 106.018 372.548 119.457 380.722 136.949C389.672 156.108 398.555 177.952 387.709 197.791Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M407.197 201.547C394.74 224.332 361.967 233.731 335.697 246.977C311.252 259.299 287.502 271.594 259.864 275.816C227.327 280.785 187.793 290.353 164.757 273.088C141.554 255.697 159.658 222.15 153.782 196.312C148.465 172.93 118.868 151.882 132.076 129.377C145.423 106.632 186.592 109.086 212.944 95.499C237.244 82.9695 251.802 56.0922 279.574 53.3254C307.764 50.5189 330.121 68.0015 351.244 81.7254C371.862 95.1221 389.635 110.705 399.107 130.989C409.482 153.207 419.776 178.537 407.197 201.547Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M431.016 206.135C416.463 232.759 378.183 243.745 347.496 259.218C318.942 273.618 291.201 287.986 258.919 292.922C220.916 298.733 174.738 309.912 147.835 289.741C120.738 269.426 141.889 230.233 135.032 200.046C128.825 172.728 94.2618 148.142 109.691 121.847C125.285 95.2726 173.37 98.1357 204.15 82.2587C232.536 67.6177 249.542 36.2153 281.981 32.9808C314.907 29.6971 341.018 50.1224 365.686 66.1528C389.765 81.8011 410.521 100.006 421.58 123.704C433.694 149.66 445.712 179.253 431.016 206.135Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M457.002 211.142C440.16 241.948 395.873 254.667 360.369 272.576C327.333 289.239 295.237 305.871 257.89 311.584C213.922 318.309 160.497 331.249 129.377 307.912C98.0315 284.407 122.508 239.05 114.578 204.119C107.402 172.51 67.4179 144.061 85.273 113.632C103.317 82.8802 158.945 86.1887 194.559 67.8146C227.399 50.8704 247.079 14.531 284.609 10.7858C322.702 6.98421 352.906 30.6151 381.442 49.1643C409.297 67.27 433.307 88.3348 446.095 115.757C460.108 145.79 474.007 180.033 457.002 211.142Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M482.264 216.009C463.197 250.887 413.07 265.286 372.884 285.56C335.492 304.428 299.162 323.256 256.888 329.726C207.122 337.343 146.652 351.991 111.431 325.578C75.9549 298.97 103.663 247.623 94.6912 208.08C86.5728 172.296 41.3209 140.093 61.5321 105.646C81.9595 70.8318 144.922 74.5738 185.233 53.7716C222.405 34.5882 244.684 -6.55084 287.162 -10.7929C330.278 -15.0985 364.462 11.6508 396.759 32.6469C428.285 53.1418 455.458 76.9867 469.933 108.028C485.787 142.029 501.516 180.791 482.264 216.009Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M505.361 220.461C484.263 259.059 428.794 274.995 384.325 297.434C342.947 318.314 302.747 339.154 255.972 346.314C200.905 354.746 133.992 370.958 95.022 341.73C55.769 312.284 86.4326 255.461 76.5085 211.701C67.5279 172.101 17.4573 136.466 39.8254 98.3438C62.4308 59.8163 132.1 63.9551 176.705 40.9323C217.839 19.7013 242.492 -25.8259 289.496 -30.522C337.205 -35.2888 375.027 -5.68803 410.763 17.5472C445.644 40.2265 475.71 66.6131 491.726 100.964C509.265 138.589 526.666 181.487 505.361 220.461Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M529.902 225.189C506.644 267.74 445.501 285.309 396.483 310.048C350.873 333.07 306.56 356.04 254.998 363.936C194.301 373.234 120.543 391.112 77.5885 358.887C34.3234 326.432 68.1263 263.788 57.1906 215.549C47.2938 171.895 -7.89501 132.615 16.7632 90.5866C41.6832 48.1134 118.478 52.6732 167.644 27.2921C212.988 3.88459 240.166 -46.3051 291.977 -51.4839C344.565 -56.7402 386.254 -24.1094 425.643 1.50265C464.088 26.5036 497.229 55.5894 514.879 93.4587C534.211 134.935 553.389 182.225 529.902 225.189Z" stroke="#C334AF"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M556.61 230.335C531 277.187 463.683 296.536 409.714 323.777C359.496 349.124 310.706 374.42 253.941 383.118C187.112 393.356 105.907 413.041 58.6176 377.564C10.985 341.828 48.2059 272.851 36.1679 219.735C25.2748 171.668 -35.4847 128.417 -8.33384 82.1425C19.1052 35.3757 103.653 40.3942 157.787 12.4454C207.708 -13.3278 237.634 -68.5917 294.677 -74.2955C352.576 -80.0849 398.472 -44.1579 441.837 -15.9575C484.164 11.5689 520.647 43.5951 540.079 85.2901C561.358 130.958 582.47 183.028 556.61 230.335Z" stroke="#C334AF"/>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_2963_43361" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(103.063 144.321) rotate(-90) scale(132.804 357.649)">
<stop stop-color="#FFDD55"/>
<stop offset="0.1" stop-color="#FFDD55"/>
<stop offset="0.5" stop-color="#FF543E"/>
<stop offset="1" stop-color="#C837AB"/>
</radialGradient>
<radialGradient id="paint1_radial_2963_43361" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(103.063 144.321) rotate(-90) scale(132.804 357.649)">
<stop stop-color="#FFDD55"/>
<stop offset="0.1" stop-color="#FFDD55"/>
<stop offset="0.5" stop-color="#FF543E"/>
<stop offset="1" stop-color="#C837AB"/>
</radialGradient>
</defs>
</svg>
