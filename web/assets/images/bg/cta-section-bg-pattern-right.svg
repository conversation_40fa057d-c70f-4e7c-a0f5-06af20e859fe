<svg width="628" height="474" viewBox="0 0 628 474" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2980_39476" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="628" height="474">
<rect width="628" height="474" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2980_39476)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M733.868 724.851C708.811 740.107 671.399 730.741 638.187 728.822C607.284 727.036 577.078 725.619 547.611 714.087C512.922 700.512 468.784 687.469 455.939 657.499C443.001 627.312 479.653 604.713 488.458 576.072C496.426 550.153 479.283 512.759 504.912 498.199C530.814 483.486 569.777 509.229 603.262 510.855C634.141 512.357 663.556 494.28 692.334 507.314C721.544 520.544 733.603 550.343 746.569 575.76C759.224 600.57 767.859 625.91 765.709 651.146C763.354 678.795 759.165 709.443 733.868 724.851Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M779.226 764.161C744.932 785.053 693.75 772.272 648.314 769.674C606.034 767.263 564.707 765.353 524.409 749.602C476.966 731.065 416.596 713.265 399.061 672.285C381.399 631.007 431.574 600.066 443.658 560.883C454.593 525.426 431.188 474.295 466.272 454.36C501.729 434.212 555.005 469.386 600.814 471.583C643.062 473.611 683.327 448.86 722.683 466.662C762.631 484.733 779.089 525.479 796.797 560.233C814.08 594.155 825.859 628.806 822.887 663.33C819.627 701.142 813.856 743.065 779.226 764.161Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M820.189 799.669C777.546 825.649 713.932 809.788 657.456 806.577C604.908 803.589 553.54 801.236 503.449 781.685C444.487 758.66 369.455 736.566 347.684 685.638C325.756 634.345 388.145 595.868 403.191 547.165C416.807 503.092 387.745 439.554 431.365 414.761C475.452 389.705 541.656 433.398 598.598 436.11C651.108 438.612 701.18 407.834 750.09 429.943C799.741 452.382 820.172 503.02 842.165 546.208C863.627 588.36 878.248 631.423 874.53 674.331C870.451 721.327 863.25 773.433 820.189 799.669Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M858.231 832.639C807.837 863.336 732.682 844.613 665.95 840.84C603.855 837.333 543.162 834.551 483.99 811.47C414.329 784.285 325.686 758.199 299.982 698.036C274.09 637.446 347.826 591.97 365.62 534.426C381.724 482.354 347.41 407.294 398.962 377.993C451.06 348.376 529.27 399.983 596.551 403.172C658.592 406.117 717.768 369.738 775.55 395.846C834.201 422.347 858.324 482.165 884.291 533.182C909.634 582.977 926.894 633.849 922.486 684.544C917.648 740.069 909.12 801.632 858.231 832.639Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M897.733 866.878C839.288 902.489 752.145 880.784 674.765 876.428C602.766 872.364 532.387 869.155 463.782 842.396C383.015 810.892 280.228 780.67 250.439 710.92C220.435 640.663 305.946 587.921 326.598 521.198C345.287 460.818 305.518 373.793 365.306 339.81C425.73 305.465 516.401 365.283 594.416 368.968C666.36 372.368 734.989 330.182 801.983 360.439C869.984 391.157 897.938 460.51 928.031 519.657C957.403 577.392 977.404 636.37 972.275 695.151C966.658 759.533 956.752 830.915 897.733 866.878Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M946.018 908.72C877.733 950.332 775.936 925.001 685.547 919.91C601.438 915.181 519.222 911.448 439.084 880.204C344.739 843.421 224.675 808.128 189.896 726.652C154.862 644.592 254.765 582.974 278.91 505.029C300.758 434.495 254.324 332.849 324.176 293.141C394.77 253.012 500.677 322.872 591.812 327.163C675.855 331.121 756.035 281.83 834.291 317.166C913.724 353.031 946.36 434.045 981.501 503.126C1015.8 570.559 1039.15 639.452 1033.14 708.115C1026.56 783.321 1014.97 866.705 946.018 908.72Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1049.9 998.75C960.439 1053.26 827.12 1020.12 708.737 1013.48C598.579 1007.31 490.894 1002.45 385.941 961.548C262.387 913.397 105.148 867.202 59.6239 760.522C13.7729 653.058 144.651 572.33 176.304 470.244C204.949 377.864 144.173 244.756 235.677 192.734C328.156 140.16 466.842 231.62 586.206 237.217C696.281 242.378 801.319 177.803 903.802 224.06C1007.82 271.011 1050.54 377.094 1096.54 467.558C1141.43 555.861 1171.99 646.08 1164.09 736.006C1155.44 834.503 1140.23 943.706 1049.9 998.75Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M998.691 954.37C919.674 1002.52 801.892 973.23 697.304 967.357C599.987 961.892 504.856 957.59 412.138 921.45C302.984 878.899 164.068 838.084 123.842 743.826C83.3229 648.888 198.933 577.577 226.885 487.392C252.178 405.782 198.471 288.182 279.304 242.231C360.994 195.792 483.519 276.602 588.97 281.557C686.212 286.125 778.996 229.084 869.537 269.958C961.437 311.445 999.186 405.167 1039.83 485.093C1079.5 563.108 1106.5 642.815 1099.53 722.257C1091.91 809.273 1078.48 905.748 998.691 954.37Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1096.72 1039.33C997.722 1099.66 850.194 1062.99 719.187 1055.66C597.284 1048.83 478.12 1043.46 361.991 998.208C225.27 944.938 51.276 893.83 0.909179 775.785C-49.817 656.872 95.0193 567.535 130.059 454.566C161.767 352.34 94.5238 205.052 195.791 147.48C298.133 89.2974 451.592 190.494 583.679 196.678C705.487 202.381 821.727 130.916 935.13 182.096C1050.24 234.043 1097.49 351.427 1148.38 451.53C1198.06 549.241 1231.86 649.071 1223.12 748.582C1213.53 857.572 1196.68 978.417 1096.72 1039.33Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1146.46 1082.44C1037.33 1148.95 874.71 1108.54 730.294 1100.46C595.914 1092.95 464.563 1087.03 336.545 1037.16C185.841 978.448 -5.96405 922.126 -61.4693 791.994C-117.38 660.926 42.2894 562.434 80.9248 437.91C115.886 325.222 41.7732 162.871 153.412 99.3998C266.234 35.2571 435.392 146.8 580.992 153.607C715.27 159.884 843.414 81.1007 968.419 137.509C1095.3 194.765 1147.38 324.157 1203.47 434.498C1258.21 542.201 1295.47 652.244 1285.82 761.937C1275.25 882.081 1256.67 1015.29 1146.46 1082.44Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1200.6 1129.36C1080.43 1202.59 901.383 1158.11 742.377 1149.23C594.423 1140.96 449.793 1134.45 308.849 1079.55C142.92 1014.91 -68.2538 952.912 -129.359 809.645C-190.908 665.337 -15.0957 556.889 27.4529 419.78C65.9576 295.706 -15.6278 116.954 107.293 47.0718C231.52 -23.5549 417.756 99.244 578.073 106.732C725.91 113.637 867.011 26.889 1004.64 88.9891C1144.34 152.021 1201.67 294.479 1263.42 415.965C1323.69 534.546 1364.69 655.702 1354.06 776.476C1342.41 908.753 1321.94 1055.42 1200.6 1129.36Z" stroke="white"/>
</g>
<g filter="url(#filter0_d_2980_39476)">
<circle cx="162.891" cy="26.5375" r="13" fill="#FF4267"/>
<circle cx="162.891" cy="26.5375" r="13" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2980_39476)">
<circle cx="573.391" cy="234.037" r="11.5" fill="#FF813A"/>
<circle cx="573.391" cy="234.037" r="11.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2980_39476)">
<circle cx="89.3906" cy="376.037" r="10.5" fill="#7D42FB"/>
<circle cx="89.3906" cy="376.037" r="10.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2980_39476)">
<circle cx="383.391" cy="281.037" r="12.5" fill="#2FF2B8"/>
<circle cx="383.391" cy="281.037" r="12.5" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2980_39476" x="137.391" y="5.03748" width="51" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39476"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39476" result="shape"/>
</filter>
<filter id="filter1_d_2980_39476" x="549.391" y="214.037" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39476"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39476" result="shape"/>
</filter>
<filter id="filter2_d_2980_39476" x="66.3906" y="357.037" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39476"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39476" result="shape"/>
</filter>
<filter id="filter3_d_2980_39476" x="358.391" y="260.037" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39476"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39476" result="shape"/>
</filter>
</defs>
</svg>
