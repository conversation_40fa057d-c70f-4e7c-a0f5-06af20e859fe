<?xml version="1.0" encoding="utf-8"?>
<svg width="1626" height="560" viewBox="0 0 1626 560" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2985_40737)">
    <mask id="mask0_2985_40737" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-1" y="-108" width="1627" height="670">
      <rect x="-0.496094" y="-107.768" width="1626" height="669" fill="#563AFF"/>
    </mask>
    <g mask="url(#mask0_2985_40737)">
      <g opacity="0.6">
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M900.952 734.6C886.9 760.3 849.835 770.86 820.132 785.768C792.493 799.639 765.642 813.481 734.37 818.203C697.555 823.762 652.831 834.496 626.718 814.973C600.417 795.307 620.829 757.47 614.119 728.296C608.046 701.895 574.499 678.092 589.394 652.709C604.449 627.057 651.051 629.894 680.844 614.596C708.32 600.491 724.732 570.177 756.16 567.098C788.06 563.974 813.406 583.741 837.345 599.265C860.711 614.418 880.863 632.036 891.63 654.943C903.426 680.042 915.135 708.649 900.952 734.6Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M959.872 745.987C940.646 781.17 889.956 795.646 849.333 816.063C811.536 835.069 774.813 854.033 732.056 860.507C681.721 868.136 620.568 882.851 584.888 856.141C548.948 829.236 576.891 777.434 567.743 737.504C559.466 701.37 513.626 668.805 534.015 634.054C554.62 598.934 618.327 602.79 659.072 581.839C696.65 562.518 719.113 521.015 762.082 516.785C805.696 512.492 840.328 539.538 873.042 560.774C904.973 581.503 932.505 605.608 947.207 636.963C963.305 671.305 979.287 710.458 959.872 745.987Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1013.1 756.274C989.193 800.027 926.202 818.037 875.719 843.433C828.745 867.062 783.111 890.651 729.977 898.723C667.429 908.214 591.437 926.529 547.113 893.324C502.47 859.882 537.213 795.466 525.866 745.821C515.598 700.895 458.654 660.417 484 617.205C509.618 573.533 588.776 578.311 639.414 552.251C686.111 528.22 714.045 476.612 767.437 471.342C821.635 465.989 864.654 499.611 905.297 526.007C944.963 551.772 979.163 581.737 997.414 620.718C1017.4 663.416 1037.24 712.092 1013.1 756.274Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1062.52 765.825C1034.27 817.524 959.857 838.817 900.216 868.842C844.721 896.784 790.802 924.655 728.044 934.203C654.153 945.432 564.385 967.082 512.036 927.848C459.311 888.34 500.372 812.209 486.979 753.543C474.861 700.454 407.607 652.628 437.562 601.558C467.831 549.943 561.341 555.579 621.164 524.774C676.332 496.373 709.342 435.376 772.414 429.14C836.433 422.811 887.24 462.534 935.24 493.722C982.089 524.164 1022.48 559.57 1044.03 605.633C1067.63 656.089 1091.05 713.609 1062.52 765.825Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1113.84 775.743C1081.08 835.711 994.801 860.403 925.653 895.236C861.308 927.637 798.793 959.97 726.029 971.041C640.366 984.074 536.287 1009.2 475.607 963.712C414.486 917.889 462.102 829.598 446.59 761.563C432.553 699.997 354.591 644.539 389.331 585.309C424.44 525.45 532.842 531.974 602.203 496.245C666.168 463.296 704.456 392.559 777.574 385.318C851.796 377.973 910.691 424.035 966.329 460.199C1020.64 495.501 1067.46 536.554 1092.43 589.972C1119.78 648.48 1146.93 715.185 1113.84 775.743Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1176.57 787.861C1138.29 857.93 1037.52 886.795 956.744 927.481C881.586 965.348 808.564 1003.13 723.576 1016.08C623.52 1031.32 501.952 1060.67 431.085 1007.53C359.706 954.003 415.336 850.85 397.232 771.365C380.848 699.437 289.803 634.653 330.387 565.449C371.404 495.512 498.016 503.125 579.036 461.373C653.752 422.872 698.481 340.225 783.888 331.76C870.578 323.167 939.359 376.982 1004.34 419.224C1067.76 460.459 1122.44 508.42 1151.6 570.827C1183.53 639.181 1215.22 717.11 1176.57 787.861Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1245 801.083C1200.71 882.161 1084.12 915.576 990.664 962.67C903.707 1006.49 819.224 1050.22 720.898 1065.21C605.14 1082.84 464.493 1116.83 382.516 1055.34C299.946 993.408 364.32 874.034 343.386 782.058C324.442 698.828 219.119 623.869 266.084 543.786C313.546 462.853 460.02 471.65 553.761 423.332C640.205 378.774 691.964 283.134 790.774 273.332C891.068 263.384 970.632 325.644 1045.8 374.524C1119.17 422.235 1182.42 477.73 1216.14 549.944C1253.08 629.036 1289.73 719.208 1245 801.083Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1311.53 813.937C1261.38 905.731 1129.42 943.562 1023.64 996.875C925.217 1046.49 829.589 1096 718.293 1112.97C587.27 1132.94 428.074 1171.41 335.297 1101.82C241.845 1031.71 314.72 896.574 291.035 792.455C269.603 698.234 150.403 613.383 203.565 522.723C257.296 431.1 423.084 441.05 529.188 386.347C627.034 335.901 685.629 227.628 797.468 216.526C910.987 205.259 1001.04 275.736 1086.11 331.063C1169.15 385.07 1240.73 447.889 1278.89 529.635C1320.69 619.173 1362.16 721.248 1311.53 813.937Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1372.36 825.696C1316.87 927.279 1170.84 969.148 1053.79 1028.15C944.876 1083.06 839.06 1137.86 715.913 1156.64C570.934 1178.75 394.779 1221.33 292.124 1144.32C188.724 1066.74 269.372 917.184 243.173 801.96C219.465 697.691 87.5722 603.797 146.408 503.466C205.867 402.069 389.313 413.075 506.721 352.532C614.994 296.701 679.836 176.88 803.589 164.59C929.199 152.115 1028.83 230.104 1122.96 291.332C1214.84 351.095 1294.05 420.611 1336.27 511.072C1382.51 610.156 1428.39 723.121 1372.36 825.696Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1436.98 838.182C1375.81 950.168 1214.85 996.327 1085.83 1061.38C965.768 1121.92 849.129 1182.33 713.379 1203.03C553.578 1227.42 359.4 1274.37 246.25 1189.46C132.281 1103.95 221.186 939.078 192.316 812.061C166.189 697.118 20.8148 593.617 85.674 483.007C151.222 371.226 353.431 383.351 482.846 316.607C602.198 255.053 673.68 122.96 810.09 109.407C948.547 95.6509 1058.37 181.624 1162.12 249.115C1263.38 314.995 1350.69 391.622 1397.22 491.349C1448.19 600.575 1498.76 725.105 1436.98 838.182Z" stroke="#265bc3"/>
        <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1507.32 851.772C1439.96 975.079 1262.74 1025.91 1120.69 1097.55C988.501 1164.2 860.076 1230.72 710.626 1253.53C534.682 1280.38 320.899 1332.08 196.33 1238.6C70.857 1144.45 168.752 962.907 136.972 823.049C108.214 696.487 -51.8338 582.523 19.5817 460.738C91.7554 337.657 314.377 351.002 456.87 277.505C588.27 209.73 666.981 64.2823 817.165 49.3553C969.604 34.2041 1090.51 128.862 1204.73 203.173C1316.22 275.709 1412.33 360.082 1463.56 469.883C1519.66 590.148 1575.33 727.266 1507.32 851.772Z" stroke="#265bc3"/>
      </g>
    </g>
    <g filter="url(#filter0_d_2985_40737)">
      <ellipse rx="14" ry="13.975" transform="matrix(1 0 0 -1 370 67.9159)" fill="#7D42FB"/>
      <ellipse rx="14" ry="13.975" transform="matrix(1 0 0 -1 370 67.9159)" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter1_d_2985_40737)">
      <ellipse rx="16" ry="15.9715" transform="matrix(1 0 0 -1 1200 53.9408)" fill="#1DE4FF"/>
      <ellipse rx="16" ry="15.9715" transform="matrix(1 0 0 -1 1200 53.9408)" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter2_d_2985_40737)">
      <ellipse rx="11.2204" ry="11.2004" transform="matrix(1 0 0 -1 1476.78 166.714)" fill="#FFCD42"/>
      <ellipse rx="11.2204" ry="11.2004" transform="matrix(1 0 0 -1 1476.78 166.714)" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter3_d_2985_40737)">
      <ellipse rx="15.0688" ry="15.042" transform="matrix(1 0 0 -1 220.155 221.767)" fill="#2FF2B8"/>
      <ellipse rx="15.0688" ry="15.042" transform="matrix(1 0 0 -1 220.155 221.767)" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter4_d_2985_40737)">
      <ellipse rx="13.6714" ry="13.647" transform="matrix(1 0 0 -1 433.828 328.317)" fill="#FF4267"/>
      <ellipse rx="13.6714" ry="13.647" transform="matrix(1 0 0 -1 433.828 328.317)" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter5_d_2985_40737)">
      <ellipse rx="13.2306" ry="13.2071" transform="matrix(1 0 0 -1 1312.23 279.963)" fill="#FF813A"/>
      <ellipse rx="13.2306" ry="13.2071" transform="matrix(1 0 0 -1 1312.23 279.963)" stroke="white" stroke-width="3"/>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_2985_40737" x="347.5" y="49.4409" width="45" height="44.9501" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="3.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <filter id="filter1_d_2985_40737" x="1171.5" y="29.4694" width="57" height="56.9429" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <filter id="filter2_d_2985_40737" x="1453.05" y="147.014" width="47.4414" height="47.4009" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <filter id="filter3_d_2985_40737" x="192.586" y="198.225" width="55.1367" height="55.084" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <filter id="filter4_d_2985_40737" x="407.656" y="306.17" width="52.3438" height="52.294" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <filter id="filter5_d_2985_40737" x="1286.5" y="258.256" width="51.4609" height="51.4141" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2985_40737"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2985_40737" result="shape"/>
    </filter>
    <clipPath id="clip0_2985_40737">
      <rect width="1626" height="560" fill="white"/>
    </clipPath>
  </defs>
</svg>