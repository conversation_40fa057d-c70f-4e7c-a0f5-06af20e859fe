<?xml version="1.0" encoding="utf-8"?>
<svg width="426" height="468" viewBox="0 0 426 468" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2926_25939)">
    <g opacity="0.6">
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M7.74818 494.867C-6.11625 520.243 -42.6365 530.701 -71.9067 545.44C-99.1417 559.159 -125.602 572.848 -156.404 577.534C-192.665 583.057 -236.722 593.692 -262.412 574.449C-288.289 555.066 -268.139 517.705 -274.711 488.919C-280.657 462.87 -313.663 439.411 -298.962 414.347C-284.104 389.016 -238.216 391.769 -208.857 376.646C-181.78 362.699 -165.581 332.766 -134.628 329.698C-103.209 326.583 -78.2745 346.068 -54.7185 361.366C-31.7269 376.298 -11.9047 393.666 -1.32829 416.266C10.2531 441.02 21.7485 469.242 7.74818 494.867Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M46.0795 502.26C28.8408 533.816 -16.5413 546.828 -52.9172 565.16C-86.7636 582.217 -119.645 599.245 -157.923 605.087C-202.983 611.957 -257.73 625.195 -289.643 601.273C-321.786 577.181 -296.732 530.722 -304.885 494.933C-312.262 462.545 -353.263 433.385 -334.987 402.218C-316.514 370.719 -259.496 374.13 -223.009 355.32C-189.362 337.973 -169.217 300.752 -130.756 296.929C-91.713 293.046 -60.7394 317.269 -31.4743 336.284C-2.91268 354.843 21.7098 376.434 34.8399 404.531C49.2173 435.307 63.4873 470.394 46.0795 502.26Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M81.6734 509.125C61.2989 546.413 7.68929 561.796 -35.2848 583.47C-75.2714 603.641 -114.123 623.758 -159.334 630.669C-212.565 638.797 -277.237 654.445 -314.928 626.18C-352.891 597.718 -323.281 542.809 -332.903 500.517C-341.608 462.245 -390.033 427.791 -368.433 390.956C-346.608 353.729 -279.252 357.753 -236.146 335.516C-196.395 315.015 -172.59 271.022 -127.156 266.499C-81.0379 261.909 -44.4568 290.527 -9.89433 312.993C23.839 334.922 52.9191 360.432 68.4226 393.634C85.3978 430.003 102.245 471.465 81.6734 509.125Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M118.638 516.254C95.0112 559.505 32.8522 577.345 -16.9724 602.489C-63.3356 625.878 -108.382 649.217 -160.8 657.23C-222.511 666.664 -297.493 684.826 -341.184 652.053C-385.191 619.042 -350.854 555.363 -361.999 506.316C-372.083 461.933 -428.218 421.981 -403.168 379.262C-377.852 336.088 -299.77 340.745 -249.791 314.954C-203.701 291.171 -176.091 240.153 -123.418 234.901C-69.9512 229.574 -27.5472 262.759 12.515 288.81C51.6196 314.239 85.3283 343.818 103.294 382.322C122.971 424.495 142.495 472.577 118.638 516.254Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M163.813 524.964C136.212 575.5 63.6082 596.355 5.40593 625.724C-48.7494 653.059 -101.365 680.333 -162.591 689.702C-234.67 700.732 -322.253 721.952 -373.277 683.663C-424.671 645.102 -384.554 570.706 -397.561 513.404C-409.332 461.551 -474.887 414.88 -445.622 364.967C-416.047 314.525 -324.847 319.959 -266.468 289.822C-212.632 262.03 -180.377 202.422 -118.851 196.283C-56.4021 190.05 -6.88032 228.821 39.907 259.25C85.5755 288.953 124.941 323.51 145.917 368.493C168.892 417.762 191.687 473.936 163.813 524.964Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M213.098 534.468C181.156 592.944 97.1592 617.087 29.821 651.081C-32.8362 682.712 -93.7109 714.281 -164.545 725.127C-247.936 737.891 -349.263 762.455 -408.287 718.156C-467.738 673.54 -421.316 587.443 -436.356 521.137C-449.966 461.136 -525.801 407.136 -491.937 349.375C-457.713 291.002 -352.207 297.282 -284.661 262.405C-222.374 230.242 -185.05 161.262 -113.869 154.153C-41.6205 146.937 15.6652 191.793 69.7891 227.003C122.618 261.371 168.158 301.356 192.412 353.408C218.989 410.418 245.351 475.416 213.098 534.468Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M261.012 543.706C224.848 609.912 129.774 637.245 53.5575 675.728C-17.3622 711.542 -86.2671 747.282 -166.444 759.564C-260.833 774.022 -375.524 801.828 -442.324 751.69C-509.61 701.182 -457.057 603.715 -474.074 528.655C-489.471 460.731 -575.298 399.604 -536.964 334.216C-498.221 268.132 -378.803 275.235 -302.35 235.749C-231.846 199.335 -189.592 121.245 -109.026 113.193C-27.2503 105.02 37.5842 155.795 98.8399 195.65C158.632 234.553 210.169 279.815 237.624 338.738C267.693 403.277 297.525 476.856 261.012 543.706Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M304.821 552.157C264.806 625.422 159.602 655.672 75.2602 698.266C-3.21893 737.901 -79.4652 777.458 -168.18 791.049C-272.622 807.054 -399.531 837.829 -473.443 782.347C-547.892 726.454 -489.734 618.593 -508.557 535.527C-525.59 460.359 -620.555 392.718 -578.131 320.354C-535.257 247.221 -403.12 255.077 -318.521 211.376C-240.505 171.075 -193.745 84.6557 -104.597 75.7415C-14.1107 66.6932 57.625 122.881 125.403 166.986C191.56 210.036 248.583 260.123 278.961 325.328C312.225 396.747 345.229 478.176 304.821 552.157Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M351.365 561.131C307.254 641.9 191.287 675.25 98.3182 722.211C11.8114 765.91 -72.2332 809.512 -170.027 824.5C-285.148 842.15 -425.04 876.086 -506.509 814.916C-588.567 753.31 -524.456 634.399 -545.197 542.832C-563.967 459.969 -668.64 385.407 -621.873 305.629C-574.609 225.007 -428.956 233.662 -335.706 185.484C-249.706 141.052 -198.159 45.7823 -99.8924 35.9518C-0.151572 25.9745 78.9173 87.9141 153.624 136.531C226.54 183.987 289.396 239.198 322.872 311.081C359.537 389.811 395.912 479.576 351.365 561.131Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M402.02 570.899C353.448 659.833 225.771 696.562 123.412 748.27C28.1667 796.384 -64.3705 844.4 -172.034 860.911C-298.783 880.344 -452.8 917.712 -542.491 850.368C-632.832 782.534 -562.238 651.602 -585.069 550.778C-605.73 459.539 -720.968 377.439 -669.473 289.601C-617.431 200.829 -457.076 210.355 -354.402 157.303C-259.721 108.38 -202.961 3.47826 -94.7717 -7.34858C15.0411 -18.338 102.089 49.8584 184.337 103.388C264.617 155.639 333.812 216.431 370.666 295.576C411.026 382.263 451.068 481.102 402.02 570.899Z" stroke="#265bc3"/>
    </g>
    <g filter="url(#filter0_d_2926_25939)">
      <circle cx="273.296" cy="320.527" r="12.4041" fill="#FF4267"/>
      <circle cx="273.296" cy="320.527" r="12.4041" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter1_d_2926_25939)">
      <circle cx="26.4167" cy="391.715" r="13.8927" fill="#2FF2B8"/>
      <circle cx="26.4167" cy="391.715" r="13.8927" stroke="white" stroke-width="3"/>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_2926_25939" x="250.155" y="300.745" width="46.281" height="46.2813" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3.35868"/>
      <feGaussianBlur stdDeviation="4.61819"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_25939"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_25939" result="shape"/>
    </filter>
    <filter id="filter1_d_2926_25939" x="1.78755" y="370.445" width="49.2582" height="49.2579" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3.35868"/>
      <feGaussianBlur stdDeviation="4.61819"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_25939"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_25939" result="shape"/>
    </filter>
    <clipPath id="clip0_2926_25939">
      <rect width="426" height="468" fill="white"/>
    </clipPath>
  </defs>
</svg>