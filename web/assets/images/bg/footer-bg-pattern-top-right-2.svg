<?xml version="1.0" encoding="utf-8"?>
<svg width="266" height="324" viewBox="0 0 266 324" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2926_25449)">
    <g opacity="0.6">
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M560.009 -71.736C546.145 -46.3605 509.625 -35.9022 480.355 -21.1638C453.119 -7.44413 426.659 6.24437 395.857 10.9307C359.596 16.4532 315.539 27.0887 289.849 7.84574C263.973 -11.5371 284.122 -48.8982 277.551 -77.684C271.604 -103.733 238.598 -127.193 253.299 -152.257C268.157 -177.588 314.045 -174.835 343.405 -189.958C370.482 -203.905 386.68 -233.838 417.634 -236.906C449.052 -240.02 473.987 -220.535 497.543 -205.238C520.534 -190.306 540.357 -172.937 550.933 -150.337C562.514 -125.583 574.01 -97.3611 560.009 -71.736Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M598.341 -64.3422C581.102 -32.7866 535.72 -19.7741 499.344 -1.44208C465.498 15.6147 432.616 32.642 394.338 38.4848C349.279 45.3549 294.531 58.5922 262.618 34.6703C230.475 10.5783 255.529 -35.8806 247.377 -71.6699C240 -104.057 198.998 -133.217 217.274 -164.385C235.747 -195.883 292.765 -192.472 329.252 -211.283C362.899 -228.629 383.044 -265.851 421.506 -269.674C460.548 -273.557 491.522 -249.333 520.787 -230.319C549.349 -211.76 573.971 -190.169 587.101 -162.072C601.479 -131.295 615.749 -96.2081 598.341 -64.3422Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M633.935 -57.4775C613.56 -20.1899 559.951 -4.80617 516.976 16.8677C476.99 37.038 438.138 57.1558 392.927 64.0667C339.696 72.1948 275.024 87.8426 237.333 59.5779C199.37 31.1151 228.98 -23.7932 219.359 -66.0857C210.653 -104.358 162.228 -138.812 183.828 -175.646C205.654 -212.874 273.009 -208.85 316.115 -231.086C355.866 -251.587 379.671 -295.58 425.106 -300.103C471.223 -304.694 507.804 -276.075 542.367 -253.609C576.1 -231.681 605.18 -206.17 620.684 -172.968C637.659 -136.6 654.506 -95.1371 633.935 -57.4775Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M670.899 -50.3484C647.272 -7.09761 585.113 10.7423 535.289 35.8869C488.926 59.2756 443.88 82.6148 391.461 90.6279C329.75 100.062 254.768 118.223 211.077 85.4507C167.07 52.439 201.407 -11.2399 190.263 -60.2861C180.178 -104.67 124.043 -144.621 149.093 -187.341C174.409 -230.515 252.491 -225.857 302.47 -251.648C348.56 -275.431 376.17 -326.45 428.843 -331.702C482.31 -337.029 524.714 -303.844 564.776 -277.793C603.881 -252.363 637.59 -222.784 655.555 -184.281C675.232 -142.108 694.756 -94.0256 670.899 -50.3484Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M716.075 -41.6399C688.473 8.89624 615.869 29.7513 557.667 59.1206C503.512 86.4553 450.896 113.729 389.67 123.099C317.591 134.129 230.009 155.348 178.984 117.06C127.591 78.499 167.707 4.10206 154.701 -53.1994C142.929 -105.053 77.3745 -151.723 106.639 -201.636C136.214 -252.079 227.414 -246.644 285.793 -276.782C339.63 -304.573 371.885 -364.181 433.41 -370.321C495.859 -376.554 545.381 -337.783 592.168 -307.354C637.837 -277.651 677.203 -243.094 698.179 -198.111C721.154 -148.842 743.948 -92.668 716.075 -41.6399Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M765.359 -32.136C733.417 26.3401 649.42 50.483 582.082 84.4779C519.425 116.108 458.55 147.678 387.717 158.523C304.325 171.288 202.998 195.851 143.974 151.553C84.5231 106.936 130.945 20.8396 115.905 -45.4662C102.295 -105.467 26.4601 -159.468 60.3247 -217.228C94.5485 -275.601 200.054 -269.321 267.6 -304.199C329.887 -336.362 367.212 -405.341 438.392 -412.451C510.641 -419.667 567.926 -374.811 622.05 -339.601C674.88 -305.232 720.419 -265.247 744.674 -213.196C771.25 -156.186 797.612 -91.1871 765.359 -32.136Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M813.273 -22.8972C777.11 43.3082 682.036 70.6411 605.819 109.125C534.899 144.939 465.994 180.679 385.817 192.961C291.429 207.419 176.738 235.224 109.938 185.087C42.6517 134.578 95.2044 37.1119 78.1878 -37.9483C62.79 -105.873 -23.0364 -166.999 15.2969 -232.388C54.0402 -298.471 173.458 -291.368 249.912 -330.855C320.415 -367.269 362.669 -445.359 443.235 -453.411C525.011 -461.584 589.846 -410.808 651.101 -370.954C710.893 -332.05 762.431 -286.788 789.885 -227.866C819.955 -163.326 849.786 -89.7475 813.273 -22.8972Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M857.082 -14.4468C817.067 58.8185 711.863 89.0689 627.521 131.663C549.042 171.297 472.796 210.854 384.081 224.445C279.639 240.451 152.73 271.225 78.8181 215.744C4.36942 159.851 62.527 51.9895 43.7046 -31.0766C26.6717 -106.244 -68.2941 -173.886 -25.8698 -246.25C17.0044 -319.382 149.141 -311.526 233.74 -355.228C311.756 -395.528 358.516 -481.948 447.664 -490.862C538.151 -499.91 609.886 -443.722 677.664 -399.617C743.822 -356.568 800.845 -306.481 831.222 -241.276C864.486 -169.856 897.49 -88.4277 857.082 -14.4468Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M903.627 -5.47222C859.515 75.2965 743.549 108.646 650.58 155.607C564.073 199.306 480.028 242.909 382.234 257.897C267.114 275.546 127.221 309.482 45.7523 248.313C-36.3058 186.706 27.806 67.7955 7.0649 -23.7717C-11.7058 -106.635 -116.379 -181.197 -69.6112 -260.974C-22.3471 -341.597 123.306 -332.941 216.556 -381.12C302.555 -425.551 354.103 -520.821 452.369 -530.652C552.11 -540.629 631.179 -478.689 705.886 -430.073C778.802 -382.616 841.658 -327.406 875.133 -255.522C911.799 -176.792 948.173 -87.0273 903.627 -5.47222Z" stroke="#265bc3"/>
    </g>
    <g filter="url(#filter0_d_2926_25449)">
      <circle cx="113.343" cy="70.7215" r="12.532" fill="#FFCD42"/>
      <circle cx="113.343" cy="70.7215" r="12.532" stroke="white" stroke-width="3"/>
    </g>
    <g filter="url(#filter1_d_2926_25449)">
      <circle cx="238.29" cy="283.789" r="14.7892" fill="#1DE4FF"/>
      <circle cx="238.29" cy="283.789" r="14.7892" stroke="white" stroke-width="3"/>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_2926_25449" x="90.075" y="50.8118" width="46.5368" height="46.5372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3.35868"/>
      <feGaussianBlur stdDeviation="4.61819"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_25449"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_25449" result="shape"/>
    </filter>
    <filter id="filter1_d_2926_25449" x="212.764" y="261.622" width="51.0512" height="51.0509" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3.35868"/>
      <feGaussianBlur stdDeviation="4.61819"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_25449"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_25449" result="shape"/>
    </filter>
    <clipPath id="clip0_2926_25449">
      <rect width="266" height="324" fill="white"/>
    </clipPath>
  </defs>
</svg>