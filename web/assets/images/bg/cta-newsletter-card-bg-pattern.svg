<svg width="504" height="424" viewBox="0 0 504 424" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2980_39569)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M761.683 766.926C727.389 787.817 676.207 775.037 630.771 772.438C588.491 770.027 547.164 768.117 506.866 752.366C459.423 733.83 399.053 716.029 381.518 675.049C363.856 633.771 414.031 602.83 426.115 563.648C437.05 528.191 413.645 477.06 448.729 457.124C484.186 436.977 537.462 472.15 583.271 474.348C625.519 476.375 665.784 451.624 705.14 469.427C745.088 487.497 761.546 528.244 779.254 562.997C796.537 596.919 808.316 631.57 805.344 666.094C802.084 703.906 796.313 745.829 761.683 766.926Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M802.646 802.433C760.003 828.414 696.389 812.552 639.913 809.342C587.365 806.354 535.997 804 485.906 784.449C426.944 761.424 351.912 739.33 330.141 688.402C308.213 637.109 370.603 598.632 385.648 549.929C399.264 505.857 370.202 442.318 413.822 417.525C457.909 392.47 524.113 436.162 581.055 438.875C633.565 441.376 683.637 410.599 732.547 432.707C782.198 455.146 802.629 505.785 824.622 548.973C846.084 591.125 860.705 634.187 856.987 677.095C852.908 724.091 845.707 776.197 802.646 802.433Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M840.688 835.403C790.294 866.1 715.139 847.377 648.407 843.604C586.312 840.098 525.619 837.316 466.447 814.235C396.786 787.049 308.143 760.963 282.439 700.8C256.547 640.21 330.283 594.734 348.077 537.191C364.181 485.118 329.867 410.058 381.419 380.757C433.517 351.141 511.727 402.748 579.008 405.936C641.049 408.881 700.225 372.503 758.007 398.61C816.658 425.111 840.781 484.929 866.748 535.946C892.091 585.741 909.351 636.613 904.943 687.308C900.105 742.833 891.577 804.396 840.688 835.403Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M880.19 869.642C821.745 905.253 734.602 883.548 657.222 879.192C585.223 875.129 514.844 871.92 446.239 845.16C365.472 813.656 262.685 783.434 232.896 713.684C202.892 643.427 288.403 590.685 309.055 523.962C327.744 463.582 287.975 376.558 347.763 342.574C408.187 308.229 498.858 368.047 576.873 371.732C648.817 375.132 717.446 332.946 784.44 363.203C852.441 393.921 880.395 463.274 910.488 522.421C939.86 580.156 959.861 639.135 954.732 697.915C949.115 762.297 939.209 833.679 880.19 869.642Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M928.475 911.485C860.19 953.096 758.393 927.766 668.004 922.674C583.895 917.945 501.679 914.212 421.541 882.968C327.196 846.185 207.132 810.892 172.353 729.416C137.319 647.356 237.222 585.738 261.367 507.794C283.215 437.259 236.781 335.613 306.633 295.905C377.227 255.776 483.134 325.636 574.269 329.927C658.312 333.885 738.492 284.594 816.748 319.93C896.181 355.795 928.817 436.809 963.958 505.891C998.256 573.323 1021.6 642.216 1015.6 710.879C1009.02 786.086 997.428 869.47 928.475 911.485Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1032.36 1001.51C942.896 1056.03 809.577 1022.88 691.194 1016.24C581.036 1010.07 473.351 1005.21 368.398 964.312C244.844 916.161 87.605 869.966 42.0809 763.286C-3.77003 655.822 127.108 575.095 158.761 473.008C187.406 380.628 126.63 247.521 218.134 195.498C310.613 142.925 449.299 234.385 568.663 239.981C678.738 245.143 783.776 180.567 886.259 226.824C990.281 273.776 1033 379.858 1078.99 470.322C1123.89 558.625 1154.44 648.845 1146.55 738.77C1137.9 837.268 1122.69 946.47 1032.36 1001.51Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M981.149 957.134C902.131 1005.28 784.349 975.994 679.761 970.122C582.444 964.656 487.313 960.355 394.595 924.215C285.441 881.663 146.526 840.848 106.299 746.59C65.78 651.652 181.39 580.341 209.342 490.156C234.635 408.546 180.928 290.946 261.761 244.995C343.451 198.556 465.976 279.366 571.427 284.321C668.669 288.889 761.453 231.848 851.994 272.722C943.894 314.209 981.643 407.931 1022.29 487.857C1061.96 565.872 1088.96 645.58 1081.99 725.021C1074.37 812.037 1060.94 908.512 981.149 957.134Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1079.17 1042.1C980.179 1102.43 832.651 1065.76 701.644 1058.42C579.741 1051.59 460.577 1046.23 344.448 1000.97C207.727 947.702 33.7331 896.595 -16.6338 778.549C-67.36 659.636 77.4763 570.299 112.516 457.331C144.224 355.104 76.9808 207.816 178.248 150.245C280.59 92.0616 434.049 193.259 566.136 199.442C687.944 205.145 804.184 133.681 917.587 184.86C1032.69 236.807 1079.95 354.191 1130.84 454.295C1180.51 552.005 1214.31 651.836 1205.57 751.347C1195.99 860.336 1179.14 981.182 1079.17 1042.1Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1128.92 1085.21C1019.79 1151.72 857.167 1111.3 712.751 1103.23C578.371 1095.72 447.02 1089.8 319.002 1039.92C168.298 981.212 -23.507 924.891 -79.0123 794.758C-134.923 663.69 24.7465 565.199 63.3818 440.674C98.343 327.986 24.2303 165.635 135.869 102.164C248.691 38.0214 417.849 149.565 563.449 156.371C697.727 162.649 825.871 83.865 950.876 140.273C1077.76 197.529 1129.84 326.922 1185.93 437.263C1240.67 544.965 1277.93 655.008 1268.28 764.702C1257.71 884.845 1239.12 1018.06 1128.92 1085.21Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1183.06 1132.13C1062.89 1205.36 883.84 1160.88 724.834 1151.99C576.88 1143.72 432.25 1137.21 291.306 1082.32C125.377 1017.68 -85.7968 955.677 -146.902 812.409C-208.451 668.101 -32.6386 559.654 9.90991 422.545C48.4146 298.471 -33.1707 119.718 89.7498 49.836C213.977 -20.7906 400.213 102.008 560.53 109.497C708.367 116.402 849.468 29.6533 987.099 91.7533C1126.79 154.785 1184.13 297.243 1245.88 418.729C1306.15 537.31 1347.15 658.467 1336.52 779.24C1324.87 911.518 1304.4 1058.19 1183.06 1132.13Z" stroke="white"/>
</g>
<g filter="url(#filter0_d_2980_39569)">
<circle cx="145.348" cy="29.3018" r="13" fill="#FF4267"/>
<circle cx="145.348" cy="29.3018" r="13" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2980_39569)">
<circle cx="71.8477" cy="378.802" r="10.5" fill="#7D42FB"/>
<circle cx="71.8477" cy="378.802" r="10.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2980_39569)">
<circle cx="365.848" cy="283.802" r="12.5" fill="#2FF2B8"/>
<circle cx="365.848" cy="283.802" r="12.5" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2980_39569" x="119.848" y="7.80176" width="51" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39569"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39569" result="shape"/>
</filter>
<filter id="filter1_d_2980_39569" x="48.8477" y="359.802" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39569"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39569" result="shape"/>
</filter>
<filter id="filter2_d_2980_39569" x="340.848" y="262.802" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2980_39569"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2980_39569" result="shape"/>
</filter>
<clipPath id="clip0_2980_39569">
<rect width="504" height="424" fill="white"/>
</clipPath>
</defs>
</svg>
