<?xml version="1.0" encoding="utf-8"?>
<svg width="1632" height="596" viewBox="0 0 1632 596" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2992_42854)">
    <g opacity="0.6">
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M903.959 734.934C889.887 760.674 852.804 771.27 823.082 786.213C795.427 800.118 768.558 813.994 737.274 818.74C700.444 824.329 655.699 835.102 629.589 815.57C603.291 795.896 623.733 757.999 617.038 728.792C610.979 702.362 577.435 678.549 592.35 653.126C607.426 627.432 654.04 630.245 683.852 614.911C711.346 600.772 727.781 570.409 759.221 567.308C791.133 564.161 816.475 583.939 840.412 599.467C863.777 614.625 883.925 632.253 894.681 655.183C906.466 680.305 918.162 708.941 903.959 734.934Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M962.887 746.299C943.633 781.538 892.918 796.062 852.271 816.529C814.449 835.581 777.704 854.59 734.93 861.098C684.574 868.767 623.392 883.537 587.717 856.814C551.782 829.897 579.764 778.014 570.638 738.039C562.38 701.865 516.544 669.287 536.96 634.48C557.593 599.303 621.318 603.126 662.089 582.125C699.691 562.757 722.186 521.189 765.17 516.929C808.801 512.604 843.428 539.663 876.14 560.906C908.068 581.642 935.595 605.761 950.283 637.146C966.366 671.522 982.329 710.714 962.887 746.299Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1016.12 756.567C992.186 800.388 929.164 818.459 878.649 843.916C831.646 867.603 785.983 891.249 732.828 899.363C670.254 908.903 594.226 927.286 549.909 894.066C505.271 860.609 540.064 796.092 528.743 746.391C518.498 701.415 461.56 660.921 486.94 617.639C512.592 573.897 591.773 578.634 642.443 552.511C689.168 528.423 717.143 476.733 770.555 471.424C824.773 466.032 867.786 499.671 908.426 526.076C948.09 551.849 982.283 581.832 1000.52 620.851C1020.48 663.59 1040.3 712.315 1016.12 756.567Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1065.55 766.1C1037.26 817.881 962.809 839.244 903.131 869.343C847.602 897.353 793.649 925.29 730.864 934.888C656.943 946.175 567.133 967.905 514.791 928.654C462.073 889.128 503.192 812.877 489.83 754.146C477.741 700.997 410.494 653.151 440.489 601.999C470.798 550.302 564.334 555.889 624.195 525.01C679.397 496.54 712.455 435.448 775.55 429.166C839.594 422.791 890.394 462.534 938.391 493.732C985.236 524.184 1025.62 559.611 1047.15 605.718C1070.72 656.223 1094.12 713.802 1065.55 766.1Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1116.88 776C1084.07 836.062 997.746 860.836 928.554 895.754C864.17 928.234 801.615 960.645 728.821 971.773C643.123 984.873 538.996 1010.09 478.323 964.583C417.211 918.74 464.894 830.309 449.418 762.199C435.413 700.564 357.46 645.083 392.246 585.759C427.402 525.803 535.835 532.271 605.24 496.456C669.245 463.428 707.587 392.579 780.734 385.286C854.983 377.887 913.869 423.972 969.503 460.148C1023.81 495.462 1070.62 536.538 1095.57 590.008C1122.89 648.573 1150.01 715.345 1116.88 776Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1179.62 788.094C1141.29 858.274 1040.46 887.235 959.637 928.02C884.432 965.979 811.364 1003.85 726.34 1016.87C626.244 1032.18 504.619 1061.65 433.761 1008.48C362.391 954.931 418.101 851.616 400.039 772.042C383.692 700.033 292.656 635.222 333.296 565.908C374.367 495.858 501.016 503.406 582.086 461.554C656.849 422.96 701.641 340.183 787.081 331.656C873.804 323.001 942.575 376.842 1007.55 419.098C1070.97 460.347 1125.64 508.336 1154.76 570.804C1186.67 639.224 1218.32 717.232 1179.62 788.094Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1248.06 801.292C1203.7 882.497 1087.05 916.024 993.542 963.233C906.53 1007.16 821.994 1051 723.627 1066.06C607.822 1083.79 467.11 1117.9 385.143 1056.38C302.584 994.42 367.05 874.858 346.164 782.78C327.264 699.457 221.952 624.466 268.979 544.255C316.506 463.192 463.021 471.914 556.821 423.479C643.319 378.814 695.152 283.023 794 273.15C894.331 263.129 973.884 325.421 1049.05 374.317C1122.41 422.044 1185.65 477.571 1219.33 549.855C1256.24 629.024 1292.85 719.288 1248.06 801.292Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1314.6 814.121C1264.38 906.06 1132.35 944.017 1026.51 997.46C928.02 1047.19 832.332 1096.83 720.99 1113.88C589.914 1133.96 430.643 1172.57 337.878 1102.95C244.438 1032.81 317.418 897.456 293.787 793.22C272.404 698.894 153.217 614.007 206.45 523.202C260.253 431.433 426.088 441.297 532.259 386.462C630.167 335.894 688.845 227.451 800.727 216.269C914.288 204.919 1004.32 275.431 1089.39 330.777C1172.42 384.802 1243.99 447.657 1282.12 529.482C1323.88 619.108 1365.3 721.286 1314.6 814.121Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1375.43 825.858C1319.86 927.601 1173.77 969.61 1056.64 1028.76C947.658 1083.8 841.775 1138.73 718.577 1157.61C573.539 1179.83 397.301 1222.57 294.66 1145.52C191.273 1067.9 272.036 918.118 245.898 802.764C222.244 698.379 90.3656 604.446 149.28 503.954C208.819 402.395 392.317 413.305 509.799 352.617C618.14 296.652 683.075 176.641 806.874 164.262C932.533 151.697 1032.15 229.725 1126.27 290.973C1218.15 350.757 1297.33 420.312 1339.52 510.862C1385.71 610.042 1431.55 723.121 1375.43 825.858Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1440.07 838.32C1378.81 950.483 1217.77 996.796 1088.66 1062.01C968.528 1122.7 851.815 1183.25 716.009 1204.06C556.142 1228.57 361.874 1275.7 248.738 1190.75C134.785 1105.2 223.816 940.067 195.013 812.908C168.946 697.836 23.5875 594.293 88.5336 483.506C154.169 371.546 356.436 383.566 485.932 316.661C605.359 254.958 676.944 122.657 813.405 109.006C951.915 95.1503 1061.72 181.166 1165.46 248.679C1266.72 314.582 1354.01 391.253 1400.5 491.077C1451.41 600.409 1501.93 725.065 1440.07 838.32Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1510.41 851.885C1442.96 975.386 1265.66 1026.39 1123.51 1098.2C991.244 1165.01 862.738 1231.69 713.227 1254.62C537.21 1281.61 323.327 1333.5 198.775 1239.98C73.3175 1145.78 171.352 963.956 139.646 823.942C110.955 697.239 -49.0767 583.227 22.4346 461.247C94.705 337.969 317.39 351.198 459.973 277.525C591.456 209.586 670.278 63.9094 820.52 48.8743C973.016 33.6133 1093.9 128.317 1208.12 202.654C1319.6 275.214 1415.69 359.636 1466.87 469.544C1522.92 589.926 1578.53 727.183 1510.41 851.885Z" stroke="#265bc3"/>
    </g>
    <mask id="mask0_2992_42854" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1632" height="596">
      <rect width="1632" height="596" fill="#563AFF"/>
    </mask>
    <g mask="url(#mask0_2992_42854)">
      <g filter="url(#filter0_d_2992_42854)">
        <circle cx="396.752" cy="515.159" r="14.7913" fill="#7D42FB"/>
        <circle cx="396.752" cy="515.159" r="14.7913" stroke="white" stroke-width="3"/>
      </g>
      <g filter="url(#filter1_d_2992_42854)">
        <circle cx="343.887" cy="96.5507" r="15.8241" fill="#FF4267"/>
        <circle cx="343.887" cy="96.5507" r="15.8241" stroke="white" stroke-width="3"/>
      </g>
      <g filter="url(#filter2_d_2992_42854)">
        <circle cx="1293.78" cy="95.1877" r="14.8197" fill="#FF813A"/>
        <circle cx="1293.78" cy="95.1877" r="14.8197" stroke="white" stroke-width="3"/>
      </g>
      <g filter="url(#filter3_d_2992_42854)">
        <circle cx="1330.45" cy="491.856" r="14.4879" fill="#1DE4FF"/>
        <circle cx="1330.45" cy="491.856" r="14.4879" stroke="white" stroke-width="3"/>
      </g>
      <g filter="url(#filter4_d_2992_42854)">
        <circle cx="1457.1" cy="271.284" r="15.5547" fill="#FFCD42"/>
        <circle cx="1457.1" cy="271.284" r="15.5547" stroke="white" stroke-width="3"/>
      </g>
      <g filter="url(#filter5_d_2992_42854)">
        <circle cx="166.703" cy="361.11" r="15.7416" fill="#2FF2B8"/>
        <circle cx="166.703" cy="361.11" r="15.7416" stroke="white" stroke-width="3"/>
      </g>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_2992_42854" x="373.461" y="495.868" width="46.5859" height="46.5826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="3.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <filter id="filter1_d_2992_42854" x="315.562" y="72.2266" width="56.6484" height="56.6482" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <filter id="filter2_d_2992_42854" x="1266.46" y="71.868" width="54.6406" height="54.6394" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <filter id="filter3_d_2992_42854" x="1303.46" y="468.868" width="53.9766" height="53.9758" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <filter id="filter4_d_2992_42854" x="1429.05" y="247.229" width="56.1094" height="56.1094" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <filter id="filter5_d_2992_42854" x="138.461" y="336.868" width="56.4844" height="56.4833" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_42854"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_42854" result="shape"/>
    </filter>
    <clipPath id="clip0_2992_42854">
      <rect width="1632" height="596" fill="white"/>
    </clipPath>
  </defs>
</svg>