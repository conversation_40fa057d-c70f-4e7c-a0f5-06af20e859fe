<svg width="1542" height="812" viewBox="0 0 1542 812" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2944_27827" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1542" height="812">
<rect width="1542" height="812" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2944_27827)">
<g opacity="0.5">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M840.804 779.62C832.093 795.554 809.069 802.098 790.626 811.335C773.464 819.932 756.79 828.512 737.366 831.435C714.498 834.876 686.72 841.525 670.486 829.415C654.132 817.217 666.793 793.757 662.601 775.669C658.812 759.298 637.956 744.533 647.195 728.797C656.532 712.892 685.485 714.657 703.985 705.178C721.046 696.436 731.222 677.638 750.747 675.737C770.563 673.804 786.32 686.066 801.201 695.696C815.727 705.095 828.257 716.023 834.962 730.231C842.308 745.79 849.6 763.531 840.804 779.62Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M901.64 791.353C887.568 817.093 850.486 827.689 820.764 842.632C793.108 856.537 766.24 870.413 734.955 875.159C698.126 880.748 653.38 891.521 627.271 871.989C600.973 852.315 621.414 814.418 614.719 785.211C608.661 758.781 575.117 734.968 590.032 709.545C605.108 683.851 651.722 686.664 681.534 671.33C709.027 657.191 725.463 626.828 756.903 623.727C788.814 620.58 814.157 640.357 838.094 655.886C861.459 671.044 881.607 688.672 892.363 711.601C904.148 736.724 915.844 765.36 901.64 791.353Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M960.575 802.718C941.322 837.957 890.607 852.481 849.959 872.948C812.138 892 775.393 911.009 732.618 917.517C682.262 925.186 621.08 939.956 585.405 913.233C549.47 886.316 577.452 834.433 568.326 794.458C560.068 758.284 514.233 725.706 534.648 690.899C555.282 655.722 619.006 659.545 659.777 638.544C697.379 619.176 719.874 577.608 762.859 573.348C806.49 569.023 841.116 596.082 873.828 617.325C905.757 638.061 933.284 662.18 947.971 693.565C964.054 727.941 980.018 767.133 960.575 802.718Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1013.81 812.986C989.867 856.807 926.845 874.878 876.33 900.335C829.328 924.022 783.665 947.668 730.509 955.781C667.935 965.322 591.908 983.704 547.59 950.484C502.953 917.028 537.745 852.511 526.424 802.81C516.18 757.834 459.241 717.339 484.621 674.057C510.274 630.316 589.455 635.052 640.124 608.93C686.85 584.841 714.825 533.152 768.236 527.843C822.455 522.451 865.467 556.089 906.108 582.495C945.771 608.268 979.964 638.251 998.198 677.27C1018.16 720.008 1037.98 768.734 1013.81 812.986Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1063.23 822.519C1034.94 874.3 960.493 895.663 900.816 925.761C845.286 953.772 791.333 981.709 728.549 991.307C654.628 1002.59 564.818 1024.32 512.476 985.073C459.758 945.547 500.877 869.296 487.515 810.564C475.425 757.416 408.178 709.57 438.174 658.418C468.483 606.721 562.019 612.308 621.88 581.429C677.082 552.959 710.14 491.867 773.235 485.585C837.278 479.21 888.078 518.953 936.075 550.151C982.92 580.603 1023.3 616.029 1044.83 662.137C1068.41 712.642 1091.8 770.221 1063.23 822.519Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1114.57 832.418C1081.76 892.481 995.437 917.255 926.246 952.173C861.861 984.653 799.306 1017.06 726.513 1028.19C640.815 1041.29 536.688 1066.51 476.015 1021C414.902 975.159 462.585 886.728 447.109 818.618C433.105 756.983 355.151 701.502 389.937 642.178C425.094 582.222 533.526 588.69 602.931 552.874C666.936 519.847 705.279 448.998 778.425 441.704C852.674 434.306 911.56 480.391 967.195 516.567C1021.5 551.881 1068.31 592.957 1093.26 646.427C1120.58 704.992 1147.7 771.764 1114.57 832.418Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1177.3 844.513C1138.97 914.693 1038.15 943.654 957.322 984.439C882.117 1022.4 809.049 1060.27 724.026 1073.28C623.929 1088.6 502.304 1118.07 431.447 1064.9C360.077 1011.35 415.786 908.035 397.725 828.461C381.378 756.452 290.342 691.641 330.981 622.327C372.053 552.277 498.701 559.825 579.772 517.973C654.535 479.379 699.327 396.601 784.767 388.075C871.49 379.419 940.261 433.261 1005.23 475.517C1068.65 516.766 1123.32 564.755 1152.45 627.223C1184.36 695.643 1216.01 773.651 1177.3 844.513Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1245.74 857.71C1201.39 938.916 1084.74 972.443 991.227 1019.65C904.216 1063.58 819.679 1107.42 721.313 1122.48C605.507 1140.2 464.795 1174.32 382.829 1112.8C300.269 1050.84 364.736 931.277 343.849 839.199C324.949 755.876 219.637 680.885 266.665 600.674C314.191 519.611 460.706 528.332 554.507 479.898C641.005 435.233 692.838 339.442 791.685 329.569C892.017 319.548 971.569 381.84 1046.73 430.736C1120.09 478.462 1183.33 533.99 1217.02 606.274C1253.92 685.443 1290.53 775.706 1245.74 857.71Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1312.28 870.54C1262.06 962.479 1130.03 1000.44 1024.19 1053.88C925.705 1103.61 830.017 1153.25 718.675 1170.3C587.599 1190.38 428.328 1228.99 335.563 1159.37C242.124 1089.23 315.103 953.875 291.472 849.639C270.089 755.313 150.903 670.426 204.136 579.621C257.939 487.852 423.774 497.716 529.945 442.881C627.853 392.313 686.53 283.87 798.412 272.688C911.974 261.338 1002.01 331.85 1087.07 387.196C1170.11 441.221 1241.68 504.076 1279.8 585.901C1321.56 675.527 1362.99 777.705 1312.28 870.54Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1373.12 882.277C1317.55 984.02 1171.45 1026.03 1054.33 1085.18C945.345 1140.22 839.463 1195.15 716.265 1214.03C571.226 1236.25 394.989 1278.99 292.347 1201.94C188.961 1124.32 269.724 974.537 243.585 859.183C219.932 754.798 88.0531 660.865 146.967 560.373C206.507 458.814 390.004 469.724 507.487 409.036C615.827 353.071 680.762 233.06 804.562 220.681C930.22 208.116 1029.84 286.144 1123.96 347.392C1215.83 407.175 1295.02 476.731 1337.21 567.281C1383.4 666.461 1429.23 779.54 1373.12 882.277Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1437.76 894.739C1376.5 1006.9 1215.46 1053.21 1086.35 1118.43C966.218 1179.11 849.506 1239.66 713.7 1260.48C553.832 1284.99 359.564 1332.11 246.429 1247.17C132.475 1161.62 221.507 996.486 192.704 869.327C166.637 754.255 21.2779 650.711 86.224 539.925C151.859 427.965 354.127 439.984 483.623 373.079C603.05 311.377 674.634 179.076 811.096 165.425C949.605 151.569 1059.41 237.584 1163.15 305.098C1264.41 371.001 1351.7 447.672 1398.19 547.496C1449.1 656.828 1499.62 781.484 1437.76 894.739Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1508.1 908.304C1440.65 1031.81 1263.34 1082.81 1121.2 1154.62C988.931 1221.43 860.425 1288.11 710.913 1311.04C534.896 1338.03 321.014 1389.92 196.461 1296.4C71.004 1202.2 169.038 1020.38 137.332 880.361C108.641 753.658 -51.3901 639.646 20.1211 517.666C92.3916 394.388 315.077 407.617 457.659 333.944C589.142 266.005 667.965 120.328 818.207 105.293C970.703 90.0322 1091.59 184.736 1205.8 259.073C1317.29 331.633 1413.38 416.054 1464.56 525.963C1520.61 646.345 1576.21 783.602 1508.1 908.304Z" stroke="white"/>
</g>
</g>
<g filter="url(#filter0_d_2944_27827)">
<circle cx="857.535" cy="273.791" r="15.3957" fill="#FF4267"/>
<circle cx="857.535" cy="273.791" r="15.3957" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2944_27827)">
<circle cx="1494.63" cy="598.029" r="13.4374" fill="#1DE4FF"/>
<circle cx="1494.63" cy="598.029" r="13.4374" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2944_27827)">
<circle cx="1370.38" cy="34.4523" r="15.9226" fill="#FF813A"/>
<circle cx="1370.38" cy="34.4523" r="15.9226" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2944_27827)">
<circle cx="159.17" cy="542.498" r="14.7903" fill="#2FF2B8"/>
<circle cx="159.17" cy="542.498" r="14.7903" stroke="white" stroke-width="3"/>
</g>
<defs>
<filter id="filter0_d_2944_27827" x="829.64" y="249.896" width="55.791" height="55.7914" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2944_27827"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2944_27827" result="shape"/>
</filter>
<filter id="filter1_d_2944_27827" x="1468.69" y="576.092" width="51.875" height="51.8748" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2944_27827"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2944_27827" result="shape"/>
</filter>
<filter id="filter2_d_2944_27827" x="1341.96" y="10.0297" width="56.8447" height="56.8452" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2944_27827"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2944_27827" result="shape"/>
</filter>
<filter id="filter3_d_2944_27827" x="131.88" y="519.208" width="54.5811" height="54.5806" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2944_27827"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2944_27827" result="shape"/>
</filter>
</defs>
</svg>
