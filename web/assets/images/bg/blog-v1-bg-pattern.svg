<svg width="1626" height="628" viewBox="0 0 1626 628" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2979_41577" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1626" height="628">
<rect width="1626" height="628" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2979_41577)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M840.533 678.634C831.823 694.568 808.799 701.112 790.355 710.349C773.193 718.946 756.519 727.526 737.096 730.449C714.227 733.89 686.449 740.539 670.215 728.429C653.862 716.232 666.522 692.771 662.33 674.683C658.541 658.312 637.686 643.547 646.925 627.811C656.261 611.907 685.214 613.671 703.715 604.192C720.775 595.45 730.952 576.652 750.476 574.751C770.292 572.818 786.05 585.08 800.931 594.71C815.456 604.109 827.986 615.037 834.691 629.245C842.037 644.805 849.329 662.545 840.533 678.634Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M901.369 690.367C887.297 716.107 850.214 726.703 820.493 741.646C792.837 755.551 765.968 769.427 734.684 774.173C697.854 779.762 653.109 790.535 626.999 771.003C600.701 751.329 621.143 713.432 614.448 684.226C608.389 657.795 574.846 633.983 589.761 608.559C604.836 582.865 651.451 585.678 681.263 570.344C708.756 556.205 725.191 525.843 756.631 522.741C788.543 519.594 813.885 539.372 837.822 554.9C861.187 570.058 881.335 587.686 892.091 610.616C903.876 635.738 915.572 664.374 901.369 690.367Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M960.301 701.732C941.048 736.971 890.332 751.495 849.685 771.962C811.863 791.014 775.118 810.023 732.344 816.531C681.988 824.2 620.806 838.97 585.131 812.247C549.196 785.33 577.178 733.447 568.052 693.472C559.794 657.298 513.958 624.72 534.374 589.913C555.007 554.736 618.732 558.559 659.503 537.558C697.105 518.19 719.6 476.622 762.584 472.362C806.215 468.037 840.842 495.096 873.554 516.339C905.482 537.075 933.009 561.194 947.697 592.579C963.78 626.955 979.743 666.147 960.301 701.732Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1013.53 712C989.596 755.821 926.574 773.892 876.059 799.349C829.056 823.036 783.394 846.682 730.238 854.796C667.664 864.336 591.636 882.719 547.319 849.499C502.681 816.042 537.474 751.525 526.153 701.824C515.908 656.848 458.97 616.354 484.35 573.072C510.003 529.33 589.183 534.067 639.853 507.944C686.579 483.856 714.553 432.166 767.965 426.857C822.183 421.465 865.196 455.104 905.836 481.509C945.5 507.282 979.693 537.265 997.927 576.284C1017.89 619.023 1037.71 667.748 1013.53 712Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1062.96 721.533C1034.67 773.314 960.223 794.677 900.545 824.776C845.016 852.786 791.063 880.724 728.278 890.321C654.357 901.608 564.547 923.338 512.205 884.087C459.487 844.561 500.606 768.31 487.245 709.579C475.155 656.43 407.908 608.584 437.903 557.432C468.212 505.735 561.748 511.322 621.609 480.443C676.811 451.973 709.869 390.881 772.965 384.599C837.008 378.224 887.808 417.967 935.805 449.165C982.65 479.617 1023.03 515.044 1044.56 561.151C1068.14 611.656 1091.53 669.235 1062.96 721.533Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1114.29 731.433C1081.48 791.495 995.163 816.269 925.972 851.187C861.588 883.667 799.033 916.078 726.239 927.206C640.541 940.306 536.414 965.527 475.741 920.016C414.629 874.173 462.312 785.742 446.836 717.632C432.831 655.997 354.878 600.516 389.664 541.192C424.82 481.236 533.253 487.704 602.658 451.889C666.663 418.861 705.005 348.012 778.152 340.719C852.401 333.321 911.287 379.405 966.921 415.581C1021.23 450.895 1068.04 491.971 1092.99 545.441C1120.31 604.007 1147.42 670.778 1114.29 731.433Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1177.03 743.527C1138.7 813.707 1037.88 842.668 957.051 883.453C881.846 921.413 808.778 959.288 723.754 972.299C623.658 987.616 502.033 1017.08 431.175 963.913C359.805 910.364 415.515 807.049 397.453 727.475C381.106 655.466 290.071 590.655 330.71 521.341C371.781 451.292 498.43 458.839 579.5 416.987C654.263 378.393 699.056 295.616 784.495 287.089C871.218 278.434 939.989 332.275 1004.96 374.531C1068.38 415.78 1123.05 463.769 1152.18 526.237C1184.08 594.657 1215.74 672.665 1177.03 743.527Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1245.47 756.725C1201.11 837.93 1084.47 871.457 990.956 918.666C903.944 962.591 819.408 1006.43 721.041 1021.49C605.236 1039.22 464.524 1073.33 382.557 1011.81C299.998 949.853 364.464 830.292 343.578 738.213C324.678 654.89 219.366 579.899 266.393 499.688C313.92 418.625 460.435 427.347 554.235 378.912C640.734 334.247 692.566 238.456 791.414 228.583C891.745 218.562 971.298 280.854 1046.46 329.75C1119.82 377.477 1183.06 433.004 1216.75 505.288C1253.65 584.457 1290.26 674.721 1245.47 756.725Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1312.01 769.554C1261.79 861.493 1129.76 899.45 1023.92 952.893C925.434 1002.63 829.746 1052.26 718.404 1069.32C587.328 1089.39 428.057 1128.01 335.292 1058.38C241.852 988.24 314.832 852.889 291.201 748.653C269.818 654.327 150.631 569.44 203.865 478.635C257.667 386.866 423.502 396.73 529.673 341.895C627.581 291.327 686.259 182.884 798.141 171.702C911.702 160.352 1001.74 230.864 1086.8 286.21C1169.84 340.235 1241.41 403.09 1279.53 484.915C1321.29 574.541 1362.72 676.719 1312.01 769.554Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1372.85 781.291C1317.28 883.034 1171.18 925.043 1054.06 984.193C945.072 1039.23 839.189 1094.17 715.991 1113.04C570.953 1135.27 394.715 1178 292.074 1100.96C188.687 1023.34 269.45 873.551 243.312 758.197C219.658 653.812 87.7797 559.879 146.694 459.388C206.233 357.829 389.731 368.738 507.214 308.05C615.554 252.085 680.489 132.075 804.288 119.695C929.947 107.13 1029.57 185.158 1123.69 246.407C1215.56 306.19 1294.75 375.745 1336.93 466.295C1383.13 565.475 1428.96 678.554 1372.85 781.291Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1437.48 793.753C1376.22 905.916 1215.18 952.229 1086.08 1017.44C965.946 1078.13 849.233 1138.68 713.427 1159.49C553.56 1184 359.292 1231.13 246.156 1146.18C132.203 1060.63 221.234 895.5 192.431 768.341C166.364 653.269 21.0054 549.726 85.9516 438.939C151.587 326.979 353.854 338.999 483.35 272.094C602.777 210.391 674.361 78.0904 810.823 64.4389C949.333 50.5834 1059.14 136.599 1162.88 204.112C1264.14 270.015 1351.43 346.686 1397.91 446.51C1448.83 555.842 1499.34 680.498 1437.48 793.753Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1507.83 807.318C1440.37 930.819 1263.07 981.824 1120.92 1053.63C988.658 1120.45 860.152 1187.13 710.641 1210.06C534.624 1237.04 320.741 1288.93 196.189 1195.41C70.7315 1101.21 168.766 919.39 137.06 779.375C108.369 652.672 -51.6626 538.66 19.8486 416.68C92.1191 293.402 314.804 306.631 457.387 232.958C588.87 165.019 667.692 19.3425 817.934 4.3073C970.431 -10.9537 1091.31 83.7503 1205.53 158.087C1317.02 230.647 1413.11 315.069 1464.29 424.977C1520.33 545.359 1575.94 682.616 1507.83 807.318Z" stroke="white"/>
</g>
<g filter="url(#filter0_d_2979_41577)">
<circle cx="410.176" cy="338.799" r="19.5" fill="#7D42FB"/>
<circle cx="410.176" cy="338.799" r="19.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2979_41577)">
<circle cx="296.051" cy="53.8011" r="15.5" fill="#FF4267"/>
<circle cx="296.051" cy="53.8011" r="15.5" stroke="white" stroke-width="2.16279"/>
</g>
<g filter="url(#filter2_d_2979_41577)">
<circle cx="1438.93" cy="38.9396" r="10" fill="#FF813A"/>
<circle cx="1438.93" cy="38.9396" r="10" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2979_41577)">
<circle cx="1325.89" cy="244.801" r="21.5" fill="#1DE4FF"/>
<circle cx="1325.89" cy="244.801" r="21.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_2979_41577)">
<circle cx="1478.29" cy="439.647" r="21.5" fill="#FFCD42"/>
<circle cx="1478.29" cy="439.647" r="21.5" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter5_d_2979_41577)">
<circle cx="203.75" cy="292.799" r="21.5" fill="#2FF2B8"/>
<circle cx="203.75" cy="292.799" r="21.5" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2979_41577" x="382.176" y="314.799" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
<filter id="filter1_d_2979_41577" x="271.539" y="32.1732" width="49.0245" height="49.0233" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.88372"/>
<feGaussianBlur stdDeviation="3.96512"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
<filter id="filter2_d_2979_41577" x="1416.43" y="20.4396" width="45" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
<filter id="filter3_d_2979_41577" x="1291.89" y="214.801" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
<filter id="filter4_d_2979_41577" x="1444.29" y="409.647" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
<filter id="filter5_d_2979_41577" x="169.75" y="262.799" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2979_41577"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2979_41577" result="shape"/>
</filter>
</defs>
</svg>
