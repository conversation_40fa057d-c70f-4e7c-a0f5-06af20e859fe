<?xml version="1.0" encoding="utf-8"?>
<svg width="1540" height="1060" viewBox="0 0 1540 1060" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="mask0_2907_30129" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1540" height="1060">
    <rect width="1540" height="1060" fill="#563AFF"/>
  </mask>
  <g mask="url(#mask0_2907_30129)">
    <g opacity="0.6">
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M839.772 929.433C831.061 945.367 808.038 951.911 789.594 961.148C772.432 969.745 755.758 978.325 736.334 981.248C713.466 984.689 685.688 991.338 669.454 979.228C653.101 967.031 665.761 943.57 661.569 925.482C657.78 909.111 636.925 894.346 646.163 878.61C655.5 862.706 684.453 864.47 702.953 854.991C720.014 846.249 730.191 827.451 749.715 825.55C769.531 823.617 785.288 835.879 800.169 845.509C814.695 854.909 827.225 865.836 833.93 880.044C841.276 895.604 848.568 913.344 839.772 929.433Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M900.608 941.166C886.537 966.906 849.454 977.502 819.732 992.445C792.076 1006.35 765.208 1020.23 733.923 1024.97C697.094 1030.56 652.348 1041.33 626.239 1021.8C599.941 1002.13 620.382 964.231 613.687 935.025C607.629 908.594 574.085 884.782 589 859.358C604.076 833.664 650.69 836.477 680.502 821.143C707.995 807.004 724.431 776.642 755.871 773.54C787.782 770.393 813.125 790.171 837.062 805.699C860.427 820.857 880.575 838.486 891.331 861.415C903.116 886.538 914.812 915.173 900.608 941.166Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M959.543 952.531C940.29 987.77 889.575 1002.29 848.927 1022.76C811.106 1041.81 774.361 1060.82 731.586 1067.33C681.23 1075 620.048 1089.77 584.373 1063.05C548.438 1036.13 576.42 984.246 567.294 944.272C559.036 908.097 513.201 875.519 533.616 840.712C554.25 805.536 617.974 809.359 658.745 788.357C696.347 768.989 718.842 727.421 761.827 723.161C805.458 718.836 840.084 745.895 872.796 767.138C904.725 787.874 932.252 811.994 946.939 843.378C963.022 877.754 978.986 916.946 959.543 952.531Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1012.77 962.799C988.835 1006.62 925.813 1024.69 875.298 1050.15C828.296 1073.83 782.633 1097.48 729.477 1105.59C666.903 1115.14 590.876 1133.52 546.558 1100.3C501.92 1066.84 536.713 1002.32 525.392 952.623C515.147 907.647 458.209 867.152 483.589 823.871C509.242 780.129 588.422 784.865 639.092 758.743C685.818 734.655 713.792 682.965 767.204 677.656C821.422 672.264 864.435 705.903 905.076 732.308C944.739 758.081 978.932 788.064 997.166 827.083C1017.13 869.822 1036.95 918.547 1012.77 962.799Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1062.2 972.332C1033.91 1024.11 959.462 1045.48 899.784 1075.57C844.255 1103.59 790.302 1131.52 727.517 1141.12C653.596 1152.41 563.786 1174.14 511.444 1134.89C458.726 1095.36 499.845 1019.11 486.483 960.378C474.394 907.229 407.147 859.383 437.142 808.231C467.451 756.534 560.987 762.121 620.848 731.242C676.05 702.772 709.108 641.68 772.203 635.398C836.247 629.023 887.047 668.766 935.044 699.964C981.889 730.416 1022.27 765.843 1043.8 811.95C1067.38 862.455 1090.77 920.034 1062.2 972.332Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1113.53 982.232C1080.72 1042.29 994.405 1067.07 925.214 1101.99C860.829 1134.47 798.274 1166.88 725.481 1178C639.783 1191.11 535.656 1216.33 474.983 1170.82C413.87 1124.97 461.553 1036.54 446.077 968.431C432.073 906.796 354.119 851.315 388.905 791.991C424.062 732.035 532.494 738.503 601.899 702.688C665.904 669.66 704.247 598.811 777.393 591.517C851.642 584.119 910.529 630.204 966.163 666.38C1020.47 701.694 1067.28 742.77 1092.23 796.24C1119.55 854.805 1146.67 921.577 1113.53 982.232Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1176.27 994.326C1137.94 1064.51 1037.12 1093.47 956.291 1134.25C881.085 1172.21 808.018 1210.09 722.994 1223.1C622.898 1238.42 501.273 1267.88 430.415 1214.71C359.045 1161.16 414.755 1057.85 396.693 978.274C380.346 906.265 289.31 841.454 329.95 772.14C371.021 702.091 497.669 709.638 578.74 667.786C653.503 629.192 698.295 546.415 783.735 537.888C870.458 529.233 939.229 583.074 1004.2 625.33C1067.62 666.579 1122.29 714.568 1151.42 777.036C1183.32 845.456 1214.98 923.464 1176.27 994.326Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1244.71 1007.52C1200.35 1088.73 1083.71 1122.26 990.196 1169.46C903.184 1213.39 818.648 1257.23 720.281 1272.29C604.476 1290.02 463.764 1324.13 381.797 1262.61C299.238 1200.65 363.704 1081.09 342.818 989.012C323.918 905.689 218.606 830.699 265.633 750.487C313.16 669.424 459.675 678.146 553.475 629.711C639.974 585.046 691.806 489.255 790.654 479.382C890.985 469.361 970.538 531.653 1045.7 580.549C1119.06 628.276 1182.3 683.803 1215.99 756.087C1252.89 835.256 1289.5 925.52 1244.71 1007.52Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1311.25 1020.35C1261.03 1112.29 1129 1150.25 1023.16 1203.69C924.673 1253.43 828.985 1303.06 717.643 1320.11C586.567 1340.19 427.296 1378.81 334.531 1309.18C241.091 1239.04 314.071 1103.69 290.44 999.452C269.057 905.126 149.87 820.239 203.104 729.435C256.906 637.665 422.741 647.529 528.912 592.694C626.82 542.126 685.498 433.683 797.38 422.501C910.941 411.151 1000.98 481.663 1086.04 537.009C1169.07 591.034 1240.64 653.889 1278.77 735.715C1320.53 825.34 1361.96 927.519 1311.25 1020.35Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1372.09 1032.09C1316.52 1133.83 1170.42 1175.84 1053.3 1234.99C944.313 1290.03 838.431 1344.96 715.233 1363.84C570.194 1386.07 393.957 1428.8 291.315 1351.75C187.929 1274.14 268.692 1124.35 242.553 1009C218.9 904.612 87.0211 810.678 145.936 710.187C205.475 608.628 388.972 619.537 506.455 558.849C614.796 502.884 679.73 382.874 803.53 370.494C929.188 357.929 1028.81 435.957 1122.93 497.206C1214.8 556.989 1293.99 626.544 1336.18 717.094C1382.37 816.274 1428.2 929.353 1372.09 1032.09Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1436.72 1044.55C1375.47 1156.72 1214.42 1203.03 1085.32 1268.24C965.186 1328.93 848.474 1389.48 712.668 1410.29C552.801 1434.8 358.532 1481.93 245.397 1396.98C131.443 1311.43 220.475 1146.3 191.672 1019.14C165.605 904.068 20.2459 800.525 85.192 689.738C150.827 577.778 353.095 589.798 482.591 522.893C602.018 461.19 673.602 328.889 810.064 315.238C948.573 301.382 1058.38 387.398 1162.12 454.911C1263.38 520.814 1350.67 597.485 1397.15 697.309C1448.07 806.641 1498.58 931.297 1436.72 1044.55Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1507.07 1058.12C1439.62 1181.62 1262.31 1232.62 1120.17 1304.43C987.899 1371.25 859.393 1437.93 709.881 1460.85C533.864 1487.84 319.982 1539.73 195.429 1446.21C69.972 1352.01 168.006 1170.19 136.3 1030.17C107.609 903.471 -52.4221 789.459 19.0891 667.479C91.3596 544.201 314.045 557.43 456.627 483.757C588.11 415.818 666.933 270.141 817.175 255.106C969.671 239.845 1090.55 334.549 1204.77 408.886C1316.26 481.446 1412.35 565.868 1463.53 675.776C1519.57 796.158 1575.18 933.415 1507.07 1058.12Z" stroke="#265bc3"/>
    </g>
  </g>
  <g filter="url(#filter0_d_2907_30129)">
    <circle cx="314.56" cy="158.162" r="13.6714" fill="#FF4267"/>
    <circle cx="314.56" cy="158.162" r="13.6714" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter1_d_2907_30129)">
    <circle cx="1369.13" cy="194.769" r="13.2306" fill="#FF813A"/>
    <circle cx="1369.13" cy="194.769" r="13.2306" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter2_d_2907_30129)">
    <circle cx="1397.63" cy="562" r="11.2204" fill="#FFCD42"/>
    <circle cx="1397.63" cy="562" r="11.2204" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter3_d_2907_30129)">
    <circle cx="158.24" cy="465.069" r="15.0688" fill="#2FF2B8"/>
    <circle cx="158.24" cy="465.069" r="15.0688" stroke="white" stroke-width="3"/>
  </g>
  <defs>
    <filter id="filter0_d_2907_30129" x="288.388" y="135.99" width="52.3428" height="52.3428" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2907_30129"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2907_30129" result="shape"/>
    </filter>
    <filter id="filter1_d_2907_30129" x="1343.4" y="173.039" width="51.4613" height="51.4613" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2907_30129"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2907_30129" result="shape"/>
    </filter>
    <filter id="filter2_d_2907_30129" x="1373.91" y="542.28" width="47.4409" height="47.4409" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2907_30129"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2907_30129" result="shape"/>
    </filter>
    <filter id="filter3_d_2907_30129" x="130.671" y="441.5" width="55.1377" height="55.1377" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2907_30129"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2907_30129" result="shape"/>
    </filter>
  </defs>
</svg>