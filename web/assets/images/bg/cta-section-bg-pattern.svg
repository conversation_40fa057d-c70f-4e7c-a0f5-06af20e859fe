<svg width="1515" height="544" viewBox="0 0 1515 544" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2923_24409" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1515" height="544">
<rect x="0.996582" width="1514" height="544" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2923_24409)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M902.532 693.446C888.46 719.187 851.377 729.782 821.656 744.726C794 758.631 767.131 772.506 735.847 777.253C699.017 782.841 654.272 793.615 628.162 774.083C601.864 754.408 622.306 716.512 615.611 687.305C609.552 660.875 576.009 637.062 590.924 611.638C605.999 585.945 652.614 588.758 682.426 573.423C709.919 559.284 726.354 528.922 757.794 525.821C789.706 522.674 815.048 542.451 838.985 557.98C862.35 573.138 882.498 590.766 893.254 613.695C905.039 638.818 916.736 667.454 902.532 693.446Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M961.467 704.812C942.213 740.051 891.498 754.574 850.851 775.041C813.029 794.094 776.284 813.103 733.509 819.611C683.154 827.28 621.972 842.049 586.297 815.327C550.362 788.41 578.344 736.527 569.218 696.552C560.96 660.377 515.124 627.799 535.54 592.993C556.173 557.816 619.898 561.639 660.669 540.638C698.27 521.27 720.766 479.702 763.75 475.441C807.381 471.117 842.008 498.175 874.72 519.419C906.648 540.155 934.175 564.274 948.862 595.658C964.945 630.035 980.909 669.226 961.467 704.812Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1014.7 715.08C990.758 758.901 927.737 776.971 877.221 802.429C830.219 826.115 784.556 849.761 731.401 857.875C668.826 867.415 592.799 885.798 548.481 852.578C503.844 819.121 538.637 754.604 527.315 704.904C517.071 659.928 460.133 619.433 485.513 576.151C511.165 532.409 590.346 537.146 641.016 511.024C687.741 486.935 715.716 435.245 769.127 429.937C823.346 424.544 866.359 458.183 906.999 484.588C946.662 510.362 980.856 540.344 999.089 579.363C1019.06 622.102 1038.87 670.828 1014.7 715.08Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1064.13 724.612C1035.83 776.393 961.385 797.756 901.708 827.855C846.178 855.865 792.225 883.803 729.441 893.4C655.52 904.687 565.71 926.417 513.368 887.166C460.649 847.64 501.769 771.389 488.407 712.658C476.317 659.509 409.07 611.663 439.066 560.511C469.375 508.814 562.911 514.402 622.772 483.522C677.974 455.052 711.032 393.96 774.127 387.679C838.17 381.304 888.97 421.046 936.967 452.244C983.812 482.696 1024.2 518.123 1045.73 564.231C1069.3 614.735 1092.69 672.314 1064.13 724.612Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1115.46 734.512C1082.65 794.574 996.329 819.348 927.137 854.266C862.753 886.746 800.198 919.157 727.404 930.285C641.706 943.385 537.579 968.606 476.906 923.095C415.794 877.252 463.477 788.821 448.001 720.711C433.996 659.076 356.043 603.595 390.829 544.271C425.985 484.315 534.418 490.783 603.823 454.968C667.828 421.94 706.17 351.091 779.317 343.798C853.566 336.4 912.452 382.484 968.086 418.66C1022.39 453.974 1069.2 495.05 1094.15 548.52C1121.48 607.086 1148.59 673.857 1115.46 734.512Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1178.19 746.607C1139.86 816.786 1039.04 845.747 958.214 886.533C883.009 924.492 809.941 962.367 724.918 975.378C624.821 990.696 503.196 1020.16 432.339 966.992C360.969 913.443 416.678 810.128 398.617 730.554C382.27 658.546 291.234 593.735 331.873 524.421C372.945 454.371 499.593 461.918 580.664 420.066C655.426 381.472 700.219 298.695 785.659 290.169C872.382 281.513 941.153 335.355 1006.13 377.611C1069.55 418.86 1124.21 466.848 1153.34 529.316C1185.25 597.736 1216.9 675.744 1178.19 746.607Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1246.63 759.804C1202.28 841.009 1085.63 874.537 992.119 921.745C905.108 965.67 820.571 1009.51 722.205 1024.57C606.399 1042.3 465.687 1076.41 383.721 1014.89C301.161 952.933 365.628 833.371 344.741 741.292C325.841 657.969 220.529 582.979 267.557 502.767C315.083 421.705 461.599 430.426 555.399 381.992C641.897 337.326 693.73 241.535 792.578 231.663C892.909 221.642 972.461 283.933 1047.62 332.829C1120.99 380.556 1184.23 436.083 1217.91 508.367C1254.82 587.536 1291.43 677.8 1246.63 759.804Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1313.17 772.634C1262.95 864.573 1130.92 902.53 1025.08 955.972C926.597 1005.71 830.909 1055.34 719.567 1072.39C588.49 1092.47 429.219 1131.09 336.454 1061.46C243.015 991.319 315.994 855.968 292.363 751.732C270.981 657.406 151.794 572.52 205.027 481.715C258.83 389.945 424.665 399.809 530.836 344.974C628.744 294.406 687.422 185.963 799.303 174.781C912.865 163.431 1002.9 233.943 1087.97 289.289C1171 343.314 1242.57 406.17 1280.69 487.995C1322.45 577.621 1363.88 679.799 1313.17 772.634Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1374.01 784.37C1318.44 886.113 1172.35 928.122 1055.22 987.272C946.237 1042.31 840.354 1097.24 717.156 1116.12C572.118 1138.35 395.88 1181.08 293.239 1104.03C189.852 1026.42 270.615 876.63 244.477 761.277C220.823 656.892 88.9447 562.958 147.859 462.467C207.398 360.908 390.896 371.817 508.379 311.129C616.719 255.164 681.654 135.154 805.453 122.775C931.112 110.209 1030.73 188.237 1124.85 249.486C1216.73 309.269 1295.91 378.824 1338.1 469.374C1384.29 568.554 1430.12 681.633 1374.01 784.37Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1438.65 796.833C1377.39 908.996 1216.35 955.309 1087.24 1020.52C967.11 1081.21 850.397 1141.76 714.591 1162.57C554.724 1187.08 360.456 1234.21 247.32 1149.26C133.367 1063.71 222.398 898.579 193.595 771.42C167.529 656.349 22.1695 552.805 87.1156 442.019C152.751 330.058 355.018 342.078 484.514 275.173C603.941 213.471 675.526 81.1699 811.987 67.5184C950.497 53.6629 1060.3 139.678 1164.04 207.192C1265.3 273.095 1352.59 349.765 1399.08 449.589C1450 558.922 1500.51 683.578 1438.65 796.833Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1508.99 810.397C1441.54 933.899 1264.23 984.904 1122.09 1056.71C989.822 1123.53 861.316 1190.21 711.805 1213.13C535.788 1240.12 321.905 1292.01 197.353 1198.49C71.8956 1104.29 169.93 922.469 138.224 782.455C109.533 655.751 -50.4985 541.739 21.0127 419.76C93.2832 296.482 315.968 309.71 458.551 236.037C590.034 168.098 668.856 22.4218 819.098 7.38658C971.595 -7.8744 1092.48 86.8296 1206.69 161.166C1318.18 233.726 1414.27 318.148 1465.45 428.057C1521.5 548.438 1577.1 685.695 1508.99 810.397Z" stroke="white"/>
</g>
<g filter="url(#filter0_d_2923_24409)">
<circle cx="780.382" cy="13.4248" r="11.1997" fill="#FFCD42"/>
<circle cx="780.382" cy="13.4248" r="11.1997" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2923_24409)">
<circle cx="110.389" cy="414.816" r="12.1921" fill="#FF4267"/>
<circle cx="110.389" cy="414.816" r="12.1921" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2923_24409)">
<circle cx="594.027" cy="415.978" r="10.5547" fill="#2FF2B8"/>
<circle cx="594.027" cy="415.978" r="10.5547" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2923_24409" x="756.682" y="-6.2749" width="47.3994" height="47.3994" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2923_24409"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2923_24409" result="shape"/>
</filter>
<filter id="filter1_d_2923_24409" x="85.3915" y="393.929" width="49.9954" height="49.9954" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.11111"/>
<feGaussianBlur stdDeviation="5.65278"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2923_24409"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2923_24409" result="shape"/>
</filter>
<filter id="filter2_d_2923_24409" x="568.833" y="395.562" width="50.3872" height="50.3872" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.77778"/>
<feGaussianBlur stdDeviation="6.56944"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2923_24409"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2923_24409" result="shape"/>
</filter>
</defs>
</svg>
