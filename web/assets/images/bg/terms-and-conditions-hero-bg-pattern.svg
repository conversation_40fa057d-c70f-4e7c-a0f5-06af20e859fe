<svg width="1630" height="584" viewBox="0 0 1630 584" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2990_42130)">
<mask id="mask0_2990_42130" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-221" y="-94" width="2072" height="678">
<rect x="-220.566" y="-94" width="2071.13" height="677.574" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2990_42130)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M902.978 720.566C888.906 746.306 851.824 756.902 822.102 771.845C794.446 785.75 767.578 799.626 736.293 804.372C699.464 809.961 654.718 820.734 628.608 801.202C602.311 781.528 622.752 743.631 616.057 714.424C609.999 687.994 576.455 664.181 591.37 638.758C606.445 613.064 653.06 615.877 682.872 600.543C710.365 586.404 726.8 556.041 758.24 552.94C790.152 549.793 815.495 569.57 839.432 585.099C862.796 600.257 882.944 617.885 893.701 640.814C905.485 665.937 917.182 694.573 902.978 720.566Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M961.91 731.931C942.657 767.17 891.941 781.694 851.294 802.161C813.473 821.213 776.728 840.222 733.953 846.73C683.597 854.399 622.415 869.169 586.74 842.446C550.805 815.529 578.787 763.646 569.661 723.671C561.403 687.497 515.568 654.919 535.983 620.112C556.617 584.935 620.341 588.758 661.112 567.757C698.714 548.389 721.209 506.821 764.194 502.561C807.825 498.236 842.451 525.295 875.163 546.538C907.092 567.274 934.619 591.393 949.306 622.778C965.389 657.154 981.353 696.346 961.91 731.931Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1015.14 742.199C991.205 786.02 928.183 804.091 877.668 829.548C830.666 853.235 785.003 876.881 731.847 884.995C669.273 894.535 593.246 912.918 548.928 879.697C504.291 846.241 539.083 781.724 527.762 732.023C517.518 687.047 460.579 646.552 485.959 603.271C511.612 559.529 590.793 564.265 641.462 538.143C688.188 514.055 716.163 462.365 769.574 457.056C823.793 451.664 866.805 485.303 907.446 511.708C947.109 537.481 981.302 567.464 999.536 606.483C1019.5 649.222 1039.32 697.947 1015.14 742.199Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1064.57 751.732C1036.28 803.513 961.832 824.876 902.154 854.974C846.625 882.985 792.672 910.922 729.888 920.52C655.966 931.807 566.157 953.537 513.815 914.286C461.096 874.76 502.216 798.509 488.854 739.777C476.764 686.629 409.517 638.783 439.512 587.631C469.822 535.934 563.358 541.521 623.219 510.642C678.421 482.172 711.479 421.08 774.574 414.798C838.617 408.423 889.417 448.166 937.414 479.364C984.259 509.816 1024.64 545.242 1046.17 591.35C1069.75 641.855 1093.14 699.434 1064.57 751.732Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1115.9 761.632C1083.09 821.694 996.773 846.468 927.582 881.386C863.197 913.866 800.642 946.277 727.849 957.405C642.151 970.505 538.023 995.726 477.35 950.215C416.238 904.372 463.921 815.941 448.445 747.831C434.441 686.196 356.487 630.715 391.273 571.391C426.429 511.435 534.862 517.903 604.267 482.088C668.272 449.06 706.615 378.211 779.761 370.918C854.01 363.519 912.896 409.604 968.531 445.78C1022.84 481.094 1069.65 522.17 1094.59 575.64C1121.92 634.205 1149.03 700.977 1115.9 761.632Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1178.64 773.726C1140.31 843.906 1039.49 872.867 958.66 913.652C883.455 951.611 810.387 989.487 725.364 1002.5C625.267 1017.82 503.642 1047.28 432.785 994.112C361.415 940.563 417.124 837.248 399.062 757.674C382.716 685.665 291.68 620.854 332.319 551.54C373.391 481.49 500.039 489.038 581.11 447.186C655.872 408.592 700.665 325.814 786.105 317.288C872.828 308.632 941.598 362.474 1006.57 404.73C1069.99 445.979 1124.66 493.968 1153.79 556.436C1185.69 624.856 1217.35 702.864 1178.64 773.726Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1247.08 786.923C1202.72 868.129 1086.08 901.656 992.565 948.865C905.554 992.79 821.017 1036.63 722.651 1051.69C606.845 1069.42 466.133 1103.53 384.167 1042.01C301.607 980.052 366.073 860.49 345.187 768.412C326.287 685.089 220.975 610.098 268.003 529.887C315.529 448.824 462.044 457.546 555.845 409.111C642.343 364.446 694.175 268.655 793.023 258.782C893.355 248.761 972.907 311.053 1048.07 359.949C1121.43 407.676 1184.67 463.203 1218.36 535.487C1255.26 614.656 1291.87 704.919 1247.08 786.923Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1313.62 799.753C1263.4 891.692 1131.37 929.649 1025.53 983.092C927.043 1032.83 831.355 1082.46 720.013 1099.51C588.937 1119.59 429.666 1158.2 336.901 1088.58C243.461 1018.44 316.441 883.088 292.81 778.852C271.427 684.526 152.241 599.639 205.474 508.834C259.277 417.065 425.112 426.929 531.282 372.094C629.19 321.526 687.868 213.083 799.75 201.901C913.312 190.551 1003.35 261.063 1088.41 316.409C1171.45 370.433 1243.02 433.289 1281.14 515.114C1322.9 604.74 1364.33 706.918 1313.62 799.753Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1374.46 811.49C1318.89 913.233 1172.79 955.242 1055.66 1014.39C946.681 1069.43 840.798 1124.36 717.601 1143.24C572.562 1165.47 396.325 1208.2 293.683 1131.15C190.297 1053.54 271.06 903.75 244.921 788.396C221.268 684.011 89.389 590.078 148.303 489.586C207.843 388.027 391.34 398.937 508.823 338.249C617.163 282.284 682.098 162.273 805.898 149.894C931.556 137.329 1031.18 215.357 1125.3 276.605C1217.17 336.389 1296.36 405.944 1338.54 496.494C1384.74 595.674 1430.57 708.753 1374.46 811.49Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1439.09 823.952C1377.83 936.115 1216.79 982.428 1087.69 1047.64C967.555 1108.33 850.843 1168.88 715.037 1189.69C555.169 1214.2 360.901 1261.33 247.766 1176.38C133.812 1090.83 222.844 925.699 194.041 798.54C167.974 683.468 22.6148 579.925 87.5609 469.138C153.196 357.178 355.464 369.198 484.96 302.292C604.387 240.59 675.971 108.289 812.433 94.6378C950.942 80.7823 1060.74 166.798 1164.49 234.311C1265.75 300.214 1353.04 376.885 1399.52 476.709C1450.44 586.041 1500.95 710.697 1439.09 823.952Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1509.44 837.517C1441.98 961.018 1264.68 1012.02 1122.53 1083.83C990.267 1150.65 861.761 1217.33 712.25 1240.25C536.233 1267.24 322.351 1319.13 197.798 1225.61C72.3409 1131.41 170.375 949.588 138.669 809.574C109.978 682.871 -50.0532 568.859 21.458 446.879C93.7285 323.601 316.413 336.83 458.996 263.157C590.479 195.218 669.302 49.5414 819.543 34.5062C972.04 19.2452 1092.92 113.949 1207.14 188.286C1318.63 260.846 1414.72 345.268 1465.89 455.176C1521.94 575.558 1577.55 712.815 1509.44 837.517Z" stroke="white"/>
</g>
</g>
<g filter="url(#filter0_d_2990_42130)">
<circle cx="395.791" cy="500.791" r="14.7913" fill="#7D42FB"/>
<circle cx="395.791" cy="500.791" r="14.7913" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2990_42130)">
<circle cx="342.93" cy="82.1826" r="15.8241" fill="#FF4267"/>
<circle cx="342.93" cy="82.1826" r="15.8241" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2990_42130)">
<circle cx="1292.82" cy="80.8197" r="14.8197" fill="#FF813A"/>
<circle cx="1292.82" cy="80.8197" r="14.8197" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2990_42130)">
<circle cx="1329.49" cy="477.488" r="14.4879" fill="#1DE4FF"/>
<circle cx="1329.49" cy="477.488" r="14.4879" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_2990_42130)">
<circle cx="1456.14" cy="256.916" r="15.5547" fill="#FFCD42"/>
<circle cx="1456.14" cy="256.916" r="15.5547" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter5_d_2990_42130)">
<circle cx="165.742" cy="346.742" r="15.7416" fill="#2FF2B8"/>
<circle cx="165.742" cy="346.742" r="15.7416" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2990_42130" x="372.5" y="481.5" width="46.582" height="46.5826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<filter id="filter1_d_2990_42130" x="314.605" y="57.8585" width="56.6484" height="56.6482" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<filter id="filter2_d_2990_42130" x="1265.5" y="57.5" width="54.6406" height="54.6394" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<filter id="filter3_d_2990_42130" x="1302.5" y="454.5" width="53.9766" height="53.9758" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<filter id="filter4_d_2990_42130" x="1428.09" y="232.861" width="56.1094" height="56.1094" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<filter id="filter5_d_2990_42130" x="137.5" y="322.5" width="56.4844" height="56.4833" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2990_42130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2990_42130" result="shape"/>
</filter>
<clipPath id="clip0_2990_42130">
<rect width="1630" height="584" fill="white"/>
</clipPath>
</defs>
</svg>
