<svg width="1294" height="1206" viewBox="0 0 1294 1206" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2938_27255)">
<mask id="mask0_2938_27255" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-17" y="-1" width="1312" height="1207">
<rect x="-16.5183" y="-0.0223389" width="1310.59" height="1205.9" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2938_27255)">
<g opacity="0.4">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1261.61 617.458C1253.5 632.31 1232.03 638.41 1214.84 647.021C1198.84 655.035 1183.3 663.032 1165.2 665.757C1143.88 668.964 1117.99 675.162 1102.85 663.874C1087.61 652.504 1099.41 630.636 1095.5 613.775C1091.97 598.515 1072.53 584.751 1081.14 570.083C1089.85 555.258 1116.83 556.903 1134.08 548.067C1149.98 539.918 1159.47 522.396 1177.67 520.624C1196.14 518.822 1210.83 530.251 1224.7 539.228C1238.24 547.99 1249.92 558.176 1256.17 571.42C1263.02 585.924 1269.81 602.46 1261.61 617.458Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1318.32 628.394C1305.21 652.388 1270.64 662.265 1242.93 676.194C1217.16 689.156 1192.11 702.09 1162.95 706.514C1128.62 711.724 1086.91 721.766 1062.57 703.559C1038.06 685.22 1057.11 649.895 1050.87 622.67C1045.22 598.033 1013.96 575.836 1027.86 552.137C1041.91 528.187 1085.36 530.809 1113.15 516.515C1138.78 503.336 1154.1 475.034 1183.41 472.143C1213.15 469.209 1236.78 487.645 1259.09 502.12C1280.87 516.249 1299.65 532.681 1309.68 554.054C1320.66 577.473 1331.56 604.166 1318.32 628.394Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1373.26 638.989C1355.31 671.836 1308.04 685.374 1270.15 704.453C1234.89 722.212 1200.64 739.932 1160.77 745.998C1113.83 753.147 1056.8 766.914 1023.55 742.005C990.049 716.914 1016.13 668.551 1007.63 631.289C999.928 597.569 957.202 567.202 976.233 534.757C995.466 501.967 1054.87 505.531 1092.87 485.954C1127.92 467.9 1148.89 429.153 1188.96 425.182C1229.63 421.15 1261.91 446.373 1292.4 466.175C1322.16 485.504 1347.82 507.987 1361.51 537.242C1376.5 569.285 1391.38 605.818 1373.26 638.989Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1422.88 648.56C1400.56 689.408 1341.82 706.252 1294.73 729.982C1250.92 752.062 1208.35 774.103 1158.8 781.666C1100.48 790.559 1029.61 807.695 988.296 776.728C946.687 745.542 979.119 685.403 968.566 639.074C959.017 597.15 905.942 559.403 929.6 519.058C953.512 478.284 1027.32 482.7 1074.55 458.35C1118.11 435.896 1144.18 387.713 1193.97 382.765C1244.51 377.738 1284.6 409.095 1322.49 433.708C1359.46 457.733 1391.33 485.681 1408.33 522.052C1426.94 561.891 1445.41 607.31 1422.88 648.56Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1468.95 657.445C1442.58 705.713 1373.18 725.627 1317.55 753.683C1265.79 779.793 1215.5 805.835 1156.98 814.781C1088.07 825.302 1004.36 845.558 955.565 808.97C906.424 772.126 944.753 701.049 932.298 646.302C921.029 596.76 858.344 552.161 886.304 504.479C914.557 456.29 1001.75 461.499 1057.55 432.715C1109 406.176 1139.82 349.229 1198.63 343.374C1258.33 337.432 1305.68 374.477 1350.42 403.559C1394.09 431.945 1431.73 464.967 1451.8 507.947C1473.77 555.024 1495.58 608.696 1468.95 657.445Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1516.8 666.674C1486.22 722.66 1405.76 745.754 1341.26 778.302C1281.24 808.578 1222.93 838.79 1155.08 849.163C1075.2 861.375 978.134 884.884 921.577 842.461C864.612 799.729 909.059 717.298 894.634 653.81C881.579 596.356 808.915 544.64 841.341 489.341C874.112 433.454 975.187 439.483 1039.88 406.098C1099.54 375.311 1135.29 309.269 1203.47 302.471C1272.68 295.575 1327.57 338.532 1379.43 372.254C1430.05 405.171 1473.68 443.461 1496.94 493.302C1522.41 547.894 1547.68 610.135 1516.8 666.674Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1575.28 677.948C1539.55 743.365 1445.57 770.361 1370.23 808.379C1300.12 843.763 1232.02 879.068 1152.76 891.196C1059.46 905.474 946.084 932.942 880.034 883.379C813.507 833.464 865.436 737.159 848.6 662.984C833.362 595.862 748.504 535.449 786.385 470.838C824.67 405.541 942.725 412.576 1018.29 373.564C1087.98 337.589 1129.74 260.428 1209.38 252.481C1290.22 244.412 1354.32 294.6 1414.89 333.989C1474 372.439 1524.96 417.172 1552.11 475.401C1581.86 539.178 1611.36 611.893 1575.28 677.948Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1639.08 690.25C1597.73 765.945 1489 797.197 1401.83 841.203C1320.72 882.147 1241.92 923.013 1150.23 937.052C1042.28 953.575 911.12 985.372 834.715 928.029C757.757 870.274 817.85 758.825 798.381 672.994C780.763 595.325 682.597 525.423 726.433 450.654C770.735 375.091 907.309 383.221 994.744 338.073C1075.37 296.439 1123.69 207.147 1215.83 197.945C1309.35 188.604 1383.51 246.668 1453.57 292.246C1521.96 336.735 1580.9 388.494 1612.3 455.874C1646.7 529.671 1680.83 613.81 1639.08 690.25Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1701.1 702.209C1654.29 787.909 1531.22 823.291 1432.56 873.107C1340.75 919.467 1251.56 965.731 1147.77 981.63C1025.59 1000.35 877.126 1036.34 790.656 971.437C703.556 906.056 771.584 779.889 749.557 682.726C729.625 594.8 618.525 515.673 668.146 431.03C718.298 345.487 872.881 354.682 971.848 303.567C1063.11 256.431 1117.81 155.346 1222.1 144.922C1327.96 134.343 1411.88 200.07 1491.17 251.661C1568.57 302.02 1635.29 360.61 1670.83 436.884C1709.75 520.428 1748.37 615.673 1701.1 702.209Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1757.81 713.149C1706.01 807.989 1569.83 847.147 1460.65 902.283C1359.06 953.589 1260.36 1004.79 1145.53 1022.39C1010.33 1043.11 846.05 1082.94 750.373 1011.12C654.001 938.772 729.285 799.149 704.92 691.623C682.871 594.32 559.941 506.761 614.858 413.088C670.357 318.42 841.403 328.589 950.915 272.019C1051.9 219.851 1112.43 107.984 1227.83 96.445C1344.96 84.7323 1437.82 157.466 1525.56 214.558C1611.2 270.285 1685.01 335.121 1724.34 419.526C1767.4 511.977 1810.12 617.383 1757.81 713.149Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1818.06 724.766C1760.96 829.318 1610.85 872.488 1490.5 933.278C1378.52 989.845 1269.73 1046.29 1143.13 1065.69C994.115 1088.54 813.029 1132.46 707.57 1053.28C601.348 973.535 684.339 819.608 657.49 701.078C633.192 593.814 497.696 497.296 558.236 394.027C619.417 289.663 807.96 300.868 928.669 238.502C1039.99 180.987 1106.72 57.6629 1233.92 44.9377C1363.03 32.0223 1465.39 112.201 1562.09 175.134C1656.48 236.565 1737.84 308.033 1781.18 401.084C1828.64 502.998 1875.72 619.195 1818.06 724.766Z" stroke="#6E7191"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1883.63 737.41C1820.76 852.531 1655.48 900.075 1522.98 967.011C1399.69 1029.29 1279.9 1091.45 1140.54 1112.82C976.464 1137.98 777.094 1186.35 660.993 1099.17C544.048 1011.36 635.431 841.877 605.876 711.363C579.132 593.257 429.959 486.981 496.618 373.278C563.985 258.365 771.56 270.696 904.467 202.022C1027.03 138.693 1100.5 2.90117 1240.55 -11.1138C1382.7 -25.3393 1495.38 62.9387 1601.85 132.231C1705.77 199.868 1795.34 278.561 1843.04 381.012C1895.29 493.226 1947.12 621.169 1883.63 737.41Z" stroke="#6E7191"/>
</g>
<g filter="url(#filter0_d_2938_27255)">
<circle cx="110.222" cy="998.124" r="16.146" fill="#FF4267"/>
<circle cx="110.222" cy="998.124" r="16.146" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2938_27255)">
<circle cx="1275.4" cy="149.636" r="15.8091" fill="#FF813A"/>
<circle cx="1275.4" cy="149.636" r="15.8091" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2938_27255)">
<circle cx="25.0762" cy="182.978" r="14" fill="#7D42FB"/>
<circle cx="25.0762" cy="182.978" r="14" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2938_27255)">
<circle cx="1245.16" cy="953.16" r="16.3525" fill="#1DE4FF"/>
<circle cx="1245.16" cy="953.16" r="16.3525" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_2938_27255)">
<circle cx="657.619" cy="1098.88" r="16.3525" fill="#2FF2B8"/>
<circle cx="657.619" cy="1098.88" r="16.3525" stroke="white" stroke-width="3"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2938_27255" x="81.5762" y="973.478" width="57.292" height="57.2921" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_27255"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_27255" result="shape"/>
</filter>
<filter id="filter1_d_2938_27255" x="1247.09" y="125.327" width="56.6182" height="56.6183" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_27255"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_27255" result="shape"/>
</filter>
<filter id="filter2_d_2938_27255" x="-1.42383" y="160.478" width="53" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_27255"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_27255" result="shape"/>
</filter>
<filter id="filter3_d_2938_27255" x="1216.31" y="928.308" width="57.7051" height="57.705" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_27255"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_27255" result="shape"/>
</filter>
<filter id="filter4_d_2938_27255" x="628.766" y="1074.02" width="57.7051" height="57.705" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_27255"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_27255" result="shape"/>
</filter>
<clipPath id="clip0_2938_27255">
<rect width="1294" height="1206" fill="white"/>
</clipPath>
</defs>
</svg>
