<?xml version="1.0" encoding="utf-8"?>
<svg width="1632" height="580" viewBox="0 0 1632 580" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="mask0_2992_44421" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1632" height="580">
    <rect width="1632" height="580" fill="#563AFF"/>
  </mask>
  <g mask="url(#mask0_2992_44421)">
    <g opacity="0.6">
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M843.287 697.779C834.577 713.713 811.553 720.257 793.109 729.494C775.947 738.091 759.273 746.671 739.85 749.594C716.981 753.035 689.203 759.684 672.969 747.574C656.616 735.377 669.276 711.917 665.084 693.829C661.295 677.457 640.44 662.692 649.679 646.956C659.015 631.052 687.968 632.817 706.468 623.337C723.529 614.595 733.706 595.797 753.23 593.896C773.046 591.964 788.804 604.225 803.685 613.855C818.21 623.255 830.74 634.182 837.445 648.39C844.791 663.95 852.083 681.69 843.287 697.779Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M904.123 709.512C890.051 735.253 852.968 745.848 823.246 760.792C795.591 774.697 768.722 788.572 737.438 793.319C700.608 798.907 655.863 809.68 629.753 790.148C603.455 770.474 623.897 732.577 617.202 703.371C611.143 676.94 577.6 653.128 592.514 627.704C607.59 602.011 654.205 604.823 684.016 589.489C711.51 575.35 727.945 544.988 759.385 541.887C791.297 538.739 816.639 558.517 840.576 574.045C863.941 589.203 884.089 606.832 894.845 629.761C906.63 654.884 918.326 683.52 904.123 709.512Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M963.051 720.878C943.798 756.116 893.082 770.64 852.435 791.107C814.613 810.159 777.868 829.169 735.094 835.677C684.738 843.346 623.556 858.115 587.881 831.392C551.946 804.476 579.928 752.592 570.802 712.618C562.544 676.443 516.708 643.865 537.124 609.058C557.757 573.882 621.482 577.705 662.253 556.703C699.855 537.335 722.35 495.767 765.334 491.507C808.965 487.182 843.592 514.241 876.304 535.485C908.232 556.22 935.759 580.34 950.447 611.724C966.53 646.1 982.493 685.292 963.051 720.878Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1016.29 731.145C992.35 774.967 929.328 793.037 878.813 818.495C831.81 842.181 786.148 865.827 732.992 873.941C670.418 883.481 594.39 901.864 550.073 868.644C505.435 835.187 540.228 770.67 528.907 720.969C518.662 675.994 461.724 635.499 487.104 592.217C512.756 548.475 591.937 553.212 642.607 527.09C689.332 503.001 717.307 451.311 770.719 446.003C824.937 440.61 867.95 474.249 908.59 500.654C948.254 526.428 982.447 556.41 1000.68 595.429C1020.65 638.168 1040.46 686.893 1016.29 731.145Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1065.71 740.678C1037.42 792.459 962.973 813.822 903.295 843.921C847.766 871.931 793.813 899.869 731.028 909.466C657.107 920.753 567.297 942.483 514.955 903.232C462.237 863.706 503.356 787.455 489.995 728.724C477.905 675.575 410.658 627.729 440.653 576.577C470.962 524.88 564.498 530.468 624.359 499.588C679.561 471.118 712.619 410.026 775.715 403.745C839.758 397.369 890.558 437.112 938.555 468.31C985.4 498.762 1025.78 534.189 1047.31 580.297C1070.89 630.801 1094.28 688.38 1065.71 740.678Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1117.04 750.578C1084.23 810.64 997.91 835.414 928.718 870.332C864.334 902.812 801.779 935.223 728.985 946.351C643.287 959.452 539.16 984.672 478.487 939.161C417.375 893.318 465.058 804.888 449.582 736.777C435.577 675.142 357.624 619.662 392.41 560.337C427.566 500.381 535.999 506.849 605.404 471.034C669.409 438.006 707.751 367.157 780.898 359.864C855.147 352.466 914.033 398.55 969.667 434.726C1023.97 470.04 1070.78 511.117 1095.73 564.586C1123.06 623.152 1150.17 689.923 1117.04 750.578Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1179.78 762.672C1141.45 832.852 1040.63 861.813 959.801 902.598C884.596 940.558 811.528 978.433 726.504 991.444C626.408 1006.76 504.783 1036.23 433.925 983.058C362.555 929.509 418.265 826.194 400.203 746.62C383.856 674.611 292.821 609.8 333.46 540.487C374.531 470.437 501.18 477.984 582.25 436.132C657.013 397.538 701.806 314.761 787.245 306.235C873.968 297.579 942.739 351.42 1007.71 393.677C1071.13 434.926 1125.8 482.914 1154.93 545.382C1186.83 613.802 1218.49 691.81 1179.78 762.672Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1248.22 775.87C1203.86 857.075 1087.22 890.602 993.706 937.811C906.694 981.736 822.158 1025.58 723.791 1040.64C607.986 1058.36 467.274 1092.47 385.307 1030.96C302.748 968.999 367.214 849.437 346.328 757.358C327.428 674.035 222.116 599.045 269.143 518.833C316.67 437.77 463.185 446.492 556.985 398.057C643.484 353.392 695.316 257.601 794.164 247.729C894.495 237.708 974.048 299.999 1049.21 348.895C1122.57 396.622 1185.81 452.149 1219.5 524.433C1256.4 603.602 1293.01 693.866 1248.22 775.87Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1314.76 788.699C1264.54 880.639 1132.51 918.596 1026.67 972.038C928.184 1021.77 832.496 1071.4 721.154 1088.46C590.078 1108.54 430.807 1147.15 338.042 1077.53C244.602 1007.39 317.582 872.034 293.951 767.798C272.568 673.472 153.381 588.585 206.615 497.781C260.417 406.011 426.252 415.875 532.423 361.04C630.331 310.472 689.009 202.029 800.891 190.847C914.452 179.497 1004.49 250.009 1089.55 305.355C1172.59 359.38 1244.16 422.235 1282.28 504.061C1324.04 593.687 1365.47 695.865 1314.76 788.699Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1375.6 800.436C1320.03 902.18 1173.93 944.188 1056.81 1003.34C947.822 1058.38 841.939 1113.31 718.741 1132.18C573.703 1154.41 397.465 1197.15 294.824 1120.1C191.437 1042.48 272.2 892.696 246.062 777.343C222.408 672.958 90.5297 579.024 149.444 478.533C208.983 376.974 392.481 387.884 509.964 327.195C618.304 271.23 683.239 151.22 807.038 138.841C932.697 126.275 1032.32 204.303 1126.44 265.552C1218.31 325.335 1297.5 394.89 1339.68 485.44C1385.88 584.62 1431.71 697.699 1375.6 800.436Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1440.23 812.898C1378.97 925.062 1217.93 971.374 1088.82 1036.59C968.692 1097.27 851.979 1157.82 716.173 1178.64C556.306 1203.15 362.038 1250.27 248.902 1165.33C134.949 1079.78 223.98 914.645 195.177 787.486C169.111 672.415 23.7515 568.871 88.6976 458.084C154.333 346.124 356.6 358.144 486.096 291.239C605.523 229.536 677.108 97.2356 813.569 83.5841C952.079 69.7286 1061.88 155.744 1165.63 223.257C1266.88 289.16 1354.17 365.831 1400.66 465.655C1451.58 574.987 1502.09 699.643 1440.23 812.898Z" stroke="#265bc3"/>
      <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1510.58 826.463C1443.12 949.965 1265.82 1000.97 1123.67 1072.78C991.408 1139.59 862.902 1206.27 713.391 1229.2C537.374 1256.19 323.491 1308.08 198.939 1214.56C73.4815 1120.36 171.516 938.535 139.81 798.52C111.119 671.817 -48.9126 557.805 22.5986 435.825C94.8691 312.547 317.554 325.776 460.137 252.103C591.62 184.164 670.442 38.4877 820.684 23.4525C973.181 8.19152 1094.06 102.896 1208.28 177.232C1319.77 249.792 1415.86 334.214 1467.04 444.122C1523.08 564.504 1578.69 701.761 1510.58 826.463Z" stroke="#265bc3"/>
    </g>
  </g>
  <g filter="url(#filter0_d_2992_44421)">
    <circle cx="308.419" cy="491.844" r="14.6218" fill="#7D42FB"/>
    <circle cx="308.419" cy="491.844" r="13.1218" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter1_d_2992_44421)">
    <circle cx="340.123" cy="107.379" r="17.0835" fill="#FF4267"/>
    <circle cx="340.123" cy="107.379" r="15.5835" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter2_d_2992_44421)">
    <circle cx="1327" cy="81.6435" r="14.0405" fill="#FF813A"/>
    <circle cx="1327" cy="81.6435" r="12.5405" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter3_d_2992_44421)">
    <circle cx="1378.44" cy="447.817" r="17.5145" fill="#1DE4FF"/>
    <circle cx="1378.44" cy="447.817" r="16.0145" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter4_d_2992_44421)">
    <circle cx="1441.19" cy="269.638" r="17.2964" fill="#FFCD42"/>
    <circle cx="1441.19" cy="269.638" r="15.7964" stroke="white" stroke-width="3"/>
  </g>
  <g filter="url(#filter5_d_2992_44421)">
    <circle cx="174.874" cy="341.382" r="15.8114" fill="#2FF2B8"/>
    <circle cx="174.874" cy="341.382" r="14.3114" stroke="white" stroke-width="3"/>
  </g>
  <defs>
    <filter id="filter0_d_2992_44421" x="286.797" y="474.222" width="43.2422" height="43.2437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="3.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
    <filter id="filter1_d_2992_44421" x="312.039" y="83.2953" width="56.1641" height="56.1671" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
    <filter id="filter2_d_2992_44421" x="1301.96" y="60.603" width="50.0781" height="50.081" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
    <filter id="filter3_d_2992_44421" x="1349.93" y="423.302" width="57.0312" height="57.0289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
    <filter id="filter4_d_2992_44421" x="1412.89" y="245.341" width="56.5938" height="56.5928" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
    <filter id="filter5_d_2992_44421" x="148.062" y="318.571" width="53.625" height="53.6227" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="5.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2992_44421"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2992_44421" result="shape"/>
    </filter>
  </defs>
</svg>