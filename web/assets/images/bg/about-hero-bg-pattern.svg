<svg width="1542" height="860" viewBox="0 0 1542 860" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2949_27016" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1542" height="860">
<rect width="1542" height="860" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2949_27016)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M841.161 715.459C832.451 731.393 809.427 737.937 790.983 747.174C773.821 755.771 757.147 764.351 737.724 767.274C714.855 770.715 687.077 777.364 670.843 765.254C654.49 753.057 667.15 729.597 662.958 711.509C659.169 695.137 638.314 680.372 647.553 664.636C656.889 648.732 685.842 650.496 704.342 641.017C721.403 632.275 731.58 613.477 751.104 611.576C770.92 609.644 786.678 621.905 801.559 631.535C816.084 640.935 828.614 651.862 835.319 666.07C842.665 681.63 849.957 699.37 841.161 715.459Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M901.997 727.192C887.925 752.933 850.842 763.528 821.12 778.471C793.465 792.376 766.596 806.252 735.312 810.998C698.482 816.587 653.737 827.36 627.627 807.828C601.329 788.154 621.771 750.257 615.076 721.051C609.017 694.62 575.474 670.808 590.388 645.384C605.464 619.691 652.079 622.503 681.891 607.169C709.384 593.03 725.819 562.668 757.259 559.566C789.171 556.419 814.513 576.197 838.45 591.725C861.815 606.883 881.963 624.512 892.719 647.441C904.504 672.564 916.2 701.2 901.997 727.192Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M960.929 738.557C941.675 773.796 890.96 788.32 850.313 808.787C812.491 827.839 775.746 846.849 732.972 853.356C682.616 861.025 621.434 875.795 585.759 849.072C549.824 822.155 577.806 770.272 568.68 730.298C560.422 694.123 514.586 661.545 535.002 626.738C555.635 591.562 619.36 595.385 660.131 574.383C697.732 555.015 720.228 513.447 763.212 509.187C806.843 504.862 841.47 531.921 874.182 553.164C906.11 573.9 933.637 598.02 948.324 629.404C964.407 663.78 980.371 702.972 960.929 738.557Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1014.16 748.825C990.224 792.647 927.202 810.717 876.687 836.175C829.684 859.861 784.022 883.507 730.866 891.621C668.292 901.161 592.264 919.544 547.947 886.324C503.309 852.867 538.102 788.35 526.781 738.649C516.536 693.673 459.598 653.179 484.978 609.897C510.63 566.155 589.811 570.892 640.481 544.77C687.207 520.681 715.181 468.991 768.593 463.683C822.811 458.29 865.824 491.929 906.464 518.334C946.128 544.108 980.321 574.09 998.554 613.109C1018.52 655.848 1038.34 704.573 1014.16 748.825Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1063.59 758.358C1035.3 810.139 960.851 831.502 901.173 861.601C845.644 889.611 791.691 917.549 728.906 927.146C654.985 938.433 565.175 960.163 512.833 920.912C460.115 881.386 501.234 805.135 487.872 746.404C475.783 693.255 408.536 645.409 438.531 594.257C468.84 542.56 562.376 548.147 622.237 517.268C677.439 488.798 710.497 427.706 773.592 421.425C837.636 415.049 888.436 454.792 936.433 485.99C983.278 516.442 1023.66 551.869 1045.19 597.977C1068.76 648.481 1092.16 706.06 1063.59 758.358Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1114.92 768.258C1082.11 828.32 995.791 853.094 926.6 888.012C862.216 920.492 799.661 952.903 726.867 964.031C641.169 977.132 537.042 1002.35 476.369 956.841C415.257 910.998 462.94 822.568 447.464 754.457C433.459 692.822 355.506 637.341 390.292 578.017C425.448 518.061 533.881 524.529 603.286 488.714C667.291 455.686 705.633 384.837 778.779 377.544C853.029 370.146 911.915 416.23 967.549 452.406C1021.85 487.72 1068.66 528.796 1093.61 582.266C1120.94 640.832 1148.05 707.603 1114.92 768.258Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1177.66 780.352C1139.33 850.532 1038.5 879.493 957.679 920.278C882.474 958.238 809.406 996.113 724.382 1009.12C624.286 1024.44 502.661 1053.91 431.803 1000.74C360.433 947.189 416.143 843.874 398.081 764.3C381.734 692.291 290.698 627.48 331.338 558.166C372.409 488.117 499.058 495.664 580.128 453.812C654.891 415.218 699.683 332.441 785.123 323.915C871.846 315.259 940.617 369.1 1005.59 411.356C1069.01 452.605 1123.68 500.594 1152.81 563.062C1184.71 631.482 1216.37 709.49 1177.66 780.352Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1246.1 793.55C1201.74 874.755 1085.1 908.282 991.584 955.491C904.572 999.416 820.036 1043.26 721.669 1058.32C605.864 1076.04 465.152 1110.15 383.185 1048.64C300.626 986.679 365.092 867.117 344.206 775.038C325.306 691.715 219.994 616.725 267.021 536.513C314.548 455.45 461.063 464.172 554.863 415.737C641.361 371.072 693.194 275.281 792.042 265.409C892.373 255.388 971.926 317.679 1047.09 366.575C1120.45 414.302 1183.69 469.829 1217.37 542.113C1254.28 621.282 1290.89 711.546 1246.1 793.55Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1312.64 806.379C1262.42 898.319 1130.39 936.276 1024.55 989.718C926.062 1039.45 830.374 1089.08 719.032 1106.14C587.956 1126.22 428.685 1164.83 335.92 1095.21C242.48 1025.07 315.46 889.714 291.829 785.478C270.446 691.152 151.259 606.265 204.492 515.461C258.295 423.691 424.13 433.555 530.301 378.72C628.209 328.152 686.887 219.709 798.769 208.527C912.33 197.177 1002.37 267.689 1087.43 323.035C1170.46 377.06 1242.03 439.915 1280.16 521.741C1321.92 611.366 1363.34 713.545 1312.64 806.379Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1373.47 818.116C1317.9 919.859 1171.81 961.868 1054.68 1021.02C945.7 1076.06 839.817 1130.99 716.619 1149.86C571.581 1172.09 395.343 1214.83 292.702 1137.78C189.315 1060.16 270.078 910.376 243.94 795.023C220.286 690.638 88.4076 596.704 147.322 496.213C206.861 394.654 390.359 405.563 507.841 344.875C616.182 288.91 681.117 168.9 804.916 156.521C930.575 143.955 1030.19 221.983 1124.32 283.232C1216.19 343.015 1295.38 412.57 1337.56 503.12C1383.76 602.3 1429.59 715.379 1373.47 818.116Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1438.11 830.578C1376.85 942.742 1215.81 989.054 1086.71 1054.27C966.574 1114.95 849.861 1175.5 714.055 1196.32C554.188 1220.83 359.919 1267.95 246.784 1183.01C132.83 1097.46 221.862 932.325 193.059 805.166C166.992 690.095 21.6334 586.551 86.5795 475.764C152.215 363.804 354.482 375.824 483.978 308.919C603.405 247.216 674.989 114.916 811.451 101.264C949.961 87.4085 1059.76 173.424 1163.51 240.937C1264.77 306.84 1352.05 383.511 1398.54 483.335C1449.46 592.667 1499.97 717.323 1438.11 830.578Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1508.45 844.143C1441 967.644 1263.7 1018.65 1121.55 1090.46C989.286 1157.27 860.78 1223.95 711.269 1246.88C535.252 1273.87 321.369 1325.76 196.817 1232.24C71.3595 1138.04 169.394 956.215 137.688 816.2C108.997 689.497 -51.0347 575.485 20.4766 453.505C92.747 330.227 315.432 343.456 458.014 269.783C589.498 201.844 668.32 56.1676 818.562 41.1324C971.058 25.8714 1091.94 120.575 1206.16 194.912C1317.64 267.472 1413.73 351.894 1464.91 461.802C1520.96 582.184 1576.57 719.441 1508.45 844.143Z" stroke="white"/>
</g>
</g>
<g filter="url(#filter0_d_2949_27016)">
<circle cx="227.039" cy="152.322" r="14.0069" fill="#7D42FB"/>
<circle cx="227.039" cy="152.322" r="14.0069" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2949_27016)">
<circle cx="1068.07" cy="39.5344" r="14.9092" fill="#FF4267"/>
<circle cx="1068.07" cy="39.5344" r="14.9092" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2949_27016)">
<circle cx="1469.52" cy="262.626" r="13.3963" fill="#2FF2B8"/>
<circle cx="1469.52" cy="262.626" r="13.3963" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_2949_27016)">
<circle cx="1439.33" cy="597.208" r="16.793" fill="#FFCD42"/>
<circle cx="1439.33" cy="597.208" r="16.793" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_2949_27016)">
<circle cx="175.806" cy="750.926" r="14.7936" fill="#2FF2B8"/>
<circle cx="175.806" cy="750.926" r="14.7936" stroke="white" stroke-width="3"/>
</g>
<defs>
<filter id="filter0_d_2949_27016" x="200.532" y="129.815" width="53.0137" height="53.0138" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2949_27016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2949_27016" result="shape"/>
</filter>
<filter id="filter1_d_2949_27016" x="1040.66" y="16.1252" width="54.8184" height="54.8184" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2949_27016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2949_27016" result="shape"/>
</filter>
<filter id="filter2_d_2949_27016" x="1443.62" y="240.73" width="51.793" height="51.7926" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2949_27016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2949_27016" result="shape"/>
</filter>
<filter id="filter3_d_2949_27016" x="1410.03" y="571.915" width="58.5859" height="58.586" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2949_27016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2949_27016" result="shape"/>
</filter>
<filter id="filter4_d_2949_27016" x="148.513" y="727.632" width="54.5869" height="54.5873" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2949_27016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2949_27016" result="shape"/>
</filter>
</defs>
</svg>
