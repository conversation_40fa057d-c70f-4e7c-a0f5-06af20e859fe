<svg width="1244" height="536" viewBox="0 0 1244 536" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2938_28207" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1244" height="536">
<rect width="1244" height="536" fill="#563AFF"/>
</mask>
<g mask="url(#mask0_2938_28207)">
<g opacity="0.6">
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M960.15 768.937C940.897 804.176 890.181 818.699 849.534 839.166C811.713 858.219 774.968 877.228 732.193 883.736C681.837 891.405 620.655 906.174 584.98 879.452C549.045 852.535 577.027 800.652 567.901 760.677C559.643 724.502 513.808 691.924 534.223 657.118C554.857 621.941 618.581 625.764 659.352 604.763C696.954 585.395 719.449 543.827 762.434 539.566C806.065 535.242 840.691 562.3 873.403 583.544C905.331 604.28 932.859 628.399 947.546 659.783C963.629 694.16 979.593 733.351 960.15 768.937Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1013.38 779.205C989.442 823.026 926.42 841.096 875.905 866.554C828.902 890.24 783.24 913.886 730.084 922C667.51 931.54 591.482 949.923 547.165 916.703C502.527 883.246 537.32 818.729 525.999 769.029C515.754 724.053 458.816 683.558 484.196 640.276C509.848 596.534 589.029 601.271 639.699 575.149C686.425 551.06 714.399 499.37 767.811 494.062C822.029 488.669 865.042 522.308 905.682 548.713C945.346 574.487 979.539 604.469 997.773 643.488C1017.74 686.227 1037.56 734.953 1013.38 779.205Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1062.81 788.737C1034.52 840.518 960.069 861.881 900.391 891.98C844.862 919.99 790.909 947.928 728.124 957.525C654.203 968.812 564.393 990.542 512.051 951.291C459.333 911.765 500.452 835.514 487.09 776.783C475.001 723.634 407.754 675.788 437.749 624.636C468.058 572.939 561.594 578.527 621.455 547.647C676.657 519.177 709.715 458.085 772.811 451.804C836.854 445.429 887.654 485.171 935.651 516.369C982.496 546.821 1022.88 582.248 1044.41 628.356C1067.98 678.86 1091.38 736.439 1062.81 788.737Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1114.14 798.637C1081.33 858.699 995.012 883.473 925.821 918.391C861.436 950.871 798.881 983.282 726.088 994.41C640.39 1007.51 536.262 1032.73 475.589 987.22C414.477 941.377 462.16 852.946 446.684 784.836C432.68 723.201 354.726 667.72 389.512 608.396C424.669 548.44 533.101 554.908 602.506 519.093C666.511 486.065 704.854 415.216 778 407.923C852.249 400.525 911.135 446.609 966.77 482.785C1021.07 518.099 1067.89 559.175 1092.83 612.645C1120.16 671.211 1147.27 737.982 1114.14 798.637Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1176.88 810.732C1138.55 880.911 1037.72 909.872 956.898 950.658C881.693 988.617 808.625 1026.49 723.601 1039.5C623.505 1054.82 501.88 1084.29 431.022 1031.12C359.652 977.568 415.362 874.253 397.3 794.679C380.953 722.671 289.917 657.86 330.557 588.546C371.628 518.496 498.277 526.043 579.347 484.191C654.11 445.597 698.902 362.82 784.342 354.294C871.065 345.638 939.836 399.48 1004.81 441.736C1068.23 482.985 1122.9 530.973 1152.03 593.441C1183.93 661.861 1215.58 739.869 1176.88 810.732Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1245.32 823.929C1200.96 905.134 1084.32 938.662 990.803 985.87C903.791 1029.8 819.255 1073.64 720.888 1088.7C605.083 1106.42 464.371 1140.53 382.404 1079.02C299.845 1017.06 364.311 897.496 343.425 805.417C324.525 722.094 219.213 647.104 266.24 566.892C313.767 485.83 460.282 494.551 554.082 446.117C640.58 401.451 692.413 305.66 791.261 295.788C891.592 285.767 971.145 348.058 1046.31 396.954C1119.67 444.681 1182.91 500.208 1216.59 572.492C1253.5 651.661 1290.11 741.925 1245.32 823.929Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1311.86 836.759C1261.64 928.698 1129.61 966.655 1023.77 1020.1C925.28 1069.83 829.592 1119.46 718.25 1136.52C587.174 1156.6 427.903 1195.21 335.138 1125.59C241.698 1055.44 314.678 920.093 291.047 815.857C269.664 721.531 150.477 636.645 203.71 545.84C257.513 454.07 423.348 463.934 529.519 409.099C627.427 358.531 686.105 250.088 797.987 238.906C911.548 227.556 1001.58 298.068 1086.65 353.414C1169.68 407.439 1241.25 470.295 1279.38 552.12C1321.14 641.746 1362.56 743.924 1311.86 836.759Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1372.69 848.495C1317.13 950.238 1171.03 992.247 1053.9 1051.4C944.92 1106.44 839.037 1161.37 715.84 1180.24C570.801 1202.47 394.564 1245.21 291.922 1168.16C188.536 1090.54 269.299 940.755 243.16 825.402C219.507 721.017 87.6281 627.083 146.542 526.592C206.082 425.033 389.579 435.942 507.062 375.254C615.402 319.289 680.337 199.279 804.137 186.9C929.795 174.334 1029.41 252.362 1123.54 313.611C1215.41 373.394 1294.6 442.949 1336.78 533.499C1382.98 632.679 1428.81 745.758 1372.69 848.495Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1437.33 860.958C1376.07 973.121 1215.03 1019.43 1085.92 1084.65C965.793 1145.33 849.081 1205.88 713.275 1226.7C553.407 1251.21 359.139 1298.33 246.004 1213.39C132.05 1127.84 221.082 962.704 192.279 835.545C166.212 720.474 20.8528 616.93 85.799 506.144C151.434 394.183 353.702 406.203 483.198 339.298C602.625 277.596 674.209 145.295 810.671 131.643C949.18 117.788 1058.98 203.803 1162.73 271.317C1263.99 337.22 1351.27 413.89 1397.76 513.714C1448.68 623.047 1499.19 747.703 1437.33 860.958Z" stroke="white"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M1507.67 874.522C1440.22 998.024 1262.92 1049.03 1120.77 1120.84C988.505 1187.65 860 1254.33 710.488 1277.26C534.471 1304.25 320.589 1356.14 196.036 1262.62C70.5789 1168.42 168.613 986.594 136.907 846.58C108.216 719.876 -51.8152 605.864 19.6961 483.885C91.9665 360.607 314.651 373.835 457.234 300.162C588.717 232.223 667.54 86.5468 817.782 71.5116C970.278 56.2506 1091.16 150.955 1205.38 225.291C1316.86 297.851 1412.95 382.273 1464.13 492.182C1520.18 612.563 1575.79 749.82 1507.67 874.522Z" stroke="white"/>
</g>
<g filter="url(#filter0_d_2938_28207)">
<circle cx="766.614" cy="140.633" r="11.6288" fill="#FFCD42"/>
<circle cx="766.614" cy="140.633" r="11.6288" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_2938_28207)">
<circle cx="128.949" cy="461.539" r="12.1839" fill="#FF4267"/>
<circle cx="128.949" cy="461.539" r="12.1839" stroke="white" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_2938_28207)">
<circle cx="531.308" cy="458.782" r="14.8605" fill="#2FF2B8"/>
<circle cx="531.308" cy="458.782" r="14.8605" stroke="white" stroke-width="3"/>
</g>
</g>
<defs>
<filter id="filter0_d_2938_28207" x="742.485" y="120.504" width="48.2576" height="48.2578" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_28207"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_28207" result="shape"/>
</filter>
<filter id="filter1_d_2938_28207" x="103.96" y="440.661" width="49.979" height="49.9788" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.11111"/>
<feGaussianBlur stdDeviation="5.65278"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_28207"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_28207" result="shape"/>
</filter>
<filter id="filter2_d_2938_28207" x="502.726" y="434.644" width="57.1654" height="57.1656" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.44444"/>
<feGaussianBlur stdDeviation="6.11111"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2938_28207"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2938_28207" result="shape"/>
</filter>
</defs>
</svg>
