let blobs = [];

function reInit(recorderId) {
    $('#' + recorderId + ' .recordButton').removeClass('disabled').removeClass('d-none');
    $('#' + recorderId + ' .stopButton').html('<i class="fas fa-stop"></i>').addClass('d-none');
}

function makeWaveform(recorderId) {
    var analyser = Fr.voice.recorder.analyser;

    var bufferLength = analyser.frequencyBinCount;
    var dataArray = new Uint8Array(bufferLength);

    var WIDTH = 300,
        HEIGHT = 50;

    var canvasCtx = $('#' + recorderId + ' #level')[0].getContext('2d');
    canvasCtx.clearRect(0, 0, WIDTH, HEIGHT);

    function draw() {
        var drawVisual = requestAnimationFrame(draw);

        analyser.getByteTimeDomainData(dataArray);

        canvasCtx.fillStyle = 'rgb(255, 255, 255)';
        canvasCtx.fillRect(0, 0, WIDTH, HEIGHT);
        canvasCtx.lineWidth = 1;
        canvasCtx.strokeStyle = 'rgb(0, 0, 0)';

        canvasCtx.beginPath();

        var sliceWidth = WIDTH * 1.0 / bufferLength;
        var x = 0;
        for(var i = 0; i < bufferLength; i++) {
            var v = dataArray[i] / 128.0;
            var y = v * HEIGHT/2;

            if (i === 0) {
                canvasCtx.moveTo(x, y);
            } else {
                canvasCtx.lineTo(x, y);
            }

            x += sliceWidth;
        }
        canvasCtx.lineTo(WIDTH, HEIGHT/2);
        canvasCtx.stroke();
    };
    draw();
}

$(document).ready(function(){
    $(document).on("click", ".recordButton:not(.disabled)", function() {
        let recorderId = $(this).closest('.recorder').attr('id');
        reInit(recorderId);
        Fr.voice.record(recorderId, function() {
            $('#' + recorderId + ' .recordButton').addClass('disabled d-none');
            $('#' + recorderId + ' .stopButton').removeClass('disabled d-none');
            makeWaveform(recorderId);
        });
    });

    $(document).on("click", ".stopButton:not(.disabled)", function() {
        let recorderId = $(this).closest('.recorder').attr('id');

        Fr.voice.pause();
        $(this).addClass('disabled').html('<i class="fas fa-circle-notch fa-spin"></i>');

        $('#' + recorderId + ' #level').remove();
        $('#' + recorderId + ' #canvas-container').html('<canvas id="level" height="40" width="300"></canvas>');

        Fr.voice.export(function(blob) {
            var url = URL.createObjectURL(blob);
            blobs[recorderId] = blob;

            TranscriptFile(recorderId);

            //add new file
            /*var container = '<div class="file" id="' + recorderId + '-file">';
            container += '<audio controls id="audio" src="' + url + '"></audio>';
            container += '<div class="actions">';
            container += '<a class="btn btn-primary btn-save mr-2" onclick="TranscriptFile(\'' + recorderId + '\');">' + __('Transcrire') + '</a>';
            container += '<a class="btn btn-clean btn-icon btn-light-danger" onclick="DeleteFile(\'' + recorderId + '\');"><i class="fas fa-trash"></i></a>';
            container += '</div>';
            container += '</div>';

            $('#' + recorderId + ' .recorder-container #result').html(container);
            $('#' + recorderId + ' .stopButton').addClass('d-none disabled');
            $('#' + recorderId + ' .recordButton').removeClass('d-none disabled');*/
        }, "blob");
    });

});


function TranscriptFile(recorderId) {
    let blob = blobs[recorderId];

    let formData = new FormData();
    formData.append('file', blob);
    formData.append('recorderId', recorderId);
    formData.append('CSRFGuard_token', CSRFGuard_token);

    $('#' + recorderId + ' .actions .btn-save').attr('disabled', true).addClass('disabled spinner spinner-left');
    $.ajax({
        url: baseDir + '/ajax/audio_recorder/transcript/',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(data) {
            $('#' + recorderId + ' .stopButton').addClass('d-none disabled');
            $('#' + recorderId + ' .recordButton').removeClass('d-none disabled');
            if (data.status) {
                $('#' + recorderId + ' #result').html('');
                let target = $('#' + recorderId).data('target');
                if ($('#' + target).hasClass('ckeditor') || $('#' + target).hasClass('ckeditorsmall')) {
                    if (typeof editors[target] !== 'undefined') {
                        let editor = editors[target];
                        /*let value = editor.getData();
                        value += '<p>' + data.content + '</p>';
                        editor.setData(value);*/

                        editor.model.change(writer => {
                            writer.insertText(data.content, editor.model.document.selection.getFirstPosition());
                        });
                    }
                } else if ($('#' + target).hasClass('tinymce') || $('#' + target).hasClass('tinymce-small')) {
                    if (typeof editors[target] !== 'undefined') {
                        let editor = tinyMCE.get(target);
                        let cursorPosition = editor.selection.getBookmark(2).start;
                        let sum = cursorPosition.reduce(function(previousValue, currentValue) {
                            return currentValue + previousValue;
                        });
                        if (!sum) {
                            editor.setContent(editor.getContent() + '<p>' + data.content + '</p>');
                        } else {
                            editor.execCommand('mceInsertContent', false, '<p>' + data.content + '</p>');
                        }
                    }
                } else {
                    let value = $('#' + target).val();
                    if (value) {
                        value += "\n";
                    }
                    value += data.content;
                    $('#' + target).val(value);
                    if ($('#' + target).hasClass('autosize')) {
                        autosize.update($('#' + target));
                    }
                }
                ShowToast('success', __('Transcription effectuée'));
                if ($('#' + recorderId).hasClass('autoclose')) {
                    $('#' + recorderId).addClass('d-none');
                }
            } else {
                $('#' + recorderId + ' #result').html('<div class="alert alert-custom alert-light-danger">' + data.message + '</div>');
            }
        }
    });
}

function DeleteFile(recorderId) {
    $('#' + recorderId + ' #result').html('');
}

onGotDevices = function(devInfos) {
    var index, info, name, options, _i, _len;
    options = '<option value="no-input">(No input)</option>';
    index = 0;
    for (_i = 0, _len = devInfos.length; _i < _len; _i++) {
        info = devInfos[_i];
        if (info.kind !== 'audioinput') {
            continue;
        }
        name = info.label || ("Audio in " + (++index));
        options += '<option value="'+info.deviceId+'"';
        if (_i == 0) {
            options += ' selected';
        }
        options += '>' + name + '</option>';
    }
    $('.recorder').each(function() {
        $(this).find('#audio-in-select').html(options);
    });
};


$(document).ready(function() {
    if ((navigator.mediaDevices != null) && (navigator.mediaDevices.enumerateDevices != null)) {
        navigator.mediaDevices.enumerateDevices().then(onGotDevices)["catch"](function(err) {
            return onError("Could not enumerate audio devices: " + err);
        });
    } else {
        $('.recorder').each(function() {
            $(this).find('#audio-in-select').html('<option value="no-input" selected>Aucune source</option><option value="default-audio-input">Entrée audio par défaut</option>');
        });
    }
});
