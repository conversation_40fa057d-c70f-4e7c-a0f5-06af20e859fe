let cropper;

$(document).ready(function () {
    initDropZones();
    initDropZoneRotate();

    $(document).on('click', '.btn-display-camera-container', function() {
        let cameraContainer = $(this).closest('.form-group').find('.cameraContainer');
        $(cameraContainer).removeClass('d-none');

        let dropzoneContainer = $(this).closest('.dropzone');
        $(dropzoneContainer).addClass('d-none');

        let target = $(cameraContainer).data('target');
        initCameraContainer(target);
    });
});

function initDropZones() {
    if ($('.dropzone').length > 0) {
        $('.dropzone').each(function() {
            if ($(this).hasClass('d-none')) {
                return true; //continue
            }

            var dropZoneId = $(this).prop('id');
            initDropZone(dropZoneId);
        });
    }
}

function initDropZone(dropZoneId) {
    var id = '#' + dropZoneId;

    var options = {
        url: baseDir + '/lib/upload/',
        parallelUploads: 1,
        maxFilesize: 50,
        maxFiles: 1,
        timeout: 120000,
    };
    if ($(id).hasClass('dropzone-big')) {
        options.addRemoveLinks = true;
        options.dictCancelUpload = __('Annuler');
        options.dictRemoveFile = '<i class="fas fa-trash"></i>' + __('Supprimer');
        options.thumbnailWidth = 300;
        options.thumbnailHeight = 300;

        if ($(id).hasClass('dropzone-img-square')) {
            options.thumbnailWidth = 120;
            options.thumbnailHeight = 120;
        }
    }
    if ($(id).data('nb-files')) {
        options.maxFiles = $(id).data('nb-files');
    }
    if ($(id).data('accepted-files')) {
        //image/*,application/pdf,.psd
        options.acceptedFiles = $(id).data('accepted-files');
    }

    let needsCrop = false;
    if ($(id).data('crop')) {
        needsCrop = true;
    }

    // set the preview element template
    if ($(id + " .dropzone-item").length) {
        var previewNode = $(id + " .dropzone-item");
        previewNode.id = "";
        var previewTemplate = previewNode.parent('.dropzone-items').html();
        previewNode.remove();

        options.previewTemplate = previewTemplate;
        options.previewsContainer = id + " .dropzone-items";
    }

    if ($('.dropzone-select').length) {
        options.clickable = '.dropzone-select';
    }

    var dropzone = new Dropzone(id, options);

    dropzone.on("addedfile", function(file) {
        $(document).find( id + ' .dropzone-item').css('display', '');
    });

    // Update the total progress bar
    dropzone.on("totaluploadprogress", function(progress) {
        $( id + " .progress-bar").css('width', progress + "%");
    });

    dropzone.on("sending", function(file) {
        // Show the total progress bar when upload starts
        $( id + " .progress-bar").css('opacity', "1");
    });

    // Hide the total progress bar when nothing's uploading anymore
    dropzone.on("complete", function(file) {
        var thisProgressBar = id + " .dz-complete";
        setTimeout(function(){
            $( thisProgressBar + " .progress-bar, " + thisProgressBar + " .progress").css('opacity', '0');
        }, 300);

        if (file.status == 'success') {
            var fileName = file.name;
            if (typeof file.xhr !== 'undefined' && typeof file.xhr.response !== 'undefined') {
                var response = JSON.parse(file.xhr.response);
                if (response && typeof response.files[0] != 'undefined') {
                    fileName = response.files[0].name;
                }
            }

            if (needsCrop && file.type.includes('image/')) {
                //crop image
                initCropImage(dropzone, dropZoneId, file);
            } else {
                $(file.previewElement).append('<input type="hidden" name="' + dropZoneId + '[]" value="' + fileName + '">');
            }
        }
    });
}

function displayDropZone(id) {
    $('#preview-' + id + ' .btn').tooltip('dispose');
    $('#preview-' + id).remove();
    $('#' + id).removeClass('d-none');
    initDropZone(id);
}

function initCropImage(dropzone, dropZoneId, file) {
    let fileName = file.name;
    let imageUrl = cdnDir + 'lib/upload/files/' + file.name;
    if (typeof file.xhr !== 'undefined' && typeof file.xhr.response != 'undefined') {
        var response = JSON.parse(file.xhr.response);
        if (response && typeof response.files[0] != 'undefined') {
            fileName = response.files[0].name;
            imageUrl = response.files[0].url;
        }
    }

    let boundaryWidth = window.innerWidth - 120;
    let boundaryHeight = window.innerHeight - 250;
    if ($('.mobile-nav').is(':visible')) {
        boundaryHeight = boundaryHeight - 150;
    }

    let contentType = 'image/png';
    let extension = imageUrl.split('.').pop();
    if (extension == 'jpg' || extension == 'jpeg') {
        contentType = 'image/jpeg';
    }

    if (typeof cropper !== 'undefined') {
        cropper.destroy();
    }

    $('#ModalCropper #cropper-image').attr('src', imageUrl);
    $('#ModalCropper #btnCrop').attr('disabled', true);
    $('#ModalCropper #style').html('');
    $('#cropper-image').css('max-height', (parseInt(boundaryHeight) + 100) + 'px');

    var image = document.getElementById('cropper-image');
    var opts = {
        /*aspectRatio: requiredWidth/requiredHeight,*/
        viewMode: 0,
        dragMode: 'move',
        guides: false,
        center: false,
        restore: false,
        cropBoxMovable: true,
        cropBoxResizable: true,
        toggleDragModeOnDblclick: true,
        /*minCropBoxWidth: viewPortWidth,
        minCropBoxHeight: viewPortHeight,*/
        ready() {
            $('#ModalCropper #btnCrop').attr('disabled', false);
        },
    };

    $('#ModalCropper').off('shown.bs.modal');
    $('#ModalCropper').on('shown.bs.modal', function() {
        cropper = new Cropper(image, opts);
        /*var cropBoxData = {
            left: boundaryWidth / 2 - viewPortWidth / 2,
            top: boundaryHeight / 2 - viewPortHeight / 2,
            width: viewPortWidth,
            height: viewPortHeight,
        };
        cropper.setCropBoxData(cropBoxData);*/
    });
    $('#ModalCropper').modal({backdrop: 'static', keyboard: false}, 'show');

    $('#ModalCropper #btnCancel').off('click');
    $('#ModalCropper #btnCancel').on('click', function() {
        //dropzone.removeFile(file);

        $(file.previewElement).append('<input type="hidden" name="' + dropZoneId + '[]" value="' + fileName + '">');
        $(file.previewElement).find('.dz-image img').attr('src', imageUrl);
        $('#ModalCropper').modal('hide');
    });

    $('#ModalCropper #btnCrop').off('click');
    $('#ModalCropper #btnCrop').on('click', function() {
        $('#ModalCropper #btnCrop').attr('disabled', true).addClass('spinner spinner-left');

        var croppedCanvas = cropper.getCroppedCanvas({
            /*width: requiredWidth,
            height: requiredHeight,*/
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high',
            fillColor: 'transparent'
        });

        var canvasData = cropper.getCanvasData();
        var canvasWidth = canvasData.width;
        var canvasHeight = canvasData.height;

        croppedCanvas.toBlob((blob) => {
            const formData = new FormData();
            formData.append('img', blob, 'img');
            formData.append('CSRFGuard_token', CSRFGuard_token);
            formData.append('originalImg', imageUrl);
            $.ajax({
                url: baseDir + '/ajax/image/crop/',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success(data) {
                    $('#ModalCropper #btnCrop').attr('disabled', false).removeClass('spinner spinner-left');
                    if (data.status) {
                        $(file.previewElement).append('<input type="hidden" name="' + dropZoneId + '[]" value="' + fileName + '">');
                        $(file.previewElement).find('.dz-image img').attr('src', imageUrl);
                        $('#ModalCropper').modal('hide');
                    } else {
                        ShowToast('error', data.message);
                    }
                },
                error() {
                    $('#ModalCropper #btnCrop').attr('disabled', false).removeClass('spinner spinner-left');
                    ShowToast('error', 'Upload error');
                },
            });
        }, contentType);
    });
}

$.fn.imgRotate = function(direction) {
    let container = $(this).closest('.dz-preview');
    let fileName = container.find('input[type="hidden"]').val();
    KTApp.block('.dropzone', {});
    $.post(
        baseDir + '/ajax/image/rotate/' + direction + '/',
        {fileName: fileName, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            KTApp.unblock('.dropzone');
            if (data.status) {
                container.find('.dz-image img').attr('src', data.img64);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function initDropZoneRotate() {
    $(document).on('click', '.rotate-left', function() {
        $(this).imgRotate('left');
    });
    $(document).on('click', '.rotate-right', function() {
        $(this).imgRotate('right');
    });
}
