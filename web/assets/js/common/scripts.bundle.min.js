"use strict";var KTApp=function(){var e={},t=function(e){var t=e.data("theme")?"tooltip-"+e.data("theme"):"",a="auto"==e.data("width")?"tooltop-auto-width":"",n=e.data("trigger")?e.data("trigger"):"hover";$(e).tooltip({trigger:n,template:'<div class="tooltip '+t+" "+a+'" role="tooltip">                <div class="arrow"></div>                <div class="tooltip-inner"></div>            </div>'})},a=function(){$('[data-toggle="tooltip"]').each(function(){t($(this))})},n=function(e){var t=e.data("skin")?"popover-"+e.data("skin"):"",a=e.data("trigger")?e.data("trigger"):"hover";e.popover({trigger:a,template:'            <div class="popover '+t+'" role="tooltip">                <div class="arrow"></div>                <h3 class="popover-header"></h3>                <div class="popover-body"></div>            </div>'})},i=function(){$('[data-toggle="popover"]').each(function(){n($(this))})},o=function(){$(".custom-file-input").on("change",function(){var e=$(this).val();$(this).next(".custom-file-label").addClass("selected").html(e)})},r=function(){$('[data-scroll="true"]').each(function(){var e=$(this);KTUtil.scrollInit(this,{mobileNativeScroll:!0,handleWindowResize:!0,rememberPosition:"true"==e.data("remember-position")?!0:!1})})},l=function(){$("body").on("click","[data-close=alert]",function(){$(this).closest(".alert").hide()})},d=function(e,t){var e=$(e);new KTCard(e[0],t)},s=function(){$('[data-card="true"]').each(function(){var e=$(this),t={};e.data("data-card-initialized")!==!0&&(d(e,t),e.data("data-card-initialized",!0))})},c=function(){if("undefined"!=typeof Sticky){new Sticky('[data-sticky="true"]')}},u=function(e){var t;e&&$("body").on("show.bs.dropdown",e,function(e){t=$(e.target).find(".dropdown-menu"),$("body").append(t.detach()),t.css("display","block"),t.position({my:"right top",at:"right bottom",of:$(e.relatedTarget)})}).on("hide.bs.dropdown",e,function(e){$(e.target).append(t.detach()),t.hide()})},f=function(){$("body").on("show.bs.dropdown",function(e){var t=$(e.target).find("[data-attach='body']");if(0!==t.length){var a=$(e.target).find(".dropdown-menu"),n=a.detach();t.data("dropdown-menu",n),$("body").append(n),n.css("display","block"),n.position({my:"right top",at:"right bottom",of:$(e.relatedTarget)})}}),$("body").on("hide.bs.dropdown",function(e){var t=$(e.target).find("[data-attach='body']");if(0!==t.length){var a=t.data("dropdown-menu");$(e.target).append(a.detach()),a.hide()}})};return{init:function(t){t&&(e=t),KTApp.initComponents()},initComponents:function(){r(),a(),i(),l(),o(),s(),c(),f()},initTooltips:function(){a()},initTooltip:function(e){t(e)},initPopovers:function(){i()},initPopover:function(e){n(e)},initCard:function(e,t){d(e,t)},initCards:function(){s()},initSticky:function(){initSticky()},initAbsoluteDropdown:function(e){u(e)},block:function(e,t){var a=$(e);t=$.extend(!0,{opacity:.05,overlayColor:"#000000",type:"",size:"",state:"primary",centerX:!0,centerY:!0,message:"",shadow:!0,width:"auto"},t);var n,i=t.type?"spinner-"+t.type:"",o=t.state?"spinner-"+t.state:"",r=t.size?"spinner-"+t.size:"",l='<span class="spinner '+i+" "+o+" "+r+'"></span';if(t.message&&t.message.length>0){var d="blockui "+(t.shadow===!1?"blockui":"");n='<div class="'+d+'"><span>'+t.message+"</span>"+l+"</div>";var a=document.createElement("div");$("body").prepend(a),KTUtil.addClass(a,d),a.innerHTML=n,t.width=KTUtil.actualWidth(a)+10,KTUtil.remove(a),"body"==e&&(n='<div class="'+d+'" style="margin-left:-'+t.width/2+'px;"><span>'+t.message+"</span><span>"+l+"</span></div>")}else n=l;var s={message:n,centerY:t.centerY,centerX:t.centerX,css:{top:"30%",left:"50%",border:"0",padding:"0",backgroundColor:"none",width:t.width},overlayCSS:{backgroundColor:t.overlayColor,opacity:t.opacity,cursor:"wait",zIndex:"body"==e?1100:10},onUnblock:function(){a&&a[0]&&(KTUtil.css(a[0],"position",""),KTUtil.css(a[0],"zoom",""))}};if("body"==e)s.css.top="50%",$.blockUI(s);else{var a=$(e);a.block(s)}},unblock:function(e){e&&"body"!=e?$(e).unblock():$.unblockUI()},blockPage:function(e){return KTApp.block("body",e)},unblockPage:function(){return KTApp.unblock("body")},getSettings:function(){return e}}}();"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTApp),$(document).ready(function(){KTApp.init(KTAppSettings)});var KTCard=function(e,t){var a=this,n=KTUtil.getById(e),i=KTUtil.getBody();if(n){var o={toggleSpeed:400,sticky:{releseOnReverse:!1,offset:300,zIndex:101}},r={construct:function(e){return KTUtil.data(n).has("card")?a=KTUtil.data(n).get("card"):(r.init(e),r.build(),KTUtil.data(n).set("card",a)),a},init:function(e){a.element=n,a.events=[],a.options=KTUtil.deepExtend({},o,e),a.header=KTUtil.child(n,".card-header"),a.footer=KTUtil.child(n,".card-footer"),KTUtil.child(n,".card-body")?a.body=KTUtil.child(n,".card-body"):KTUtil.child(n,".form")&&(a.body=KTUtil.child(n,".form"))},build:function(){var e=KTUtil.find(a.header,"[data-card-tool=remove]");e&&KTUtil.addEvent(e,"click",function(e){e.preventDefault(),r.remove()});var t=KTUtil.find(a.header,"[data-card-tool=reload]");t&&KTUtil.addEvent(t,"click",function(e){e.preventDefault(),r.reload()});var n=KTUtil.find(a.header,"[data-card-tool=toggle]");n&&KTUtil.addEvent(n,"click",function(e){e.preventDefault(),r.toggle()})},initSticky:function(){a.options.sticky.offset;a.header&&window.addEventListener("scroll",r.onScrollSticky)},onScrollSticky:function(e){var t=a.options.sticky.offset;if(!isNaN(t)){var n=KTUtil.getScrollTop();n>=t&&KTUtil.hasClass(i,"card-sticky-on")===!1?(r.eventTrigger("stickyOn"),KTUtil.addClass(i,"card-sticky-on"),r.updateSticky()):t>=n&&KTUtil.hasClass(i,"card-sticky-on")&&(r.eventTrigger("stickyOff"),KTUtil.removeClass(i,"card-sticky-on"),r.resetSticky())}},updateSticky:function(){if(a.header){var e;if(KTUtil.hasClass(i,"card-sticky-on")){e=a.options.sticky.position.top instanceof Function?parseInt(a.options.sticky.position.top.call(this,a)):parseInt(a.options.sticky.position.top);var t;t=a.options.sticky.position.left instanceof Function?parseInt(a.options.sticky.position.left.call(this,a)):parseInt(a.options.sticky.position.left);var n;n=a.options.sticky.position.right instanceof Function?parseInt(a.options.sticky.position.right.call(this,a)):parseInt(a.options.sticky.position.right),KTUtil.css(a.header,"z-index",a.options.sticky.zIndex),KTUtil.css(a.header,"top",e+"px"),KTUtil.css(a.header,"left",t+"px"),KTUtil.css(a.header,"right",n+"px")}}},resetSticky:function(){a.header&&KTUtil.hasClass(i,"card-sticky-on")===!1&&(KTUtil.css(a.header,"z-index",""),KTUtil.css(a.header,"top",""),KTUtil.css(a.header,"left",""),KTUtil.css(a.header,"right",""))},remove:function(){r.eventTrigger("beforeRemove")!==!1&&(KTUtil.remove(n),r.eventTrigger("afterRemove"))},setContent:function(e){e&&(a.body.innerHTML=e)},getBody:function(){return a.body},getSelf:function(){return n},reload:function(){r.eventTrigger("reload")},toggle:function(){KTUtil.hasClass(n,"card-collapse")||KTUtil.hasClass(n,"card-collapsed")?r.expand():r.collapse()},collapse:function(){r.eventTrigger("beforeCollapse")!==!1&&(KTUtil.slideUp(a.body,a.options.toggleSpeed,function(){r.eventTrigger("afterCollapse")}),KTUtil.addClass(n,"card-collapse"))},expand:function(){r.eventTrigger("beforeExpand")!==!1&&(KTUtil.slideDown(a.body,a.options.toggleSpeed,function(){r.eventTrigger("afterExpand")}),KTUtil.removeClass(n,"card-collapse"),KTUtil.removeClass(n,"card-collapsed"))},eventTrigger:function(e){for(var t=0;t<a.events.length;t++){var n=a.events[t];if(n.name==e){if(1!=n.one)return n.handler.call(this,a);if(0==n.fired)return a.events[t].fired=!0,n.handler.call(this,a)}}},addEvent:function(e,t,n){return a.events.push({name:e,handler:t,one:n,fired:!1}),a}};return a.setDefaults=function(e){o=e},a.remove=function(){return r.remove(html)},a.initSticky=function(){return r.initSticky()},a.updateSticky=function(){return r.updateSticky()},a.resetSticky=function(){return r.resetSticky()},a.destroySticky=function(){r.resetSticky(),window.removeEventListener("scroll",r.onScrollSticky)},a.reload=function(){return r.reload()},a.setContent=function(e){return r.setContent(e)},a.toggle=function(){return r.toggle()},a.collapse=function(){return r.collapse()},a.expand=function(){return r.expand()},a.getBody=function(){return r.getBody()},a.getSelf=function(){return r.getSelf()},a.on=function(e,t){return r.addEvent(e,t)},a.one=function(e,t){return r.addEvent(e,t,!0)},r.construct.apply(a,[t]),a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTCard);var KTCookie=function(){return{getCookie:function(e){var t=document.cookie.match(new RegExp("(?:^|; )"+e.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g,"\\$1")+"=([^;]*)"));return t?decodeURIComponent(t[1]):void 0},setCookie:function(e,t,a){a||(a={}),a=Object.assign({},{path:"/"},a),a.expires instanceof Date&&(a.expires=a.expires.toUTCString());var n=encodeURIComponent(e)+"="+encodeURIComponent(t);for(var i in a)if(a.hasOwnProperty(i)){n+="; "+i;var o=a[i];o!==!0&&(n+="="+o)}document.cookie=n},deleteCookie:function(e){setCookie(e,"",{"max-age":-1})}}}();"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTCookie);var KTDialog=function(e){var t,a=this,n=KTUtil.getBody(),i={placement:"top center",type:"loader",width:100,state:"default",message:"Loading..."},o={construct:function(e){return o.init(e),a},init:function(e){a.events=[],a.options=KTUtil.deepExtend({},i,e),a.state=!1},show:function(){return o.eventTrigger("show"),t=document.createElement("DIV"),KTUtil.setHTML(t,a.options.message),KTUtil.addClass(t,"dialog dialog-shown"),KTUtil.addClass(t,"dialog-"+a.options.state),KTUtil.addClass(t,"dialog-"+a.options.type),"top center"==a.options.placement&&KTUtil.addClass(t,"dialog-top-center"),n.appendChild(t),a.state="shown",o.eventTrigger("shown"),a},hide:function(){return t&&(o.eventTrigger("hide"),t.remove(),a.state="hidden",o.eventTrigger("hidden")),a},eventTrigger:function(e){for(var t=0;t<a.events.length;t++){var n=a.events[t];if(n.name==e){if(1!=n.one)return n.handler.call(this,a);if(0==n.fired)return a.events[t].fired=!0,n.handler.call(this,a)}}},addEvent:function(e,t,n){return a.events.push({name:e,handler:t,one:n,fired:!1}),a}};return a.setDefaults=function(e){i=e},a.shown=function(){return"shown"==a.state},a.hidden=function(){return"hidden"==a.state},a.show=function(){return o.show()},a.hide=function(){return o.hide()},a.on=function(e,t){return o.addEvent(e,t)},a.one=function(e,t){return o.addEvent(e,t,!0)},o.construct.apply(a,[e]),a};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTDialog);var KTHeader=function(e,t){var a=this,n=!1,i=KTUtil.getById(e),o=KTUtil.getBody();if(void 0!==i){var r={offset:{desktop:!0,tabletAndMobile:!0},releseOnReverse:{desktop:!1,tabletAndMobile:!1}},l={construct:function(e){return KTUtil.data(i).has("header")?a=KTUtil.data(i).get("header"):(l.init(e),l.build(),KTUtil.data(i).set("header",a)),a},init:function(e){a.events=[],a.options=KTUtil.deepExtend({},r,e)},build:function(){var e=!0,t=0;window.addEventListener("scroll",function(){var n,i=0;KTUtil.isBreakpointDown("lg")&&a.options.offset.tabletAndMobile===!1||KTUtil.isBreakpointUp("lg")&&a.options.offset.desktop===!1||(KTUtil.isBreakpointUp("lg")?i=a.options.offset.desktop:KTUtil.isBreakpointDown("lg")&&(i=a.options.offset.tabletAndMobile),n=KTUtil.getScrollTop(),KTUtil.isBreakpointDown("lg")&&a.options.releseOnReverse.tabletAndMobile||KTUtil.isBreakpointUp("lg")&&a.options.releseOnReverse.desktop?(n>i&&n>t?(o.hasAttribute("data-header-scroll")===!1&&o.setAttribute("data-header-scroll","on"),e&&(l.eventTrigger("scrollOn",a),e=!1)):(o.hasAttribute("data-header-scroll")===!0&&o.removeAttribute("data-header-scroll"),0==e&&(l.eventTrigger("scrollOff",a),e=!0)),t=n):n>i?(o.hasAttribute("data-header-scroll")===!1&&o.setAttribute("data-header-scroll","on"),e&&(l.eventTrigger("scrollOn",a),e=!1)):(o.hasAttribute("data-header-scroll")===!0&&o.removeAttribute("data-header-scroll"),0==e&&(l.eventTrigger("scrollOff",a),e=!0)))})},eventTrigger:function(e,t){for(var n=0;n<a.events.length;n++){var i=a.events[n];if(i.name==e){if(1!=i.one)return i.handler.call(this,a,t);if(0==i.fired)return a.events[n].fired=!0,i.handler.call(this,a,t)}}},addEvent:function(e,t,n){a.events.push({name:e,handler:t,one:n,fired:!1})}};return a.setDefaults=function(e){r=e},a.on=function(e,t){return l.addEvent(e,t)},l.construct.apply(a,[t]),n=!0,a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTHeader);var KTImageInput=function(e,t){var a=this,n=KTUtil.getById(e);KTUtil.getBody();if(n){var i={editMode:!1},o={construct:function(e){return KTUtil.data(n).has("imageinput")?a=KTUtil.data(n).get("imageinput"):(o.init(e),o.build(),KTUtil.data(n).set("imageinput",a)),a},init:function(e){a.element=n,a.events=[],a.input=KTUtil.find(n,'input[type="file"]'),a.wrapper=KTUtil.find(n,".image-input-wrapper"),a.cancel=KTUtil.find(n,'[data-action="cancel"]'),a.remove=KTUtil.find(n,'[data-action="remove"]'),a.src=KTUtil.css(a.wrapper,"backgroundImage"),a.hidden=KTUtil.find(n,'input[type="hidden"]'),a.options=KTUtil.deepExtend({},i,e)},build:function(){KTUtil.addEvent(a.input,"change",function(e){if(e.preventDefault(),a.input&&a.input.files&&a.input.files[0]){var t=new FileReader;t.onload=function(e){KTUtil.css(a.wrapper,"background-image","url("+e.target.result+")")},t.readAsDataURL(a.input.files[0]),KTUtil.addClass(a.element,"image-input-changed"),KTUtil.removeClass(a.element,"image-input-empty"),o.eventTrigger("change")}}),KTUtil.addEvent(a.cancel,"click",function(e){e.preventDefault(),o.eventTrigger("cancel"),KTUtil.removeClass(a.element,"image-input-changed"),KTUtil.removeClass(a.element,"image-input-empty"),KTUtil.css(a.wrapper,"background-image",a.src),a.input.value="",a.hidden&&(a.hidden.value="0")}),KTUtil.addEvent(a.remove,"click",function(e){e.preventDefault(),o.eventTrigger("remove"),KTUtil.removeClass(a.element,"image-input-changed"),KTUtil.addClass(a.element,"image-input-empty"),KTUtil.css(a.wrapper,"background-image","none"),a.input.value="",a.hidden&&(a.hidden.value="1")})},eventTrigger:function(e){for(var t=0;t<a.events.length;t++){var n=a.events[t];if(n.name==e){if(1!=n.one)return n.handler.call(this,a);if(0==n.fired)return a.events[t].fired=!0,n.handler.call(this,a)}}},addEvent:function(e,t,n){return a.events.push({name:e,handler:t,one:n,fired:!1}),a}};return a.setDefaults=function(e){i=e},a.on=function(e,t){return o.addEvent(e,t)},a.one=function(e,t){return o.addEvent(e,t,!0)},o.construct.apply(a,[t]),a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTImageInput);var KTMenu=function(e,t){var a=this,n=!1,i=KTUtil.getById(e),o=KTUtil.getBody();if(i){var r={scroll:{rememberPosition:!1},accordion:{slideSpeed:200,autoScroll:!1,autoScrollSpeed:1200,expandAll:!0},dropdown:{timeout:500}},l={construct:function(e){return KTUtil.data(i).has("menu")?a=KTUtil.data(i).get("menu"):(l.init(e),l.reset(),l.build(),KTUtil.data(i).set("menu",a)),a},init:function(e){a.events=[],a.eventHandlers={},a.options=KTUtil.deepExtend({},r,e),a.pauseDropdownHoverTime=0,a.uid=KTUtil.getUniqueID()},update:function(e){a.options=KTUtil.deepExtend({},r,e),a.pauseDropdownHoverTime=0,l.reset(),a.eventHandlers={},l.build(),KTUtil.data(i).set("menu",a)},reload:function(){l.reset(),l.build(),l.resetSubmenuProps()},build:function(){a.eventHandlers.event_1=KTUtil.on(i,".menu-toggle","click",l.handleSubmenuAccordion),("dropdown"===l.getSubmenuMode()||l.isConditionalSubmenuDropdown())&&(a.eventHandlers.event_2=KTUtil.on(i,'[data-menu-toggle="hover"]',"mouseover",l.handleSubmenuDrodownHoverEnter),a.eventHandlers.event_3=KTUtil.on(i,'[data-menu-toggle="hover"]',"mouseout",l.handleSubmenuDrodownHoverExit),a.eventHandlers.event_4=KTUtil.on(i,'[data-menu-toggle="click"] > .menu-toggle, [data-menu-toggle="click"] > .menu-link .menu-toggle',"click",l.handleSubmenuDropdownClick),a.eventHandlers.event_5=KTUtil.on(i,'[data-menu-toggle="tab"] > .menu-toggle, [data-menu-toggle="tab"] > .menu-link .menu-toggle',"click",l.handleSubmenuDropdownTabClick)),a.eventHandlers.event_6=KTUtil.on(i,".menu-item > .menu-link:not(.menu-toggle):not(.menu-link-toggle-skip)","click",l.handleLinkClick),a.options.scroll&&a.options.scroll.height&&l.scrollInit()},reset:function(){KTUtil.off(i,"click",a.eventHandlers.event_1),KTUtil.off(i,"mouseover",a.eventHandlers.event_2),KTUtil.off(i,"mouseout",a.eventHandlers.event_3),KTUtil.off(i,"click",a.eventHandlers.event_4),KTUtil.off(i,"click",a.eventHandlers.event_5),KTUtil.off(i,"click",a.eventHandlers.event_6)},scrollInit:function(){a.options.scroll&&a.options.scroll.height?(KTUtil.scrollDestroy(i,!0),KTUtil.scrollInit(i,{mobileNativeScroll:!0,windowScroll:!1,resetHeightOnDestroy:!0,handleWindowResize:!0,height:a.options.scroll.height,rememberPosition:a.options.scroll.rememberPosition})):KTUtil.scrollDestroy(i,!0)},scrollUpdate:function(){a.options.scroll&&a.options.scroll.height&&KTUtil.scrollUpdate(i)},scrollTop:function(){a.options.scroll&&a.options.scroll.height&&KTUtil.scrollTop(i)},getSubmenuMode:function(e){return KTUtil.isBreakpointUp("lg")?e&&KTUtil.hasAttr(e,"data-menu-toggle")&&"hover"==KTUtil.attr(e,"data-menu-toggle")?"dropdown":KTUtil.isset(a.options.submenu,"desktop.state.body")?KTUtil.hasClasses(o,a.options.submenu.desktop.state.body)?a.options.submenu.desktop.state.mode:a.options.submenu.desktop["default"]:KTUtil.isset(a.options.submenu,"desktop")?a.options.submenu.desktop:void 0:KTUtil.isBreakpointUp("md")&&KTUtil.isBreakpointDown("lg")&&KTUtil.isset(a.options.submenu,"tablet")?a.options.submenu.tablet:KTUtil.isBreakpointDown("md")&&KTUtil.isset(a.options.submenu,"mobile")?a.options.submenu.mobile:!1},isConditionalSubmenuDropdown:function(){return KTUtil.isBreakpointUp("lg")&&KTUtil.isset(a.options.submenu,"desktop.state.body")?!0:!1},resetSubmenuProps:function(e){var t=KTUtil.findAll(i,".menu-submenu");if(t)for(var a=0,n=t.length;n>a;a++){var o=t[0];KTUtil.css(o,"display",""),KTUtil.css(o,"overflow",""),o.hasAttribute("data-hor-direction")&&(KTUtil.removeClass(o,"menu-submenu-left"),KTUtil.removeClass(o,"menu-submenu-right"),KTUtil.addClass(o,o.getAttribute("data-hor-direction")))}},handleSubmenuDrodownHoverEnter:function(e){if("accordion"!==l.getSubmenuMode(this)&&a.resumeDropdownHover()!==!1){var t=this;"1"==t.getAttribute("data-hover")&&(t.removeAttribute("data-hover"),clearTimeout(t.getAttribute("data-timeout")),t.removeAttribute("data-timeout")),l.showSubmenuDropdown(t)}},handleSubmenuDrodownHoverExit:function(e){if(a.resumeDropdownHover()!==!1&&"accordion"!==l.getSubmenuMode(this)){var t=this,n=a.options.dropdown.timeout,i=setTimeout(function(){"1"==t.getAttribute("data-hover")&&l.hideSubmenuDropdown(t,!0)},n);t.setAttribute("data-hover","1"),t.setAttribute("data-timeout",i)}},handleSubmenuDropdownClick:function(e){if("accordion"!==l.getSubmenuMode(this)){var t=this.closest(".menu-item"),a=l.eventTrigger("submenuToggle",this,e);a!==!1&&"accordion"!=t.getAttribute("data-menu-submenu-mode")&&(KTUtil.hasClass(t,"menu-item-hover")===!1?(KTUtil.addClass(t,"menu-item-open-dropdown"),l.showSubmenuDropdown(t)):(KTUtil.removeClass(t,"menu-item-open-dropdown"),l.hideSubmenuDropdown(t,!0)),e.preventDefault())}},handleSubmenuDropdownTabClick:function(e){if("accordion"!==l.getSubmenuMode(this)){var t=this.closest(".menu-item"),a=l.eventTrigger("submenuToggle",this,e);a!==!1&&"accordion"!=t.getAttribute("data-menu-submenu-mode")&&(0==KTUtil.hasClass(t,"menu-item-hover")&&(KTUtil.addClass(t,"menu-item-open-dropdown"),l.showSubmenuDropdown(t)),e.preventDefault())}},handleLinkClick:function(e){var t=this.closest(".menu-item.menu-item-submenu"),a=l.eventTrigger("linkClick",this,e);a!==!1&&t&&"dropdown"===l.getSubmenuMode(t)&&l.hideSubmenuDropdowns()},handleSubmenuDropdownClose:function(e,t){if("accordion"!==l.getSubmenuMode(t)){var a=i.querySelectorAll(".menu-item.menu-item-submenu.menu-item-hover:not(.menu-item-tabs)");if(a.length>0&&KTUtil.hasClass(t,"menu-toggle")===!1&&0===t.querySelectorAll(".menu-toggle").length)for(var n=0,o=a.length;o>n;n++)l.hideSubmenuDropdown(a[0],!0)}},handleSubmenuAccordion:function(e,t){var n,i=t?t:this,o=l.eventTrigger("submenuToggle",this,e);if(o!==!1){if("dropdown"===l.getSubmenuMode(t)&&(n=i.closest(".menu-item"))&&"accordion"!=n.getAttribute("data-menu-submenu-mode"))return void e.preventDefault();var r=i.closest(".menu-item"),d=KTUtil.child(r,".menu-submenu, .menu-inner");if(!KTUtil.hasClass(i.closest(".menu-item"),"menu-item-open-always")&&r&&d){e.preventDefault();var s=a.options.accordion.slideSpeed;if(KTUtil.hasClass(r,"menu-item-open")===!1){if(a.options.accordion.expandAll===!1){var c=i.closest(".menu-nav, .menu-subnav"),u=KTUtil.children(c,".menu-item.menu-item-open.menu-item-submenu:not(.menu-item-here):not(.menu-item-open-always)");if(c&&u)for(var f=0,p=u.length;p>f;f++){var g=u[0],m=KTUtil.child(g,".menu-submenu");m&&KTUtil.slideUp(m,s,function(){l.scrollUpdate(),KTUtil.removeClass(g,"menu-item-open")})}}KTUtil.slideDown(d,s,function(){l.scrollToItem(i),l.scrollUpdate(),l.eventTrigger("submenuToggle",d,e)}),KTUtil.addClass(r,"menu-item-open")}else KTUtil.slideUp(d,s,function(){l.scrollToItem(i),l.eventTrigger("submenuToggle",d,e)}),KTUtil.removeClass(r,"menu-item-open")}}},scrollToItem:function(e){KTUtil.isBreakpointUp("lg")&&a.options.accordion.autoScroll&&"1"!==i.getAttribute("data-menu-scroll")&&KTUtil.scrollTo(e,a.options.accordion.autoScrollSpeed)},hideSubmenuDropdown:function(e,t){t&&(KTUtil.removeClass(e,"menu-item-hover"),KTUtil.removeClass(e,"menu-item-active-tab")),e.removeAttribute("data-hover"),e.getAttribute("data-menu-toggle-class")&&KTUtil.removeClass(o,e.getAttribute("data-menu-toggle-class"));var a=e.getAttribute("data-timeout");e.removeAttribute("data-timeout"),clearTimeout(a)},hideSubmenuDropdowns:function(){var e;if(e=i.querySelectorAll('.menu-item-submenu.menu-item-hover:not(.menu-item-tabs):not([data-menu-toggle="tab"])'))for(var t=0,a=e.length;a>t;t++)l.hideSubmenuDropdown(e[t],!0)},showSubmenuDropdown:function(e){var t=i.querySelectorAll(".menu-item-submenu.menu-item-hover, .menu-item-submenu.menu-item-active-tab");if(t)for(var a=0,n=t.length;n>a;a++){var r=t[a];e!==r&&r.contains(e)===!1&&e.contains(r)===!1&&l.hideSubmenuDropdown(r,!0)}KTUtil.addClass(e,"menu-item-hover");var d=KTUtil.find(e,".menu-submenu");d&&d.hasAttribute("data-hor-direction")===!1&&(KTUtil.hasClass(d,"menu-submenu-left")?d.setAttribute("data-hor-direction","menu-submenu-left"):KTUtil.hasClass(d,"menu-submenu-right")&&d.setAttribute("data-hor-direction","menu-submenu-right")),d&&KTUtil.isOffscreen(d,"left",15)===!0?(KTUtil.removeClass(d,"menu-submenu-left"),KTUtil.addClass(d,"menu-submenu-right")):d&&KTUtil.isOffscreen(d,"right",15)===!0&&(KTUtil.removeClass(d,"menu-submenu-right"),KTUtil.addClass(d,"menu-submenu-left")),e.getAttribute("data-menu-toggle-class")&&KTUtil.addClass(o,e.getAttribute("data-menu-toggle-class"))},createSubmenuDropdownClickDropoff:function(e){var t,a=(t=KTUtil.child(e,".menu-submenu")?KTUtil.css(t,"z-index"):0)-1,n=document.createElement('<div class="menu-dropoff" style="background: transparent; position: fixed; top: 0; bottom: 0; left: 0; right: 0; z-index: '+a+'"></div>');o.appendChild(n),KTUtil.addEvent(n,"click",function(t){t.stopPropagation(),t.preventDefault(),KTUtil.remove(this),l.hideSubmenuDropdown(e,!0)})},pauseDropdownHover:function(e){var t=new Date;a.pauseDropdownHoverTime=t.getTime()+e},resumeDropdownHover:function(){var e=new Date;return e.getTime()>a.pauseDropdownHoverTime?!0:!1},resetActiveItem:function(e){var t,n;t=i.querySelectorAll(".menu-item-active");for(var o=0,r=t.length;r>o;o++){var l=t[0];KTUtil.removeClass(l,"menu-item-active"),KTUtil.hide(KTUtil.child(l,".menu-submenu")),n=KTUtil.parents(l,".menu-item-submenu")||[];for(var d=0,s=n.length;s>d;d++){var c=n[o];KTUtil.removeClass(c,"menu-item-open"),KTUtil.hide(KTUtil.child(c,".menu-submenu"))}}if(a.options.accordion.expandAll===!1&&(t=i.querySelectorAll(".menu-item-open")))for(var o=0,r=t.length;r>o;o++)KTUtil.removeClass(n[0],"menu-item-open")},setActiveItem:function(e){l.resetActiveItem();for(var t=KTUtil.parents(e,".menu-item-submenu")||[],a=0,n=t.length;n>a;a++)KTUtil.addClass(t[a],"menu-item-open");KTUtil.addClass(e,"menu-item-active")},getBreadcrumbs:function(e){var t,a=[],n=KTUtil.child(e,".menu-link");a.push({text:t=KTUtil.child(n,".menu-text")?t.innerHTML:"",title:n.getAttribute("title"),href:n.getAttribute("href")});for(var i=KTUtil.parents(e,".menu-item-submenu"),o=0,r=i.length;r>o;o++){var l=KTUtil.child(i[o],".menu-link");a.push({text:t=KTUtil.child(l,".menu-text")?t.innerHTML:"",title:l.getAttribute("title"),href:l.getAttribute("href")})}return a.reverse()},getPageTitle:function(e){var t;return t=KTUtil.child(e,".menu-text")?t.innerHTML:""},eventTrigger:function(e,t,n){for(var i=0;i<a.events.length;i++){var o=a.events[i];if(o.name==e){if(1!=o.one)return o.handler.call(this,t,n);if(0==o.fired)return a.events[i].fired=!0,o.handler.call(this,t,n)}}},addEvent:function(e,t,n){a.events.push({name:e,handler:t,one:n,fired:!1})},removeEvent:function(e){a.events[e]&&delete a.events[e]}};return a.setDefaults=function(e){r=e},a.scrollUpdate=function(){return l.scrollUpdate()},a.scrollReInit=function(){return l.scrollInit()},a.scrollTop=function(){return l.scrollTop()},a.setActiveItem=function(e){return l.setActiveItem(e)},a.reload=function(){return l.reload()},a.update=function(e){return l.update(e)},a.getBreadcrumbs=function(e){return l.getBreadcrumbs(e)},a.getPageTitle=function(e){return l.getPageTitle(e)},a.getSubmenuMode=function(e){return l.getSubmenuMode(e)},a.hideDropdown=function(e){l.hideSubmenuDropdown(e,!0)},a.hideDropdowns=function(){l.hideSubmenuDropdowns()},a.pauseDropdownHover=function(e){l.pauseDropdownHover(e)},a.resumeDropdownHover=function(){return l.resumeDropdownHover()},a.on=function(e,t){return l.addEvent(e,t)},a.off=function(e){return l.removeEvent(e)},a.one=function(e,t){return l.addEvent(e,t,!0)},l.construct.apply(a,[t]),KTUtil.addResizeHandler(function(){n&&a.reload()}),n=!0,a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTMenu),document.addEventListener("click",function(e){var t,a=KTUtil.getByTagName("body")[0];if(t=a.querySelectorAll('.menu-nav .menu-item.menu-item-submenu.menu-item-hover:not(.menu-item-tabs)[data-menu-toggle="click"]'))for(var n=0,i=t.length;i>n;n++){var o=t[n].closest(".menu-nav").parentNode;if(o){var r=KTUtil.data(o).get("menu");if(!r)break;if(!r||"dropdown"!==r.getSubmenuMode())break;e.target!==o&&o.contains(e.target)===!1&&r.hideDropdowns()}}});var KTOffcanvas=function(e,t){var a=this,n=!1,i=KTUtil.getById(e),o=KTUtil.getBody();if(i){var r={attrCustom:""},l={construct:function(e){return KTUtil.data(i).has("offcanvas")?a=KTUtil.data(i).get("offcanvas"):(l.init(e),l.build(),KTUtil.data(i).set("offcanvas",a)),a},init:function(e){a.events=[],a.options=KTUtil.deepExtend({},r,e),a.classBase=a.options.baseClass,a.attrCustom=a.options.attrCustom,a.classShown=a.classBase+"-on",a.classOverlay=a.classBase+"-overlay",a.target,a.state=KTUtil.hasClass(i,a.classShown)?"shown":"hidden"},build:function(){if(a.options.toggleBy)if("string"==typeof a.options.toggleBy)KTUtil.addEvent(KTUtil.getById(a.options.toggleBy),"click",function(e){e.preventDefault(),a.target=this,l.toggle()});else if(a.options.toggleBy&&a.options.toggleBy[0])if(a.options.toggleBy[0].target)for(var e in a.options.toggleBy)KTUtil.addEvent(KTUtil.getById(a.options.toggleBy[e].target),"click",function(e){e.preventDefault(),a.target=this,l.toggle()});else for(var e in a.options.toggleBy)KTUtil.addEvent(KTUtil.getById(a.options.toggleBy[e]),"click",function(e){e.preventDefault(),a.target=this,l.toggle()});else a.options.toggleBy&&a.options.toggleBy.target&&KTUtil.addEvent(KTUtil.getById(a.options.toggleBy.target),"click",function(e){e.preventDefault(),a.target=this,l.toggle()});var t=KTUtil.getById(a.options.closeBy);t&&KTUtil.addEvent(t,"click",function(e){e.preventDefault(),a.target=this,l.hide()})},isShown:function(){return"shown"==a.state?!0:!1},toggle:function(){l.eventTrigger("toggle"),"shown"==a.state?l.hide():l.show()},show:function(){"shown"!=a.state&&(l.eventTrigger("beforeShow"),l.toggleClass("show"),KTUtil.attr(o,"data-offcanvas-"+a.classBase,"on"),KTUtil.addClass(i,a.classShown),a.attrCustom.length>0&&KTUtil.attr(o,"data-offcanvas-"+a.classCustom,"on"),a.state="shown",a.options.overlay&&(a.overlay=KTUtil.insertAfter(document.createElement("DIV"),i),KTUtil.addClass(a.overlay,a.classOverlay),KTUtil.addEvent(a.overlay,"click",function(e){e.preventDefault(),l.hide(a.target)})),l.eventTrigger("afterShow"))},hide:function(){"hidden"!=a.state&&(l.eventTrigger("beforeHide"),l.toggleClass("hide"),KTUtil.removeAttr(o,"data-offcanvas-"+a.classBase),KTUtil.removeClass(i,a.classShown),a.attrCustom.length>0&&KTUtil.removeAttr(o,"data-offcanvas-"+a.attrCustom),a.state="hidden",a.options.overlay&&a.overlay&&KTUtil.remove(a.overlay),l.eventTrigger("afterHide"))},toggleClass:function(e){var t,n=KTUtil.attr(a.target,"id");if(a.options.toggleBy&&a.options.toggleBy[0]&&a.options.toggleBy[0].target)for(var i in a.options.toggleBy)a.options.toggleBy[i].target===n&&(t=a.options.toggleBy[i]);else a.options.toggleBy&&a.options.toggleBy.target&&(t=a.options.toggleBy);if(t){var o=KTUtil.getById(t.target);"show"===e&&KTUtil.addClass(o,t.state),"hide"===e&&KTUtil.removeClass(o,t.state)}},eventTrigger:function(e,t){for(var n=0;n<a.events.length;n++){var i=a.events[n];if(i.name==e){if(1!=i.one)return i.handler.call(this,a,t);if(0==i.fired)return a.events[n].fired=!0,i.handler.call(this,a,t)}}},addEvent:function(e,t,n){a.events.push({name:e,handler:t,one:n,fired:!1})}};return a.setDefaults=function(e){r=e},a.isShown=function(){return l.isShown()},a.hide=function(){return l.hide()},a.show=function(){return l.show()},a.on=function(e,t){return l.addEvent(e,t)},a.one=function(e,t){return l.addEvent(e,t,!0)},l.construct.apply(a,[t]),n=!0,a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTOffcanvas);var KTScrolltop=function(e,t){var a=this,n=!1,i=KTUtil.getById(e),o=KTUtil.getBody();if(i){var r={offset:300,speed:6e3},l={construct:function(e){return KTUtil.data(i).has("scrolltop")?a=KTUtil.data(i).get("scrolltop"):(l.init(e),l.build(),KTUtil.data(i).set("scrolltop",a)),a},init:function(e){a.events=[],a.options=KTUtil.deepExtend({},r,e)},build:function(){var e;window.addEventListener("scroll",function(){KTUtil.throttle(e,function(){l.handle()},200)}),KTUtil.addEvent(i,"click",l.scroll)},handle:function(){var e=KTUtil.getScrollTop();e>a.options.offset?o.hasAttribute("data-scrolltop")===!1&&o.setAttribute("data-scrolltop","on"):o.hasAttribute("data-scrolltop")===!0&&o.removeAttribute("data-scrolltop")},scroll:function(e){e.preventDefault(),KTUtil.scrollTop(0,a.options.speed)},eventTrigger:function(e,t){for(var n=0;n<a.events.length;n++){var i=a.events[n];if(i.name==e){if(1!=i.one)return i.handler.call(this,a,t);if(0==i.fired)return a.events[n].fired=!0,i.handler.call(this,a,t)}}},addEvent:function(e,t,n){a.events.push({name:e,handler:t,one:n,fired:!1})}};return a.setDefaults=function(e){r=e},a.on=function(e,t){return l.addEvent(e,t)},a.one=function(e,t){return l.addEvent(e,t,!0)},l.construct.apply(a,[t]),n=!0,a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTScrolltop);var KTToggle=function(e,t,a){var n=this,i=e,o=t;if(i){var r={targetToggleMode:"class"},l={construct:function(e){return KTUtil.data(i).has("toggle")?n=KTUtil.data(i).get("toggle"):(l.init(e),l.build(),KTUtil.data(i).set("toggle",n)),n},init:function(e){n.element=i,n.events=[],n.options=KTUtil.deepExtend({},r,e),n.target=o,n.targetState=n.options.targetState,n.toggleState=n.options.toggleState,"class"==n.options.targetToggleMode?n.state=KTUtil.hasClasses(n.target,n.targetState)?"on":"off":n.state=KTUtil.hasAttr(n.target,"data-"+n.targetState)?KTUtil.attr(n.target,"data-"+n.targetState):"off";
    },build:function(){KTUtil.addEvent(i,"mouseup",l.toggle)},toggle:function(e){return l.eventTrigger("beforeToggle"),"off"==n.state?l.toggleOn():l.toggleOff(),l.eventTrigger("afterToggle"),e.preventDefault(),n},toggleOn:function(){return l.eventTrigger("beforeOn"),"class"==n.options.targetToggleMode?KTUtil.addClass(n.target,n.targetState):KTUtil.attr(n.target,"data-"+n.targetState,"on"),n.toggleState&&KTUtil.addClass(i,n.toggleState),n.state="on",l.eventTrigger("afterOn"),l.eventTrigger("toggle"),n},toggleOff:function(){return l.eventTrigger("beforeOff"),"class"==n.options.targetToggleMode?KTUtil.removeClass(n.target,n.targetState):KTUtil.removeAttr(n.target,"data-"+n.targetState),n.toggleState&&KTUtil.removeClass(i,n.toggleState),n.state="off",l.eventTrigger("afterOff"),l.eventTrigger("toggle"),n},eventTrigger:function(e){for(var t=0;t<n.events.length;t++){var a=n.events[t];if(a.name==e){if(1!=a.one)return a.handler.call(this,n);if(0==a.fired)return n.events[t].fired=!0,a.handler.call(this,n)}}},addEvent:function(e,t,a){return n.events.push({name:e,handler:t,one:a,fired:!1}),n}};return n.setDefaults=function(e){r=e},n.getState=function(){return n.state},n.toggle=function(){return l.toggle()},n.toggleOn=function(){return l.toggleOn()},n.toggleOff=function(){return l.toggleOff()},n.on=function(e,t){return l.addEvent(e,t)},n.one=function(e,t){return l.addEvent(e,t,!0)},l.construct.apply(n,[a]),n}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTToggle),Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest=function(e){var t=this,a=this;if(!document.documentElement.contains(t))return null;do{if(a.matches(e))return a;a=a.parentElement}while(null!==a);return null}),function(e){for(var t=0;t<e.length;t++)!window[e[t]]||"remove"in window[e[t]].prototype||(window[e[t]].prototype.remove=function(){this.parentNode.removeChild(this)})}(["Element","CharacterData","DocumentType"]),function(){for(var e=0,t=["webkit","moz"],a=0;a<t.length&&!window.requestAnimationFrame;++a)window.requestAnimationFrame=window[t[a]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[a]+"CancelAnimationFrame"]||window[t[a]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t){var a=(new Date).getTime(),n=Math.max(0,16-(a-e)),i=window.setTimeout(function(){t(a+n)},n);return e=a+n,i}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),function(e){e.forEach(function(e){e.hasOwnProperty("prepend")||Object.defineProperty(e,"prepend",{configurable:!0,enumerable:!0,writable:!0,value:function(){var e=Array.prototype.slice.call(arguments),t=document.createDocumentFragment();e.forEach(function(e){var a=e instanceof Node;t.appendChild(a?e:document.createTextNode(String(e)))}),this.insertBefore(t,this.firstChild)}})})}([Element.prototype,Document.prototype,DocumentFragment.prototype]),void 0==Element.prototype.getAttributeNames&&(Element.prototype.getAttributeNames=function(){for(var e=this.attributes,t=e.length,a=new Array(t),n=0;t>n;n++)a[n]=e[n].name;return a}),window.KTUtilElementDataStore={},window.KTUtilElementDataStoreID=0,window.KTUtilDelegatedEventHandlers={};var KTUtil=function(){var e=[],t={sm:544,md:768,lg:992,xl:1200},a=function(){var t,a=function(){for(var t=0;t<e.length;t++){var a=e[t];a.call()}};window.addEventListener("resize",function(){KTUtil.throttle(t,function(){a()},200)})};return{init:function(e){e&&e.breakpoints&&(t=e.breakpoints),a()},addResizeHandler:function(t){e.push(t)},removeResizeHandler:function(t){for(var a=0;a<e.length;a++)t===e[a]&&delete e[a]},runResizeHandlers:function(){_runResizeHandlers()},resize:function(){if("function"==typeof Event)window.dispatchEvent(new Event("resize"));else{var e=window.document.createEvent("UIEvents");e.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(e)}},getURLParam:function(e){var t,a,n=window.location.search.substring(1),i=n.split("&");for(t=0;t<i.length;t++)if(a=i[t].split("="),a[0]==e)return unescape(a[1]);return null},isMobileDevice:function(){var e=this.getViewPort().width<this.getBreakpoint("lg")?!0:!1;return e===!1&&(e=null!=navigator.userAgent.match(/iPad/i)),e},isDesktopDevice:function(){return KTUtil.isMobileDevice()?!1:!0},getViewPort:function(){var e=window,t="inner";return"innerWidth"in window||(t="client",e=document.documentElement||document.body),{width:e[t+"Width"],height:e[t+"Height"]}},isInResponsiveRange:function(e){var t=this.getViewPort().width;return"general"==e?!0:"desktop"==e&&t>=this.getBreakpoint("lg")+1?!0:"tablet"==e&&t>=this.getBreakpoint("md")+1&&t<this.getBreakpoint("lg")?!0:"mobile"==e&&t<=this.getBreakpoint("md")?!0:"desktop-and-tablet"==e&&t>=this.getBreakpoint("md")+1?!0:"tablet-and-mobile"==e&&t<=this.getBreakpoint("lg")?!0:"minimal-desktop-and-below"==e&&t<=this.getBreakpoint("xl")?!0:!1},isBreakpointUp:function(e){var t=this.getViewPort().width,a=this.getBreakpoint(e);return t>=a},isBreakpointDown:function(e){var t=this.getViewPort().width,a=this.getBreakpoint(e);return a>t},getUniqueID:function(e){return e+Math.floor(Math.random()*(new Date).getTime())},getBreakpoint:function(e){return t[e]},isset:function(e,t){var a;if(t=t||"",-1!==t.indexOf("["))throw new Error("Unsupported object path notation.");t=t.split(".");do{if(void 0===e)return!1;if(a=t.shift(),!e.hasOwnProperty(a))return!1;e=e[a]}while(t.length);return!0},getHighestZindex:function(e){for(var t,a;e&&e!==document;){if(t=KTUtil.css(e,"position"),("absolute"===t||"relative"===t||"fixed"===t)&&(a=parseInt(KTUtil.css(e,"z-index")),!isNaN(a)&&0!==a))return a;e=e.parentNode}return null},hasFixedPositionedParent:function(e){for(var t;e&&e!==document;){if(t=KTUtil.css(e,"position"),"fixed"===t)return!0;e=e.parentNode}return!1},sleep:function(e){for(var t=(new Date).getTime(),a=0;1e7>a&&!((new Date).getTime()-t>e);a++);},getRandomInt:function(e,t){return Math.floor(Math.random()*(t-e+1))+e},isAngularVersion:function(){return void 0!==window.Zone?!0:!1},deepExtend:function(e){e=e||{};for(var t=1;t<arguments.length;t++){var a=arguments[t];if(a)for(var n in a)a.hasOwnProperty(n)&&("object"==typeof a[n]?e[n]=KTUtil.deepExtend(e[n],a[n]):e[n]=a[n])}return e},extend:function(e){e=e||{};for(var t=1;t<arguments.length;t++)if(arguments[t])for(var a in arguments[t])arguments[t].hasOwnProperty(a)&&(e[a]=arguments[t][a]);return e},getById:function(e){return"string"==typeof e?document.getElementById(e):e},getByTag:function(e){return document.getElementsByTagName(e)},getByTagName:function(e){return document.getElementsByTagName(e)},getByClass:function(e){return document.getElementsByClassName(e)},getBody:function(){return document.getElementsByTagName("body")[0]},hasClasses:function(e,t){if(e){for(var a=t.split(" "),n=0;n<a.length;n++)if(0==KTUtil.hasClass(e,KTUtil.trim(a[n])))return!1;return!0}},hasClass:function(e,t){return e?e.classList?e.classList.contains(t):new RegExp("\\b"+t+"\\b").test(e.className):void 0},addClass:function(e,t){if(e&&"undefined"!=typeof t){var a=t.split(" ");if(e.classList)for(var n=0;n<a.length;n++)a[n]&&a[n].length>0&&e.classList.add(KTUtil.trim(a[n]));else if(!KTUtil.hasClass(e,t))for(var i=0;i<a.length;i++)e.className+=" "+KTUtil.trim(a[i])}},removeClass:function(e,t){if(e&&"undefined"!=typeof t){var a=t.split(" ");if(e.classList)for(var n=0;n<a.length;n++)e.classList.remove(KTUtil.trim(a[n]));else if(KTUtil.hasClass(e,t))for(var i=0;i<a.length;i++)e.className=e.className.replace(new RegExp("\\b"+KTUtil.trim(a[i])+"\\b","g"),"")}},triggerCustomEvent:function(e,t,a){var n;window.CustomEvent?n=new CustomEvent(t,{detail:a}):(n=document.createEvent("CustomEvent"),n.initCustomEvent(t,!0,!0,a)),e.dispatchEvent(n)},triggerEvent:function(e,t){var a;if(e.ownerDocument)a=e.ownerDocument;else{if(9!=e.nodeType)throw new Error("Invalid node passed to fireEvent: "+e.id);a=e}if(e.dispatchEvent){var n="";switch(t){case"click":case"mouseenter":case"mouseleave":case"mousedown":case"mouseup":n="MouseEvents";break;case"focus":case"change":case"blur":case"select":n="HTMLEvents";break;default:throw"fireEvent: Couldn't find an event class for event '"+t+"'."}var i=a.createEvent(n),o="change"==t?!1:!0;i.initEvent(t,o,!0),i.synthetic=!0,e.dispatchEvent(i,!0)}else if(e.fireEvent){var i=a.createEventObject();i.synthetic=!0,e.fireEvent("on"+t,i)}},index:function(e){for(var t=e.parentNode.children,a=0;a<t.length;a++)if(t[a]==e)return a},trim:function(e){return e.trim()},eventTriggered:function(e){return e.currentTarget.dataset.triggered?!0:(e.currentTarget.dataset.triggered=!0,!1)},remove:function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},find:function(e,t){return e=KTUtil.getById(e),e?e.querySelector(t):void 0},findAll:function(e,t){return e=KTUtil.getById(e),e?e.querySelectorAll(t):void 0},insertAfter:function(e,t){return t.parentNode.insertBefore(e,t.nextSibling)},parents:function(e,t){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),a=t.length;--a>=0&&t.item(a)!==this;);return a>-1});for(var a=[];e&&e!==document;e=e.parentNode)t?e.matches(t)&&a.push(e):a.push(e);return a},children:function(e,t,a){if(e&&e.childNodes){for(var n,i=[],n=0,o=e.childNodes.length;o>n;++n)1==e.childNodes[n].nodeType&&KTUtil.matches(e.childNodes[n],t,a)&&i.push(e.childNodes[n]);return i}},child:function(e,t,a){var n=KTUtil.children(e,t,a);return n?n[0]:null},matches:function(e,t,a){var n=Element.prototype,i=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.msMatchesSelector||function(e){return-1!==[].indexOf.call(document.querySelectorAll(e),this)};return e&&e.tagName?i.call(e,t):!1},data:function(e){return{set:function(t,a){e&&(void 0===e.customDataTag&&(window.KTUtilElementDataStoreID++,e.customDataTag=window.KTUtilElementDataStoreID),void 0===window.KTUtilElementDataStore[e.customDataTag]&&(window.KTUtilElementDataStore[e.customDataTag]={}),window.KTUtilElementDataStore[e.customDataTag][t]=a)},get:function(t){return e?void 0===e.customDataTag?null:this.has(t)?window.KTUtilElementDataStore[e.customDataTag][t]:null:void 0},has:function(t){return e?void 0===e.customDataTag?!1:window.KTUtilElementDataStore[e.customDataTag]&&window.KTUtilElementDataStore[e.customDataTag][t]?!0:!1:!1},remove:function(t){e&&this.has(t)&&delete window.KTUtilElementDataStore[e.customDataTag][t]}}},outerWidth:function(e,t){var a;return t===!0?(a=parseFloat(e.offsetWidth),a+=parseFloat(KTUtil.css(e,"margin-left"))+parseFloat(KTUtil.css(e,"margin-right")),parseFloat(a)):a=parseFloat(e.offsetWidth)},offset:function(e){var t,a;if(e)return e.getClientRects().length?(t=e.getBoundingClientRect(),a=e.ownerDocument.defaultView,{top:t.top+a.pageYOffset,left:t.left+a.pageXOffset}):{top:0,left:0}},height:function(e){return KTUtil.css(e,"height")},outerHeight:function(e,t){var a,n=e.offsetHeight;return"undefined"!=typeof t&&t===!0?(a=getComputedStyle(e),n+=parseInt(a.marginTop)+parseInt(a.marginBottom)):n},visible:function(e){return!(0===e.offsetWidth&&0===e.offsetHeight)},attr:function(e,t,a){return void 0!=e?void 0===a?e.getAttribute(t):void e.setAttribute(t,a):void 0},hasAttr:function(e,t){return void 0!=e?e.getAttribute(t)?!0:!1:void 0},removeAttr:function(e,t){void 0!=e&&e.removeAttribute(t)},animate:function(e,t,a,n,i,o){function r(l){var u=(l||+new Date)-c;u>=0&&n(i(u,e,s,a)),u>=0&&u>=a?(n(t),o()):d(r)}var i,l={};if(l.linear=function(e,t,a,n){return a*e/n+t},i=l.linear,"number"==typeof e&&"number"==typeof t&&"number"==typeof a&&"function"==typeof n){"function"!=typeof o&&(o=function(){});var d=window.requestAnimationFrame||function(e){window.setTimeout(e,20)},s=t-e;n(e);var c=window.performance&&window.performance.now?window.performance.now():+new Date;d(r)}},actualCss:function(e,t,a){var n="";if(e instanceof HTMLElement!=!1){if(e.getAttribute("kt-hidden-"+t)&&a!==!1)return parseFloat(e.getAttribute("kt-hidden-"+t));var i;return n=e.style.cssText,e.style.cssText="position: absolute; visibility: hidden; display: block;","width"==t?i=e.offsetWidth:"height"==t&&(i=e.offsetHeight),e.style.cssText=n,e.setAttribute("kt-hidden-"+t,i),parseFloat(i)}},actualHeight:function(e,t){return KTUtil.actualCss(e,"height",t)},actualWidth:function(e,t){return KTUtil.actualCss(e,"width",t)},getScroll:function(e,t){return t="scroll"+t,e==window||e==document?self["scrollTop"==t?"pageYOffset":"pageXOffset"]||browserSupportsBoxModel&&document.documentElement[t]||document.body[t]:e[t]},css:function(e,t,a){if(e)if(void 0!==a)e.style[t]=a;else{var n=(e.ownerDocument||document).defaultView;if(n&&n.getComputedStyle)return t=t.replace(/([A-Z])/g,"-$1").toLowerCase(),n.getComputedStyle(e,null).getPropertyValue(t);if(e.currentStyle)return t=t.replace(/\-(\w)/g,function(e,t){return t.toUpperCase()}),a=e.currentStyle[t],/^\d+(em|pt|%|ex)?$/i.test(a)?function(t){var a=e.style.left,n=e.runtimeStyle.left;return e.runtimeStyle.left=e.currentStyle.left,e.style.left=t||0,t=e.style.pixelLeft+"px",e.style.left=a,e.runtimeStyle.left=n,t}(a):a}},slide:function(e,t,a,n,i){if(!(!e||"up"==t&&KTUtil.visible(e)===!1||"down"==t&&KTUtil.visible(e)===!0)){a=a?a:600;var o=KTUtil.actualHeight(e),r=!1,l=!1;KTUtil.css(e,"padding-top")&&KTUtil.data(e).has("slide-padding-top")!==!0&&KTUtil.data(e).set("slide-padding-top",KTUtil.css(e,"padding-top")),KTUtil.css(e,"padding-bottom")&&KTUtil.data(e).has("slide-padding-bottom")!==!0&&KTUtil.data(e).set("slide-padding-bottom",KTUtil.css(e,"padding-bottom")),KTUtil.data(e).has("slide-padding-top")&&(r=parseInt(KTUtil.data(e).get("slide-padding-top"))),KTUtil.data(e).has("slide-padding-bottom")&&(l=parseInt(KTUtil.data(e).get("slide-padding-bottom"))),"up"==t?(e.style.cssText="display: block; overflow: hidden;",r&&KTUtil.animate(0,r,a,function(t){e.style.paddingTop=r-t+"px"},"linear"),l&&KTUtil.animate(0,l,a,function(t){e.style.paddingBottom=l-t+"px"},"linear"),KTUtil.animate(0,o,a,function(t){e.style.height=o-t+"px"},"linear",function(){e.style.height="",e.style.display="none","function"==typeof n&&n()})):"down"==t&&(e.style.cssText="display: block; overflow: hidden;",r&&KTUtil.animate(0,r,a,function(t){e.style.paddingTop=t+"px"},"linear",function(){e.style.paddingTop=""}),l&&KTUtil.animate(0,l,a,function(t){e.style.paddingBottom=t+"px"},"linear",function(){e.style.paddingBottom=""}),KTUtil.animate(0,o,a,function(t){e.style.height=t+"px"},"linear",function(){e.style.height="",e.style.display="",e.style.overflow="","function"==typeof n&&n()}))}},slideUp:function(e,t,a){KTUtil.slide(e,"up",t,a)},slideDown:function(e,t,a){KTUtil.slide(e,"down",t,a)},show:function(e,t){"undefined"!=typeof e&&(e.style.display=t?t:"block")},hide:function(e){"undefined"!=typeof e&&(e.style.display="none")},addEvent:function(e,t,a,n){"undefined"!=typeof e&&null!==e&&e.addEventListener(t,a)},removeEvent:function(e,t,a){null!==e&&e.removeEventListener(t,a)},on:function(e,t,a,n){if(t){var i=KTUtil.getUniqueID("event");return window.KTUtilDelegatedEventHandlers[i]=function(a){for(var i=e.querySelectorAll(t),o=a.target;o&&o!==e;){for(var r=0,l=i.length;l>r;r++)o===i[r]&&n.call(o,a);o=o.parentNode}},KTUtil.addEvent(e,a,window.KTUtilDelegatedEventHandlers[i]),i}},off:function(e,t,a){e&&window.KTUtilDelegatedEventHandlers[a]&&(KTUtil.removeEvent(e,t,window.KTUtilDelegatedEventHandlers[a]),delete window.KTUtilDelegatedEventHandlers[a])},one:function(e,t,a){e.addEventListener(t,function n(t){return t.target&&t.target.removeEventListener&&t.target.removeEventListener(t.type,n),e&&e.removeEventListener&&t.currentTarget.removeEventListener(t.type,n),a(t)})},hash:function(e){var t,a,n=0;if(0===e.length)return n;for(t=0;t<e.length;t++)a=e.charCodeAt(t),n=(n<<5)-n+a,n|=0;return n},animateClass:function(e,t,a){var n,i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var o in i)void 0!==e.style[o]&&(n=i[o]);KTUtil.addClass(e,"animated "+t),KTUtil.one(e,n,function(){KTUtil.removeClass(e,"animated "+t)}),a&&KTUtil.one(e,n,a)},transitionEnd:function(e,t){var a,n={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"mozTransitionEnd",WebkitTransition:"webkitTransitionEnd",msTransition:"msTransitionEnd"};for(var i in n)void 0!==e.style[i]&&(a=n[i]);KTUtil.one(e,a,t)},animationEnd:function(e,t){var a,n={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var i in n)void 0!==e.style[i]&&(a=n[i]);KTUtil.one(e,a,t)},animateDelay:function(e,t){for(var a=["webkit-","moz-","ms-","o-",""],n=0;n<a.length;n++)KTUtil.css(e,a[n]+"animation-delay",t)},animateDuration:function(e,t){for(var a=["webkit-","moz-","ms-","o-",""],n=0;n<a.length;n++)KTUtil.css(e,a[n]+"animation-duration",t)},scrollTo:function(e,t,a){var n,i,a=a?a:500,o=e?KTUtil.offset(e).top:0,r=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;t&&(r+=t),n=r,i=o,KTUtil.animate(n,i,a,function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e})},scrollTop:function(e,t){KTUtil.scrollTo(null,e,t)},isArray:function(e){return e&&Array.isArray(e)},ready:function(e){(document.attachEvent?"complete"===document.readyState:"loading"!==document.readyState)?e():document.addEventListener("DOMContentLoaded",e)},isEmpty:function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},numberString:function(e){e+="";for(var t=e.split("."),a=t[0],n=t.length>1?"."+t[1]:"",i=/(\d+)(\d{3})/;i.test(a);)a=a.replace(i,"$1,$2");return a+n},detectIE:function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var a=e.indexOf("Trident/");if(a>0){var n=e.indexOf("rv:");return parseInt(e.substring(n+3,e.indexOf(".",n)),10)}var i=e.indexOf("Edge/");return i>0?parseInt(e.substring(i+5,e.indexOf(".",i)),10):!1},isRTL:function(){var e=KTUtil.getByTagName("html")[0];return e?"rtl"==KTUtil.attr(e,"direction"):void 0},scrollInit:function(e,t){function a(){var a,n,i=e.getAttributeNames();if(i.length>0&&i.forEach(function(a){if(/^data-.*/g.test(a)&&0==["scroll","height","mobile-height"].includes(n)){var n=a.replace("data-","").toLowerCase().replace(/(?:[\s-])\w/g,function(e){return e.replace("-","").toUpperCase()});t[n]=KTUtil.filterBoolean(e.getAttribute(a))}}),n=t.height instanceof Function?t.height.call():KTUtil.isMobileDevice()===!0&&t.mobileHeight?parseInt(t.mobileHeight):t.height?parseInt(t.height):parseInt(KTUtil.css(e,"height")),n===!1)return void KTUtil.scrollDestroy(e,!0);if(n=parseInt(n),(t.mobileNativeScroll||t.disableForMobile)&&KTUtil.isMobileDevice()===!0)return a=KTUtil.data(e).get("ps"),void(a?(t.resetHeightOnDestroy?KTUtil.css(e,"height","auto"):(KTUtil.css(e,"overflow","auto"),n>0&&KTUtil.css(e,"height",n+"px")),a.destroy(),a=KTUtil.data(e).remove("ps")):n>0&&(KTUtil.css(e,"overflow","auto"),KTUtil.css(e,"height",n+"px")));if(n>0&&KTUtil.css(e,"height",n+"px"),t.desktopNativeScroll)return void KTUtil.css(e,"overflow","auto");"true"==KTUtil.attr(e,"data-window-scroll")&&(t.windowScroll=!0),a=KTUtil.data(e).get("ps"),a?a.update():(KTUtil.css(e,"overflow","hidden"),KTUtil.addClass(e,"scroll"),a=new PerfectScrollbar(e,t),KTUtil.data(e).set("ps",a));var o=KTUtil.attr(e,"id");if(t.rememberPosition===!0&&KTCookie&&o){if(KTCookie.getCookie(o)){var r=parseInt(KTCookie.getCookie(o));r>0&&(e.scrollTop=r)}e.addEventListener("ps-scroll-y",function(){KTCookie.setCookie(o,e.scrollTop)})}}if(e){var n={wheelSpeed:.5,swipeEasing:!0,wheelPropagation:!1,minScrollbarLength:40,maxScrollbarLength:300,suppressScrollX:!0};t=KTUtil.deepExtend({},n,t),a(),t.handleWindowResize&&KTUtil.addResizeHandler(function(){a()})}},scrollUpdate:function(e){var t=KTUtil.data(e).get("ps");t&&t.update()},scrollUpdateAll:function(e){for(var t=KTUtil.findAll(e,".ps"),a=0,n=t.length;n>a;a++)KTUtil.scrollUpdate(t[a])},scrollDestroy:function(e,t){var a=KTUtil.data(e).get("ps");a&&(a.destroy(),a=KTUtil.data(e).remove("ps")),e&&t&&(e.style.setProperty("overflow",""),e.style.setProperty("height",""))},filterBoolean:function(e){return e===!0||"true"===e?!0:e===!1||"false"===e?!1:e},setHTML:function(e,t){e.innerHTML=t},getHTML:function(e){return e?e.innerHTML:void 0},getDocumentHeight:function(){var e=document.body,t=document.documentElement;return Math.max(e.scrollHeight,e.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight)},getScrollTop:function(){return(document.scrollingElement||document.documentElement).scrollTop},changeColor:function(e,t){var a=!1;"#"==e[0]&&(e=e.slice(1),a=!0);var n=parseInt(e,16),i=(n>>16)+t;i>255?i=255:0>i&&(i=0);var o=(n>>8&255)+t;o>255?o=255:0>o&&(o=0);var r=(255&n)+t;return r>255?r=255:0>r&&(r=0),(a?"#":"")+(r|o<<8|i<<16).toString(16)},throttle:function(e,t,a){e||(e=setTimeout(function(){t(),e=void 0},a))},debounce:function(e,t,a){clearTimeout(e),e=setTimeout(t,a)},btnWait:function(e,t,a,n){if(e&&("undefined"!=typeof n&&n===!0&&KTUtil.attr(e,"disabled",!0),t&&(KTUtil.addClass(e,t),KTUtil.attr(e,"wait-class",t)),a)){var i=KTUtil.find(e,".btn-caption");i?(KTUtil.data(i).set("caption",KTUtil.getHTML(i)),KTUtil.setHTML(i,a)):(KTUtil.data(e).set("caption",KTUtil.getHTML(e)),KTUtil.setHTML(e,a))}},btnRelease:function(e){if(e){KTUtil.removeAttr(e,"disabled"),KTUtil.hasAttr(e,"wait-class")&&KTUtil.removeClass(e,KTUtil.attr(e,"wait-class"));var t=KTUtil.find(e,".btn-caption");t&&KTUtil.data(t).has("caption")?KTUtil.setHTML(t,KTUtil.data(t).get("caption")):KTUtil.data(e).has("caption")&&KTUtil.setHTML(e,KTUtil.data(e).get("caption"))}},isOffscreen:function(e,t,a){a=a||0;var n=KTUtil.getViewPort().width,i=KTUtil.getViewPort().height,o=KTUtil.offset(e).top,r=KTUtil.outerHeight(e)+a,l=KTUtil.offset(e).left,d=KTUtil.outerWidth(e)+a;if("bottom"==t){if(o+r>i)return!0;if(i>o+1.5*r)return!0}if("top"==t){if(0>o)return!0;if(o>r)return!0}return"left"==t&&0>l?!0:"right"==t&&l+d>n?!0:!1}}}();"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTUtil),KTUtil.ready(function(){"undefined"!=typeof KTAppSettings?KTUtil.init(KTAppSettings):KTUtil.init()}),window.onload=function(){var e=KTUtil.getByTagName("body");e&&e[0]&&KTUtil.removeClass(e[0],"page-loading")};var KTWizard=function(e,t){var a=this,n=KTUtil.getById(e);KTUtil.getBody();if(n){var i={startStep:1,clickableSteps:!1},o={construct:function(e){return KTUtil.data(n).has("wizard")?a=KTUtil.data(n).get("wizard"):(o.init(e),o.build(),KTUtil.data(n).set("wizard",a)),a},init:function(e){a.element=n,a.events=[],a.options=KTUtil.deepExtend({},i,e),a.steps=KTUtil.findAll(n,'[data-wizard-type="step"]'),a.btnNext=KTUtil.find(n,'[data-wizard-type="action-next"]'),a.btnPrev=KTUtil.find(n,'[data-wizard-type="action-prev"]'),a.btnSubmit=KTUtil.find(n,'[data-wizard-type="action-submit"]'),a.events=[],a.lastStep=0,a.currentStep=1,a.newStep=0,a.stopped=!1,a.totalSteps=a.steps.length,a.options.startStep>1&&o.goTo(a.options.startStep),o.updateUI()},build:function(){KTUtil.addEvent(a.btnNext,"click",function(e){e.preventDefault(),o.setNewStep(o.getNextStep()),o.eventTrigger("change")!==!1&&o.goTo(o.getNextStep())}),KTUtil.addEvent(a.btnPrev,"click",function(e){e.preventDefault(),o.setNewStep(o.getPrevStep()),o.eventTrigger("change")!==!1&&o.goTo(o.getPrevStep())}),a.options.clickableSteps===!0&&KTUtil.on(n,'[data-wizard-type="step"]',"click",function(){var e=KTUtil.index(this)+1;e!==a.currentStep&&(o.setNewStep(e),o.eventTrigger("change")!==!1&&o.goTo(e))}),KTUtil.addEvent(a.btnSubmit,"click",function(e){e.preventDefault(),o.eventTrigger("submit")})},goTo:function(e){if(a.stopped===!0)return void(a.stopped=!1);if(!(e===a.currentStep||e>a.totalSteps||0>e))return e=parseInt(e),a.lastStep=a.currentStep,a.currentStep=e,a.newStep=0,o.updateUI(),o.eventTrigger("changed"),a},stop:function(){a.stopped=!0},resume:function(){a.stopped=!1},isLastStep:function(){return a.currentStep===a.totalSteps},isFirstStep:function(){return 1===a.currentStep},isBetweenStep:function(){return o.isLastStep()===!1&&o.isFirstStep()===!1},updateUI:function(){var e="",t=a.currentStep-1;e=o.isLastStep()?"last":o.isFirstStep()?"first":"between",KTUtil.attr(a.element,"data-wizard-state",e);var n=KTUtil.findAll(a.element,'[data-wizard-type="step"]');if(n&&n.length>0)for(var i=0,r=n.length;r>i;i++)i==t?KTUtil.attr(n[i],"data-wizard-state","current"):t>i?KTUtil.attr(n[i],"data-wizard-state","done"):KTUtil.attr(n[i],"data-wizard-state","pending");var l=KTUtil.findAll(a.element,'[data-wizard-type="step-info"]');if(l&&l.length>0)for(var i=0,r=l.length;r>i;i++)i==t?KTUtil.attr(l[i],"data-wizard-state","current"):KTUtil.removeAttr(l[i],"data-wizard-state");var d=KTUtil.findAll(a.element,'[data-wizard-type="step-content"]');if(d&&d.length>0)for(var i=0,r=d.length;r>i;i++)i==t?KTUtil.attr(d[i],"data-wizard-state","current"):KTUtil.removeAttr(d[i],"data-wizard-state")},getNextStep:function(){return a.totalSteps>=a.currentStep+1?a.currentStep+1:a.totalSteps},getPrevStep:function(){return a.currentStep-1>=1?a.currentStep-1:1},getNewStep:function(){return a.newStep},setNewStep:function(e){a.newStep=e},eventTrigger:function(e,t){for(var n=0;n<a.events.length;n++){var i=a.events[n];if(i.name==e){if(1!=i.one)return i.handler.call(this,a);if(0==i.fired)return a.events[n].fired=!0,i.handler.call(this,a)}}},addEvent:function(e,t,n){return a.events.push({name:e,handler:t,one:n,fired:!1}),a}};return a.setDefaults=function(e){i=e},a.goNext=function(){return o.goTo(o.getNextStep())},a.goPrev=function(){return o.goTo(o.getPrevStep())},a.goLast=function(){return o.goTo(o.getLastStep())},a.goFirst=function(){return o.goTo(o.getFirstStep())},a.goTo=function(e){return o.goTo(e)},a.stop=function(){return o.stop()},a.resume=function(){return o.resume()},a.getStep=function(){return a.currentStep},a.getNewStep=function(){return o.getNewStep()},a.setNewStep=function(e){o.setNewStep(e)},a.isLastStep=function(){return o.isLastStep()},a.isFirstStep=function(){return o.isFirstStep()},a.on=function(e,t){return o.addEvent(e,t)},a.one=function(e,t){return o.addEvent(e,t,!0)},o.construct.apply(a,[t]),a}};"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTWizard),function(e){var t="KTDatatable",a="",n=KTUtil,i=KTApp;if("undefined"==typeof n)throw new Error("Util class is required and must be included before "+t);e.fn[t]=function(o){if(0===e(this).length)return void console.warn("No "+t+" element exist.");var r=this;r.debug=!1,r.API={record:null,value:null,params:null};var l={isInit:!1,cellOffset:110,iconOffset:15,stateId:"meta",ajaxParams:{},pagingObject:{},init:function(n){var i=!1;null===n.data.source&&(l.extractTable(),i=!0),l.setupBaseDOM.call(),l.setupDOM(r.table),e(r).on(a+"datatable-on-layout-updated",l.afterRender),r.debug&&l.stateRemove(l.stateId),l.setDataSourceQuery(l.getOption("data.source.read.params.query")),e.each(l.getOption("extensions"),function(a,n){"function"==typeof e.fn[t][a]&&("object"!=typeof n&&(n=e.extend({},n)),new e.fn[t][a](r,n))}),l.spinnerCallback(!0),("remote"===n.data.type||"local"===n.data.type)&&(n.data.saveState===!1&&l.stateRemove(l.stateId),"local"===n.data.type&&"object"==typeof n.data.source&&(r.dataSet=r.originalDataSet=l.dataMapCallback(n.data.source)),l.dataRender()),i&&(e(r.tableHead).find("tr").remove(),e(r.tableFoot).find("tr").remove()),l.setHeadTitle(),l.getOption("layout.footer")&&l.setHeadTitle(r.tableFoot),"undefined"!=typeof n.layout.header&&n.layout.header===!1&&e(r.table).find("thead").remove(),"undefined"!=typeof n.layout.footer&&n.layout.footer===!1&&e(r.table).find("tfoot").remove(),(null===n.data.type||"local"===n.data.type)&&(l.setupCellField.call(),l.setupTemplateCell.call(),l.setupSubDatatable.call(),l.setupSystemColumn.call(),l.redraw());var o,d=!1;e(window).resize(function(){e(this).width()!==o&&(o=e(this).width(),l.fullRender()),d||(o=e(this).width(),d=!0)}),e(r).height("");var s="";return e(l.getOption("search.input")).on("keyup",function(t){if(!l.getOption("search.onEnter")||13===t.which){var a=e(this).val();s!==a&&(l.search(a),s=a)}}),r},extractTable:function(){var t=[],a=e(r).find("tr:first-child th").get().map(function(a,n){var i=e(a).data("field"),r=e(a).data("title");"undefined"==typeof i&&(i=e(a).text().trim()),"undefined"==typeof r&&(r=e(a).text().trim());var l={field:i,title:r};for(var d in o.columns)o.columns[d].field===i&&(l=e.extend(!0,{},o.columns[d],l));return t.push(l),i});o.columns=t;var i=[],l=[];e(r).find("tr").each(function(){e(this).find("td").length&&i.push(e(this).prop("attributes"));var t={};e(this).find("td").each(function(e,n){t[a[e]]=n.innerHTML.trim()}),n.isEmpty(t)||l.push(t)}),o.data.attr.rowProps=i,o.data.source=l},layoutUpdate:function(){l.setupSubDatatable.call(),l.setupSystemColumn.call(),l.setupHover.call(),"undefined"==typeof o.detail&&1===l.getDepth()&&l.lockTable.call(),l.resetScroll(),l.isLocked()||(l.redraw.call(),l.isSubtable()||l.getOption("rows.autoHide")!==!0||l.autoHide(),e(r.table).find("."+a+"datatable-row").css("height","")),l.columnHide.call(),l.rowEvenOdd.call(),l.sorting.call(),l.scrollbar.call(),l.isInit||(l.dropdownFix(),e(r).trigger(a+"datatable-on-init",{table:e(r.wrap).attr("id"),options:o}),l.isInit=!0),e(r).trigger(a+"datatable-on-layout-updated",{table:e(r.wrap).attr("id")})},dropdownFix:function(){var t;e("body").on("show.bs.dropdown","."+a+"datatable ."+a+"datatable-body",function(a){t=e(a.target).find(".dropdown-menu"),e("body").append(t.detach()),t.css("display","block"),t.position({my:"right top",at:"right bottom",of:e(a.relatedTarget)}),r.closest(".modal").length&&t.css("z-index","2000")}).on("hide.bs.dropdown","."+a+"datatable ."+a+"datatable-body",function(a){e(a.target).append(t.detach()),t.hide()}),e(window).on("resize",function(e){"undefined"!=typeof t&&t.hide()})},lockTable:function(){var t={lockEnabled:!1,init:function(){t.lockEnabled=l.lockEnabledColumns(),(0!==t.lockEnabled.left.length||0!==t.lockEnabled.right.length)&&t.enable()},enable:function(){var n=function(n){if(e(n).find("."+a+"datatable-lock").length>0)return void l.log("Locked container already exist in: ",n);if(0===e(n).find("."+a+"datatable-row").length)return void l.log("No row exist in: ",n);var i=e("<div/>").addClass(a+"datatable-lock "+a+"datatable-lock-left"),o=e("<div/>").addClass(a+"datatable-lock "+a+"datatable-lock-scroll"),d=e("<div/>").addClass(a+"datatable-lock "+a+"datatable-lock-right");e(n).find("."+a+"datatable-row").each(function(){var t=e("<tr/>").addClass(a+"datatable-row").data("obj",e(this).data("obj")).appendTo(i),n=e("<tr/>").addClass(a+"datatable-row").data("obj",e(this).data("obj")).appendTo(o),r=e("<tr/>").addClass(a+"datatable-row").data("obj",e(this).data("obj")).appendTo(d);e(this).find("."+a+"datatable-cell").each(function(){var a=e(this).data("locked");"undefined"!=typeof a?(("undefined"!=typeof a.left||a===!0)&&e(this).appendTo(t),"undefined"!=typeof a.right&&e(this).appendTo(r)):e(this).appendTo(n)}),e(this).remove()}),t.lockEnabled.left.length>0&&(e(r.wrap).addClass(a+"datatable-lock"),e(i).appendTo(n)),(t.lockEnabled.left.length>0||t.lockEnabled.right.length>0)&&e(o).appendTo(n),t.lockEnabled.right.length>0&&(e(r.wrap).addClass(a+"datatable-lock"),e(d).appendTo(n))};e(r.table).find("thead,tbody,tfoot").each(function(){var t=this;0===e(this).find("."+a+"datatable-lock").length&&e(this).ready(function(){n(t)})})}};return t.init(),t},fullRender:function(){e(r.tableHead).empty(),l.setHeadTitle(),l.getOption("layout.footer")&&(e(r.tableFoot).empty(),
        l.setHeadTitle(r.tableFoot)),l.spinnerCallback(!0),e(r.wrap).removeClass(a+"datatable-loaded"),l.insertData()},lockEnabledColumns:function(){var t=e(window).width(),a=o.columns,i={left:[],right:[]};return e.each(a,function(e,a){"undefined"!=typeof a.locked&&("undefined"!=typeof a.locked.left&&n.getBreakpoint(a.locked.left)<=t&&i.left.push(a.locked.left),"undefined"!=typeof a.locked.right&&n.getBreakpoint(a.locked.right)<=t&&i.right.push(a.locked.right))}),i},afterRender:function(t,n){e(r).ready(function(){l.isLocked()&&l.redraw(),e(r.tableBody).css("visibility",""),e(r.wrap).addClass(a+"datatable-loaded"),l.spinnerCallback(!1)})},hoverTimer:0,isScrolling:!1,setupHover:function(){e(window).scroll(function(e){clearTimeout(l.hoverTimer),l.isScrolling=!0}),e(r.tableBody).find("."+a+"datatable-cell").off("mouseenter","mouseleave").on("mouseenter",function(){if(l.hoverTimer=setTimeout(function(){l.isScrolling=!1},200),!l.isScrolling){var t=e(this).closest("."+a+"datatable-row").addClass(a+"datatable-row-hover"),n=e(t).index()+1;e(t).closest("."+a+"datatable-lock").parent().find("."+a+"datatable-row:nth-child("+n+")").addClass(a+"datatable-row-hover")}}).on("mouseleave",function(){var t=e(this).closest("."+a+"datatable-row").removeClass(a+"datatable-row-hover"),n=e(t).index()+1;e(t).closest("."+a+"datatable-lock").parent().find("."+a+"datatable-row:nth-child("+n+")").removeClass(a+"datatable-row-hover")})},adjustLockContainer:function(){if(!l.isLocked())return 0;var t=e(r.tableHead).width(),n=e(r.tableHead).find("."+a+"datatable-lock-left").width(),i=e(r.tableHead).find("."+a+"datatable-lock-right").width();"undefined"==typeof n&&(n=0),"undefined"==typeof i&&(i=0);var o=Math.floor(t-n-i);return e(r.table).find("."+a+"datatable-lock-scroll").css("width",o),o},dragResize:function(){var t,n,i=!1,o=void 0;e(r.tableHead).find("."+a+"datatable-cell").mousedown(function(r){o=e(this),i=!0,t=r.pageX,n=e(this).width(),e(o).addClass(a+"datatable-cell-resizing")}).mousemove(function(l){if(i){var d=e(o).index(),s=e(r.tableBody),c=e(o).closest("."+a+"datatable-lock");if(c){var u=e(c).index();s=e(r.tableBody).find("."+a+"datatable-lock").eq(u)}e(s).find("."+a+"datatable-row").each(function(i,o){e(o).find("."+a+"datatable-cell").eq(d).width(n+(l.pageX-t)).children().width(n+(l.pageX-t))}),e(o).children().css("width",n+(l.pageX-t))}}).mouseup(function(){e(o).removeClass(a+"datatable-cell-resizing"),i=!1}),e(document).mouseup(function(){e(o).removeClass(a+"datatable-cell-resizing"),i=!1})},initHeight:function(){if(o.layout.height&&o.layout.scroll){var t=e(r.tableHead).find("."+a+"datatable-row").outerHeight(),n=e(r.tableFoot).find("."+a+"datatable-row").outerHeight(),i=o.layout.height;t>0&&(i-=t),n>0&&(i-=n),i-=2,e(r.tableBody).css("max-height",Math.floor(parseFloat(i)))}},setupBaseDOM:function(){r.initialDatatable=e(r).clone(),"TABLE"===e(r).prop("tagName")?(r.table=e(r).removeClass(a+"datatable").addClass(a+"datatable-table"),0===e(r.table).parents("."+a+"datatable").length&&(r.table.wrap(e("<div/>").addClass(a+"datatable").addClass(a+"datatable-"+o.layout.theme)),r.wrap=e(r.table).parent())):(r.wrap=e(r).addClass(a+"datatable").addClass(a+"datatable-"+o.layout.theme),r.table=e("<table/>").addClass(a+"datatable-table").appendTo(r)),"undefined"!=typeof o.layout["class"]&&e(r.wrap).addClass(o.layout["class"]),e(r.table).removeClass(a+"datatable-destroyed").css("display","block"),"undefined"==typeof e(r).attr("id")&&(l.setOption("data.saveState",!1),e(r.table).attr("id",n.getUniqueID(a+"datatable-"))),l.getOption("layout.minHeight")&&e(r.table).css("min-height",l.getOption("layout.minHeight")),l.getOption("layout.height")&&e(r.table).css("max-height",l.getOption("layout.height")),null===o.data.type&&e(r.table).css("width","").css("display",""),r.tableHead=e(r.table).find("thead"),0===e(r.tableHead).length&&(r.tableHead=e("<thead/>").prependTo(r.table)),r.tableBody=e(r.table).find("tbody"),0===e(r.tableBody).length&&(r.tableBody=e("<tbody/>").appendTo(r.table)),"undefined"!=typeof o.layout.footer&&o.layout.footer&&(r.tableFoot=e(r.table).find("tfoot"),0===e(r.tableFoot).length&&(r.tableFoot=e("<tfoot/>").appendTo(r.table)))},setupCellField:function(t){"undefined"==typeof t&&(t=e(r.table).children());var n=o.columns;e.each(t,function(t,i){e(i).find("."+a+"datatable-row").each(function(t,i){e(i).find("."+a+"datatable-cell").each(function(t,a){"undefined"!=typeof n[t]&&e(a).data(n[t])})})})},setupTemplateCell:function(t){"undefined"==typeof t&&(t=r.tableBody);var n=o.columns;e(t).find("."+a+"datatable-row").each(function(t,i){var o=e(i).data("obj");if("undefined"!=typeof o){var d=l.getOption("rows.callback");"function"==typeof d&&d(e(i),o,t);var s=l.getOption("rows.beforeTemplate");"function"==typeof s&&s(e(i),o,t),"undefined"==typeof o&&(o={},e(i).find("."+a+"datatable-cell").each(function(t,a){var i=e.grep(n,function(t,n){return e(a).data("field")===t.field})[0];"undefined"!=typeof i&&(o[i.field]=e(a).text())})),e(i).find("."+a+"datatable-cell").each(function(a,i){var d=e.grep(n,function(t,a){return e(i).data("field")===t.field})[0];if("undefined"!=typeof d&&"undefined"!=typeof d.template){var s="";"string"==typeof d.template&&(s=l.dataPlaceholder(d.template,o)),"function"==typeof d.template&&(s=d.template(o,t,r)),"undefined"!=typeof DOMPurify&&(s=DOMPurify.sanitize(s));var c=document.createElement("span");c.innerHTML=s,e(i).html(c),"undefined"!=typeof d.overflow&&(e(c).css("overflow",d.overflow),e(c).css("position","relative"))}});var c=l.getOption("rows.afterTemplate");"function"==typeof c&&c(e(i),o,t)}})},setupSystemColumn:function(){if(r.dataSet=r.dataSet||[],0!==r.dataSet.length){var t=o.columns;e(r.tableBody).find("."+a+"datatable-row").each(function(n,i){e(i).find("."+a+"datatable-cell").each(function(n,i){var o=e.grep(t,function(t,a){return e(i).data("field")===t.field})[0];if("undefined"!=typeof o){var r=e(i).text();if("undefined"!=typeof o.selector&&o.selector!==!1){if(e(i).find("."+a+'checkbox [type="checkbox"]').length>0)return;e(i).addClass(a+"datatable-cell-check");var d=e("<label/>").addClass(a+"checkbox "+a+"checkbox-single").append(e("<input/>").attr("type","checkbox").attr("value",r).on("click",function(){e(this).is(":checked")?l.setActive(this):l.setInactive(this)})).append("&nbsp;<span></span>");"undefined"!=typeof o.selector["class"]&&e(d).addClass(o.selector["class"]),e(i).children().html(d)}if("undefined"!=typeof o.subtable&&o.subtable){if(e(i).find("."+a+"datatable-toggle-subtable").length>0)return;e(i).children().html(e("<a/>").addClass(a+"datatable-toggle-subtable").attr("href","#").attr("data-value",r).append(e("<i/>").addClass(l.getOption("layout.icons.rowDetail.collapse"))))}}})});var n=function(n){var i=e.grep(t,function(e,t){return"undefined"!=typeof e.selector&&e.selector!==!1})[0];if("undefined"!=typeof i&&"undefined"!=typeof i.selector&&i.selector!==!1){var o=e(n).find('[data-field="'+i.field+'"]');if(e(o).find("."+a+'checkbox [type="checkbox"]').length>0)return;e(o).addClass(a+"datatable-cell-check");var r=e("<label/>").addClass(a+"checkbox "+a+"checkbox-single "+a+"checkbox-all").append(e("<input/>").attr("type","checkbox").on("click",function(){e(this).is(":checked")?l.setActiveAll(!0):l.setActiveAll(!1)})).append("&nbsp;<span></span>");"undefined"!=typeof i.selector["class"]&&e(r).addClass(i.selector["class"]),e(o).children().html(r)}};o.layout.header&&n(e(r.tableHead).find("."+a+"datatable-row").first()),o.layout.footer&&n(e(r.tableFoot).find("."+a+"datatable-row").first())}},maxWidthList:{},adjustCellsWidth:function(){var t=e(r.tableBody).innerWidth()-l.iconOffset,n=e(r.tableHead).find("."+a+"datatable-row:first-child").find("."+a+"datatable-cell").not("."+a+"datatable-toggle-detail").not(":hidden").length;if(n>0){t-=l.iconOffset*n;var i=Math.floor(t/n);i<=l.cellOffset&&(i=l.cellOffset),e(r.table).find("."+a+"datatable-row").find("."+a+"datatable-cell").not("."+a+"datatable-toggle-detail").not(":hidden").each(function(t,n){var o=i,d=e(n).data("width");if("undefined"!=typeof d)if("auto"===d){var s=e(n).data("field");if(l.maxWidthList[s])o=l.maxWidthList[s];else{var c=e(r.table).find("."+a+'datatable-cell[data-field="'+s+'"]');o=l.maxWidthList[s]=Math.max.apply(null,e(c).map(function(){return e(this).outerWidth()}).get())}}else o=d;e(n).children().css("width",Math.ceil(o))})}return r},adjustCellsHeight:function(){e.each(e(r.table).children(),function(t,n){for(var i=e(n).find("."+a+"datatable-row").first().parent().find("."+a+"datatable-row").length,o=1;i>=o;o++){var r=e(n).find("."+a+"datatable-row:nth-child("+o+")");if(e(r).length>0){var l=Math.max.apply(null,e(r).map(function(){return e(this).outerHeight()}).get());e(r).css("height",Math.ceil(l))}}})},setupDOM:function(t){e(t).find("> thead").addClass(a+"datatable-head"),e(t).find("> tbody").addClass(a+"datatable-body"),e(t).find("> tfoot").addClass(a+"datatable-foot"),e(t).find("tr").addClass(a+"datatable-row"),e(t).find("tr > th, tr > td").addClass(a+"datatable-cell"),e(t).find("tr > th, tr > td").each(function(t,a){0===e(a).find("span").length&&e(a).wrapInner(e("<span/>").css("width",l.cellOffset))})},scrollbar:function(){var t={scrollable:null,tableLocked:null,initPosition:null,init:function(){var i=n.getViewPort().width;if(o.layout.scroll){e(r.wrap).addClass(a+"datatable-scroll");var d=e(r.tableBody).find("."+a+"datatable-lock-scroll");e(d).find("."+a+"datatable-row").length>0&&e(d).length>0?(t.scrollHead=e(r.tableHead).find("> ."+a+"datatable-lock-scroll > ."+a+"datatable-row"),t.scrollFoot=e(r.tableFoot).find("> ."+a+"datatable-lock-scroll > ."+a+"datatable-row"),t.tableLocked=e(r.tableBody).find("."+a+"datatable-lock:not(."+a+"datatable-lock-scroll)"),l.getOption("layout.customScrollbar")&&10!=n.detectIE()&&i>n.getBreakpoint("lg")?t.initCustomScrollbar(d[0]):t.initDefaultScrollbar(d)):e(r.tableBody).find("."+a+"datatable-row").length>0&&(t.scrollHead=e(r.tableHead).find("> ."+a+"datatable-row"),t.scrollFoot=e(r.tableFoot).find("> ."+a+"datatable-row"),l.getOption("layout.customScrollbar")&&10!=n.detectIE()&&i>n.getBreakpoint("lg")?t.initCustomScrollbar(r.tableBody):t.initDefaultScrollbar(r.tableBody))}},initDefaultScrollbar:function(a){t.initPosition=e(a).scrollLeft(),e(a).css("overflow-y","auto").off().on("scroll",t.onScrolling),e(a).css("overflow-x","auto")},onScrolling:function(a){var i=e(this).scrollLeft(),o=e(this).scrollTop();n.isRTL()&&(i-=t.initPosition),e(t.scrollHead).css("left",-i),e(t.scrollFoot).css("left",-i),e(t.tableLocked).each(function(t,a){l.isLocked()&&(o-=1),e(a).css("top",-o)})},initCustomScrollbar:function(a){t.scrollable=a,l.initScrollbar(a),t.initPosition=e(a).scrollLeft(),e(a).off().on("scroll",t.onScrolling)}};return t.init(),t},initScrollbar:function(t,a){if(t&&t.nodeName){e(r.tableBody).css("overflow","");var i=e(t).data("ps");n.hasClass(t,"ps")&&"undefined"!=typeof i?i.update():(i=new PerfectScrollbar(t,Object.assign({},{wheelSpeed:.5,swipeEasing:!0,minScrollbarLength:40,maxScrollbarLength:300,suppressScrollX:l.getOption("rows.autoHide")&&!l.isLocked()},a)),e(t).data("ps",i)),e(window).resize(function(){i.update()})}},setHeadTitle:function(t){"undefined"==typeof t&&(t=r.tableHead),t=e(t)[0];var a=o.columns,i=t.getElementsByTagName("tr")[0],d=t.getElementsByTagName("td");"undefined"==typeof i&&(i=document.createElement("tr"),t.appendChild(i)),e.each(a,function(t,a){var o=d[t];if("undefined"==typeof o&&(o=document.createElement("th"),i.appendChild(o)),"undefined"!=typeof a.title&&(o.innerHTML=a.title,o.setAttribute("data-field",a.field),n.addClass(o,a["class"]),"undefined"!=typeof a.autoHide&&(a.autoHide!==!0?o.setAttribute("data-autohide-disabled",a.autoHide):o.setAttribute("data-autohide-enabled",a.autoHide)),e(o).data(a)),"undefined"!=typeof a.attr&&e.each(a.attr,function(e,t){o.setAttribute(e,t)}),"undefined"!=typeof a.textAlign){var l="undefined"!=typeof r.textAlign[a.textAlign]?r.textAlign[a.textAlign]:"";n.addClass(o,l)}}),l.setupDOM(t)},dataRender:function(t){e(r.table).siblings("."+a+"datatable-pager").removeClass(a+"datatable-paging-loaded");var n=function(){r.dataSet=r.dataSet||[],l.localDataUpdate();var t=l.getDataSourceParam("pagination");0===t.perpage&&(t.perpage=o.data.pageSize||10),t.total=r.dataSet.length;var a=Math.max(t.perpage*(t.page-1),0),n=Math.min(a+t.perpage,t.total);return r.dataSet=e(r.dataSet).slice(a,n),t},i=function(t){var i=function(t,n){e(t.pager).hasClass(a+"datatable-paging-loaded")||(e(t.pager).remove(),t.init(n)),e(t.pager).off().on(a+"datatable-on-goto-page",function(a){e(t.pager).remove(),t.init(n)});var i=Math.max(n.perpage*(n.page-1),0),o=Math.min(i+n.perpage,n.total);l.localDataUpdate(),r.dataSet=e(r.dataSet).slice(i,o),l.insertData()};if(e(r.wrap).removeClass(a+"datatable-error"),o.pagination)if(o.data.serverPaging&&"local"!==o.data.type){var d=l.getObject("meta",t||null);null!==d?l.pagingObject=l.paging(d):l.pagingObject=l.paging(n(),i)}else l.pagingObject=l.paging(n(),i);else l.localDataUpdate();l.insertData()};return"local"===o.data.type||o.data.serverSorting===!1&&"sort"===t||o.data.serverFiltering===!1&&"search"===t?void setTimeout(function(){i(),l.setAutoColumns()}):void l.getData().done(i)},insertData:function(){r.dataSet=r.dataSet||[];var t=l.getDataSourceParam(),i=t.pagination,d=(Math.max(i.page,1)-1)*i.perpage,s=Math.min(i.page,i.pages)*i.perpage,c={};"undefined"!=typeof o.data.attr.rowProps&&o.data.attr.rowProps.length&&(c=o.data.attr.rowProps.slice(d,s));var u=document.createElement("tbody");u.style.visibility="hidden";var f=o.columns.length;if(e.each(r.dataSet,function(i,d){var s=document.createElement("tr");s.setAttribute("data-row",i),e(s).data("obj",d),"undefined"!=typeof c[i]&&e.each(c[i],function(){s.setAttribute(this.name,this.value)});for(var p=0;f>p;p+=1){var g=o.columns[p],m=[];if(l.getObject("sort.field",t)===g.field&&m.push(a+"datatable-cell-sorted"),"undefined"!=typeof g.textAlign){var h="undefined"!=typeof r.textAlign[g.textAlign]?r.textAlign[g.textAlign]:"";m.push(h)}"undefined"!=typeof g["class"]&&m.push(g["class"]);var v=document.createElement("td");n.addClass(v,m.join(" ")),v.setAttribute("data-field",g.field),"undefined"!=typeof g.autoHide&&(g.autoHide!==!0?v.setAttribute("data-autohide-disabled",g.autoHide):v.setAttribute("data-autohide-enabled",g.autoHide)),v.innerHTML=l.getObject(g.field,d),v.setAttribute("aria-label",l.getObject(g.field,d)),s.appendChild(v)}u.appendChild(s)}),0===r.dataSet.length){var p=document.createElement("span");n.addClass(p,a+"datatable-error"),p.innerHTML=l.getOption("translate.records.noRecords"),u.appendChild(p),e(r.wrap).addClass(a+"datatable-error "+a+"datatable-loaded"),l.spinnerCallback(!1)}e(r.tableBody).replaceWith(u),r.tableBody=u,l.setupDOM(r.table),l.setupCellField([r.tableBody]),l.setupTemplateCell(r.tableBody),l.layoutUpdate()},updateTableComponents:function(){r.tableHead=e(r.table).children("thead").get(0),r.tableBody=e(r.table).children("tbody").get(0),r.tableFoot=e(r.table).children("tfoot").get(0)},getData:function(){var t={dataType:"json",method:"POST",data:{},timeout:l.getOption("data.source.read.timeout")||3e4};if("local"===o.data.type&&(t.url=o.data.source),"remote"===o.data.type){var n=l.getDataSourceParam();l.getOption("data.serverPaging")||delete n.pagination,l.getOption("data.serverSorting")||delete n.sort,t.data=e.extend({},t.data,l.getOption("data.source.read.params"),n),t=e.extend({},t,l.getOption("data.source.read")),"string"!=typeof t.url&&(t.url=l.getOption("data.source.read")),"string"!=typeof t.url&&(t.url=l.getOption("data.source"))}return e.ajax(t).done(function(t,n,i){r.lastResponse=t,r.dataSet=r.originalDataSet=l.dataMapCallback(t),l.setAutoColumns(),e(r).trigger(a+"datatable-on-ajax-done",[r.dataSet])}).fail(function(t,n,i){e(r).trigger(a+"datatable-on-ajax-fail",[t]),e(r.tableBody).html(e("<span/>").addClass(a+"datatable-error").html(l.getOption("translate.records.noRecords"))),e(r.wrap).addClass(a+"datatable-error "+a+"datatable-loaded"),l.spinnerCallback(!1)}).always(function(){})},paging:function(t,i){var o={meta:null,pager:null,paginateEvent:null,pagerLayout:{pagination:null,info:null},callback:null,init:function(t){o.meta=t,o.meta.page=parseInt(o.meta.page),o.meta.pages=parseInt(o.meta.pages),o.meta.perpage=parseInt(o.meta.perpage),o.meta.total=parseInt(o.meta.total),o.meta.pages=Math.max(Math.ceil(o.meta.total/o.meta.perpage),1),o.meta.page>o.meta.pages&&(o.meta.page=o.meta.pages),o.paginateEvent=l.getTablePrefix("paging"),o.pager=e(r.table).siblings("."+a+"datatable-pager"),e(o.pager).hasClass(a+"datatable-paging-loaded")||(e(o.pager).remove(),0!==o.meta.pages&&(l.setDataSourceParam("pagination",{page:o.meta.page,pages:o.meta.pages,perpage:o.meta.perpage,total:o.meta.total}),o.callback=o.serverCallback,"function"==typeof i&&(o.callback=i),o.addPaginateEvent(),o.populate(),o.meta.page=Math.max(o.meta.page||1,o.meta.page),e(r).trigger(o.paginateEvent,o.meta),o.pagingBreakpoint.call(),e(window).resize(o.pagingBreakpoint)))},serverCallback:function(e,t){l.dataRender()},populate:function(){if(r.dataSet=r.dataSet||[],0!==r.dataSet.length){var t=l.getOption("layout.icons.pagination"),n=l.getOption("translate.toolbar.pagination.items.default");o.pager=e("<div/>").addClass(a+"datatable-pager "+a+"datatable-paging-loaded");var i=e("<ul/>").addClass(a+"datatable-pager-nav my-2 mb-sm-0");o.pagerLayout.pagination=i,e("<li/>").append(e("<a/>").attr("title",n.first).addClass(a+"datatable-pager-link "+a+"datatable-pager-link-first").append(e("<i/>").addClass(t.first)).on("click",o.gotoMorePage).attr("data-page",1)).appendTo(i),e("<li/>").append(e("<a/>").attr("title",n.prev).addClass(a+"datatable-pager-link "+a+"datatable-pager-link-prev").append(e("<i/>").addClass(t.prev)).on("click",o.gotoMorePage)).appendTo(i),e("<li/>").append(e("<input/>").attr("type","text").addClass(a+"datatable-pager-input form-control").attr("title",n.input).on("keyup",function(){e(this).attr("data-page",Math.abs(e(this).val()))}).on("keypress",function(e){13===e.which&&o.gotoMorePage(e)})).appendTo(i);var d=l.getOption("toolbar.items.pagination.pages.desktop.pagesNumber"),s=Math.ceil(o.meta.page/d)*d,c=s-d;s>o.meta.pages&&(s=o.meta.pages),0>c&&(c=0);for(var u=c;(s||1)>u;u++){var f=u+1;e("<li/>").append(e("<a/>").addClass(a+"datatable-pager-link "+a+"datatable-pager-link-number").text(f).attr("data-page",f).attr("title",f).on("click",o.gotoPage)).appendTo(i)}e("<li/>").append(e("<a/>").attr("title",n.next).addClass(a+"datatable-pager-link "+a+"datatable-pager-link-next").append(e("<i/>").addClass(t.next)).on("click",o.gotoMorePage)).appendTo(i),e("<li/>").append(e("<a/>").attr("title",n.last).addClass(a+"datatable-pager-link "+a+"datatable-pager-link-last").append(e("<i/>").addClass(t.last)).on("click",o.gotoMorePage).attr("data-page",o.meta.pages)).appendTo(i),l.getOption("toolbar.items.info")&&(o.pagerLayout.info=e("<div/>").addClass(a+"datatable-pager-info my-2 mb-sm-0").append(e("<span/>").addClass(a+"datatable-pager-detail"))),e.each(l.getOption("toolbar.layout"),function(t,a){e(o.pagerLayout[a]).appendTo(o.pager)});var p=e("<select/>").addClass("selectpicker "+a+"datatable-pager-size").attr("title",l.getOption("translate.toolbar.pagination.items.default.select")).attr("data-width","60px").attr("data-container","body").val(o.meta.perpage).on("change",o.updatePerpage).prependTo(o.pagerLayout.info),g=l.getOption("toolbar.items.pagination.pageSizeSelect");0==g.length&&(g=[5,10,20,30,50,100]),e.each(g,function(t,a){var n=a;-1===a&&(n=l.getOption("translate.toolbar.pagination.items.default.all")),e("<option/>").attr("value",a).html(n).appendTo(p)}),e(r).ready(function(){e(".selectpicker").selectpicker().on("hide.bs.select",function(){e(this).closest(".bootstrap-select").removeClass("dropup")}).siblings(".dropdown-toggle").attr("title",l.getOption("translate.toolbar.pagination.items.default.select"))}),o.paste()}},paste:function(){e.each(e.unique(l.getOption("toolbar.placement")),function(t,n){"bottom"===n&&e(o.pager).clone(!0).insertAfter(r.table),"top"===n&&e(o.pager).clone(!0).addClass(a+"datatable-pager-top").insertBefore(r.table)})},gotoMorePage:function(t){if(t.preventDefault(),"disabled"===e(this).attr("disabled"))return!1;var a=e(this).attr("data-page");return"undefined"==typeof a&&(a=e(t.target).attr("data-page")),o.openPage(parseInt(a)),!1},gotoPage:function(t){t.preventDefault(),e(this).hasClass(a+"datatable-pager-link-active")||o.openPage(parseInt(e(this).data("page")))},openPage:function(t){o.meta.page=parseInt(t),e(r).trigger(o.paginateEvent,o.meta),o.callback(o,o.meta),e(o.pager).trigger(a+"datatable-on-goto-page",o.meta)},updatePerpage:function(t){t.preventDefault(),e(this).selectpicker("toggle"),o.pager=e(r.table).siblings("."+a+"datatable-pager").removeClass(a+"datatable-paging-loaded"),t.originalEvent&&(o.meta.perpage=parseInt(e(this).val())),e(o.pager).find("select."+a+"datatable-pager-size").val(o.meta.perpage).attr("data-selected",o.meta.perpage),l.setDataSourceParam("pagination",{page:o.meta.page,pages:o.meta.pages,perpage:o.meta.perpage,total:o.meta.total}),e(o.pager).trigger(a+"datatable-on-update-perpage",o.meta),e(r).trigger(o.paginateEvent,o.meta),o.callback(o,o.meta),o.updateInfo.call()},addPaginateEvent:function(t){e(r).off(o.paginateEvent).on(o.paginateEvent,function(t,n){l.spinnerCallback(!0),o.pager=e(r.table).siblings("."+a+"datatable-pager");var i=e(o.pager).find("."+a+"datatable-pager-nav");e(i).find("."+a+"datatable-pager-link-active").removeClass(a+"datatable-pager-link-active"),e(i).find("."+a+'datatable-pager-link-number[data-page="'+n.page+'"]').addClass(a+"datatable-pager-link-active"),e(i).find("."+a+"datatable-pager-link-prev").attr("data-page",Math.max(n.page-1,1)),e(i).find("."+a+"datatable-pager-link-next").attr("data-page",Math.min(n.page+1,n.pages)),e(o.pager).each(function(){e(this).find("."+a+'datatable-pager-input[type="text"]').prop("value",n.page)}),l.setDataSourceParam("pagination",{page:o.meta.page,pages:o.meta.pages,perpage:o.meta.perpage,total:o.meta.total}),e(o.pager).find("select."+a+"datatable-pager-size").val(n.perpage).attr("data-selected",n.perpage),e(r.table).find("."+a+'checkbox > [type="checkbox"]').prop("checked",!1),e(r.table).find("."+a+"datatable-row-active").removeClass(a+"datatable-row-active"),o.updateInfo.call(),o.pagingBreakpoint.call()})},updateInfo:function(){var t=Math.max(o.meta.perpage*(o.meta.page-1)+1,1),n=Math.min(t+o.meta.perpage-1,o.meta.total);e(o.pager).find("."+a+"datatable-pager-info").find("."+a+"datatable-pager-detail").html(l.dataPlaceholder(l.getOption("translate.toolbar.pagination.items.info"),{start:0===o.meta.total?0:t,end:-1===o.meta.perpage?o.meta.total:n,pageSize:-1===o.meta.perpage||o.meta.perpage>=o.meta.total?o.meta.total:o.meta.perpage,total:o.meta.total}))},pagingBreakpoint:function(){var t=e(r.table).siblings("."+a+"datatable-pager").find("."+a+"datatable-pager-nav");if(0!==e(t).length){var i=l.getCurrentPage(),d=e(t).find("."+a+"datatable-pager-input").closest("li");e(t).find("li").show(),e.each(l.getOption("toolbar.items.pagination.pages"),function(r,s){if(n.isInResponsiveRange(r)){switch(r){case"desktop":case"tablet":var c=Math.ceil(i/s.pagesNumber)*s.pagesNumber;c-s.pagesNumber;e(d).hide(),o.meta=l.getDataSourceParam("pagination"),o.paginationUpdate();break;case"mobile":e(d).show(),e(t).find("."+a+"datatable-pager-link-more-prev").closest("li").hide(),e(t).find("."+a+"datatable-pager-link-more-next").closest("li").hide(),e(t).find("."+a+"datatable-pager-link-number").closest("li").hide()}return!1}})}},paginationUpdate:function(){var t=e(r.table).siblings("."+a+"datatable-pager").find("."+a+"datatable-pager-nav"),n=e(t).find("."+a+"datatable-pager-link-more-prev"),i=e(t).find("."+a+"datatable-pager-link-more-next"),d=e(t).find("."+a+"datatable-pager-link-first"),s=e(t).find("."+a+"datatable-pager-link-prev"),c=e(t).find("."+a+"datatable-pager-link-next"),u=e(t).find("."+a+"datatable-pager-link-last"),f=e(t).find("."+a+"datatable-pager-link-number"),p=Math.max(e(f).first().data("page")-1,1);e(n).each(function(t,a){e(a).attr("data-page",p)}),1===p?e(n).parent().hide():e(n).parent().show();var g=Math.min(e(f).last().data("page")+1,o.meta.pages);e(i).each(function(t,a){e(i).attr("data-page",g).show()}),g===o.meta.pages&&g===e(f).last().data("page")?e(i).parent().hide():e(i).parent().show(),1===o.meta.page?(e(d).attr("disabled",!0).addClass(a+"datatable-pager-link-disabled"),e(s).attr("disabled",!0).addClass(a+"datatable-pager-link-disabled")):(e(d).removeAttr("disabled").removeClass(a+"datatable-pager-link-disabled"),e(s).removeAttr("disabled").removeClass(a+"datatable-pager-link-disabled")),o.meta.page===o.meta.pages?(e(c).attr("disabled",!0).addClass(a+"datatable-pager-link-disabled"),e(u).attr("disabled",!0).addClass(a+"datatable-pager-link-disabled")):(e(c).removeAttr("disabled").removeClass(a+"datatable-pager-link-disabled"),e(u).removeAttr("disabled").removeClass(a+"datatable-pager-link-disabled"));var m=l.getOption("toolbar.items.pagination.navigation");m.first||e(d).remove(),m.prev||e(s).remove(),m.next||e(c).remove(),m.last||e(u).remove(),m.more||(e(n).remove(),e(i).remove())}};return o.init(t),o},columnHide:function(){var t=n.getViewPort().width;e.each(o.columns,function(i,o){if("undefined"!=typeof o.responsive||"undefined"!=typeof o.visible){var d=o.field,s=e.grep(e(r.table).find("."+a+"datatable-cell"),function(t,a){return d===e(t).data("field")});setTimeout(function(){l.getObject("visible",o)===!1?e(s).hide():(n.getBreakpoint(l.getObject("responsive.hidden",o))>=t?e(s).hide():e(s).show(),n.getBreakpoint(l.getObject("responsive.visible",o))<=t?e(s).show():e(s).hide())})}})},setupSubDatatable:function(){var t=l.getOption("detail.content");if("function"==typeof t&&!(e(r.table).find("."+a+"datatable-subtable").length>0)){e(r.wrap).addClass(a+"datatable-subtable"),o.columns[0].subtable=!0;var n=function(n){n.preventDefault();var i=e(this).closest("."+a+"datatable-row"),d=e(i).next("."+a+"datatable-row-subtable");0===e(d).length&&(d=e("<tr/>").addClass(a+"datatable-row-subtable "+a+"datatable-row-loading").hide().append(e("<td/>").addClass(a+"datatable-subtable").attr("colspan",l.getTotalColumns())),e(i).after(d),e(i).hasClass(a+"datatable-row-even")&&e(d).addClass(a+"datatable-row-subtable-even")),e(d).toggle();var s=e(d).find("."+a+"datatable-subtable"),c=e(this).closest("[data-field]:first-child").find("."+a+"datatable-toggle-subtable").data("value"),u=e(this).find("i").removeAttr("class");e(i).hasClass(a+"datatable-row-subtable-expanded")?(e(u).addClass(l.getOption("layout.icons.rowDetail.collapse")),e(i).removeClass(a+"datatable-row-subtable-expanded"),e(r).trigger(a+"datatable-on-collapse-subtable",[i])):(e(u).addClass(l.getOption("layout.icons.rowDetail.expand")),e(i).addClass(a+"datatable-row-subtable-expanded"),e(r).trigger(a+"datatable-on-expand-subtable",[i])),0===e(s).find("."+a+"datatable").length&&(e.map(r.dataSet,function(e,t){return c===e[o.columns[0].field]?(n.data=e,!0):!1}),n.detailCell=s,n.parentRow=i,n.subTable=s,t(n),e(s).children("."+a+"datatable").on(a+"datatable-on-init",function(t){e(d).removeClass(a+"datatable-row-loading")}),"local"===l.getOption("data.type")&&e(d).removeClass(a+"datatable-row-loading"))},i=o.columns;e(r.tableBody).find("."+a+"datatable-row").each(function(t,o){e(o).find("."+a+"datatable-cell").each(function(t,o){var r=e.grep(i,function(t,a){return e(o).data("field")===t.field})[0];if("undefined"!=typeof r){var d=e(o).text();if("undefined"!=typeof r.subtable&&r.subtable){if(e(o).find("."+a+"datatable-toggle-subtable").length>0)return;e(o).html(e("<a/>").addClass(a+"datatable-toggle-subtable").attr("href","#").attr("data-value",d).attr("title",l.getOption("detail.title")).on("click",n).append(e("<i/>").css("width",e(o).data("width")).addClass(l.getOption("layout.icons.rowDetail.collapse"))))}}})})}},dataMapCallback:function(e){var t=e;return"function"==typeof l.getOption("data.source.read.map")?l.getOption("data.source.read.map")(e):("undefined"!=typeof e&&"undefined"!=typeof e.data&&(t=e.data),t)},isSpinning:!1,spinnerCallback:function(e,t){"undefined"==typeof t&&(t=r);var a=l.getOption("layout.spinner");"undefined"!=typeof a&&a&&(e?l.isSpinning||("undefined"!=typeof a.message&&a.message===!0&&(a.message=l.getOption("translate.records.processing")),l.isSpinning=!0,"undefined"!=typeof i&&i.block(t,a)):(l.isSpinning=!1,"undefined"!=typeof i&&i.unblock(t)))},sortCallback:function(t,a,n){var i=n.type||"string",o=n.format||"",r=n.field;return e(t).sort(function(n,l){var d=n[r],s=l[r];switch(i){case"date":if("undefined"==typeof moment)throw new Error("Moment.js is required.");var c=moment(d,o).diff(moment(s,o));return"asc"===a?c>0?1:0>c?-1:0:0>c?1:c>0?-1:0;case"number":return isNaN(parseFloat(d))&&null!=d&&(d=Number(d.replace(/[^0-9\.-]+/g,""))),isNaN(parseFloat(s))&&null!=s&&(s=Number(s.replace(/[^0-9\.-]+/g,""))),d=parseFloat(d),s=parseFloat(s),"asc"===a?d>s?1:s>d?-1:0:s>d?1:d>s?-1:0;case"html":return e(t).sort(function(t,n){return d=e(t[r]).text(),s=e(n[r]).text(),"asc"===a?d>s?1:s>d?-1:0:s>d?1:d>s?-1:0});case"string":default:return"asc"===a?d>s?1:s>d?-1:0:s>d?1:d>s?-1:0}})},log:function(e,t){"undefined"==typeof t&&(t=""),r.debug&&console.log(e,t)},autoHide:function(){var t=!1,n=e(r.table).find("[data-autohide-enabled]");n.length&&(t=!0,n.hide());var i=function(t){t.preventDefault();var n=e(this).closest("."+a+"datatable-row"),i=e(n).next();if(e(i).hasClass(a+"datatable-row-detail"))e(this).find("i").removeClass(l.getOption("layout.icons.rowDetail.expand")).addClass(l.getOption("layout.icons.rowDetail.collapse")),e(i).remove();else{e(this).find("i").removeClass(l.getOption("layout.icons.rowDetail.collapse")).addClass(l.getOption("layout.icons.rowDetail.expand"));var r=e(n).find("."+a+"datatable-cell:hidden"),d=r.clone().show();i=e("<tr/>").addClass(a+"datatable-row-detail").insertAfter(n);var s=e("<td/>").addClass(a+"datatable-detail").attr("colspan",l.getTotalColumns()).appendTo(i),c=e("<table/>");e(d).each(function(){var t=e(this).data("field"),n=e.grep(o.columns,function(e,a){return t===e.field})[0];("undefined"==typeof n||n.visible!==!1)&&e(c).append(e('<tr class="'+a+'datatable-row"></tr>').append(e('<td class="'+a+'datatable-cell"></td>').append(e("<span/>").append(n.title))).append(this))}),e(s).append(c)}};setTimeout(function(){e(r.table).find("."+a+"datatable-cell").show(),e(r.tableBody).each(function(){for(var n=0;e(this)[0].offsetWidth<e(this)[0].scrollWidth&&n<o.columns.length;)e(r.table).find("."+a+"datatable-row").each(function(n){var i=e(this).find("."+a+"datatable-cell:not(:hidden):not([data-autohide-disabled])").last();i.length&&(e(i).hide(),t=!0)}),n++}),t&&e(r.tableBody).find("."+a+"datatable-row").each(function(){0===e(this).find("."+a+"datatable-toggle-detail").length&&e(this).prepend(e("<td/>").addClass(a+"datatable-cell "+a+"datatable-toggle-detail").append(e("<a/>").addClass(a+"datatable-toggle-detail").attr("href","").on("click",i).append('<i class="'+l.getOption("layout.icons.rowDetail.collapse")+'"></i>'))),0===e(r.tableHead).find("."+a+"datatable-toggle-detail").length?(e(r.tableHead).find("."+a+"datatable-row").first().prepend('<th class="'+a+"datatable-cell "+a+'datatable-toggle-detail"><span></span></th>'),e(r.tableFoot).find("."+a+"datatable-row").first().prepend('<th class="'+a+"datatable-cell "+a+'datatable-toggle-detail"><span></span></th>')):e(r.tableHead).find("."+a+"datatable-toggle-detail").find("span")})}),l.adjustCellsWidth.call()},setAutoColumns:function(){l.getOption("data.autoColumns")&&(e.each(r.dataSet[0],function(t,a){var n=e.grep(o.columns,function(e,a){return t===e.field});0===n.length&&o.columns.push({field:t,title:t})}),e(r.tableHead).find("."+a+"datatable-row").remove(),l.setHeadTitle(),l.getOption("layout.footer")&&(e(r.tableFoot).find("."+a+"datatable-row").remove(),l.setHeadTitle(r.tableFoot)))},isLocked:function(){var e=l.lockEnabledColumns();return e.left.length>0||e.right.length>0;
    },isSubtable:function(){return n.hasClass(r.wrap[0],a+"datatable-subtable")||!1},getExtraSpace:function(t){var a=parseInt(e(t).css("paddingRight"))+parseInt(e(t).css("paddingLeft")),n=parseInt(e(t).css("marginRight"))+parseInt(e(t).css("marginLeft")),i=Math.ceil(e(t).css("border-right-width").replace("px",""));return a+n+i},dataPlaceholder:function(t,a){var n=t;return e.each(a,function(e,t){n=n.replace("{{"+e+"}}",t)}),n},getTableId:function(t){"undefined"==typeof t&&(t="");var a=e(r).attr("id");return"undefined"==typeof a&&(a=e(r).attr("class").split(" ")[0]),a+t},getTablePrefix:function(e){return"undefined"!=typeof e&&(e="-"+e),l.getTableId()+"-"+l.getDepth()+e},getDepth:function(){var t=0,n=r.table;do n=e(n).parents("."+a+"datatable-table"),t++;while(e(n).length>0);return t},stateKeep:function(e,t){e=l.getTablePrefix(e),l.getOption("data.saveState")!==!1&&localStorage&&localStorage.setItem(e,JSON.stringify(t))},stateGet:function(e,t){if(e=l.getTablePrefix(e),l.getOption("data.saveState")!==!1){var a=null;return localStorage&&(a=localStorage.getItem(e)),"undefined"!=typeof a&&null!==a?JSON.parse(a):void 0}},stateUpdate:function(t,a){var n=l.stateGet(t);("undefined"==typeof n||null===n)&&(n={}),l.stateKeep(t,e.extend({},n,a))},stateRemove:function(e){e=l.getTablePrefix(e),localStorage&&localStorage.removeItem(e)},getTotalColumns:function(t){return"undefined"==typeof t&&(t=r.tableBody),e(t).find("."+a+"datatable-row").first().find("."+a+"datatable-cell").length},getOneRow:function(t,n,i){"undefined"==typeof i&&(i=!0);var o=e(t).find("."+a+"datatable-row:not(."+a+"datatable-row-detail):nth-child("+n+")");return i&&(o=o.find("."+a+"datatable-cell")),o},sortColumn:function(t,n,i){"undefined"==typeof n&&(n="asc"),"undefined"==typeof i&&(i=!1);var o=e(t).index(),l=e(r.tableBody).find("."+a+"datatable-row"),d=e(t).closest("."+a+"datatable-lock").index();-1!==d&&(l=e(r.tableBody).find("."+a+"datatable-lock:nth-child("+(d+1)+")").find("."+a+"datatable-row"));var s=e(l).parent();e(l).sort(function(t,a){var r=e(t).find("td:nth-child("+o+")").text(),l=e(a).find("td:nth-child("+o+")").text();return i&&(r=parseInt(r),l=parseInt(l)),"asc"===n?r>l?1:l>r?-1:0:l>r?1:r>l?-1:0}).appendTo(s)},sorting:function(){var t={init:function(){o.sortable&&(e(r.tableHead).find("."+a+"datatable-cell:not(."+a+"datatable-cell-check)").addClass(a+"datatable-cell-sort").off("click").on("click",t.sortClick),t.setIcon())},setIcon:function(){var t=l.getDataSourceParam("sort");if(!e.isEmptyObject(t)){var n=l.getColumnByField(t.field);if("undefined"!=typeof n&&!("undefined"!=typeof n.sortable&&n.sortable===!1||"undefined"!=typeof n.selector&&n.selector===!0)){var i=e(r.tableHead).find("."+a+'datatable-cell[data-field="'+t.field+'"]').attr("data-sort",t.sort),o=e(i).find("span"),d=e(o).find("i"),s=l.getOption("layout.icons.sort");e(d).length>0?e(d).removeAttr("class").addClass(s[t.sort]):e(o).append(e("<i/>").addClass(s[t.sort])),e(i).addClass(a+"datatable-cell-sorted")}}},sortClick:function(i){var d=l.getDataSourceParam("sort"),s=e(this).data("field"),c=l.getColumnByField(s);if("undefined"!=typeof c&&("undefined"==typeof c.sortable||c.sortable!==!1)&&("undefined"==typeof c.selector||c.selector!==!0)&&(e(r.tableHead).find("th").removeClass(a+"datatable-cell-sorted"),n.addClass(this,a+"datatable-cell-sorted"),e(r.tableHead).find("."+a+"datatable-cell > span > i").remove(),o.sortable)){l.spinnerCallback(!0);var u="desc";l.getObject("field",d)===s&&(u=l.getObject("sort",d)),u="undefined"==typeof u||"desc"===u?"asc":"desc",d={field:s,sort:u},l.setDataSourceParam("sort",d),t.setIcon(),setTimeout(function(){l.dataRender("sort"),e(r).trigger(a+"datatable-on-sort",d)},300)}}};t.init()},localDataUpdate:function(){var t=l.getDataSourceParam();"undefined"==typeof r.originalDataSet&&(r.originalDataSet=r.dataSet);var a=l.getObject("sort.field",t),n=l.getObject("sort.sort",t),i=l.getColumnByField(a);if("undefined"!=typeof i&&l.getOption("data.serverSorting")!==!0?"function"==typeof i.sortCallback?r.dataSet=i.sortCallback(r.originalDataSet,n,i):r.dataSet=l.sortCallback(r.originalDataSet,n,i):r.dataSet=r.originalDataSet,"object"==typeof t.query&&!l.getOption("data.serverFiltering")){t.query=t.query||{};var o=function(e){for(var t in e)if(e.hasOwnProperty(t))if("string"==typeof e[t]){if(e[t].toLowerCase()==d||-1!==e[t].toLowerCase().indexOf(d))return!0}else if("number"==typeof e[t]){if(e[t]===d)return!0}else if("object"==typeof e[t]&&o(e[t]))return!0;return!1},d=e(l.getOption("search.input")).val();"undefined"!=typeof d&&""!==d&&(d=d.toLowerCase(),r.dataSet=e.grep(r.dataSet,o),delete t.query[l.getGeneralSearchKey()]),e.each(t.query,function(e,a){""===a&&delete t.query[e]}),r.dataSet=l.filterArray(r.dataSet,t.query),r.dataSet=r.dataSet.filter(function(){return!0})}return r.dataSet},filterArray:function(t,a,n){if("object"!=typeof t)return[];if("undefined"==typeof n&&(n="AND"),"object"!=typeof a)return t;if(n=n.toUpperCase(),-1===e.inArray(n,["AND","OR","NOT"]))return[];var i=Object.keys(a).length,o=[];return e.each(t,function(t,r){var d=r,s=0;e.each(a,function(e,t){t=t instanceof Array?t:[t];var a=l.getObject(e,d);if("undefined"!=typeof a&&a){var n=a.toString().toLowerCase();t.forEach(function(e,t){(e.toString().toLowerCase()==n||-1!==n.indexOf(e.toString().toLowerCase()))&&s++})}}),("AND"==n&&s==i||"OR"==n&&s>0||"NOT"==n&&0==s)&&(o[t]=r)}),t=o},resetScroll:function(){"undefined"==typeof o.detail&&1===l.getDepth()&&(e(r.table).find("."+a+"datatable-row").css("left",0),e(r.table).find("."+a+"datatable-lock").css("top",0),e(r.tableBody).scrollTop(0))},getColumnByField:function(t){if("undefined"!=typeof t){var a;return e.each(o.columns,function(e,n){return t===n.field?(a=n,!1):void 0}),a}},getDefaultSortColumn:function(){var t;return e.each(o.columns,function(a,n){return"undefined"!=typeof n.sortable&&-1!==e.inArray(n.sortable,["asc","desc"])?(t={sort:n.sortable,field:n.field},!1):void 0}),t},getHiddenDimensions:function(t,a){var n={position:"absolute",visibility:"hidden",display:"block"},i={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},o=e(t).parents().addBack().not(":visible");a="boolean"==typeof a?a:!1;var r=[];return o.each(function(){var e={};for(var t in n)e[t]=this.style[t],this.style[t]=n[t];r.push(e)}),i.width=e(t).width(),i.outerWidth=e(t).outerWidth(a),i.innerWidth=e(t).innerWidth(),i.height=e(t).height(),i.innerHeight=e(t).innerHeight(),i.outerHeight=e(t).outerHeight(a),o.each(function(e){var t=r[e];for(var a in n)this.style[a]=t[a]}),i},getGeneralSearchKey:function(){var t=e(l.getOption("search.input"));return l.getOption("search.key")||e(t).prop("name")},getObject:function(e,t){return e.split(".").reduce(function(e,t){return null!==e&&"undefined"!=typeof e[t]?e[t]:null},t)},extendObj:function(e,t,a){function n(e){var t=i[o++];"undefined"!=typeof e[t]&&null!==e[t]?"object"!=typeof e[t]&&"function"!=typeof e[t]&&(e[t]={}):e[t]={},o===i.length?e[t]=a:n(e[t])}var i=t.split("."),o=0;return n(e),e},rowEvenOdd:function(){e(r.tableBody).find("."+a+"datatable-row").removeClass(a+"datatable-row-even"),e(r.wrap).hasClass(a+"datatable-subtable")?e(r.tableBody).find("."+a+"datatable-row:not(."+a+"datatable-row-detail):even").addClass(a+"datatable-row-even"):e(r.tableBody).find("."+a+"datatable-row:nth-child(even)").addClass(a+"datatable-row-even")},timer:0,redraw:function(){return l.adjustCellsWidth.call(),l.isLocked()&&(l.scrollbar(),l.resetScroll(),l.adjustCellsHeight.call()),l.adjustLockContainer.call(),l.initHeight.call(),r},load:function(){return l.reload(),r},reload:function(){var t=function(){return function(e,t){clearTimeout(l.timer),l.timer=setTimeout(e,t)}}();return t(function(){o.data.serverFiltering||l.localDataUpdate(),l.dataRender(),e(r).trigger(a+"datatable-on-reloaded")},l.getOption("search.delay")),r},getRecord:function(t){return"undefined"==typeof r.tableBody&&(r.tableBody=e(r.table).children("tbody")),e(r.tableBody).find("."+a+"datatable-cell:first-child").each(function(n,i){if(t==e(i).text()){var o=e(i).closest("."+a+"datatable-row").index()+1;return r.API.record=r.API.value=l.getOneRow(r.tableBody,o),r}}),r},getColumn:function(t){return l.setSelectedRecords(),r.API.value=e(r.API.record).find('[data-field="'+t+'"]'),r},destroy:function(){e(r).parent().find("."+a+"datatable-pager").remove();var t=e(r.initialDatatable).addClass(a+"datatable-destroyed").show();return e(r).replaceWith(t),r=t,e(r).trigger(a+"datatable-on-destroy"),l.isInit=!1,t=null,r.dataSet=null,r.originalDataSet=null,r.tableHead=null,r.tableBody=null,r.table=null,r.wrap=null,r.API={record:null,value:null,params:null},l.ajaxParams={},l.pagingObject={},l.nodeTr=[],l.nodeTd=[],l.nodeCols=[],l.recentNode=[],t},sort:function(t,n){n="undefined"==typeof n?"asc":n,l.spinnerCallback(!0);var i={field:t,sort:n};return l.setDataSourceParam("sort",i),setTimeout(function(){l.dataRender("sort"),e(r).trigger(a+"datatable-on-sort",i),e(r.tableHead).find("."+a+"datatable-cell > span > i").remove()},300),r},getValue:function(){return e(r.API.value).text()},setActive:function(t){"string"==typeof t&&(t=e(r.tableBody).find("."+a+'checkbox-single > [type="checkbox"][value="'+t+'"]')),e(t).prop("checked",!0);var n=[];e(t).each(function(t,i){var o=(e(i).closest("tr").addClass(a+"datatable-row-active"),e(i).attr("value"));"undefined"!=typeof o&&n.push(o)}),e(r).trigger(a+"datatable-on-check",[n])},setInactive:function(t){"string"==typeof t&&(t=e(r.tableBody).find("."+a+'checkbox-single > [type="checkbox"][value="'+t+'"]')),e(t).prop("checked",!1);var n=[];e(t).each(function(t,i){var o=(e(i).closest("tr").removeClass(a+"datatable-row-active"),e(i).attr("value"));"undefined"!=typeof o&&n.push(o)}),e(r).trigger(a+"datatable-on-uncheck",[n])},setActiveAll:function(t){var n=e(r.table).find("> tbody, > thead").find("tr").not("."+a+"datatable-row-subtable").find("."+a+'datatable-cell-check [type="checkbox"]');t?l.setActive(n):l.setInactive(n)},setSelectedRecords:function(){return r.API.record=e(r.tableBody).find("."+a+"datatable-row-active"),r},getSelectedRecords:function(){return l.setSelectedRecords(),r.API.record=r.rows("."+a+"datatable-row-active").nodes(),r.API.record},getOption:function(e){return l.getObject(e,o)},setOption:function(e,t){o=l.extendObj(o,e,t)},search:function(t,a){"undefined"!=typeof a&&(a=e.makeArray(a));var n=function(){return function(e,t){clearTimeout(l.timer),l.timer=setTimeout(e,t)}}();n(function(){var n=l.getDataSourceQuery();if("undefined"==typeof a&&"undefined"!=typeof t){var i=l.getGeneralSearchKey();n[i]=t}"object"==typeof a&&(e.each(a,function(e,a){n[a]=t}),e.each(n,function(t,a){(""===a||e.isEmptyObject(a))&&delete n[t]})),l.setDataSourceQuery(n),r.setDataSourceParam("pagination",Object.assign({},r.getDataSourceParam("pagination"),{page:1})),o.data.serverFiltering||l.localDataUpdate(),l.dataRender("search")},l.getOption("search.delay"))},setDataSourceParam:function(t,a){r.API.params=e.extend({},{pagination:{page:1,perpage:l.getOption("data.pageSize")},sort:l.getDefaultSortColumn(),query:{}},r.API.params,l.stateGet(l.stateId)),r.API.params=l.extendObj(r.API.params,t,a),l.stateKeep(l.stateId,r.API.params)},getDataSourceParam:function(t){return r.API.params=e.extend({},{pagination:{page:1,perpage:l.getOption("data.pageSize")},sort:l.getDefaultSortColumn(),query:{}},r.API.params,l.stateGet(l.stateId)),"string"==typeof t?l.getObject(t,r.API.params):r.API.params},getDataSourceQuery:function(){return l.getDataSourceParam("query")||{}},setDataSourceQuery:function(e){l.setDataSourceParam("query",e)},getCurrentPage:function(){return e(r.table).siblings("."+a+"datatable-pager").last().find("."+a+"datatable-pager-nav").find("."+a+"datatable-pager-link."+a+"datatable-pager-link-active").data("page")||1},getPageSize:function(){return e(r.table).siblings("."+a+"datatable-pager").last().find("select."+a+"datatable-pager-size").val()||10},getTotalRows:function(){return r.API.params.pagination.total},getDataSet:function(){return r.originalDataSet},nodeTr:[],nodeTd:[],nodeCols:[],recentNode:[],table:function(){return"undefined"!=typeof r.table?r.table:void 0},row:function(t){return l.rows(t),l.nodeTr=l.recentNode=e(l.nodeTr).first(),r},rows:function(t){return l.isLocked()?l.nodeTr=l.recentNode=e(r.tableBody).find(t).filter("."+a+"datatable-lock-scroll > ."+a+"datatable-row"):l.nodeTr=l.recentNode=e(r.tableBody).find(t).filter("."+a+"datatable-row"),r},column:function(t){return l.nodeCols=l.recentNode=e(r.tableBody).find("."+a+"datatable-cell:nth-child("+(t+1)+")"),r},columns:function(t){var n=r.table;l.nodeTr===l.recentNode&&(n=l.nodeTr);var i=e(n).find("."+a+'datatable-cell[data-field="'+t+'"]');return i.length>0?l.nodeCols=l.recentNode=i:l.nodeCols=l.recentNode=e(n).find(t).filter("."+a+"datatable-cell"),r},cell:function(t){return l.cells(t),l.nodeTd=l.recentNode=e(l.nodeTd).first(),r},cells:function(t){var n=e(r.tableBody).find("."+a+"datatable-cell");return"undefined"!=typeof t&&(n=e(n).filter(t)),l.nodeTd=l.recentNode=n,r},remove:function(){return e(l.nodeTr.length)&&l.nodeTr===l.recentNode&&e(l.nodeTr).remove(),l.layoutUpdate(),r},visible:function(t){if(e(l.recentNode.length)){var n=l.lockEnabledColumns();if(l.recentNode===l.nodeCols){var i=l.recentNode.index();if(l.isLocked()){var r=e(l.recentNode).closest("."+a+"datatable-lock-scroll").length;r?i+=n.left.length+1:e(l.recentNode).closest("."+a+"datatable-lock-right").length&&(i+=n.left.length+r+1)}}t?(l.recentNode===l.nodeCols&&delete o.columns[i].visible,e(l.recentNode).show()):(l.recentNode===l.nodeCols&&l.setOption("columns."+i+".visible",!1),e(l.recentNode).hide()),l.columnHide(),l.redraw()}},nodes:function(){return l.recentNode},dataset:function(){return r},gotoPage:function(e){"undefined"!=typeof l.pagingObject&&(l.isInit=!0,l.pagingObject.openPage(e))}};if(e.each(l,function(e,t){r[e]=t}),"undefined"!=typeof o)if("string"==typeof o){var d=o;r=e(this).data(t),"undefined"!=typeof r&&(o=r.options,l[d].apply(this,Array.prototype.slice.call(arguments,1)))}else r.data(t)||e(this).hasClass(a+"datatable-loaded")||(r.dataSet=null,r.textAlign={left:a+"datatable-cell-left",center:a+"datatable-cell-center",right:a+"datatable-cell-right"},o=e.extend(!0,{},e.fn[t].defaults,o),r.options=o,l.init.apply(this,[o]),e(r.wrap).data(t,r));else r=e(this).data(t),"undefined"==typeof r&&e.error(t+" not initialized"),o=r.options;return r},e.fn[t].defaults={data:{type:"local",source:null,pageSize:10,saveState:!0,serverPaging:!1,serverFiltering:!1,serverSorting:!1,autoColumns:!1,attr:{rowProps:[]}},layout:{theme:"default","class":a+"datatable-primary",scroll:!1,height:null,minHeight:null,footer:!1,header:!0,customScrollbar:!0,spinner:{overlayColor:"#000000",opacity:0,type:"loader",state:"primary",message:!0},icons:{sort:{asc:"flaticon2-arrow-up",desc:"flaticon2-arrow-down"},pagination:{next:"flaticon2-next",prev:"flaticon2-back",first:"flaticon2-fast-back",last:"flaticon2-fast-next",more:"flaticon-more-1"},rowDetail:{expand:"fa fa-caret-down",collapse:"fa fa-caret-right"}}},sortable:!0,resizable:!1,filterable:!1,pagination:!0,editable:!1,columns:[],search:{onEnter:!1,input:null,delay:400,key:null},rows:{callback:function(){},beforeTemplate:function(){},afterTemplate:function(){},autoHide:!0},toolbar:{layout:["pagination","info"],placement:["bottom"],items:{pagination:{type:"default",pages:{desktop:{layout:"default",pagesNumber:5},tablet:{layout:"default",pagesNumber:3},mobile:{layout:"compact"}},navigation:{prev:!0,next:!0,first:!0,last:!0,more:!1},pageSizeSelect:[]},info:!0}},translate:{records:{processing:"Please wait...",noRecords:"No records found"},toolbar:{pagination:{items:{"default":{first:"First",prev:"Previous",next:"Next",last:"Last",more:"More pages",input:"Page number",select:"Select page size",all:"all"},info:"Showing {{start}} - {{end}} of {{total}}"}}}},extensions:{}}}(jQuery),function(e){var t="KTDatatable",a="";e.fn[t]=e.fn[t]||{},e.fn[t].checkbox=function(n,i){var o={selectedAllRows:!1,selectedRows:[],unselectedRows:[],init:function(){o.selectorEnabled()&&(n.setDataSourceParam(i.vars.selectedAllRows,!1),n.stateRemove("checkbox"),i.vars.requestIds&&n.setDataSourceParam(i.vars.requestIds,!0),e(n).on(a+"datatable-on-reloaded",function(){n.stateRemove("checkbox"),n.setDataSourceParam(i.vars.selectedAllRows,!1),o.selectedAllRows=!1,o.selectedRows=[],o.unselectedRows=[]}),o.selectedAllRows=n.getDataSourceParam(i.vars.selectedAllRows),e(n).on(a+"datatable-on-layout-updated",function(t,a){a.table==e(n.wrap).attr("id")&&n.ready(function(){o.initVars(),o.initEvent(),o.initSelect()})}),e(n).on(a+"datatable-on-check",function(t,a){a.forEach(function(e){o.selectedRows.push(e),o.unselectedRows=o.remove(o.unselectedRows,e)});var i={};i.selectedRows=e.unique(o.selectedRows),i.unselectedRows=e.unique(o.unselectedRows),n.stateKeep("checkbox",i)}),e(n).on(a+"datatable-on-uncheck",function(t,a){a.forEach(function(e){o.unselectedRows.push(e),o.selectedRows=o.remove(o.selectedRows,e)});var i={};i.selectedRows=e.unique(o.selectedRows),i.unselectedRows=e.unique(o.unselectedRows),n.stateKeep("checkbox",i)}))},initEvent:function(){e(n.tableHead).find("."+a+'checkbox-all > [type="checkbox"]').click(function(t){if(o.selectedRows=o.unselectedRows=[],n.stateRemove("checkbox"),o.selectedAllRows=!!e(this).is(":checked"),!i.vars.requestIds){e(this).is(":checked")&&(o.selectedRows=e.makeArray(e(n.tableBody).find("."+a+'checkbox-single > [type="checkbox"]').map(function(t,a){return e(a).val()})));var r={};r.selectedRows=e.unique(o.selectedRows),n.stateKeep("checkbox",r)}n.setDataSourceParam(i.vars.selectedAllRows,o.selectedAllRows),e(n).trigger(a+"datatable-on-click-checkbox",[e(this)])}),e(n.tableBody).find("."+a+'checkbox-single > [type="checkbox"]').click(function(t){var r=e(this).val();e(this).is(":checked")?(o.selectedRows.push(r),o.unselectedRows=o.remove(o.unselectedRows,r)):(o.unselectedRows.push(r),o.selectedRows=o.remove(o.selectedRows,r)),!i.vars.requestIds&&o.selectedRows.length<1&&e(n.tableHead).find("."+a+'checkbox-all > [type="checkbox"]').prop("checked",!1);var l={};l.selectedRows=o.selectedRows.filter(o.unique),l.unselectedRows=o.unselectedRows.filter(o.unique),n.stateKeep("checkbox",l),e(n).trigger(a+"datatable-on-click-checkbox",[e(this)])})},unique:function(e,t,a){return a.indexOf(e)===t},initSelect:function(){o.selectedAllRows&&i.vars.requestIds?(n.hasClass(a+"datatable-error")||e(n.tableHead).find("."+a+'checkbox-all > [type="checkbox"]').prop("checked",!0),n.setActiveAll(!0),o.unselectedRows.forEach(function(e){n.setInactive(e)})):(o.selectedRows.forEach(function(e){n.setActive(e)}),!n.hasClass(a+"datatable-error")&&e(n.tableBody).find("."+a+'checkbox-single > [type="checkbox"]').not(":checked").length<1&&e(n.tableHead).find("."+a+'checkbox-all > [type="checkbox"]').prop("checked",!0))},selectorEnabled:function(){return e.grep(n.options.columns,function(e,t){return e.selector||!1})[0]},initVars:function(){var e=n.stateGet("checkbox");"undefined"!=typeof e&&(o.selectedRows=e.selectedRows||[],o.unselectedRows=e.unselectedRows||[])},getSelectedId:function(t){if(o.initVars(),o.selectedAllRows&&i.vars.requestIds){"undefined"==typeof t&&(t=i.vars.rowIds);var a=n.getObject(t,n.lastResponse)||[];return a.length>0&&o.unselectedRows.forEach(function(e){a=o.remove(a,parseInt(e))}),e.unique(a)}return o.selectedRows},remove:function(e,t){return e.filter(function(e){return e!==t})}};return n.checkbox=function(){return o},"object"==typeof i&&(i=e.extend(!0,{},e.fn[t].checkbox["default"],i),o.init.apply(this,[i])),n},e.fn[t].checkbox["default"]={vars:{selectedAllRows:"selectedAllRows",requestIds:"requestIds",rowIds:"meta.rowIds"}}}(jQuery);var defaults={layout:{icons:{pagination:{next:"flaticon2-next",prev:"flaticon2-back",first:"flaticon2-fast-back",last:"flaticon2-fast-next",more:"flaticon-more-1"},rowDetail:{expand:"fa fa-caret-down",collapse:"fa fa-caret-right"}}}};KTUtil.isRTL()&&(defaults={layout:{icons:{pagination:{next:"flaticon2-back",prev:"flaticon2-next",first:"flaticon2-fast-next",last:"flaticon2-fast-back"},rowDetail:{collapse:"fa fa-caret-down",expand:"fa fa-caret-right"}}}}),$.extend(!0,$.fn.KTDatatable.defaults,defaults),KTUtil.ready(function(){"undefined"!=typeof KTLayoutHeader&&KTLayoutHeader.init("kt_header","kt_header_mobile"),"undefined"!=typeof KTLayoutHeaderMenu&&KTLayoutHeaderMenu.init("kt_header_menu","kt_header_menu_wrapper"),"undefined"!=typeof KTLayoutHeaderTopbar&&KTLayoutHeaderTopbar.init("kt_header_mobile_topbar_toggle"),"undefined"!=typeof KTLayoutAside&&KTLayoutAside.init("kt_aside"),"undefined"!=typeof KTLayoutAsideMenu&&KTLayoutAsideMenu.init("kt_aside_menu"),"undefined"!=typeof KTLayoutSubheader&&KTLayoutSubheader.init("kt_subheader"),"undefined"!=typeof KTLayoutContent&&KTLayoutContent.init("kt_content"),"undefined"!=typeof KTLayoutFooter&&KTLayoutFooter.init("kt_footer"),"undefined"!=typeof KTLayoutQuickUser&&KTLayoutQuickUser.init("kt_quick_user"),"undefined"!=typeof KTLayoutScrolltop&&KTLayoutScrolltop.init("kt_scrolltop"),"undefined"!=typeof KTLayoutStickyCard&&KTLayoutStickyCard.init("kt_page_sticky_card"),"undefined"!=typeof KTLayoutStretchedCard&&KTLayoutStretchedCard.init("kt_page_stretched_card"),"undefined"!=typeof KTLayoutExamples&&KTLayoutExamples.init(),"undefined"!=typeof KTLayoutDemoPanel&&KTLayoutDemoPanel.init("kt_demo_panel"),"undefined"!=typeof KTLayoutChat&&KTLayoutChat.init("kt_chat_modal"),"undefined"!=typeof KTLayoutQuickActions&&KTLayoutQuickActions.init("kt_quick_actions"),"undefined"!=typeof KTLayoutQuickNotifications&&KTLayoutQuickNotifications.init("kt_quick_notifications"),"undefined"!=typeof KTLayoutQuickPanel&&KTLayoutQuickPanel.init("kt_quick_panel"),"undefined"!=typeof KTLayoutQuickSearch&&KTLayoutQuickSearch.init("kt_quick_search"),"undefined"!=typeof KTLayoutSearch&&KTLayoutSearch().init("kt_quick_search_dropdown"),"undefined"!=typeof KTLayoutSearchOffcanvas&&KTLayoutSearchOffcanvas().init("kt_quick_search_offcanvas")});var KTLayoutAsideMenu=function(){var e,t,a=function(){var a,n="1"===KTUtil.attr(e,"data-menu-dropdown")?"dropdown":"accordion";KTUtil.isBreakpointDown("lg")&&"1"===KTUtil.attr(e,"data-menu-scroll")&&(a={rememberPosition:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return t-=parseInt(KTUtil.css(e,"marginBottom"))+parseInt(KTUtil.css(e,"marginTop"))}}),t=new KTMenu(e,{scroll:a,submenu:{desktop:n,tablet:"accordion",mobile:"accordion"},accordion:{expandAll:!1}}),t.on("linkClick",function(e){KTUtil.isBreakpointDown("lg")&&KTLayoutAside.getOffcanvas().hide()})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e},getMenu:function(){return t},pauseDropdownHover:function(e){t&&t.pauseDropdownHover(e)},closeMobileOffcanvas:function(){t&&KTUtil.isMobileDevice()&&t.hide()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutAsideMenu);var KTLayoutAside=function(){var e,t,a,n=function(){var e=KTUtil.hasClass(t,"aside-offcanvas-default")?"aside-offcanvas-default":"aside";a=new KTOffcanvas(t,{baseClass:e,overlay:!0,closeBy:"kt_aside_close_btn",toggleBy:{target:"kt_aside_mobile_toggle",state:"mobile-toggle-active"}})};return{init:function(a){t=KTUtil.getById(a),e=KTUtil.getBody(),t&&n()},getElement:function(){return t},getOffcanvas:function(){return a}}}();"undefined"!=typeof module&&(module.exports=KTLayoutAside);var KTLayoutContent=function(){var e,t=function(){var t;return t=KTUtil.getViewPort().height,e&&(t=t-parseInt(KTUtil.css(e,"paddingTop"))-parseInt(KTUtil.css(e,"paddingBottom"))),t-=KTLayoutHeader.getHeight(),t-=KTLayoutSubheader.getHeight(),t-=KTLayoutFooter.getHeight()};return{init:function(t){e=KTUtil.getById(t)},getHeight:function(){return t()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutContent);var KTLayoutFooter=function(){var e,t=function(){var t=0;return e&&(t=KTUtil.actualHeight(e)),t};return{init:function(t){e=KTUtil.getById(t)},getHeight:function(){return t()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutFooter);var KTLayoutHeaderMenu=function(){var e,t,a,n,i=function(){n=new KTOffcanvas(a,{overlay:!0,baseClass:"header-menu-wrapper",closeBy:"kt_header_menu_mobile_close_btn",toggleBy:{target:"kt_header_mobile_toggle",state:"burger-icon-active"}}),t=new KTMenu(e,{submenu:{desktop:"dropdown",tablet:"accordion",mobile:"accordion"},accordion:{slideSpeed:200,expandAll:!1}}),t.on("linkClick",function(e){KTUtil.isBreakpointDown("lg")&&n.hide()})};return{init:function(t,n){e=KTUtil.getById(t),a=KTUtil.getById(n),e&&i()},getMenuElement:function(){return e},getOffcanvasElement:function(){return a},getMenu:function(){return t},pauseDropdownHover:function(e){t&&t.pauseDropdownHover(e)},getOffcanvas:function(){return n},closeMobileOffcanvas:function(){t&&KTUtil.isMobileDevice()&&n.hide()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutHeaderMenu);var KTLayoutHeaderTopbar=function(){var e,t,a=function(){t=new KTToggle(e,KTUtil.getBody(),{targetState:"topbar-mobile-on",toggleState:"active"})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getToggleElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutHeaderTopbar);var KTLayoutHeader=function(){var e,t,a,n=function(){var t={offset:{desktop:80,tabletAndMobile:!1}};a=new KTHeader(e,t)},i=function(){var t=0;return e&&(t=KTUtil.actualHeight(e)+1),t},o=function(){var e;return e=KTUtil.actualHeight(t)};return{init:function(a,i){e=KTUtil.getById(a),t=KTUtil.getById(i),e&&n()},isFixed:function(){return KTUtil.hasClass(KTUtil.getBody(),"header-fixed")},isFixedForMobile:function(){return KTUtil.hasClass(KTUtil.getBody(),"header-mobile-fixed")},getElement:function(){return e},getElementForMobile:function(){return t},getHeader:function(){return a},getHeight:function(){return i()},getHeightForMobile:function(){return o()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutHeader);var KTLayoutStickyCard=function(){var e,t,a=function(){var a=$(e).offset().top;"undefined"!=typeof KTLayoutHeader&&(a-=KTLayoutHeader.getHeight()),t=new KTCard(e,{sticky:{offset:a,zIndex:90,position:{top:function(){var e=0;KTUtil.getBody();return KTUtil.isBreakpointUp("lg")?("undefined"!=typeof KTLayoutHeader&&KTLayoutHeader.isFixed()&&(e+=KTLayoutHeader.getHeight()),"undefined"!=typeof KTLayoutSubheader&&KTLayoutSubheader.isFixed()&&(e+=KTLayoutSubheader.getHeight())):"undefined"!=typeof KTLayoutHeader&&KTLayoutHeader.isFixedForMobile()&&(e+=KTLayoutHeader.getHeightForMobile()),e-=1},left:function(t){return KTUtil.offset(e).left},right:function(t){var a=KTUtil.getBody(),n=parseInt(KTUtil.css(e,"width")),i=parseInt(KTUtil.css(a,"width")),o=KTUtil.offset(e).left;return i-n-o}}}}),t.initSticky(),KTUtil.addResizeHandler(function(){t.updateSticky()})};return{init:function(t){e=KTUtil.getById(t),e&&a()},update:function(){t&&t.updateSticky()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutStickyCard);var KTLayoutStretchedCard=function(){var e,t=function(){var t=KTUtil.find(e,".card-scroll"),a=KTUtil.find(e,".card-body"),n=KTUtil.find(e,".card-header"),i=KTLayoutContent.getHeight();i-=parseInt(KTUtil.actualHeight(n)),i=i-parseInt(KTUtil.css(e,"marginTop"))-parseInt(KTUtil.css(e,"marginBottom")),i=i-parseInt(KTUtil.css(e,"paddingTop"))-parseInt(KTUtil.css(e,"paddingBottom")),i=i-parseInt(KTUtil.css(a,"paddingTop"))-parseInt(KTUtil.css(a,"paddingBottom")),i=i-parseInt(KTUtil.css(a,"marginTop"))-parseInt(KTUtil.css(a,"marginBottom")),i-=3,KTUtil.css(t,"height",i+"px")};return{init:function(a){e=KTUtil.getById(a),e&&(t(),KTUtil.addResizeHandler(function(){t()}))},update:function(){t()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutStretchedCard);var KTLayoutSubheader=function(){var e,t=function(){var t=0;return e&&(t=KTUtil.actualHeight(e)),t};return{init:function(t){e=KTUtil.getById(t),!e},isFixed:function(){return KTUtil.hasClass(KTUtil.getBody(),"subheader-fixed")},getElement:function(){return e},getHeight:function(){return t()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutSubheader);var KTLayoutChat=function(){var e=function(e){var a=KTUtil.find(e,".scroll"),n=KTUtil.find(e,".card-body"),i=KTUtil.find(e,".card-header"),o=KTUtil.find(e,".card-footer");a&&(KTUtil.scrollInit(a,{windowScroll:!1,mobileNativeScroll:!0,desktopNativeScroll:!1,resetHeightOnDestroy:!0,handleWindowResize:!0,rememberPosition:!0,height:function(){var e;return KTUtil.isBreakpointDown("lg")?KTUtil.hasAttr(a,"data-mobile-height")?parseInt(KTUtil.attr(a,"data-mobile-height")):400:KTUtil.isBreakpointUp("lg")&&KTUtil.hasAttr(a,"data-height")?parseInt(KTUtil.attr(a,"data-height")):(e=KTLayoutContent.getHeight(),a&&(e=e-parseInt(KTUtil.css(a,"margin-top"))-parseInt(KTUtil.css(a,"margin-bottom"))),i&&(e-=parseInt(KTUtil.css(i,"height")),e=e-parseInt(KTUtil.css(i,"margin-top"))-parseInt(KTUtil.css(i,"margin-bottom"))),n&&(e=e-parseInt(KTUtil.css(n,"padding-top"))-parseInt(KTUtil.css(n,"padding-bottom"))),o&&(e-=parseInt(KTUtil.css(o,"height")),e=e-parseInt(KTUtil.css(o,"margin-top"))-parseInt(KTUtil.css(o,"margin-bottom"))),e-=2)}}),KTUtil.on(e,".card-footer textarea","keydown",function(a){return 13==a.keyCode?(t(e),a.preventDefault(),!1):void 0}),KTUtil.on(e,".card-footer .chat-send","click",function(a){t(e)}))},t=function(e){var t=KTUtil.find(e,".messages"),a=KTUtil.find(e,".scroll"),n=KTUtil.find(e,"textarea");if(0!==n.value.length){var i=document.createElement("DIV");KTUtil.addClass(i,"d-flex flex-column mb-5 align-items-end");var o="";o+='<div class="d-flex align-items-center">',o+="	<div>",o+='		<span class="text-muted font-size-sm">2 Hours</span>',o+='		<a href="#" class="text-dark-75 text-hover-primary font-weight-bold font-size-h6">You</a>',o+="	</div>",o+='	<div class="symbol symbol-circle symbol-40 ml-3">',o+='		<img alt="Pic" src="assets/media/users/300_12.jpg"/>',o+="	</div>",o+="</div>",o+='<div class="mt-2 rounded p-5 bg-light-primary text-dark-50 font-weight-bold font-size-lg text-right max-w-400px">'+n.value+"</div>",KTUtil.setHTML(i,o),t.appendChild(i),n.value="",a.scrollTop=parseInt(KTUtil.css(t,"height"));var r;(r=KTUtil.data(a).get("ps"))&&r.update(),setTimeout(function(){var e=document.createElement("DIV");KTUtil.addClass(e,"d-flex flex-column mb-5 align-items-start");var i="";i+='<div class="d-flex align-items-center">',i+='	<div class="symbol symbol-circle symbol-40 mr-3">',i+='		<img alt="Pic" src="assets/media/users/300_12.jpg"/>',i+="	</div>",i+="	<div>",i+='		<a href="#" class="text-dark-75 text-hover-primary font-weight-bold font-size-h6">Matt Pears</a>',i+='		<span class="text-muted font-size-sm">Just now</span>',i+="	</div>",i+="</div>",i+='<div class="mt-2 rounded p-5 bg-light-success text-dark-50 font-weight-bold font-size-lg text-left max-w-400px">',i+="Right before vacation season we have the next Big Deal for you.",i+="</div>",KTUtil.setHTML(e,i),t.appendChild(e),n.value="",a.scrollTop=parseInt(KTUtil.css(t,"height"));var o;(o=KTUtil.data(a).get("ps"))&&o.update()},2e3)}};return{init:function(t){e(KTUtil.getById(t)),("keenthemes.com"==encodeURI(window.location.hostname)||"www.keenthemes.com"==encodeURI(window.location.hostname))&&setTimeout(function(){if(!KTCookie.getCookie("kt_app_chat_shown")){var e=new Date((new Date).getTime()+36e5);KTCookie.setCookie("kt_app_chat_shown",1,{expires:e}),KTUtil.getById("kt_app_chat_launch_btn")&&KTUtil.getById("kt_app_chat_launch_btn").click()}},2e3)},setup:function(t){e(t)}}}();"undefined"!=typeof module&&(module.exports=KTLayoutChat);var KTLayoutDemoPanel=function(){var e,t,a=function(){t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_demo_panel_close",toggleBy:"kt_demo_panel_toggle"});var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content"),i=KTUtil.find(e,".offcanvas-wrapper"),o=KTUtil.find(e,".offcanvas-footer");KTUtil.scrollInit(i,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);
        return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),i&&(t-=parseInt(KTUtil.css(i,"marginTop")),t-=parseInt(KTUtil.css(i,"marginBottom"))),o&&(t-=parseInt(KTUtil.actualHeight(o)),t-=parseInt(KTUtil.css(o,"marginTop")),t-=parseInt(KTUtil.css(o,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}}),"undefined"!=typeof offcanvas&&0===offcanvas.length&&offcanvas.on("hide",function(){var e=new Date((new Date).getTime()+36e5);KTCookie.setCookie("kt_demo_panel_shown",1,{expires:e})})},n=function(){("keenthemes.com"==encodeURI(window.location.hostname)||"www.keenthemes.com"==encodeURI(window.location.hostname))&&setTimeout(function(){if(!KTCookie.getCookie("kt_demo_panel_shown")){var e=new Date((new Date).getTime()+9e5);KTCookie.setCookie("kt_demo_panel_shown",1,{expires:e}),"undefined"!=typeof t&&t.show()}},4e3)};return{init:function(t){e=KTUtil.getById(t),e&&(a(),n())}}}();"undefined"!=typeof module&&(module.exports=KTLayoutDemoPanel);var KTLayoutExamples=function(){var e=function(e){var t=e;"undefined"==typeof t&&(t=document.querySelectorAll(".example:not(.example-compact):not(.example-hover):not(.example-basic)"));for(var a=0;a<t.length;++a){var n=t[a],i=KTUtil.find(n,".example-copy"),o=new ClipboardJS(i,{target:function(e){var t=e.closest(".example"),a=KTUtil.find(t,".example-code .tab-pane.active");return a||(a=KTUtil.find(t,".example-code")),a}});o.on("success",function(e){KTUtil.addClass(e.trigger,"example-copied"),e.clearSelection(),setTimeout(function(){KTUtil.removeClass(e.trigger,"example-copied")},2e3)})}},t=function(e){var t,a,n,i,o=e;if("undefined"==typeof o)var o=document.querySelectorAll(".example.example-compact");for(var r=0;r<o.length;++r){var t=o[r],a=KTUtil.find(t,".example-toggle"),n=KTUtil.find(t,".example-copy");KTUtil.addEvent(a,"click",function(){var e=this.closest(".example"),t=KTUtil.find(e,".example-code"),a=this;KTUtil.hasClass(this,"example-toggled")?KTUtil.slideUp(t,300,function(){KTUtil.removeClass(a,"example-toggled"),KTUtil.removeClass(t,"example-code-on"),KTUtil.hide(t)}):(KTUtil.addClass(t,"example-code-on"),KTUtil.addClass(this,"example-toggled"),KTUtil.slideDown(t,300,function(){KTUtil.show(t)}))});var i=new ClipboardJS(n,{target:function(e){var t=e.closest(".example"),a=KTUtil.find(t,".example-code .tab-pane.active");return a||(a=KTUtil.find(t,".example-code")),a}});i.on("success",function(e){KTUtil.addClass(e.trigger,"example-copied"),e.clearSelection(),setTimeout(function(){KTUtil.removeClass(e.trigger,"example-copied")},2e3)})}};return{init:function(a,n){e(a),t(a)}}}();"undefined"!=typeof module&&"undefined"!=typeof module.exports&&(module.exports=KTLayoutExamples);var KTLayoutQuickActions=function(){var e,t,a=function(){var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content");t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_actions_close",toggleBy:"kt_quick_actions_toggle"}),KTUtil.scrollInit(n,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickActions);var KTLayoutQuickCartPanel=function(){var e,t,a=function(){t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_cart_close",toggleBy:"kt_quick_cart_toggle"});var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content"),i=KTUtil.find(e,".offcanvas-wrapper"),o=KTUtil.find(e,".offcanvas-footer");KTUtil.scrollInit(i,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),i&&(t-=parseInt(KTUtil.css(i,"marginTop")),t-=parseInt(KTUtil.css(i,"marginBottom"))),o&&(t-=parseInt(KTUtil.actualHeight(o)),t-=parseInt(KTUtil.css(o,"marginTop")),t-=parseInt(KTUtil.css(o,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}})};return{init:function(t){e=KTUtil.getById(t),e&&a()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickCartPanel);var KTLayoutQuickNotifications=function(){var e,t,a=function(){var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content");t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_notifications_close",toggleBy:"kt_quick_notifications_toggle"}),KTUtil.scrollInit(n,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickNotifications);var KTLayoutQuickPanel=function(){var e,t,a,n,i,o=function(){var t,a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content"),t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2},r=function(){t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_panel_close",toggleBy:"kt_quick_panel_toggle"})},l=function(){KTUtil.scrollInit(a,{mobileNativeScroll:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){return o()}})},d=function(){KTUtil.scrollInit(n,{mobileNativeScroll:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){return o()}})},s=function(){KTUtil.scrollInit(i,{mobileNativeScroll:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){return o()}})},c=function(){$(e).find('a[data-toggle="tab"]').on("shown.bs.tab",function(e){KTUtil.scrollUpdate(a),KTUtil.scrollUpdate(n),KTUtil.scrollUpdate(i)})};return{init:function(t){e=KTUtil.getById(t),a=KTUtil.getById("kt_quick_panel_notifications"),n=KTUtil.getById("kt_quick_panel_logs"),i=KTUtil.getById("kt_quick_panel_settings"),r(),l(),d(),s(),c()}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickPanel);var KTLayoutQuickSearch=function(){var e,t,a=function(){var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content"),i=KTUtil.find(e,".quick-search-form"),o=KTUtil.find(e,".quick-search-wrapper");t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_search_close",toggleBy:"kt_quick_search_toggle"}),KTUtil.scrollInit(o,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),o&&(t-=parseInt(KTUtil.actualHeight(i)),t-=parseInt(KTUtil.css(i,"marginTop")),t-=parseInt(KTUtil.css(i,"marginBottom")),t-=parseInt(KTUtil.css(o,"marginTop")),t-=parseInt(KTUtil.css(o,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickSearch);var KTLayoutQuickUser=function(){var e,t,a=function(){var a=KTUtil.find(e,".offcanvas-header"),n=KTUtil.find(e,".offcanvas-content");t=new KTOffcanvas(e,{overlay:!0,baseClass:"offcanvas",placement:"right",closeBy:"kt_quick_user_close",toggleBy:"kt_quick_user_toggle"}),KTUtil.scrollInit(n,{disableForMobile:!0,resetHeightOnDestroy:!0,handleWindowResize:!0,height:function(){var t=parseInt(KTUtil.getViewPort().height);return a&&(t-=parseInt(KTUtil.actualHeight(a)),t-=parseInt(KTUtil.css(a,"marginTop")),t-=parseInt(KTUtil.css(a,"marginBottom"))),n&&(t-=parseInt(KTUtil.css(n,"marginTop")),t-=parseInt(KTUtil.css(n,"marginBottom"))),t-=parseInt(KTUtil.css(e,"paddingTop")),t-=parseInt(KTUtil.css(e,"paddingBottom")),t-=2}})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutQuickUser);var KTLayoutScrolltop=function(){var e,t,a=function(){t=new KTScrolltop(e,{offset:300,speed:600})};return{init:function(t){e=KTUtil.getById(t),e&&a()},getElement:function(){return e}}}();"undefined"!=typeof module&&(module.exports=KTLayoutScrolltop);
