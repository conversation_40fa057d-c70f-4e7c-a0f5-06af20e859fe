!function(){"use strict";function e(e){e.fn._fadeIn=e.fn.fadeIn;var t=e.noop||function(){},a=/MSIE/.test(navigator.userAgent),n=/MSIE 6.0/.test(navigator.userAgent)&&!/MSIE 8.0/.test(navigator.userAgent),r=(document.documentMode,e.isFunction(document.createElement("div").style.setExpression));e.blockUI=function(e){l(window,e)},e.unblockUI=function(e){o(window,e)},e.growlUI=function(t,a,n,r){var i=e('<div class="growlUI"></div>');t&&i.append("<h1>"+t+"</h1>"),a&&i.append("<h2>"+a+"</h2>"),void 0===n&&(n=3e3);var s=function(t){t=t||{},e.blockUI({message:i,fadeIn:void 0!==t.fadeIn?t.fadeIn:700,fadeOut:void 0!==t.fadeOut?t.fadeOut:1e3,timeout:void 0!==t.timeout?t.timeout:n,centerY:!1,showOverlay:!1,onUnblock:r,css:e.blockUI.defaults.growlCSS})};s();i.css("opacity");i.mouseover(function(){s({fadeIn:0,timeout:3e4});var t=e(".blockMsg");t.stop(),t.fadeTo(300,1)}).mouseout(function(){e(".blockMsg").fadeOut(1e3)})},e.fn.block=function(t){if(this[0]===window)return e.blockUI(t),this;var a=e.extend({},e.blockUI.defaults,t||{});return this.each(function(){var t=e(this);a.ignoreIfBlocked&&t.data("blockUI.isBlocked")||t.unblock({fadeOut:0})}),this.each(function(){"static"==e.css(this,"position")&&(this.style.position="relative",e(this).data("blockUI.static",!0)),this.style.zoom=1,l(this,t)})},e.fn.unblock=function(t){return this[0]===window?(e.unblockUI(t),this):this.each(function(){o(this,t)})},e.blockUI.version=2.7,e.blockUI.defaults={message:"<h1>Please wait...</h1>",title:null,draggable:!0,theme:!1,css:{padding:0,margin:0,width:"30%",top:"40%",left:"35%",textAlign:"center",color:"#000",border:"3px solid #aaa",backgroundColor:"#fff",cursor:"wait"},themedCSS:{width:"30%",top:"40%",left:"35%"},overlayCSS:{backgroundColor:"#000",opacity:.6,cursor:"wait"},cursorReset:"default",growlCSS:{width:"350px",top:"10px",left:"",right:"10px",border:"none",padding:"5px",opacity:.6,cursor:"default",color:"#fff",backgroundColor:"#000","-webkit-border-radius":"10px","-moz-border-radius":"10px","border-radius":"10px"},iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",forceIframe:!1,baseZ:1e3,centerX:!0,centerY:!0,allowBodyStretch:!0,bindEvents:!0,constrainTabKey:!0,fadeIn:200,fadeOut:400,timeout:0,showOverlay:!0,focusInput:!0,focusableElements:":input:enabled:visible",onBlock:null,onUnblock:null,onOverlayClick:null,quirksmodeOffsetHack:4,blockMsgClass:"blockMsg",ignoreIfBlocked:!1};var i=null,s=[];function l(l,d){var u,h,m=l==window,p=d&&void 0!==d.message?d.message:void 0;if(!(d=e.extend({},e.blockUI.defaults,d||{})).ignoreIfBlocked||!e(l).data("blockUI.isBlocked")){if(d.overlayCSS=e.extend({},e.blockUI.defaults.overlayCSS,d.overlayCSS||{}),u=e.extend({},e.blockUI.defaults.css,d.css||{}),d.onOverlayClick&&(d.overlayCSS.cursor="pointer"),h=e.extend({},e.blockUI.defaults.themedCSS,d.themedCSS||{}),p=void 0===p?d.message:p,m&&i&&o(window,{fadeOut:0}),p&&"string"!=typeof p&&(p.parentNode||p.jquery)){var g=p.jquery?p[0]:p,b={};e(l).data("blockUI.history",b),b.el=g,b.parent=g.parentNode,b.display=g.style.display,b.position=g.style.position,b.parent&&b.parent.removeChild(g)}e(l).data("blockUI.onUnblock",d.onUnblock);var A,k,I,y,E=d.baseZ;A=a||d.forceIframe?e('<iframe class="blockUI" style="z-index:'+E+++';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+d.iframeSrc+'"></iframe>'):e('<div class="blockUI" style="display:none"></div>'),k=d.theme?e('<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+E+++';display:none"></div>'):e('<div class="blockUI blockOverlay" style="z-index:'+E+++';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>'),d.theme&&m?(y='<div class="blockUI '+d.blockMsgClass+' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+(E+10)+';display:none;position:fixed">',d.title&&(y+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(d.title||"&nbsp;")+"</div>"),y+='<div class="ui-widget-content ui-dialog-content"></div>',y+="</div>"):d.theme?(y='<div class="blockUI '+d.blockMsgClass+' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+(E+10)+';display:none;position:absolute">',d.title&&(y+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(d.title||"&nbsp;")+"</div>"),y+='<div class="ui-widget-content ui-dialog-content"></div>',y+="</div>"):y=m?'<div class="blockUI '+d.blockMsgClass+' blockPage" style="z-index:'+(E+10)+';display:none;position:fixed"></div>':'<div class="blockUI '+d.blockMsgClass+' blockElement" style="z-index:'+(E+10)+';display:none;position:absolute"></div>',I=e(y),p&&(d.theme?(I.css(h),I.addClass("ui-widget-content")):I.css(u)),d.theme||k.css(d.overlayCSS),k.css("position",m?"fixed":"absolute"),(a||d.forceIframe)&&A.css("opacity",0);var C=[A,k,I],O=e(m?"body":l);e.each(C,function(){this.appendTo(O)}),d.theme&&d.draggable&&e.fn.draggable&&I.draggable({handle:".ui-dialog-titlebar",cancel:"li"});var w=r&&(!e.support.boxModel||e("object,embed",m?null:l).length>0);if(n||w){if(m&&d.allowBodyStretch&&e.support.boxModel&&e("html,body").css("height","100%"),(n||!e.support.boxModel)&&!m)var S=v(l,"borderTopWidth"),F=v(l,"borderLeftWidth"),V=S?"(0 - "+S+")":0,x=F?"(0 - "+F+")":0;e.each(C,function(e,t){var a=t[0].style;if(a.position="absolute",e<2)m?a.setExpression("height","Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.support.boxModel?0:"+d.quirksmodeOffsetHack+') + "px"'):a.setExpression("height",'this.parentNode.offsetHeight + "px"'),m?a.setExpression("width",'jQuery.support.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"'):a.setExpression("width",'this.parentNode.offsetWidth + "px"'),x&&a.setExpression("left",x),V&&a.setExpression("top",V);else if(d.centerY)m&&a.setExpression("top",'(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"'),a.marginTop=0;else if(!d.centerY&&m){var n="((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "+(d.css&&d.css.top?parseInt(d.css.top,10):0)+') + "px"';a.setExpression("top",n)}})}if(p&&(d.theme?I.find(".ui-widget-content").append(p):I.append(p),(p.jquery||p.nodeType)&&e(p).show()),(a||d.forceIframe)&&d.showOverlay&&A.show(),d.fadeIn){var H=d.onBlock?d.onBlock:t,$=d.showOverlay&&!p?H:t,T=p?H:t;d.showOverlay&&k._fadeIn(d.fadeIn,$),p&&I._fadeIn(d.fadeIn,T)}else d.showOverlay&&k.show(),p&&I.show(),d.onBlock&&d.onBlock.bind(I)();if(c(1,l,d),m?(i=I[0],s=e(d.focusableElements,i),d.focusInput&&setTimeout(f,20)):function(e,t,a){var n=e.parentNode,r=e.style,i=(n.offsetWidth-e.offsetWidth)/2-v(n,"borderLeftWidth"),s=(n.offsetHeight-e.offsetHeight)/2-v(n,"borderTopWidth");t&&(r.left=i>0?i+"px":"0");a&&(r.top=s>0?s+"px":"0")}(I[0],d.centerX,d.centerY),d.timeout){var M=setTimeout(function(){m?e.unblockUI(d):e(l).unblock(d)},d.timeout);e(l).data("blockUI.timeout",M)}}}function o(t,a){var n,r,l=t==window,o=e(t),u=o.data("blockUI.history"),f=o.data("blockUI.timeout");f&&(clearTimeout(f),o.removeData("blockUI.timeout")),a=e.extend({},e.blockUI.defaults,a||{}),c(0,t,a),null===a.onUnblock&&(a.onUnblock=o.data("blockUI.onUnblock"),o.removeData("blockUI.onUnblock")),r=l?e("body").children().filter(".blockUI").add("body > .blockUI"):o.find(">.blockUI"),a.cursorReset&&(r.length>1&&(r[1].style.cursor=a.cursorReset),r.length>2&&(r[2].style.cursor=a.cursorReset)),l&&(i=s=null),a.fadeOut?(n=r.length,r.stop().fadeOut(a.fadeOut,function(){0==--n&&d(r,u,a,t)})):d(r,u,a,t)}function d(t,a,n,r){var i=e(r);if(!i.data("blockUI.isBlocked")){t.each(function(e,t){this.parentNode&&this.parentNode.removeChild(this)}),a&&a.el&&(a.el.style.display=a.display,a.el.style.position=a.position,a.el.style.cursor="default",a.parent&&a.parent.appendChild(a.el),i.removeData("blockUI.history")),i.data("blockUI.static")&&i.css("position","static"),"function"==typeof n.onUnblock&&n.onUnblock(r,n);var s=e(document.body),l=s.width(),o=s[0].style.width;s.width(l-1).width(l),s[0].style.width=o}}function c(t,a,n){var r=a==window,s=e(a);if((t||(!r||i)&&(r||s.data("blockUI.isBlocked")))&&(s.data("blockUI.isBlocked",t),r&&n.bindEvents&&(!t||n.showOverlay))){var l="mousedown mouseup keydown keypress keyup touchstart touchend touchmove";t?e(document).bind(l,n,u):e(document).unbind(l,u)}}function u(t){if("keydown"===t.type&&t.keyCode&&9==t.keyCode&&i&&t.data.constrainTabKey){var a=s,n=!t.shiftKey&&t.target===a[a.length-1],r=t.shiftKey&&t.target===a[0];if(n||r)return setTimeout(function(){f(r)},10),!1}var l=t.data,o=e(t.target);return o.hasClass("blockOverlay")&&l.onOverlayClick&&l.onOverlayClick(t),o.parents("div."+l.blockMsgClass).length>0||0===o.parents().children().filter("div.blockUI").length}function f(e){if(s){var t=s[!0===e?s.length-1:0];t&&t.focus()}}function v(t,a){return parseInt(e.css(t,a),10)||0}}"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(jQuery)}(),("function"==typeof define&&define.amd?define:function(e,t){"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):window.toastr=t(window.jQuery)})(["jquery"],function(e){return function(){function t(t,a){return t||(t=s()),(o=e("#"+t.containerId)).length?o:(a&&(o=function(t){return(o=e("<div/>").attr("id",t.containerId).addClass(t.positionClass)).appendTo(e(t.target)),o}(t)),o)}function a(t){for(var a=o.children(),r=a.length-1;r>=0;r--)n(e(a[r]),t)}function n(t,a,n){var r=!(!n||!n.force)&&n.force;return!(!t||!r&&0!==e(":focus",t).length||(t[a.hideMethod]({duration:a.hideDuration,easing:a.hideEasing,complete:function(){l(t)}}),0))}function r(e){d&&d(e)}function i(a){function n(e){return null==e&&(e=""),e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function i(t){var a=t&&!1!==h.closeMethod?h.closeMethod:h.hideMethod,n=t&&!1!==h.closeDuration?h.closeDuration:h.hideDuration,i=t&&!1!==h.closeEasing?h.closeEasing:h.hideEasing;if(!e(":focus",g).length||t)return clearTimeout(y.intervalId),g[a]({duration:n,easing:i,complete:function(){l(g),clearTimeout(p),h.onHidden&&"hidden"!==E.state&&h.onHidden(),E.state="hidden",E.endTime=new Date,r(E)}})}function d(){(h.timeOut>0||h.extendedTimeOut>0)&&(p=setTimeout(i,h.extendedTimeOut),y.maxHideTime=parseFloat(h.extendedTimeOut),y.hideEta=(new Date).getTime()+y.maxHideTime)}function f(){clearTimeout(p),y.hideEta=0,g.stop(!0,!0)[h.showMethod]({duration:h.showDuration,easing:h.showEasing})}function v(){var e=(y.hideEta-(new Date).getTime())/y.maxHideTime*100;k.width(e+"%")}var h=s(),m=a.iconClass||h.iconClass;if(void 0!==a.optionsOverride&&(h=e.extend(h,a.optionsOverride),m=a.optionsOverride.iconClass||m),!function(e,t){if(e.preventDuplicates){if(t.message===c)return!0;c=t.message}return!1}(h,a)){u++,o=t(h,!0);var p=null,g=e("<div/>"),b=e("<div/>"),A=e("<div/>"),k=e("<div/>"),I=e(h.closeHtml),y={intervalId:null,hideEta:null,maxHideTime:null},E={toastId:u,state:"visible",startTime:new Date,options:h,map:a};return a.iconClass&&g.addClass(h.toastClass).addClass(m),function(){if(a.title){var e=a.title;h.escapeHtml&&(e=n(a.title)),b.append(e).addClass(h.titleClass),g.append(b)}}(),function(){if(a.message){var e=a.message;h.escapeHtml&&(e=n(a.message)),A.append(e).addClass(h.messageClass),g.append(A)}}(),h.closeButton&&(I.addClass(h.closeClass).attr("role","button"),g.prepend(I)),h.progressBar&&(k.addClass(h.progressClass),g.prepend(k)),h.rtl&&g.addClass("rtl"),h.newestOnTop?o.prepend(g):o.append(g),function(){var e="";switch(a.iconClass){case"toast-success":case"toast-info":e="polite";break;default:e="assertive"}g.attr("aria-live",e)}(),g.hide(),g[h.showMethod]({duration:h.showDuration,easing:h.showEasing,complete:h.onShown}),h.timeOut>0&&(p=setTimeout(i,h.timeOut),y.maxHideTime=parseFloat(h.timeOut),y.hideEta=(new Date).getTime()+y.maxHideTime,h.progressBar&&(y.intervalId=setInterval(v,10))),h.closeOnHover&&g.hover(f,d),!h.onclick&&h.tapToDismiss&&g.click(i),h.closeButton&&I&&I.click(function(e){e.stopPropagation?e.stopPropagation():void 0!==e.cancelBubble&&!0!==e.cancelBubble&&(e.cancelBubble=!0),h.onCloseClick&&h.onCloseClick(e),i(!0)}),h.onclick&&g.click(function(e){h.onclick(e),i()}),r(E),h.debug&&console&&console.log(E),g}}function s(){return e.extend({},{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1},v.options)}function l(e){o||(o=t()),e.is(":visible")||(e.remove(),e=null,0===o.children().length&&(o.remove(),c=void 0))}var o,d,c,u=0,f={error:"error",info:"info",success:"success",warning:"warning"},v={clear:function(e,r){var i=s();o||t(i),n(e,i,r)||a(i)},remove:function(a){var n=s();return o||t(n),a&&0===e(":focus",a).length?void l(a):void(o.children().length&&o.remove())},error:function(e,t,a){return i({type:f.error,iconClass:s().iconClasses.error,message:e,optionsOverride:a,title:t})},getContainer:t,info:function(e,t,a){return i({type:f.info,iconClass:s().iconClasses.info,message:e,optionsOverride:a,title:t})},options:{},subscribe:function(e){d=e},success:function(e,t,a){return i({type:f.success,iconClass:s().iconClasses.success,message:e,optionsOverride:a,title:t})},version:"2.1.4",warning:function(e,t,a){return i({type:f.warning,iconClass:s().iconClasses.warning,message:e,optionsOverride:a,title:t})}};return v}()}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).FormValidation={})}(this,function(e){"use strict";function t(e){for(var t=e.length,a=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],n=0,r=0;t--;)r+=a[n][parseInt(e.charAt(t),10)],n=1-n;return r%10==0&&r>0}function a(e){for(var t=e.length,a=5,n=0;n<t;n++)a=(2*(a||10)%11+parseInt(e.charAt(n),10))%10;return 1===a}function n(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",a=e.length,n=t.length,r=Math.floor(n/2),i=0;i<a;i++)r=(2*(r||n)%(n+1)+t.indexOf(e.charAt(i)))%n;return 1===r}function r(e){for(var t=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],a=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],n=e.reverse(),r=0,i=0;i<n.length;i++)r=t[r][a[i%8][n[i]]];return 0===r}var i={luhn:t,mod11And10:a,mod37And36:n,verhoeff:r};function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,a){return t&&l(e.prototype,t),a&&l(e,a),e}function d(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){return(f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?v(e):t}function m(e,t,a,n){var r=(a.getAttribute("type")||"").toLowerCase();switch(a.tagName.toLowerCase()){case"textarea":return a.value;case"select":var i=a,s=i.selectedIndex;return s>=0?i.options.item(s).value:"";case"input":if("radio"===r||"checkbox"===r){var l=n.filter(function(e){return e.checked}).length;return 0===l?"":l+""}return a.value;default:return""}}function p(e,t){var a=Array.isArray(t)?t:[t],n=e;return a.forEach(function(e){n=n.replace("%s",e)}),n}function g(e,t){if("function"==typeof e)return e.apply(this,t);if("string"==typeof e){var a=e;"()"===a.substring(a.length-2)&&(a=a.substring(0,a.length-2));var n=a.split("."),r=n.pop(),i=window,s=!0,l=!1,o=void 0;try{for(var d,c=n[Symbol.iterator]();!(s=(d=c.next()).done);s=!0){i=i[d.value]}}catch(e){l=!0,o=e}finally{try{!s&&null!=c.return&&c.return()}finally{if(l)throw o}}return void 0===i[r]?null:i[r].apply(this,t)}}var b={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DANKORT:{length:[16],prefix:["5019"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},ELO:{length:[16],prefix:["4011","4312","4389","4514","4573","4576","5041","5066","5067","509","6277","6362","6363","650","6516","6550"]},FORBRUGSFORENINGEN:{length:[16],prefix:["600722"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","5868","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]},VISA_ELECTRON:{length:[16],prefix:["4026","417500","4405","4508","4844","4913","4917"]}};function A(e,t,a,n){if(isNaN(e)||isNaN(t)||isNaN(a))return!1;if(e<1e3||e>9999||t<=0||t>12)return!1;if(a<=0||a>[31,e%400==0||e%100!=0&&e%4==0?29:28,31,30,31,30,31,31,30,31,30,31][t-1])return!1;if(!0===n){var r=new Date,i=r.getFullYear(),s=r.getMonth(),l=r.getDate();return e<i||e===i&&t-1<s||e===i&&t-1===s&&a<l}return!0}function k(e,t){return new Promise(function(a,n){var r=Object.assign({},{crossDomain:!1,headers:{},method:"GET",params:{}},t),i=Object.keys(r.params).map(function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(r.params[e]))}).join("&"),s=e.indexOf("?"),l="GET"===r.method?"".concat(e).concat(s?"?":"&").concat(i):e;if(r.crossDomain){var o=document.createElement("script"),d="___fetch".concat(Date.now(),"___");window[d]=function(e){delete window[d],a(e)},o.src="".concat(l).concat(s?"&":"?","callback=").concat(d),o.async=!0,o.addEventListener("load",function(){o.parentNode.removeChild(o)}),o.addEventListener("error",function(){return n}),document.head.appendChild(o)}else{var c=new XMLHttpRequest;c.open(r.method,l),c.setRequestHeader("X-Requested-With","XMLHttpRequest"),"POST"===r.method&&c.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),Object.keys(r.headers).forEach(function(e){return c.setRequestHeader(e,r.headers[e])}),c.addEventListener("load",function(){a(JSON.parse(this.responseText))}),c.addEventListener("error",function(){return n}),c.send(function(e){return Object.keys(e).map(function(t){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e[t]))}).join("&")}(r.params))}})}function I(e,t){if(!/^\d{13}$/.test(e))return!1;var a=parseInt(e.substr(0,2),10),n=parseInt(e.substr(2,2),10),r=parseInt(e.substr(7,2),10),i=parseInt(e.substr(12,1),10);if(a>31||n>12)return!1;for(var s=0,l=0;l<6;l++)s+=(7-l)*(parseInt(e.charAt(l),10)+parseInt(e.charAt(l+6),10));if((10===(s=11-s%11)||11===s)&&(s=0),s!==i)return!1;switch(t.toUpperCase()){case"BA":return 10<=r&&r<=19;case"MK":return 41<=r&&r<=49;case"ME":return 20<=r&&r<=29;case"RS":return 70<=r&&r<=99;case"SI":return 50<=r&&r<=59;default:return!0}}function y(e){if(!/^\d{9,10}$/.test(e))return{meta:{},valid:!1};var t=1900+parseInt(e.substr(0,2),10),a=parseInt(e.substr(2,2),10)%50%20,n=parseInt(e.substr(4,2),10);if(9===e.length){if(t>=1980&&(t-=100),t>1953)return{meta:{},valid:!1}}else t<1954&&(t+=100);if(!A(t,a,n))return{meta:{},valid:!1};if(10===e.length){var r=parseInt(e.substr(0,9),10)%11;return t<1985&&(r%=10),{meta:{},valid:"".concat(r)===e.substr(9,1)}}return{meta:{},valid:!0}}function E(e){return{meta:{},valid:/^[0-9]{11}$/.test(e)&&a(e)}}function C(e){return/^[2-9]\d{11}$/.test(e)?{meta:{},valid:r(e.split("").map(function(e){return parseInt(e,10)}))}:{meta:{},valid:!1}}function O(e){return/^\d{1,9}$/.test(e)?{meta:{},valid:t(e)}:{meta:{},valid:!1}}function w(e){if(!/^[0-9]{11}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.charAt(0),10),a=parseInt(e.substr(1,2),10);if(!A(a=100*(t%2==0?17+t/2:17+(t+1)/2)+a,parseInt(e.substr(3,2),10),parseInt(e.substr(5,2),10),!0))return{meta:{},valid:!1};var n,r=[1,2,3,4,5,6,7,8,9,1],i=0;for(n=0;n<10;n++)i+=parseInt(e.charAt(n),10)*r[n];if(10!==(i%=11))return{meta:{},valid:"".concat(i)===e.charAt(10)};for(i=0,r=[3,4,5,6,7,8,9,1,2,3],n=0;n<10;n++)i+=parseInt(e.charAt(n),10)*r[n];return 10===(i%=11)&&(i=0),{meta:{},valid:"".concat(i)===e.charAt(10)}}function S(e){if(!/^[0-9]{10}$/.test(e)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(e))return{meta:{},valid:!1};var a=e.replace(/[^0-9]/g,"");return A(parseInt(a.substr(0,2),10)+1900,parseInt(a.substr(2,2),10),parseInt(a.substr(4,2),10))?{meta:{},valid:t(a)}:{meta:{},valid:!1}}function F(e){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(e))return{meta:{},valid:!1};var a=parseInt(e.substr(0,2),10);return A(a=a>=new Date.getFullYear%100?a+1900:a+2e3,parseInt(e.substr(2,2),10),parseInt(e.substr(4,2),10))?{meta:{},valid:t(e)}:{meta:{},valid:!1}}function V(e){var t=e;return/^DE[0-9]{9}$/.test(t)&&(t=t.substr(2)),/^[0-9]{9}$/.test(t)?{meta:{},valid:a(t)}:{meta:{},valid:!1}}function x(e){var a=e;if(/^FR[0-9A-Z]{2}[0-9]{9}$/.test(a)&&(a=a.substr(2)),!/^[0-9A-Z]{2}[0-9]{9}$/.test(a))return{meta:{},valid:!1};if(!t(a.substr(2)))return{meta:{},valid:!1};if(/^[0-9]{2}$/.test(a.substr(0,2)))return{meta:{},valid:a.substr(0,2)==="".concat(parseInt(a.substr(2)+"12",10)%97)};var n,r="0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";return n=/^[0-9]$/.test(a.charAt(0))?24*r.indexOf(a.charAt(0))+r.indexOf(a.charAt(1))-10:34*r.indexOf(a.charAt(0))+r.indexOf(a.charAt(1))-100,{meta:{},valid:(parseInt(a.substr(2),10)+1+Math.floor(n/11))%11==n%11}}function H(e){var t=e;if(/^(GR|EL)[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};8===t.length&&(t="0".concat(t));for(var a=[256,128,64,32,16,8,4,2],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return{meta:{},valid:"".concat(n=n%11%10)===t.substr(8,1)}}function $(e){var t=e;return/^HR[0-9]{11}$/.test(t)&&(t=t.substr(2)),/^[0-9]{11}$/.test(t)?{meta:{},valid:a(t)}:{meta:{},valid:!1}}function T(e){var a=e;if(/^IT[0-9]{11}$/.test(a)&&(a=a.substr(2)),!/^[0-9]{11}$/.test(a))return{meta:{},valid:!1};if(0===parseInt(a.substr(0,7),10))return{meta:{},valid:!1};var n=parseInt(a.substr(7,3),10);return n<1||n>201&&999!==n&&888!==n?{meta:{},valid:!1}:{meta:{},valid:t(a)}}function M(e){var a=e;return/^SE[0-9]{10}01$/.test(a)&&(a=a.substr(2)),/^[0-9]{10}01$/.test(a)?{meta:{},valid:t(a=a.substr(0,10))}:{meta:{},valid:!1}}var N={between:function(){var e=function(e){return parseFloat("".concat(e).replace(",","."))};return{validate:function(t){var a=t.value;if(""===a)return{valid:!0};var n=Object.assign({},{inclusive:!0,message:""},t.options),r=e(n.min),i=e(n.max);return n.inclusive?{message:p(t.l10n?n.message||t.l10n.between.default:n.message,["".concat(r),"".concat(i)]),valid:parseFloat(a)>=r&&parseFloat(a)<=i}:{message:p(t.l10n?n.message||t.l10n.between.notInclusive:n.message,["".concat(r),"".concat(i)]),valid:parseFloat(a)>r&&parseFloat(a)<i}}}},blank:function(){return{validate:function(e){return{valid:!0}}}},callback:function(){return{validate:function(e){var t=g(e.options.callback,[e]);return"boolean"==typeof t?{valid:t}:t}}},choice:function(){return{validate:function(e){var t="select"===e.element.tagName.toLowerCase()?e.element.querySelectorAll("option:checked").length:e.elements.filter(function(e){return e.checked}).length,a=e.options.min?"".concat(e.options.min):"",n=e.options.max?"".concat(e.options.max):"",r=e.l10n?e.options.message||e.l10n.choice.default:e.options.message,i=!(a&&t<parseInt(a,10)||n&&t>parseInt(n,10));switch(!0){case!!a&&!!n:r=p(e.l10n?e.l10n.choice.between:e.options.message,[a,n]);break;case!!a:r=p(e.l10n?e.l10n.choice.more:e.options.message,a);break;case!!n:r=p(e.l10n?e.l10n.choice.less:e.options.message,n)}return{message:r,valid:i}}}},creditCard:function(){return{validate:function(e){if(""===e.value)return{meta:{type:null},valid:!0};if(/[^0-9-\s]+/.test(e.value))return{meta:{type:null},valid:!1};var a=e.value.replace(/\D/g,"");if(!t(a))return{meta:{type:null},valid:!1};for(var n=0,r=Object.keys(b);n<r.length;n++){var i=r[n];for(var s in b[i].prefix)if(e.value.substr(0,b[i].prefix[s].length)===b[i].prefix[s]&&-1!==b[i].length.indexOf(a.length))return{meta:{type:i},valid:!0}}return{meta:{type:null},valid:!1}}}},date:function(){var e=function(e,t,a){var n=t.indexOf("YYYY"),r=t.indexOf("MM"),i=t.indexOf("DD");if(-1===n||-1===r||-1===i)return null;var s=e.split(" "),l=s[0].split(a);if(l.length<3)return null;var o=new Date(parseInt(l[n],10),parseInt(l[r],10)-1,parseInt(l[i],10));if(s.length>1){var d=s[1].split(":");o.setHours(d.length>0?parseInt(d[0],10):0),o.setMinutes(d.length>1?parseInt(d[1],10):0),o.setSeconds(d.length>2?parseInt(d[2],10):0)}return o},t=function(e,t){var a=t.replace(/Y/g,"y").replace(/M/g,"m").replace(/D/g,"d").replace(/:m/g,":M").replace(/:mm/g,":MM").replace(/:S/,":s").replace(/:SS/,":ss"),n=e.getDate(),r=n<10?"0".concat(n):n,i=e.getMonth()+1,s=i<10?"0".concat(i):i,l="".concat(e.getFullYear()).substr(2),o=e.getFullYear(),d=e.getHours()%12||12,c=d<10?"0".concat(d):d,u=e.getHours(),f=u<10?"0".concat(u):u,v=e.getMinutes(),h=v<10?"0".concat(v):v,m=e.getSeconds(),p=m<10?"0".concat(m):m,g={H:"".concat(u),HH:"".concat(f),M:"".concat(v),MM:"".concat(h),d:"".concat(n),dd:"".concat(r),h:"".concat(d),hh:"".concat(c),m:"".concat(i),mm:"".concat(s),s:"".concat(m),ss:"".concat(p),yy:"".concat(l),yyyy:"".concat(o)};return a.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\1?|"[^"]*"|'[^']*'/g,function(e){return g[e]?g[e]:e.slice(1,e.length-1)})};return{validate:function(a){if(""===a.value)return{meta:{date:null},valid:!0};var n=Object.assign({},{format:a.element&&"date"===a.element.getAttribute("type")?"YYYY-MM-DD":"MM/DD/YYYY",message:""},a.options),r=a.l10n?a.l10n.date.default:n.message,i={message:"".concat(r),meta:{date:null},valid:!1},s=n.format.split(" "),l=s.length>1?s[1]:null,o=s.length>2?s[2]:null,d=a.value.split(" "),c=d[0],u=d.length>1?d[1]:null;if(s.length!==d.length)return i;var f=n.separator||(-1!==c.indexOf("/")?"/":-1!==c.indexOf("-")?"-":-1!==c.indexOf(".")?".":"/");if(null===f||-1===c.indexOf(f))return i;var v=c.split(f),h=s[0].split(f);if(v.length!==h.length)return i;var m=v[h.indexOf("YYYY")],g=v[h.indexOf("MM")],b=v[h.indexOf("DD")];if(!/^\d+$/.test(m)||!/^\d+$/.test(g)||!/^\d+$/.test(b)||m.length>4||g.length>2||b.length>2)return i;var k=parseInt(m,10),I=parseInt(g,10),y=parseInt(b,10);if(!A(k,I,y))return i;var E=new Date(k,I-1,y);if(l){var C=u.split(":");if(l.split(":").length!==C.length)return i;var O=C.length>0?C[0].length<=2&&/^\d+$/.test(C[0])?parseInt(C[0],10):-1:0,w=C.length>1?C[1].length<=2&&/^\d+$/.test(C[1])?parseInt(C[1],10):-1:0,S=C.length>2?C[2].length<=2&&/^\d+$/.test(C[2])?parseInt(C[2],10):-1:0;if(-1===O||-1===w||-1===S)return i;if(S<0||S>60)return i;if(O<0||O>=24||o&&O>12)return i;if(w<0||w>59)return i;E.setHours(O),E.setMinutes(w),E.setSeconds(S)}var F="function"==typeof n.min?n.min():n.min,V=F instanceof Date?F:F?e(F,h,f):E,x="function"==typeof n.max?n.max():n.max,H=x instanceof Date?x:x?e(x,h,f):E,$=F instanceof Date?t(V,n.format):F,T=x instanceof Date?t(H,n.format):x;switch(!0){case!!$&&!T:return{message:p(a.l10n?a.l10n.date.min:r,$),meta:{date:E},valid:E.getTime()>=V.getTime()};case!!T&&!$:return{message:p(a.l10n?a.l10n.date.max:r,T),meta:{date:E},valid:E.getTime()<=H.getTime()};case!!T&&!!$:return{message:p(a.l10n?a.l10n.date.range:r,[$,T]),meta:{date:E},valid:E.getTime()<=H.getTime()&&E.getTime()>=V.getTime()};default:return{message:"".concat(r),meta:{date:E},valid:!0}}}}},different:function(){return{validate:function(e){var t="function"==typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:""===t||e.value!==t}}}},digits:function(){return{validate:function(e){return{valid:""===e.value||/^\d+$/.test(e.value)}}}},emailAddress:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{multiple:!1,separator:/[,;]/},e.options),a=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;if(!0===t.multiple||"true"==="".concat(t.multiple)){for(var n=t.separator||/[,;]/,r=function(e,t){for(var a=e.split(/"/),n=a.length,r=[],i="",s=0;s<n;s++)if(s%2==0){var l=a[s].split(t),o=l.length;if(1===o)i+=l[0];else{r.push(i+l[0]);for(var d=1;d<o-1;d++)r.push(l[d]);i=l[o-1]}}else i+='"'+a[s],s<n-1&&(i+='"');return r.push(i),r}(e.value,n),i=r.length,s=0;s<i;s++)if(!a.test(r[s]))return{valid:!1};return{valid:!0}}return{valid:a.test(e.value)}}}},file:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t,a=e.options.extension?e.options.extension.toLowerCase().split(","):null,n=e.options.type?e.options.type.toLowerCase().split(","):null;if(window.File&&window.FileList&&window.FileReader){var r=e.element.files,i=r.length,s=0;if(e.options.maxFiles&&i>parseInt("".concat(e.options.maxFiles),10))return{meta:{error:"INVALID_MAX_FILES"},valid:!1};if(e.options.minFiles&&i<parseInt("".concat(e.options.minFiles),10))return{meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var l={},o=0;o<i;o++){if(s+=r[o].size,l={ext:t=r[o].name.substr(r[o].name.lastIndexOf(".")+1),file:r[o],size:r[o].size,type:r[o].type},e.options.minSize&&r[o].size<parseInt("".concat(e.options.minSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_SIZE"},l),valid:!1};if(e.options.maxSize&&r[o].size>parseInt("".concat(e.options.maxSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_SIZE"},l),valid:!1};if(a&&-1===a.indexOf(t.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_EXTENSION"},l),valid:!1};if(r[o].type&&n&&-1===n.indexOf(r[o].type.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_TYPE"},l),valid:!1}}if(e.options.maxTotalSize&&s>parseInt("".concat(e.options.maxTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:s},l),valid:!1};if(e.options.minTotalSize&&s<parseInt("".concat(e.options.minTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:s},l),valid:!1}}else if(t=e.value.substr(e.value.lastIndexOf(".")+1),a&&-1===a.indexOf(t.toLowerCase()))return{meta:{error:"INVALID_EXTENSION",ext:t},valid:!1};return{valid:!0}}}},greaterThan:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{inclusive:!0,message:""},e.options),a=parseFloat("".concat(t.min).replace(",","."));return t.inclusive?{message:p(e.l10n?t.message||e.l10n.greaterThan.default:t.message,"".concat(a)),valid:parseFloat(e.value)>=a}:{message:p(e.l10n?t.message||e.l10n.greaterThan.notInclusive:t.message,"".concat(a)),valid:parseFloat(e.value)>a}}}},identical:function(){return{validate:function(e){var t="function"==typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:""===t||e.value===t}}}},integer:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},e.options),a="."===t.decimalSeparator?"\\.":t.decimalSeparator,n="."===t.thousandsSeparator?"\\.":t.thousandsSeparator,r=new RegExp("^-?[0-9]{1,3}(".concat(n,"[0-9]{3})*(").concat(a,"[0-9]+)?$")),i=new RegExp(n,"g"),s="".concat(e.value);if(!r.test(s))return{valid:!1};n&&(s=s.replace(i,"")),a&&(s=s.replace(a,"."));var l=parseFloat(s);return{valid:!isNaN(l)&&isFinite(l)&&Math.floor(l)===l}}}},ip:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{ipv4:!0,ipv6:!0},e.options),a=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/([0-9]|[1-2][0-9]|3[0-2]))?$/,n=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*(\/(\d|\d\d|1[0-1]\d|12[0-8]))?$/;switch(!0){case t.ipv4&&!t.ipv6:return{message:e.l10n?t.message||e.l10n.ip.ipv4:t.message,valid:a.test(e.value)};case!t.ipv4&&t.ipv6:return{message:e.l10n?t.message||e.l10n.ip.ipv6:t.message,valid:n.test(e.value)};case t.ipv4&&t.ipv6:default:return{message:e.l10n?t.message||e.l10n.ip.default:t.message,valid:a.test(e.value)||n.test(e.value)}}}}},lessThan:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{inclusive:!0,message:""},e.options),a=parseFloat("".concat(t.max).replace(",","."));return t.inclusive?{message:p(e.l10n?t.message||e.l10n.lessThan.default:t.message,"".concat(a)),valid:parseFloat(e.value)<=a}:{message:p(e.l10n?t.message||e.l10n.lessThan.notInclusive:t.message,"".concat(a)),valid:parseFloat(e.value)<a}}}},notEmpty:function(){return{validate:function(e){return{valid:""!==e.value}}}},numeric:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},e.options),a="".concat(e.value);a.substr(0,1)===t.decimalSeparator?a="0".concat(t.decimalSeparator).concat(a.substr(1)):a.substr(0,2)==="-".concat(t.decimalSeparator)&&(a="-0".concat(t.decimalSeparator).concat(a.substr(2)));var n="."===t.decimalSeparator?"\\.":t.decimalSeparator,r="."===t.thousandsSeparator?"\\.":t.thousandsSeparator,i=new RegExp("^-?[0-9]{1,3}(".concat(r,"[0-9]{3})*(").concat(n,"[0-9]+)?$")),s=new RegExp(r,"g");if(!i.test(a))return{valid:!1};r&&(a=a.replace(s,"")),n&&(a=a.replace(n,"."));var l=parseFloat(a);return{valid:!isNaN(l)&&isFinite(l)}}}},promise:function(){return{validate:function(e){return g(e.options.promise,[e])}}},regexp:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.options.regexp;if(t instanceof RegExp)return{valid:t.test(e.value)};var a=t.toString();return{valid:(e.options.flags?new RegExp(a,e.options.flags):new RegExp(a)).test(e.value)}}}},remote:function(){var e={crossDomain:!1,data:{},headers:{},method:"GET",validKey:"valid"};return{validate:function(t){if(""===t.value)return Promise.resolve({valid:!0});var a=Object.assign({},e,t.options),n=a.data;return"function"==typeof a.data&&(n=a.data.call(this,t)),"string"==typeof n&&(n=JSON.parse(n)),n[a.name||t.field]=t.value,k("function"==typeof a.url?a.url.call(this,t):a.url,{crossDomain:a.crossDomain,headers:a.headers,method:a.method,params:n}).then(function(e){return Promise.resolve({message:e.message,meta:e,valid:"true"==="".concat(e[a.validKey])})}).catch(function(e){return Promise.reject({valid:!1})})}}},stringCase:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{case:"lower"},e.options),a=(t.case||"lower").toLowerCase();return{message:t.message||(e.l10n?"upper"===a?e.l10n.stringCase.upper:e.l10n.stringCase.default:t.message),valid:"upper"===a?e.value===e.value.toUpperCase():e.value===e.value.toLowerCase()}}}},stringLength:function(){return{validate:function(e){var t=Object.assign({},{message:"",trim:!1,utf8Bytes:!1},e.options),a=!0===t.trim||"true"==="".concat(t.trim)?e.value.trim():e.value;if(""===a)return{valid:!0};var n=t.min?"".concat(t.min):"",r=t.max?"".concat(t.max):"",i=t.utf8Bytes?function(e){for(var t=e.length,a=e.length-1;a>=0;a--){var n=e.charCodeAt(a);n>127&&n<=2047?t++:n>2047&&n<=65535&&(t+=2),n>=56320&&n<=57343&&a--}return"".concat(t)}(a):a.length,s=!0,l=e.l10n?t.message||e.l10n.stringLength.default:t.message;switch((n&&i<parseInt(n,10)||r&&i>parseInt(r,10))&&(s=!1),!0){case!!n&&!!r:l=p(e.l10n?t.message||e.l10n.stringLength.between:t.message,[n,r]);break;case!!n:l=p(e.l10n?t.message||e.l10n.stringLength.more:t.message,"".concat(parseInt(n,10)-1));break;case!!r:l=p(e.l10n?t.message||e.l10n.stringLength.less:t.message,"".concat(parseInt(r,10)+1))}return{message:l,valid:s}}}},uri:function(){var e={allowEmptyProtocol:!1,allowLocal:!1,protocol:"http, https, ftp"};return{validate:function(t){if(""===t.value)return{valid:!0};var a=Object.assign({},e,t.options),n=!0===a.allowLocal||"true"==="".concat(a.allowLocal),r=!0===a.allowEmptyProtocol||"true"==="".concat(a.allowEmptyProtocol),i=a.protocol.split(",").join("|").replace(/\s/g,"");return{valid:new RegExp("^(?:(?:"+i+")://)"+(r?"?":"")+"(?:\\S+(?::\\S*)?@)?(?:"+(n?"":"(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})")+"(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9])*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))"+(n?"?":"")+")(?::\\d{2,5})?(?:/[^\\s]*)?$","i").test(t.value)}}}},base64:function(){return{validate:function(e){return{valid:""===e.value||/^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=|[A-Za-z0-9+\/]{4})$/.test(e.value)}}}},bic:function(){return{validate:function(e){return{valid:""===e.value||/^[a-zA-Z]{6}[a-zA-Z0-9]{2}([a-zA-Z0-9]{3})?$/.test(e.value)}}}},color:function(){var e=["hex","rgb","rgba","hsl","hsla","keyword"],t=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","transparent","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],a=function(e){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(e)},n=function(e){return/^hsl\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},r=function(e){return/^hsla\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)},i=function(e){return t.indexOf(e)>=0},s=function(e){return/^rgb\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){2}(\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*)\)$/.test(e)||/^rgb\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},l=function(e){return/^rgba\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)||/^rgba\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)};return{validate:function(t){if(""===t.value)return{valid:!0};var o="string"==typeof t.options.type?t.options.type.toString().replace(/s/g,"").split(","):t.options.type||e,d=!0,c=!1,u=void 0;try{for(var f,v=o[Symbol.iterator]();!(d=(f=v.next()).done);d=!0){var h=f.value.toLowerCase();if(-1!==e.indexOf(h)){var m=!0;switch(h){case"hex":m=a(t.value);break;case"hsl":m=n(t.value);break;case"hsla":m=r(t.value);break;case"keyword":m=i(t.value);break;case"rgb":m=s(t.value);break;case"rgba":m=l(t.value)}if(m)return{valid:!0}}}}catch(e){c=!0,u=e}finally{try{!d&&null!=v.return&&v.return()}finally{if(c)throw u}}return{valid:!1}}}},cusip:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!/^[0-9A-Z]{9}$/.test(t))return{valid:!1};for(var a=t.split("").map(function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-65+10+"":e}),n=a.length,r=0,i=0;i<n-1;i++){var s=parseInt(a[i],10);i%2!=0&&(s*=2),s>9&&(s-=9),r+=s}return{valid:(r=(10-r%10)%10)===parseInt(a[n-1],10)}}}},ean:function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^(\d{8}|\d{12}|\d{13}|\d{14})$/.test(e.value))return{valid:!1};for(var t=e.value.length,a=0,n=8===t?[3,1]:[1,3],r=0;r<t-1;r++)a+=parseInt(e.value.charAt(r),10)*n[r%2];return{valid:"".concat(a=(10-a%10)%10)===e.value.charAt(t-1)}}}},ein:function(){var e={ANDOVER:["10","12"],ATLANTA:["60","67"],AUSTIN:["50","53"],BROOKHAVEN:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],CINCINNATI:["30","32","35","36","37","38","61"],FRESNO:["15","24"],INTERNET:["20","26","27","45","46","47"],KANSAS_CITY:["40","44"],MEMPHIS:["94","95"],OGDEN:["80","90"],PHILADELPHIA:["33","39","41","42","43","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],SMALL_BUSINESS_ADMINISTRATION:["31"]};return{validate:function(t){if(""===t.value)return{meta:null,valid:!0};if(!/^[0-9]{2}-?[0-9]{7}$/.test(t.value))return{meta:null,valid:!1};var a="".concat(t.value.substr(0,2));for(var n in e)if(-1!==e[n].indexOf(a))return{meta:{campus:n},valid:!0};return{meta:null,valid:!1}}}},grid:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();return/^[GRID:]*([0-9A-Z]{2})[-\s]*([0-9A-Z]{5})[-\s]*([0-9A-Z]{10})[-\s]*([0-9A-Z]{1})$/g.test(t)?("GRID:"===(t=t.replace(/\s/g,"").replace(/-/g,"")).substr(0,5)&&(t=t.substr(5)),{valid:n(t)}):{valid:!1}}}},hex:function(){return{validate:function(e){return{valid:""===e.value||/^[0-9a-fA-F]+$/.test(e.value)}}}},iban:function(){var e={AD:"AD[0-9]{2}[0-9]{4}[0-9]{4}[A-Z0-9]{12}",AE:"AE[0-9]{2}[0-9]{3}[0-9]{16}",AL:"AL[0-9]{2}[0-9]{8}[A-Z0-9]{16}",AO:"AO[0-9]{2}[0-9]{21}",AT:"AT[0-9]{2}[0-9]{5}[0-9]{11}",AZ:"AZ[0-9]{2}[A-Z]{4}[A-Z0-9]{20}",BA:"BA[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{8}[0-9]{2}",BE:"BE[0-9]{2}[0-9]{3}[0-9]{7}[0-9]{2}",BF:"BF[0-9]{2}[0-9]{23}",BG:"BG[0-9]{2}[A-Z]{4}[0-9]{4}[0-9]{2}[A-Z0-9]{8}",BH:"BH[0-9]{2}[A-Z]{4}[A-Z0-9]{14}",BI:"BI[0-9]{2}[0-9]{12}",BJ:"BJ[0-9]{2}[A-Z]{1}[0-9]{23}",BR:"BR[0-9]{2}[0-9]{8}[0-9]{5}[0-9]{10}[A-Z][A-Z0-9]",CH:"CH[0-9]{2}[0-9]{5}[A-Z0-9]{12}",CI:"CI[0-9]{2}[A-Z]{1}[0-9]{23}",CM:"CM[0-9]{2}[0-9]{23}",CR:"CR[0-9]{2}[0-9][0-9]{3}[0-9]{14}",CV:"CV[0-9]{2}[0-9]{21}",CY:"CY[0-9]{2}[0-9]{3}[0-9]{5}[A-Z0-9]{16}",CZ:"CZ[0-9]{2}[0-9]{20}",DE:"DE[0-9]{2}[0-9]{8}[0-9]{10}",DK:"DK[0-9]{2}[0-9]{14}",DO:"DO[0-9]{2}[A-Z0-9]{4}[0-9]{20}",DZ:"DZ[0-9]{2}[0-9]{20}",EE:"EE[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{11}[0-9]{1}",ES:"ES[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{1}[0-9]{1}[0-9]{10}",FI:"FI[0-9]{2}[0-9]{6}[0-9]{7}[0-9]{1}",FO:"FO[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",FR:"FR[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",GB:"GB[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",GE:"GE[0-9]{2}[A-Z]{2}[0-9]{16}",GI:"GI[0-9]{2}[A-Z]{4}[A-Z0-9]{15}",GL:"GL[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",GR:"GR[0-9]{2}[0-9]{3}[0-9]{4}[A-Z0-9]{16}",GT:"GT[0-9]{2}[A-Z0-9]{4}[A-Z0-9]{20}",HR:"HR[0-9]{2}[0-9]{7}[0-9]{10}",HU:"HU[0-9]{2}[0-9]{3}[0-9]{4}[0-9]{1}[0-9]{15}[0-9]{1}",IE:"IE[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",IL:"IL[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{13}",IR:"IR[0-9]{2}[0-9]{22}",IS:"IS[0-9]{2}[0-9]{4}[0-9]{2}[0-9]{6}[0-9]{10}",IT:"IT[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",JO:"JO[0-9]{2}[A-Z]{4}[0-9]{4}[0]{8}[A-Z0-9]{10}",KW:"KW[0-9]{2}[A-Z]{4}[0-9]{22}",KZ:"KZ[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LB:"LB[0-9]{2}[0-9]{4}[A-Z0-9]{20}",LI:"LI[0-9]{2}[0-9]{5}[A-Z0-9]{12}",LT:"LT[0-9]{2}[0-9]{5}[0-9]{11}",LU:"LU[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LV:"LV[0-9]{2}[A-Z]{4}[A-Z0-9]{13}",MC:"MC[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",MD:"MD[0-9]{2}[A-Z0-9]{20}",ME:"ME[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",MG:"MG[0-9]{2}[0-9]{23}",MK:"MK[0-9]{2}[0-9]{3}[A-Z0-9]{10}[0-9]{2}",ML:"ML[0-9]{2}[A-Z]{1}[0-9]{23}",MR:"MR13[0-9]{5}[0-9]{5}[0-9]{11}[0-9]{2}",MT:"MT[0-9]{2}[A-Z]{4}[0-9]{5}[A-Z0-9]{18}",MU:"MU[0-9]{2}[A-Z]{4}[0-9]{2}[0-9]{2}[0-9]{12}[0-9]{3}[A-Z]{3}",MZ:"MZ[0-9]{2}[0-9]{21}",NL:"NL[0-9]{2}[A-Z]{4}[0-9]{10}",NO:"NO[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{1}",PK:"PK[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",PL:"PL[0-9]{2}[0-9]{8}[0-9]{16}",PS:"PS[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",PT:"PT[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{11}[0-9]{2}",QA:"QA[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",RO:"RO[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",RS:"RS[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",SA:"SA[0-9]{2}[0-9]{2}[A-Z0-9]{18}",SE:"SE[0-9]{2}[0-9]{3}[0-9]{16}[0-9]{1}",SI:"SI[0-9]{2}[0-9]{5}[0-9]{8}[0-9]{2}",SK:"SK[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{10}",SM:"SM[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",SN:"SN[0-9]{2}[A-Z]{1}[0-9]{23}",TL:"TL38[0-9]{3}[0-9]{14}[0-9]{2}",TN:"TN59[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",TR:"TR[0-9]{2}[0-9]{5}[A-Z0-9]{1}[A-Z0-9]{16}",VG:"VG[0-9]{2}[A-Z]{4}[0-9]{16}",XK:"XK[0-9]{2}[0-9]{4}[0-9]{10}[0-9]{2}"},t=["AT","BE","BG","CH","CY","CZ","DE","DK","EE","ES","FI","FR","GB","GI","GR","HR","HU","IE","IS","IT","LI","LT","LU","LV","MC","MT","NL","NO","PL","PT","RO","SE","SI","SK","SM"];return{validate:function(a){if(""===a.value)return{valid:!0};var n=Object.assign({},{message:""},a.options),r=a.value.replace(/[^a-zA-Z0-9]/g,"").toUpperCase(),i=n.country||r.substr(0,2);if(!e[i])return{message:n.message,valid:!1};if(void 0!==n.sepa){var s=-1!==t.indexOf(i);if(("true"===n.sepa||!0===n.sepa)&&!s||("false"===n.sepa||!1===n.sepa)&&s)return{message:n.message,valid:!1}}var l=p(a.l10n?n.message||a.l10n.iban.country:n.message,a.l10n?a.l10n.iban.countries[i]:i);if(!new RegExp("^".concat(e[i],"$")).test(a.value))return{message:l,valid:!1};r=(r="".concat(r.substr(4)).concat(r.substr(0,4))).split("").map(function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-65+10:e}).join("");for(var o=parseInt(r.substr(0,1),10),d=r.length,c=1;c<d;++c)o=(10*o+parseInt(r.substr(c,1),10))%97;return{message:l,valid:1===o}}}},id:function(){var e=["AR","BA","BG","BR","CH","CL","CN","CO","CZ","DK","EE","ES","FI","FR","HK","HR","ID","IE","IL","IS","KR","LT","LV","ME","MK","MX","MY","NL","NO","PE","PL","RO","RS","SE","SI","SK","SM","TH","TR","TW","UY","ZA"];return{validate:function(t){if(""===t.value)return{valid:!0};var a=Object.assign({},{message:""},t.options),n=t.value.substr(0,2);if(n="function"==typeof a.country?a.country.call(this):a.country,-1===e.indexOf(n))return{valid:!0};var r={meta:{},valid:!0};switch(n.toLowerCase()){case"ar":r=function(e){var t=e.replace(/\./g,"");return{meta:{},valid:/^\d{7,8}$/.test(t)}}(t.value);break;case"ba":r=function(e){return{meta:{},valid:I(e,"BA")}}(t.value);break;case"bg":r=function(e){if(!/^\d{10}$/.test(e)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/\s/g,""),a=parseInt(t.substr(0,2),10)+1900,n=parseInt(t.substr(2,2),10);if(n>40?(a+=100,n-=40):n>20&&(a-=100,n-=20),!A(a,n,parseInt(t.substr(4,2),10)))return{meta:{},valid:!1};for(var r=0,i=[2,4,8,5,10,9,7,3,6],s=0;s<9;s++)r+=parseInt(t.charAt(s),10)*i[s];return{meta:{},valid:"".concat(r=r%11%10)===t.substr(9,1)}}(t.value);break;case"br":r=function(e){var t=e.replace(/\D/g,"");if(!/^\d{11}$/.test(t)||/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(t))return{meta:{},valid:!1};var a,n=0;for(a=0;a<9;a++)n+=(10-a)*parseInt(t.charAt(a),10);if((10==(n=11-n%11)||11===n)&&(n=0),"".concat(n)!==t.charAt(9))return{meta:{},valid:!1};var r=0;for(a=0;a<10;a++)r+=(11-a)*parseInt(t.charAt(a),10);return(10==(r=11-r%11)||11===r)&&(r=0),{meta:{},valid:"".concat(r)===t.charAt(10)}}(t.value);break;case"ch":r=function(e){if(!/^756[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{2}$/.test(e))return{meta:{},valid:!1};for(var t=e.replace(/\D/g,"").substr(3),a=t.length,n=8===a?[3,1]:[1,3],r=0,i=0;i<a-1;i++)r+=parseInt(t.charAt(i),10)*n[i%2];return{meta:{},valid:"".concat(r=10-r%10)===t.charAt(a-1)}}(t.value);break;case"cl":r=function(e){if(!/^\d{7,8}[-]{0,1}[0-9K]$/i.test(e))return{meta:{},valid:!1};for(var t=e.replace(/\-/g,"");t.length<9;)t="0".concat(t);for(var a=[3,2,7,6,5,4,3,2],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];var i="".concat(n=11-n%11);return 11===n?i="0":10===n&&(i="K"),{meta:{},valid:i===t.charAt(8).toUpperCase()}}(t.value);break;case"cn":r=function(e){var t=e.trim();if(!/^\d{15}$/.test(t)&&!/^\d{17}[\dXx]{1}$/.test(t))return{meta:{},valid:!1};var a={11:{0:[0],1:[[0,9],[11,17]],2:[0,28,29]},12:{0:[0],1:[[0,16]],2:[0,21,23,25]},13:{0:[0],1:[[0,5],7,8,21,[23,33],[81,85]],2:[[0,5],[7,9],[23,25],27,29,30,81,83],3:[[0,4],[21,24]],4:[[0,4],6,21,[23,35],81],5:[[0,3],[21,35],81,82],6:[[0,4],[21,38],[81,84]],7:[[0,3],5,6,[21,33]],8:[[0,4],[21,28]],9:[[0,3],[21,30],[81,84]],10:[[0,3],[22,26],28,81,82],11:[[0,2],[21,28],81,82]},14:{0:[0],1:[0,1,[5,10],[21,23],81],2:[[0,3],11,12,[21,27]],3:[[0,3],11,21,22],4:[[0,2],11,21,[23,31],81],5:[[0,2],21,22,24,25,81],6:[[0,3],[21,24]],7:[[0,2],[21,29],81],8:[[0,2],[21,30],81,82],9:[[0,2],[21,32],81],10:[[0,2],[21,34],81,82],11:[[0,2],[21,30],81,82],23:[[0,3],22,23,[25,30],32,33]},15:{0:[0],1:[[0,5],[21,25]],2:[[0,7],[21,23]],3:[[0,4]],4:[[0,4],[21,26],[28,30]],5:[[0,2],[21,26],81],6:[[0,2],[21,27]],7:[[0,3],[21,27],[81,85]],8:[[0,2],[21,26]],9:[[0,2],[21,29],81],22:[[0,2],[21,24]],25:[[0,2],[22,31]],26:[[0,2],[24,27],[29,32],34],28:[0,1,[22,27]],29:[0,[21,23]]},21:{0:[0],1:[[0,6],[11,14],[22,24],81],2:[[0,4],[11,13],24,[81,83]],3:[[0,4],11,21,23,81],4:[[0,4],11,[21,23]],5:[[0,5],21,22],6:[[0,4],24,81,82],7:[[0,3],11,26,27,81,82],8:[[0,4],11,81,82],9:[[0,5],11,21,22],10:[[0,5],11,21,81],11:[[0,3],21,22],12:[[0,2],4,21,23,24,81,82],13:[[0,3],21,22,24,81,82],14:[[0,4],21,22,81]},22:{0:[0],1:[[0,6],12,22,[81,83]],2:[[0,4],11,21,[81,84]],3:[[0,3],22,23,81,82],4:[[0,3],21,22],5:[[0,3],21,23,24,81,82],6:[[0,2],4,5,[21,23],25,81],7:[[0,2],[21,24],81],8:[[0,2],21,22,81,82],24:[[0,6],24,26]},23:{0:[0],1:[[0,12],21,[23,29],[81,84]],2:[[0,8],21,[23,25],27,[29,31],81],3:[[0,7],21,81,82],4:[[0,7],21,22],5:[[0,3],5,6,[21,24]],6:[[0,6],[21,24]],7:[[0,16],22,81],8:[[0,5],11,22,26,28,33,81,82],9:[[0,4],21],10:[[0,5],24,25,81,[83,85]],11:[[0,2],21,23,24,81,82],12:[[0,2],[21,26],[81,83]],27:[[0,4],[21,23]]},31:{0:[0],1:[0,1,[3,10],[12,20]],2:[0,30]},32:{0:[0],1:[[0,7],11,[13,18],24,25],2:[[0,6],11,81,82],3:[[0,5],11,12,[21,24],81,82],4:[[0,2],4,5,11,12,81,82],5:[[0,9],[81,85]],6:[[0,2],11,12,21,23,[81,84]],7:[0,1,3,5,6,[21,24]],8:[[0,4],11,26,[29,31]],9:[[0,3],[21,25],28,81,82],10:[[0,3],11,12,23,81,84,88],11:[[0,2],11,12,[81,83]],12:[[0,4],[81,84]],13:[[0,2],11,[21,24]]},33:{0:[0],1:[[0,6],[8,10],22,27,82,83,85],2:[0,1,[3,6],11,12,25,26,[81,83]],3:[[0,4],22,24,[26,29],81,82],4:[[0,2],11,21,24,[81,83]],5:[[0,3],[21,23]],6:[[0,2],21,24,[81,83]],7:[[0,3],23,26,27,[81,84]],8:[[0,3],22,24,25,81],9:[[0,3],21,22],10:[[0,4],[21,24],81,82],11:[[0,2],[21,27],81]},34:{0:[0],1:[[0,4],11,[21,24],81],2:[[0,4],7,8,[21,23],25],3:[[0,4],11,[21,23]],4:[[0,6],21],5:[[0,4],6,[21,23]],6:[[0,4],21],7:[[0,3],11,21],8:[[0,3],11,[22,28],81],10:[[0,4],[21,24]],11:[[0,3],22,[24,26],81,82],12:[[0,4],21,22,25,26,82],13:[[0,2],[21,24]],14:[[0,2],[21,24]],15:[[0,3],[21,25]],16:[[0,2],[21,23]],17:[[0,2],[21,23]],18:[[0,2],[21,25],81]},35:{0:[0],1:[[0,5],11,[21,25],28,81,82],2:[[0,6],[11,13]],3:[[0,5],22],4:[[0,3],21,[23,30],81],5:[[0,5],21,[24,27],[81,83]],6:[[0,3],[22,29],81],7:[[0,2],[21,25],[81,84]],8:[[0,2],[21,25],81],9:[[0,2],[21,26],81,82]},36:{0:[0],1:[[0,5],11,[21,24]],2:[[0,3],22,81],3:[[0,2],13,[21,23]],4:[[0,3],21,[23,30],81,82],5:[[0,2],21],6:[[0,2],22,81],7:[[0,2],[21,35],81,82],8:[[0,3],[21,30],81],9:[[0,2],[21,26],[81,83]],10:[[0,2],[21,30]],11:[[0,2],[21,30],81]},37:{0:[0],1:[[0,5],12,13,[24,26],81],2:[[0,3],5,[11,14],[81,85]],3:[[0,6],[21,23]],4:[[0,6],81],5:[[0,3],[21,23]],6:[[0,2],[11,13],34,[81,87]],7:[[0,5],24,25,[81,86]],8:[[0,2],11,[26,32],[81,83]],9:[[0,3],11,21,23,82,83],10:[[0,2],[81,83]],11:[[0,3],21,22],12:[[0,3]],13:[[0,2],11,12,[21,29]],14:[[0,2],[21,28],81,82],15:[[0,2],[21,26],81],16:[[0,2],[21,26]],17:[[0,2],[21,28]]},41:{0:[0],1:[[0,6],8,22,[81,85]],2:[[0,5],11,[21,25]],3:[[0,7],11,[22,29],81],4:[[0,4],11,[21,23],25,81,82],5:[[0,3],5,6,22,23,26,27,81],6:[[0,3],11,21,22],7:[[0,4],11,21,[24,28],81,82],8:[[0,4],11,[21,23],25,[81,83]],9:[[0,2],22,23,[26,28]],10:[[0,2],[23,25],81,82],11:[[0,4],[21,23]],12:[[0,2],21,22,24,81,82],13:[[0,3],[21,30],81],14:[[0,3],[21,26],81],15:[[0,3],[21,28]],16:[[0,2],[21,28],81],17:[[0,2],[21,29]],90:[0,1]},42:{0:[0],1:[[0,7],[11,17]],2:[[0,5],22,81],3:[[0,3],[21,25],81],5:[[0,6],[25,29],[81,83]],6:[[0,2],6,7,[24,26],[82,84]],7:[[0,4]],8:[[0,2],4,21,22,81],9:[[0,2],[21,23],81,82,84],10:[[0,3],[22,24],81,83,87],11:[[0,2],[21,27],81,82],12:[[0,2],[21,24],81],13:[[0,3],21,81],28:[[0,2],22,23,[25,28]],90:[0,[4,6],21]},43:{0:[0],1:[[0,5],11,12,21,22,24,81],2:[[0,4],11,21,[23,25],81],3:[[0,2],4,21,81,82],4:[0,1,[5,8],12,[21,24],26,81,82],5:[[0,3],11,[21,25],[27,29],81],6:[[0,3],11,21,23,24,26,81,82],7:[[0,3],[21,26],81],8:[[0,2],11,21,22],9:[[0,3],[21,23],81],10:[[0,3],[21,28],81],11:[[0,3],[21,29]],12:[[0,2],[21,30],81],13:[[0,2],21,22,81,82],31:[0,1,[22,27],30]},44:{0:[0],1:[[0,7],[11,16],83,84],2:[[0,5],21,22,24,29,32,33,81,82],3:[0,1,[3,8]],4:[[0,4]],5:[0,1,[6,15],23,82,83],6:[0,1,[4,8]],7:[0,1,[3,5],81,[83,85]],8:[[0,4],11,23,25,[81,83]],9:[[0,3],23,[81,83]],12:[[0,3],[23,26],83,84],13:[[0,3],[22,24],81],14:[[0,2],[21,24],26,27,81],15:[[0,2],21,23,81],16:[[0,2],[21,25]],17:[[0,2],21,23,81],18:[[0,3],21,23,[25,27],81,82],19:[0],20:[0],51:[[0,3],21,22],52:[[0,3],21,22,24,81],53:[[0,2],[21,23],81]},45:{0:[0],1:[[0,9],[21,27]],2:[[0,5],[21,26]],3:[[0,5],11,12,[21,32]],4:[0,1,[3,6],11,[21,23],81],5:[[0,3],12,21],6:[[0,3],21,81],7:[[0,3],21,22],8:[[0,4],21,81],9:[[0,3],[21,24],81],10:[[0,2],[21,31]],11:[[0,2],[21,23]],12:[[0,2],[21,29],81],13:[[0,2],[21,24],81],14:[[0,2],[21,25],81]},46:{0:[0],1:[0,1,[5,8]],2:[0,1],3:[0,[21,23]],90:[[0,3],[5,7],[21,39]]},50:{0:[0],1:[[0,19]],2:[0,[22,38],[40,43]],3:[0,[81,84]]},51:{0:[0],1:[0,1,[4,8],[12,15],[21,24],29,31,32,[81,84]],3:[[0,4],11,21,22],4:[[0,3],11,21,22],5:[[0,4],21,22,24,25],6:[0,1,3,23,26,[81,83]],7:[0,1,3,4,[22,27],81],8:[[0,2],11,12,[21,24]],9:[[0,4],[21,23]],10:[[0,2],11,24,25,28],11:[[0,2],[11,13],23,24,26,29,32,33,81],13:[[0,4],[21,25],81],14:[[0,2],[21,25]],15:[[0,3],[21,29]],16:[[0,3],[21,23],81],17:[[0,3],[21,25],81],18:[[0,3],[21,27]],19:[[0,3],[21,23]],20:[[0,2],21,22,81],32:[0,[21,33]],33:[0,[21,38]],34:[0,1,[22,37]]},52:{0:[0],1:[[0,3],[11,15],[21,23],81],2:[0,1,3,21,22],3:[[0,3],[21,30],81,82],4:[[0,2],[21,25]],5:[[0,2],[21,27]],6:[[0,3],[21,28]],22:[0,1,[22,30]],23:[0,1,[22,28]],24:[0,1,[22,28]],26:[0,1,[22,36]],27:[[0,2],22,23,[25,32]]},53:{0:[0],1:[[0,3],[11,14],21,22,[24,29],81],3:[[0,2],[21,26],28,81],4:[[0,2],[21,28]],5:[[0,2],[21,24]],6:[[0,2],[21,30]],7:[[0,2],[21,24]],8:[[0,2],[21,29]],9:[[0,2],[21,27]],23:[0,1,[22,29],31],25:[[0,4],[22,32]],26:[0,1,[21,28]],27:[0,1,[22,30]],28:[0,1,22,23],29:[0,1,[22,32]],31:[0,2,3,[22,24]],34:[0,[21,23]],33:[0,21,[23,25]],35:[0,[21,28]]},54:{0:[0],1:[[0,2],[21,27]],21:[0,[21,29],32,33],22:[0,[21,29],[31,33]],23:[0,1,[22,38]],24:[0,[21,31]],25:[0,[21,27]],26:[0,[21,27]]},61:{0:[0],1:[[0,4],[11,16],22,[24,26]],2:[[0,4],22],3:[[0,4],[21,24],[26,31]],4:[[0,4],[22,31],81],5:[[0,2],[21,28],81,82],6:[[0,2],[21,32]],7:[[0,2],[21,30]],8:[[0,2],[21,31]],9:[[0,2],[21,29]],10:[[0,2],[21,26]]},62:{0:[0],1:[[0,5],11,[21,23]],2:[0,1],3:[[0,2],21],4:[[0,3],[21,23]],5:[[0,3],[21,25]],6:[[0,2],[21,23]],7:[[0,2],[21,25]],8:[[0,2],[21,26]],9:[[0,2],[21,24],81,82],10:[[0,2],[21,27]],11:[[0,2],[21,26]],12:[[0,2],[21,28]],24:[0,21,[24,29]],26:[0,21,[23,30]],29:[0,1,[21,27]],30:[0,1,[21,27]]},63:{0:[0],1:[[0,5],[21,23]],2:[0,2,[21,25]],21:[0,[21,23],[26,28]],22:[0,[21,24]],23:[0,[21,24]],25:[0,[21,25]],26:[0,[21,26]],27:[0,1,[21,26]],28:[[0,2],[21,23]]},64:{0:[0],1:[0,1,[4,6],21,22,81],2:[[0,3],5,[21,23]],3:[[0,3],[21,24],81],4:[[0,2],[21,25]],5:[[0,2],21,22]},65:{0:[0],1:[[0,9],21],2:[[0,5]],21:[0,1,22,23],22:[0,1,22,23],23:[[0,3],[23,25],27,28],28:[0,1,[22,29]],29:[0,1,[22,29]],30:[0,1,[22,24]],31:[0,1,[21,31]],32:[0,1,[21,27]],40:[0,2,3,[21,28]],42:[[0,2],21,[23,26]],43:[0,1,[21,26]],90:[[0,4]],27:[[0,2],22,23]},71:{0:[0]},81:{0:[0]},82:{0:[0]}},n=parseInt(t.substr(0,2),10),r=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);if(!a[n]||!a[n][r])return{meta:{},valid:!1};var s,l,o=!1,d=a[n][r];for(s=0;s<d.length;s++)if(Array.isArray(d[s])&&d[s][0]<=i&&i<=d[s][1]||!Array.isArray(d[s])&&i===d[s]){o=!0;break}if(!o)return{meta:{},valid:!1};if(l=18===t.length?t.substr(6,8):"19".concat(t.substr(6,6)),!A(parseInt(l.substr(0,4),10),parseInt(l.substr(4,2),10),parseInt(l.substr(6,2),10)))return{meta:{},valid:!1};if(18===t.length){var c=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],u=0;for(s=0;s<17;s++)u+=parseInt(t.charAt(s),10)*c[s];return u=(12-u%11)%11,{meta:{},valid:("X"!==t.charAt(17).toUpperCase()?parseInt(t.charAt(17),10):10)===u}}return{meta:{},valid:!0}}(t.value);break;case"co":r=function(e){var t=e.replace(/\./g,"").replace("-","");if(!/^\d{8,16}$/.test(t))return{meta:{},valid:!1};for(var a=t.length,n=[3,7,13,17,19,23,29,37,41,43,47,53,59,67,71],r=0,i=a-2;i>=0;i--)r+=parseInt(t.charAt(i),10)*n[i];return(r%=11)>=2&&(r=11-r),{meta:{},valid:"".concat(r)===t.substr(a-1)}}(t.value);break;case"cz":r=y(t.value);break;case"dk":r=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/-/g,""),a=parseInt(t.substr(0,2),10),n=parseInt(t.substr(2,2),10),r=parseInt(t.substr(4,2),10);switch(!0){case-1!=="5678".indexOf(t.charAt(6))&&r>=58:r+=1800;break;case-1!=="0123".indexOf(t.charAt(6)):case-1!=="49".indexOf(t.charAt(6))&&r>=37:r+=1900;break;default:r+=2e3}return{meta:{},valid:A(r,n,a)}}(t.value);break;case"ee":r=w(t.value);break;case"es":r=function(e){var t=/^[0-9]{8}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),a=/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),n=/^[A-HNPQS][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-J]$/.test(e);if(!t&&!a&&!n)return{meta:{},valid:!1};var r,i,s=e.replace(/-/g,"");if(t||a){i="DNI";var l="XYZ".indexOf(s.charAt(0));return-1!==l&&(s=l+s.substr(1)+"",i="NIE"),{meta:{type:i},valid:(r="TRWAGMYFPDXBNJZSQVHLCKE"[(r=parseInt(s.substr(0,8),10))%23])===s.substr(8,1)}}r=s.substr(1,7),i="CIF";for(var o=s[0],d=s.substr(-1),c=0,u=0;u<r.length;u++)if(u%2!=0)c+=parseInt(r[u],10);else{var f=""+2*parseInt(r[u],10);c+=parseInt(f[0],10),2===f.length&&(c+=parseInt(f[1],10))}var v=c-10*Math.floor(c/10);return 0!==v&&(v=10-v),{meta:{type:i},valid:-1!=="KQS".indexOf(o)?d==="JABCDEFGHI"[v]:-1!=="ABEH".indexOf(o)?d===""+v:d===""+v||d==="JABCDEFGHI"[v]}}(t.value);break;case"fi":r=function(e){if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.substr(0,2),10),a=parseInt(e.substr(2,2),10),n=parseInt(e.substr(4,2),10);if(!A(n={"+":1800,"-":1900,A:2e3}[e.charAt(6)]+n,a,t))return{meta:{},valid:!1};if(parseInt(e.substr(7,3),10)<2)return{meta:{},valid:!1};var r=parseInt(e.substr(0,6)+e.substr(7,3)+"",10);return{meta:{},valid:"0123456789ABCDEFHJKLMNPRSTUVWXY".charAt(r%31)===e.charAt(10)}}(t.value);break;case"fr":r=function(e){var t=e.toUpperCase();if(!/^(1|2)\d{2}\d{2}(\d{2}|\d[A-Z]|\d{3})\d{2,3}\d{3}\d{2}$/.test(t))return{meta:{},valid:!1};var a=t.substr(5,2);switch(!0){case/^\d{2}$/.test(a):t=e;break;case"2A"===a:t="".concat(e.substr(0,5),"19").concat(e.substr(7));break;case"2B"===a:t="".concat(e.substr(0,5),"18").concat(e.substr(7));break;default:return{meta:{},valid:!1}}var n=97-parseInt(t.substr(0,13),10)%97;return{meta:{},valid:(n<10?"0".concat(n):"".concat(n))===t.substr(13)}}(t.value);break;case"hk":r=function(e){var t=e.toUpperCase();if(!/^[A-MP-Z]{1,2}[0-9]{6}[0-9A]$/.test(t))return{meta:{},valid:!1};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ",n=t.charAt(0),r=t.charAt(1),i=0,s=t;/^[A-Z]$/.test(r)?(i+=9*(10+a.indexOf(n)),i+=8*(10+a.indexOf(r)),s=t.substr(2)):(i+=324,i+=8*(10+a.indexOf(n)),s=t.substr(1));for(var l=s.length,o=0;o<l-1;o++)i+=(7-o)*parseInt(s.charAt(o),10);var d=i%11;return{meta:{},valid:(0===d?"0":11-d==10?"A":"".concat(11-d))===s.charAt(l-1)}}(t.value);break;case"hr":r=E(t.value);break;case"id":r=C(t.value);break;case"ie":r=function(e){if(!/^\d{7}[A-W][AHWTX]?$/.test(e))return{meta:{},valid:!1};var t=function(e){for(var t=e;t.length<7;)t="0".concat(t);for(var a="WABCDEFGHIJKLMNOPQRSTUV",n=0,r=0;r<7;r++)n+=parseInt(t.charAt(r),10)*(8-r);return n+=9*a.indexOf(t.substr(7)),a[n%23]};return{meta:{},valid:9!==e.length||"A"!==e.charAt(8)&&"H"!==e.charAt(8)?e.charAt(7)===t(e.substr(0,7)):e.charAt(7)===t(e.substr(0,7)+e.substr(8)+"")}}(t.value);break;case"il":r=O(t.value);break;case"is":r=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/-/g,""),a=parseInt(t.substr(0,2),10),n=parseInt(t.substr(2,2),10),r=parseInt(t.substr(4,2),10),i=parseInt(t.charAt(9),10);if(!A(r=9===i?1900+r:100*(20+i)+r,n,a,!0))return{meta:{},valid:!1};for(var s=[3,2,7,6,5,4,3,2],l=0,o=0;o<8;o++)l+=parseInt(t.charAt(o),10)*s[o];return{meta:{},valid:"".concat(l=11-l%11)===t.charAt(8)}}(t.value);break;case"kr":r=function(e){var t=e.replace("-","");if(!/^\d{13}$/.test(t))return{meta:{},valid:!1};var a=t.charAt(6),n=parseInt(t.substr(0,2),10),r=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);switch(a){case"1":case"2":case"5":case"6":n+=1900;break;case"3":case"4":case"7":case"8":n+=2e3;break;default:n+=1800}if(!A(n,r,i))return{meta:{},valid:!1};for(var s=[2,3,4,5,6,7,8,9,2,3,4,5],l=t.length,o=0,d=0;d<l-1;d++)o+=s[d]*parseInt(t.charAt(d),10);return{meta:{},valid:"".concat((11-o%11)%10)===t.charAt(l-1)}}(t.value);break;case"lt":r=w(t.value);break;case"lv":r=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/\D/g,""),a=parseInt(t.substr(0,2),10),n=parseInt(t.substr(2,2),10),r=parseInt(t.substr(4,2),10);if(!A(r=r+1800+100*parseInt(t.charAt(6),10),n,a,!0))return{meta:{},valid:!1};for(var i=0,s=[10,5,8,4,2,1,6,3,7,9],l=0;l<10;l++)i+=parseInt(t.charAt(l),10)*s[l];return{meta:{},valid:"".concat(i=(i+1)%11%10)===t.charAt(10)}}(t.value);break;case"me":r=function(e){return{meta:{},valid:I(e,"ME")}}(t.value);break;case"mk":r=function(e){return{meta:{},valid:I(e,"MK")}}(t.value);break;case"mx":r=function(e){var t=e.toUpperCase();if(!/^[A-Z]{4}\d{6}[A-Z]{6}[0-9A-Z]\d$/.test(t))return{meta:{},valid:!1};var a=t.substr(0,4);if(["BACA","BAKA","BUEI","BUEY","CACA","CACO","CAGA","CAGO","CAKA","CAKO","COGE","COGI","COJA","COJE","COJI","COJO","COLA","CULO","FALO","FETO","GETA","GUEI","GUEY","JETA","JOTO","KACA","KACO","KAGA","KAGO","KAKA","KAKO","KOGE","KOGI","KOJA","KOJE","KOJI","KOJO","KOLA","KULO","LILO","LOCA","LOCO","LOKA","LOKO","MAME","MAMO","MEAR","MEAS","MEON","MIAR","MION","MOCO","MOKO","MULA","MULO","NACA","NACO","PEDA","PEDO","PENE","PIPI","PITO","POPO","PUTA","PUTO","QULO","RATA","ROBA","ROBE","ROBO","RUIN","SENO","TETA","VACA","VAGA","VAGO","VAKA","VUEI","VUEY","WUEI","WUEY"].indexOf(a)>=0)return{meta:{},valid:!1};var n=parseInt(t.substr(4,2),10),r=parseInt(t.substr(6,2),10),i=parseInt(t.substr(6,2),10);if(/^[0-9]$/.test(t.charAt(16))?n+=1900:n+=2e3,!A(n,r,i))return{meta:{},valid:!1};var s=t.charAt(10);if("H"!==s&&"M"!==s)return{meta:{},valid:!1};var l=t.substr(11,2);if(-1===["AS","BC","BS","CC","CH","CL","CM","CS","DF","DG","GR","GT","HG","JC","MC","MN","MS","NE","NL","NT","OC","PL","QR","QT","SL","SP","SR","TC","TL","TS","VZ","YN","ZS"].indexOf(l))return{meta:{},valid:!1};for(var o=0,d=t.length,c=0;c<d-1;c++)o+=(18-c)*"0123456789ABCDEFGHIJKLMN&OPQRSTUVWXYZ".indexOf(t.charAt(c));return{meta:{},valid:"".concat(o=(10-o%10)%10)===t.charAt(d-1)}}(t.value);break;case"my":r=function(e){if(!/^\d{12}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.substr(0,2),10),a=parseInt(e.substr(2,2),10),n=parseInt(e.substr(4,2),10);if(!A(t+1900,a,n)&&!A(t+2e3,a,n))return{meta:{},valid:!1};var r=e.substr(6,2);return{meta:{},valid:-1===["17","18","19","20","69","70","73","80","81","94","95","96","97"].indexOf(r)}}(t.value);break;case"nl":r=function(e){if(e.length<8)return{meta:{},valid:!1};var t=e;if(8===t.length&&(t="0".concat(t)),!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t))return{meta:{},valid:!1};if(t=t.replace(/\./g,""),0===parseInt(t,10))return{meta:{},valid:!1};for(var a=0,n=t.length,r=0;r<n-1;r++)a+=(9-r)*parseInt(t.charAt(r),10);return 10==(a%=11)&&(a=0),{meta:{},valid:"".concat(a)===t.charAt(n-1)}}(t.value);break;case"no":r=function(e){return/^\d{11}$/.test(e)?{meta:{},valid:"".concat(function(e){for(var t=[3,7,6,1,8,9,4,5,2],a=0,n=0;n<9;n++)a+=t[n]*parseInt(e.charAt(n),10);return 11-a%11}(e))===e.substr(-2,1)&&"".concat(function(e){for(var t=[5,4,3,2,7,6,5,4,3,2],a=0,n=0;n<10;n++)a+=t[n]*parseInt(e.charAt(n),10);return 11-a%11}(e))===e.substr(-1)}:{meta:{},valid:!1}}(t.value);break;case"pe":r=function(e){if(!/^\d{8}[0-9A-Z]*$/.test(e))return{meta:{},valid:!1};if(8===e.length)return{meta:{},valid:!0};for(var t=[3,2,7,6,5,4,3,2],a=0,n=0;n<8;n++)a+=t[n]*parseInt(e.charAt(n),10);var r=a%11,i=[6,5,4,3,2,1,1,0,9,8,7][r],s="KJIHGFEDCBA".charAt(r);return{meta:{},valid:e.charAt(8)==="".concat(i)||e.charAt(8)===s}}(t.value);break;case"pl":r=function(e){if(!/^[0-9]{11}$/.test(e))return{meta:{},valid:!1};for(var t=0,a=e.length,n=[1,3,7,9,1,3,7,9,1,3,7],r=0;r<a-1;r++)t+=n[r]*parseInt(e.charAt(r),10);return 0==(t%=10)&&(t=10),{meta:{},valid:"".concat(t=10-t)===e.charAt(a-1)}}(t.value);break;case"ro":r=function(e){if(!/^[0-9]{13}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.charAt(0),10);if(0===t||7===t||8===t)return{meta:{},valid:!1};var a=parseInt(e.substr(1,2),10),n=parseInt(e.substr(3,2),10),r=parseInt(e.substr(5,2),10);if(r>31&&n>12)return{meta:{},valid:!1};if(9!==t&&!A(a={1:1900,2:1900,3:1800,4:1800,5:2e3,6:2e3}[t+""]+a,n,r))return{meta:{},valid:!1};for(var i=0,s=[2,7,9,1,4,6,3,5,8,2,7,9],l=e.length,o=0;o<l-1;o++)i+=parseInt(e.charAt(o),10)*s[o];return 10==(i%=11)&&(i=1),{meta:{},valid:"".concat(i)===e.charAt(l-1)}}(t.value);break;case"rs":r=function(e){return{meta:{},valid:I(e,"RS")}}(t.value);break;case"se":r=S(t.value);break;case"si":r=function(e){return{meta:{},valid:I(e,"SI")}}(t.value);break;case"sk":r=y(t.value);break;case"sm":r=function(e){return{meta:{},valid:/^\d{5}$/.test(e)}}(t.value);break;case"th":r=function(e){if(13!==e.length)return{meta:{},valid:!1};for(var t=0,a=0;a<12;a++)t+=parseInt(e.charAt(a),10)*(13-a);return{meta:{},valid:(11-t%11)%10===parseInt(e.charAt(12),10)}}(t.value);break;case"tr":r=function(e){if(11!==e.length)return{meta:{},valid:!1};for(var t=0,a=0;a<10;a++)t+=parseInt(e.charAt(a),10);return{meta:{},valid:t%10===parseInt(e.charAt(10),10)}}(t.value);break;case"tw":r=function(e){var t=e.toUpperCase();if(!/^[A-Z][12][0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var a=t.length,n="ABCDEFGHJKLMNPQRSTUVXYWZIO".indexOf(t.charAt(0))+10,r=Math.floor(n/10)+n%10*(a-1),i=0,s=1;s<a-1;s++)i+=parseInt(t.charAt(s),10)*(a-1-s);return{meta:{},valid:(r+i+parseInt(t.charAt(a-1),10))%10==0}}(t.value);break;case"uy":r=function(e){if(!/^\d{8}$/.test(e))return{meta:{},valid:!1};for(var t=[2,9,8,7,6,3,4],a=0,n=0;n<7;n++)a+=parseInt(e.charAt(n),10)*t[n];return(a%=10)>0&&(a=10-a),{meta:{},valid:"".concat(a)===e.charAt(7)}}(t.value);break;case"za":r=F(t.value)}var i=p(t.l10n?a.message||t.l10n.id.country:a.message,t.l10n?t.l10n.id.countries[n.toUpperCase()]:n.toUpperCase());return Object.assign({},{message:i},r)}}},imei:function(){return{validate:function(e){if(""===e.value)return{valid:!0};switch(!0){case/^\d{15}$/.test(e.value):case/^\d{2}-\d{6}-\d{6}-\d{1}$/.test(e.value):case/^\d{2}\s\d{6}\s\d{6}\s\d{1}$/.test(e.value):return{valid:t(e.value.replace(/[^0-9]/g,""))};case/^\d{14}$/.test(e.value):case/^\d{16}$/.test(e.value):case/^\d{2}-\d{6}-\d{6}(|-\d{2})$/.test(e.value):case/^\d{2}\s\d{6}\s\d{6}(|\s\d{2})$/.test(e.value):return{valid:!0};default:return{valid:!1}}}}},imo:function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^IMO \d{7}$/i.test(e.value))return{valid:!1};for(var t=e.value.replace(/^.*(\d{7})$/,"$1"),a=0,n=6;n>=1;n--)a+=parseInt(t.slice(6-n,-n),10)*(n+1);return{valid:a%10===parseInt(t.charAt(6),10)}}}},isbn:function(){return{validate:function(e){if(""===e.value)return{meta:{type:null},valid:!0};var t;switch(!0){case/^\d{9}[\dX]$/.test(e.value):case 13===e.value.length&&/^(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):case 13===e.value.length&&/^(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t="ISBN10";break;case/^(978|979)\d{9}[\dX]$/.test(e.value):case 17===e.value.length&&/^(978|979)-(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):case 17===e.value.length&&/^(978|979)\s(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t="ISBN13";break;default:return{meta:{type:null},valid:!1}}var a,n,r=e.value.replace(/[^0-9X]/gi,"").split(""),i=r.length,s=0;switch(t){case"ISBN10":for(s=0,a=0;a<i-1;a++)s+=parseInt(r[a],10)*(10-a);return 11==(n=11-s%11)?n=0:10===n&&(n="X"),{meta:{type:t},valid:"".concat(n)===r[i-1]};case"ISBN13":for(s=0,a=0;a<i-1;a++)s+=a%2==0?parseInt(r[a],10):3*parseInt(r[a],10);return 10==(n=10-s%10)&&(n="0"),{meta:{type:t},valid:"".concat(n)===r[i-1]}}}}},isin:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!new RegExp("^(AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|KH|CM|CA|CV|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MK|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|RE|RO|RU|RW|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SZ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW)[0-9A-Z]{10}$").test(e.value))return{valid:!1};var a,n=t.length,r="";for(a=0;a<n-1;a++){var i=t.charCodeAt(a);r+=i>57?(i-55).toString():t.charAt(a)}var s="",l=r.length,o=l%2!=0?0:1;for(a=0;a<l;a++)s+=parseInt(r[a],10)*(a%2===o?2:1)+"";var d=0;for(a=0;a<s.length;a++)d+=parseInt(s.charAt(a),10);return{valid:"".concat(d=(10-d%10)%10)===t.charAt(n-1)}}}},ismn:function(){return{validate:function(e){if(""===e.value)return{meta:null,valid:!0};var t;switch(!0){case/^M\d{9}$/.test(e.value):case/^M-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^M\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN10";break;case/^9790\d{9}$/.test(e.value):case/^979-0-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^979\s0\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN13";break;default:return{meta:null,valid:!1}}var a=e.value;"ISMN10"===t&&(a="9790".concat(a.substr(1)));for(var n=0,r=(a=a.replace(/[^0-9]/gi,"")).length,i=[1,3],s=0;s<r-1;s++)n+=parseInt(a.charAt(s),10)*i[s%2];return{meta:{type:t},valid:"".concat(n=(10-n%10)%10)===a.charAt(r-1)}}}},issn:function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^\d{4}\-\d{3}[\dX]$/.test(e.value))return{valid:!1};var t=e.value.replace(/[^0-9X]/gi,"").split(""),a=t.length,n=0;"X"===t[7]&&(t[7]="10");for(var r=0;r<a;r++)n+=parseInt(t[r],10)*(8-r);return{valid:n%11==0}}}},mac:function(){return{validate:function(e){return{valid:""===e.value||/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(e.value)||/^([0-9A-Fa-f]{4}\.){2}([0-9A-Fa-f]{4})$/.test(e.value)}}}},meid:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var a=e.value;switch(!0){case/^[0-9A-F]{15}$/i.test(a):case/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}[- ][0-9A-F]$/i.test(a):case/^\d{19}$/.test(a):case/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}[- ]\d$/.test(a):var n=a.charAt(a.length-1).toUpperCase();if((a=a.replace(/[- ]/g,"")).match(/^\d*$/i))return{valid:t(a)};a=a.slice(0,-1);var r,i="";for(r=1;r<=13;r+=2)i+=(2*parseInt(a.charAt(r),16)).toString(16);var s=0;for(r=0;r<i.length;r++)s+=parseInt(i.charAt(r),16);return{valid:s%10==0?"0"===n:n===(2*(10*Math.floor((s+10)/10)-s)).toString(16).toUpperCase()};case/^[0-9A-F]{14}$/i.test(a):case/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}$/i.test(a):case/^\d{18}$/.test(a):case/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}$/.test(a):return{valid:!0};default:return{valid:!1}}}}},phone:function(){var e=["AE","BG","BR","CN","CZ","DE","DK","ES","FR","GB","IN","MA","NL","PK","RO","RU","SK","TH","US","VE"];return{validate:function(t){if(""===t.value)return{valid:!0};var a=Object.assign({},{message:""},t.options),n=t.value.trim(),r=n.substr(0,2);if(!(r="function"==typeof a.country?a.country.call(this):a.country)||-1===e.indexOf(r.toUpperCase()))return{valid:!0};var i=!0;switch(r.toUpperCase()){case"AE":i=/^(((\+|00)?971[\s\.-]?(\(0\)[\s\.-]?)?|0)(\(5(0|2|5|6)\)|5(0|2|5|6)|2|3|4|6|7|9)|60)([\s\.-]?[0-9]){7}$/.test(n);break;case"BG":i=/^(0|359|00)(((700|900)[0-9]{5}|((800)[0-9]{5}|(800)[0-9]{4}))|(87|88|89)([0-9]{7})|((2[0-9]{7})|(([3-9][0-9])(([0-9]{6})|([0-9]{5})))))$/.test(n.replace(/\+|\s|-|\/|\(|\)/gi,""));break;case"BR":i=/^(([\d]{4}[-.\s]{1}[\d]{2,3}[-.\s]{1}[\d]{2}[-.\s]{1}[\d]{2})|([\d]{4}[-.\s]{1}[\d]{3}[-.\s]{1}[\d]{4})|((\(?\+?[0-9]{2}\)?\s?)?(\(?\d{2}\)?\s?)?\d{4,5}[-.\s]?\d{4}))$/.test(n);break;case"CN":i=/^((00|\+)?(86(?:-| )))?((\d{11})|(\d{3}[- ]{1}\d{4}[- ]{1}\d{4})|((\d{2,4}[- ]){1}(\d{7,8}|(\d{3,4}[- ]{1}\d{4}))([- ]{1}\d{1,4})?))$/.test(n);break;case"CZ":i=/^(((00)([- ]?)|\+)(420)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(n);break;case"DE":i=/^(((((((00|\+)49[ \-\/]?)|0)[1-9][0-9]{1,4})[ \-\/]?)|((((00|\+)49\()|\(0)[1-9][0-9]{1,4}\)[ \-\/]?))[0-9]{1,7}([ \-\/]?[0-9]{1,5})?)$/.test(n);break;case"DK":i=/^(\+45|0045|\(45\))?\s?[2-9](\s?\d){7}$/.test(n);break;case"ES":i=/^(?:(?:(?:\+|00)34\D?))?(?:5|6|7|8|9)(?:\d\D?){8}$/.test(n);break;case"FR":i=/^(?:(?:(?:\+|00)33[ ]?(?:\(0\)[ ]?)?)|0){1}[1-9]{1}([ .-]?)(?:\d{2}\1?){3}\d{2}$/.test(n);break;case"GB":i=/^\(?(?:(?:0(?:0|11)\)?[\s-]?\(?|\+)44\)?[\s-]?\(?(?:0\)?[\s-]?\(?)?|0)(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}|\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4}|\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3})|\d{5}\)?[\s-]?\d{4,5}|8(?:00[\s-]?11[\s-]?11|45[\s-]?46[\s-]?4\d))(?:(?:[\s-]?(?:x|ext\.?\s?|\#)\d+)?)$/.test(n);break;case"IN":i=/((\+?)((0[ -]+)*|(91 )*)(\d{12}|\d{10}))|\d{5}([- ]*)\d{6}/.test(n);break;case"MA":i=/^(?:(?:(?:\+|00)212[\s]?(?:[\s]?\(0\)[\s]?)?)|0){1}(?:5[\s.-]?[2-3]|6[\s.-]?[13-9]){1}[0-9]{1}(?:[\s.-]?\d{2}){3}$/.test(n);break;case"NL":i=/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9])((\s|\s?-\s?)?[0-9])((\s|\s?-\s?)?[0-9])\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]$/gm.test(n);break;case"PK":i=/^0?3[0-9]{2}[0-9]{7}$/.test(n);break;case"RO":i=/^(\+4|)?(07[0-8]{1}[0-9]{1}|02[0-9]{2}|03[0-9]{2}){1}?(\s|\.|\-)?([0-9]{3}(\s|\.|\-|)){2}$/g.test(n);break;case"RU":i=/^((8|\+7|007)[\-\.\/ ]?)?([\(\/\.]?\d{3}[\)\/\.]?[\-\.\/ ]?)?[\d\-\.\/ ]{7,10}$/g.test(n);break;case"SK":i=/^(((00)([- ]?)|\+)(421)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(n);break;case"TH":i=/^0\(?([6|8-9]{2})*-([0-9]{3})*-([0-9]{4})$/.test(n);break;case"VE":i=/^0(?:2(?:12|4[0-9]|5[1-9]|6[0-9]|7[0-8]|8[1-35-8]|9[1-5]|3[45789])|4(?:1[246]|2[46]))\d{7}$/.test(n);break;case"US":default:i=/^(?:(1\-?)|(\+1 ?))?\(?\d{3}\)?[\-\.\s]?\d{3}[\-\.\s]?\d{4}$/.test(n)}return{message:p(t.l10n?a.message||t.l10n.phone.country:a.message,t.l10n?t.l10n.phone.countries[r]:r),valid:i}}}},rtn:function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^\d{9}$/.test(e.value))return{valid:!1};for(var t=0,a=0;a<e.value.length;a+=3)t+=3*parseInt(e.value.charAt(a),10)+7*parseInt(e.value.charAt(a+1),10)+parseInt(e.value.charAt(a+2),10);return{valid:0!==t&&t%10==0}}}},sedol:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!/^[0-9A-Z]{7}$/.test(t))return{valid:!1};for(var a=[1,3,1,7,3,9,1],n=t.length,r=0,i=0;i<n-1;i++)r+=a[i]*parseInt(t.charAt(i),36);return{valid:"".concat(r=(10-r%10)%10)===t.charAt(n-1)}}}},siren:function(){return{validate:function(e){return{valid:""===e.value||/^\d{9}$/.test(e.value)&&t(e.value)}}}},siret:function(){return{validate:function(e){if(""===e.value)return{valid:!0};for(var t,a=e.value.length,n=0,r=0;r<a;r++)t=parseInt(e.value.charAt(r),10),r%2==0&&(t*=2)>9&&(t-=9),n+=t;return{valid:n%10==0}}}},step:function(){var e=function(e,t){if(0===t)return 1;var a="".concat(e).split("."),n="".concat(t).split("."),r=(1===a.length?0:a[1].length)+(1===n.length?0:n[1].length);return function(e,t){var a,n=Math.pow(10,t),r=e*n;switch(!0){case 0===r:a=0;break;case r>0:a=1;break;case r<0:a=-1}return r%1==.5*a?(Math.floor(r)+(a>0?1:0))/n:Math.round(r)/n}(e-t*Math.floor(e/t),r)};return{validate:function(t){if(""===t.value)return{valid:!0};var a=parseFloat(t.value);if(isNaN(a)||!isFinite(a))return{valid:!1};var n=Object.assign({},{baseValue:0,message:"",step:1},t.options),r=e(a-n.baseValue,n.step);return{message:p(t.l10n?n.message||t.l10n.step.default:n.message,"".concat(n.step)),valid:0===r||r===n.step}}}},uuid:function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=Object.assign({},{message:""},e.options),a={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i},n=t.version?"".concat(t.version):"all";return{message:t.version?p(e.l10n?t.message||e.l10n.uuid.version:t.message,t.version):e.l10n?e.l10n.uuid.default:t.message,valid:null===a[n]||a[n].test(e.value)}}}},vat:function(){var e=["AR","AT","BE","BG","BR","CH","CY","CZ","DE","DK","EE","EL","ES","FI","FR","GB","GR","HR","HU","IE","IS","IT","LT","LU","LV","MT","NL","NO","PL","PT","RO","RU","RS","SE","SK","SI","VE","ZA"];return{validate:function(t){var a=t.value;if(""===a)return{valid:!0};var n=Object.assign({},{message:""},t.options),r=a.substr(0,2);if(r="function"==typeof n.country?n.country.call(this):n.country,-1===e.indexOf(r))return{valid:!0};var i={meta:{},valid:!0};switch(r.toLowerCase()){case"ar":i=function(e){var t=e.replace("-","");if(/^AR[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return{meta:{},valid:!1};for(var a=[5,4,3,2,7,6,5,4,3,2],n=0,r=0;r<10;r++)n+=parseInt(t.charAt(r),10)*a[r];return 11==(n=11-n%11)&&(n=0),{meta:{},valid:"".concat(n)===t.substr(10)}}(a);break;case"at":i=function(e){var t=e;if(/^ATU[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^U[0-9]{8}$/.test(t))return{meta:{},valid:!1};t=t.substr(1);for(var a=[1,2,1,2,1,2,1],n=0,r=0,i=0;i<7;i++)(r=parseInt(t.charAt(i),10)*a[i])>9&&(r=Math.floor(r/10)+r%10),n+=r;return 10==(n=10-(n+4)%10)&&(n=0),{meta:{},valid:"".concat(n)===t.substr(7,1)}}(a);break;case"be":i=function(e){var t=e;return/^BE[0]?[0-9]{9}$/.test(t)&&(t=t.substr(2)),/^[0]?[0-9]{9}$/.test(t)?(9===t.length&&(t="0".concat(t)),"0"===t.substr(1,1)?{meta:{},valid:!1}:{meta:{},valid:(parseInt(t.substr(0,8),10)+parseInt(t.substr(8,2),10))%97==0}):{meta:{},valid:!1}}(a);break;case"bg":i=function(e){var t=e;if(/^BG[0-9]{9,10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9,10}$/.test(t))return{meta:{},valid:!1};var a=0,n=0;if(9===t.length){for(n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*(n+1);if(10==(a%=11))for(a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*(n+3);return{meta:{},valid:"".concat(a%=10)===t.substr(8)}}return{meta:{},valid:function(e){var t=parseInt(e.substr(0,2),10)+1900,a=parseInt(e.substr(2,2),10);if(a>40?(t+=100,a-=40):a>20&&(t-=100,a-=20),!A(t,a,parseInt(e.substr(4,2),10)))return!1;for(var n=[2,4,8,5,10,9,7,3,6],r=0,i=0;i<9;i++)r+=parseInt(e.charAt(i),10)*n[i];return"".concat(r=r%11%10)===e.substr(9,1)}(t)||function(e){for(var t=[21,19,17,13,11,9,7,3,1],a=0,n=0;n<9;n++)a+=parseInt(e.charAt(n),10)*t[n];return"".concat(a%=10)===e.substr(9,1)}(t)||function(e){for(var t=[4,3,2,7,6,5,4,3,2],a=0,n=0;n<9;n++)a+=parseInt(e.charAt(n),10)*t[n];return 10!=(a=11-a%11)&&(11===a&&(a=0),"".concat(a)===e.substr(9,1))}(t)}}(a);break;case"br":i=function(e){if(""===e)return{meta:{},valid:!0};var t=e.replace(/[^\d]+/g,"");if(""===t||14!==t.length)return{meta:{},valid:!1};if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return{meta:{},valid:!1};var a,n=t.length-2,r=t.substring(0,n),i=t.substring(n),s=0,l=n-7;for(a=n;a>=1;a--)s+=parseInt(r.charAt(n-a),10)*l--,l<2&&(l=9);var o=s%11<2?0:11-s%11;if(o!==parseInt(i.charAt(0),10))return{meta:{},valid:!1};for(n+=1,r=t.substring(0,n),s=0,l=n-7,a=n;a>=1;a--)s+=parseInt(r.charAt(n-a),10)*l--,l<2&&(l=9);return{meta:{},valid:(o=s%11<2?0:11-s%11)===parseInt(i.charAt(1),10)}}(a);break;case"ch":i=function(e){var t=e;if(/^CHE[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t)&&(t=t.substr(2)),!/^E[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t))return{meta:{},valid:!1};t=t.substr(1);for(var a=[5,4,3,2,7,6,5,4],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return 10==(n=11-n%11)?{meta:{},valid:!1}:(11===n&&(n=0),{meta:{},valid:"".concat(n)===t.substr(8,1)})}(a);break;case"cy":i=function(e){var t=e;if(/^CY[0-5|9][0-9]{7}[A-Z]$/.test(t)&&(t=t.substr(2)),!/^[0-5|9][0-9]{7}[A-Z]$/.test(t))return{meta:{},valid:!1};if("12"===t.substr(0,2))return{meta:{},valid:!1};for(var a=0,n={0:1,1:0,2:5,3:7,4:9,5:13,6:15,7:17,8:19,9:21},r=0;r<8;r++){var i=parseInt(t.charAt(r),10);r%2==0&&(i=n["".concat(i)]),a+=i}return{meta:{},valid:"".concat("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[a%26])===t.substr(8,1)}}(a);break;case"cz":i=function(e){var t=e;if(/^CZ[0-9]{8,10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8,10}$/.test(t))return{meta:{},valid:!1};var a=0,n=0;if(8===t.length){if("9"==="".concat(t.charAt(0)))return{meta:{},valid:!1};for(a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n),10)*(8-n);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a)===t.substr(7,1)}}if(9===t.length&&"6"==="".concat(t.charAt(0))){for(a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n+1),10)*(8-n);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a=[8,7,6,5,4,3,2,1,0,9,10][a-1])===t.substr(8,1)}}if(9===t.length||10===t.length){var r=1900+parseInt(t.substr(0,2),10),i=parseInt(t.substr(2,2),10)%50%20,s=parseInt(t.substr(4,2),10);if(9===t.length){if(r>=1980&&(r-=100),r>1953)return{meta:{},valid:!1}}else r<1954&&(r+=100);if(!A(r,i,s))return{meta:{},valid:!1};if(10===t.length){var l=parseInt(t.substr(0,9),10)%11;return r<1985&&(l%=10),{meta:{},valid:"".concat(l)===t.substr(9,1)}}return{meta:{},valid:!0}}return{meta:{},valid:!1}}(a);break;case"de":i=V(a);break;case"dk":i=function(e){var t=e;if(/^DK[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var a=0,n=[2,7,6,5,4,3,2,1],r=0;r<8;r++)a+=parseInt(t.charAt(r),10)*n[r];return{meta:{},valid:a%11==0}}(a);break;case"ee":i=function(e){var t=e;if(/^EE[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var a=0,n=[3,7,1,3,7,1,3,7,1],r=0;r<9;r++)a+=parseInt(t.charAt(r),10)*n[r];return{meta:{},valid:a%10==0}}(a);break;case"el":i=H(a);break;case"es":i=function(e){var t=e;if(/^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t)&&(t=t.substr(2)),!/^[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t))return{meta:{},valid:!1};var a=t.charAt(0);return/^[0-9]$/.test(a)?{meta:{type:"DNI"},valid:function(e){var t=parseInt(e.substr(0,8),10);return"".concat("TRWAGMYFPDXBNJZSQVHLCKE"[t%23])===e.substr(8,1)}(t)}:/^[XYZ]$/.test(a)?{meta:{type:"NIE"},valid:function(e){var t=["XYZ".indexOf(e.charAt(0)),e.substr(1)].join(""),a="TRWAGMYFPDXBNJZSQVHLCKE"[parseInt(t,10)%23];return"".concat(a)===e.substr(8,1)}(t)}:{meta:{type:"CIF"},valid:function(e){var t,a=e.charAt(0);if(-1!=="KLM".indexOf(a))return t=parseInt(e.substr(1,8),10),"".concat(t="TRWAGMYFPDXBNJZSQVHLCKE"[t%23])===e.substr(8,1);if(-1!=="ABCDEFGHJNPQRSUVW".indexOf(a)){for(var n=[2,1,2,1,2,1,2],r=0,i=0,s=0;s<7;s++)(i=parseInt(e.charAt(s+1),10)*n[s])>9&&(i=Math.floor(i/10)+i%10),r+=i;return 10==(r=10-r%10)&&(r=0),"".concat(r)===e.substr(8,1)||"JABCDEFGHI"[r]===e.substr(8,1)}return!1}(t)}}(a);break;case"fi":i=function(e){var t=e;if(/^FI[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var a=[7,9,10,5,8,4,2,1],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return{meta:{},valid:n%11==0}}(a);break;case"fr":i=x(a);break;case"gb":i=function(e){var t=e;if((/^GB[0-9]{9}$/.test(t)||/^GB[0-9]{12}$/.test(t)||/^GBGD[0-9]{3}$/.test(t)||/^GBHA[0-9]{3}$/.test(t)||/^GB(GD|HA)8888[0-9]{5}$/.test(t))&&(t=t.substr(2)),!(/^[0-9]{9}$/.test(t)||/^[0-9]{12}$/.test(t)||/^GD[0-9]{3}$/.test(t)||/^HA[0-9]{3}$/.test(t)||/^(GD|HA)8888[0-9]{5}$/.test(t)))return{meta:{},valid:!1};var a=t.length;if(5===a){var n=t.substr(0,2),r=parseInt(t.substr(2),10);return{meta:{},valid:"GD"===n&&r<500||"HA"===n&&r>=500}}if(11===a&&("GD8888"===t.substr(0,6)||"HA8888"===t.substr(0,6)))return"GD"===t.substr(0,2)&&parseInt(t.substr(6,3),10)>=500||"HA"===t.substr(0,2)&&parseInt(t.substr(6,3),10)<500?{meta:{},valid:!1}:{meta:{},valid:parseInt(t.substr(6,3),10)%97===parseInt(t.substr(9,2),10)};if(9===a||12===a){for(var i=[8,7,6,5,4,3,2,10,1],s=0,l=0;l<9;l++)s+=parseInt(t.charAt(l),10)*i[l];return s%=97,{meta:{},valid:parseInt(t.substr(0,3),10)>=100?0===s||42===s||55===s:0===s}}return{meta:{},valid:!0}}(a);break;case"gr":i=H(a);break;case"hr":i=$(a);break;case"hu":i=function(e){var t=e;if(/^HU[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var a=[9,7,3,1,9,7,3,1],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return{meta:{},valid:n%10==0}}(a);break;case"ie":i=function(e){var t=e;if(/^IE[0-9][0-9A-Z\*\+][0-9]{5}[A-Z]{1,2}$/.test(t)&&(t=t.substr(2)),!/^[0-9][0-9A-Z\*\+][0-9]{5}[A-Z]{1,2}$/.test(t))return{meta:{},valid:!1};var a=function(e){for(var t=e;t.length<7;)t="0".concat(t);for(var a="WABCDEFGHIJKLMNOPQRSTUV",n=0,r=0;r<7;r++)n+=parseInt(t.charAt(r),10)*(8-r);return n+=9*a.indexOf(t.substr(7)),a[n%23]};return/^[0-9]+$/.test(t.substr(0,7))?{meta:{},valid:t.charAt(7)===a("".concat(t.substr(0,7)).concat(t.substr(8)))}:-1!=="ABCDEFGHIJKLMNOPQRSTUVWXYZ+*".indexOf(t.charAt(1))?{meta:{},valid:t.charAt(7)===a("".concat(t.substr(2,5)).concat(t.substr(0,1)))}:{meta:{},valid:!0}}(a);break;case"is":i=function(e){var t=e;return/^IS[0-9]{5,6}$/.test(t)&&(t=t.substr(2)),{meta:{},valid:/^[0-9]{5,6}$/.test(t)}}(a);break;case"it":i=T(a);break;case"lt":i=function(e){var t=e;if(/^LT([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t)&&(t=t.substr(2)),!/^([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t))return{meta:{},valid:!1};var a,n=t.length,r=0;for(a=0;a<n-1;a++)r+=parseInt(t.charAt(a),10)*(1+a%9);var i=r%11;if(10===i)for(r=0,a=0;a<n-1;a++)r+=parseInt(t.charAt(a),10)*(1+(a+2)%9);return{meta:{},valid:"".concat(i=i%11%10)===t.charAt(n-1)}}(a);break;case"lu":i=function(e){var t=e;return/^LU[0-9]{8}$/.test(t)&&(t=t.substr(2)),/^[0-9]{8}$/.test(t)?{meta:{},valid:"".concat(parseInt(t.substr(0,6),10)%89)===t.substr(6,2)}:{meta:{},valid:!1}}(a);break;case"lv":i=function(e){var t=e;if(/^LV[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return{meta:{},valid:!1};var a,n=parseInt(t.charAt(0),10),r=t.length,i=0,s=[];if(n>3){for(i=0,s=[9,1,4,8,3,10,2,5,7,6,1],a=0;a<r;a++)i+=parseInt(t.charAt(a),10)*s[a];return{meta:{},valid:3==(i%=11)}}var l=parseInt(t.substr(0,2),10),o=parseInt(t.substr(2,2),10),d=parseInt(t.substr(4,2),10);if(!A(d=d+1800+100*parseInt(t.charAt(6),10),o,l))return{meta:{},valid:!1};for(i=0,s=[10,5,8,4,2,1,6,3,7,9],a=0;a<r-1;a++)i+=parseInt(t.charAt(a),10)*s[a];return{meta:{},valid:"".concat(i=(i+1)%11%10)===t.charAt(r-1)}}(a);break;case"mt":i=function(e){var t=e;if(/^MT[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var a=[3,4,6,7,8,9,10,1],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return{meta:{},valid:n%37==0}}(a);break;case"nl":i=function(e){var t=e;if(/^NL[0-9]{9}B[0-9]{2}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}B[0-9]{2}$/.test(t))return{meta:{},valid:!1};for(var a=[9,8,7,6,5,4,3,2],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return(n%=11)>9&&(n=0),{meta:{},valid:"".concat(n)===t.substr(8,1)}}(a);break;case"no":i=function(e){var t=e;if(/^NO[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var a=[3,2,7,6,5,4,3,2],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return 11==(n=11-n%11)&&(n=0),{meta:{},valid:"".concat(n)===t.substr(8,1)}}(a);break;case"pl":i=function(e){var t=e;if(/^PL[0-9]{10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{10}$/.test(t))return{meta:{},valid:!1};for(var a=[6,5,7,2,3,4,5,6,7,-1],n=0,r=0;r<10;r++)n+=parseInt(t.charAt(r),10)*a[r];return{meta:{},valid:n%11==0}}(a);break;case"pt":i=function(e){var t=e;if(/^PT[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var a=[9,8,7,6,5,4,3,2],n=0,r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*a[r];return(n=11-n%11)>9&&(n=0),{meta:{},valid:"".concat(n)===t.substr(8,1)}}(a);break;case"ro":i=function(e){var t=e;if(/^RO[1-9][0-9]{1,9}$/.test(t)&&(t=t.substr(2)),!/^[1-9][0-9]{1,9}$/.test(t))return{meta:{},valid:!1};for(var a=t.length,n=[7,5,3,2,1,7,5,3,2].slice(10-a),r=0,i=0;i<a-1;i++)r+=parseInt(t.charAt(i),10)*n[i];return{meta:{},valid:"".concat(r=10*r%11%10)===t.substr(a-1,1)}}(a);break;case"rs":i=function(e){var t=e;if(/^RS[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var a=10,n=0,r=0;r<8;r++)0==(n=(parseInt(t.charAt(r),10)+a)%10)&&(n=10),a=2*n%11;return{meta:{},valid:(a+parseInt(t.substr(8,1),10))%10==1}}(a);break;case"ru":i=function(e){var t=e;if(/^RU([0-9]{10}|[0-9]{12})$/.test(t)&&(t=t.substr(2)),!/^([0-9]{10}|[0-9]{12})$/.test(t))return{meta:{},valid:!1};var a=0;if(10===t.length){var n=[2,4,10,3,5,9,4,6,8,0],r=0;for(a=0;a<10;a++)r+=parseInt(t.charAt(a),10)*n[a];return(r%=11)>9&&(r%=10),{meta:{},valid:"".concat(r)===t.substr(9,1)}}if(12===t.length){var i=[7,2,4,10,3,5,9,4,6,8,0],s=[3,7,2,4,10,3,5,9,4,6,8,0],l=0,o=0;for(a=0;a<11;a++)l+=parseInt(t.charAt(a),10)*i[a],o+=parseInt(t.charAt(a),10)*s[a];return(l%=11)>9&&(l%=10),(o%=11)>9&&(o%=10),{meta:{},valid:"".concat(l)===t.substr(10,1)&&"".concat(o)===t.substr(11,1)}}return{meta:{},valid:!0}}(a);break;case"se":i=M(a);break;case"si":i=function(e){var t=e.match(/^(SI)?([1-9][0-9]{7})$/);if(!t)return{meta:{},valid:!1};for(var a=t[1]?e.substr(2):e,n=[8,7,6,5,4,3,2],r=0,i=0;i<7;i++)r+=parseInt(a.charAt(i),10)*n[i];return 10==(r=11-r%11)&&(r=0),{meta:{},valid:"".concat(r)===a.substr(7,1)}}(a);break;case"sk":i=function(e){var t=e;return/^SK[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)&&(t=t.substr(2)),/^[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)?{meta:{},valid:parseInt(t,10)%11==0}:{meta:{},valid:!1}}(a);break;case"ve":i=function(e){var t=e;if(/^VE[VEJPG][0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[VEJPG][0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var a=[3,2,7,6,5,4,3,2],n={E:8,G:20,J:12,P:16,V:4}[t.charAt(0)],r=0;r<8;r++)n+=parseInt(t.charAt(r+1),10)*a[r];return(11==(n=11-n%11)||10===n)&&(n=0),{meta:{},valid:"".concat(n)===t.substr(9,1)}}(a);break;case"za":i=function(e){var t=e;return/^ZA4[0-9]{9}$/.test(t)&&(t=t.substr(2)),{meta:{},valid:/^4[0-9]{9}$/.test(t)}}(a)}var s=p(t.l10n?n.message||t.l10n.vat.country:n.message,t.l10n?t.l10n.vat.countries[r.toUpperCase()]:r.toUpperCase());return Object.assign({},{message:s},i)}}},vin:function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^[a-hj-npr-z0-9]{8}[0-9xX][a-hj-npr-z0-9]{8}$/i.test(e.value))return{valid:!1};for(var t=e.value.toUpperCase(),a={A:1,B:2,C:3,D:4,E:5,F:6,G:7,H:8,J:1,K:2,L:3,M:4,N:5,P:7,R:9,S:2,T:3,U:4,V:5,W:6,X:7,Y:8,Z:9,0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9},n=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],r=t.length,i=0,s=0;s<r;s++)i+=a["".concat(t.charAt(s))]*n[s];var l="".concat(i%11);return"10"===l&&(l="X"),{valid:l===t.charAt(8)}}}},zipCode:function(){var e=["AT","BG","BR","CA","CH","CZ","DE","DK","ES","FR","GB","IE","IN","IT","MA","NL","PL","PT","RO","RU","SE","SG","SK","US"];return{validate:function(t){var a=Object.assign({},t.options);if(""===t.value||!a.country)return{valid:!0};var n=t.value.substr(0,2);if(!(n="function"==typeof a.country?a.country.call(this):a.country)||-1===e.indexOf(n.toUpperCase()))return{valid:!0};var r=!1;switch(n=n.toUpperCase()){case"AT":r=/^([1-9]{1})(\d{3})$/.test(t.value);break;case"BG":r=/^([1-9]{1}[0-9]{3})$/.test(t.value);break;case"BR":r=/^(\d{2})([\.]?)(\d{3})([\-]?)(\d{3})$/.test(t.value);break;case"CA":r=/^(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|X|Y){1}[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}\s?[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}[0-9]{1}$/i.test(t.value);break;case"CH":r=/^([1-9]{1})(\d{3})$/.test(t.value);break;case"CZ":r=/^(\d{3})([ ]?)(\d{2})$/.test(t.value);break;case"DE":r=/^(?!01000|99999)(0[1-9]\d{3}|[1-9]\d{4})$/.test(t.value);break;case"DK":r=/^(DK(-|\s)?)?\d{4}$/i.test(t.value);break;case"ES":r=/^(?:0[1-9]|[1-4][0-9]|5[0-2])\d{3}$/.test(t.value);break;case"FR":r=/^[0-9]{5}$/i.test(t.value);break;case"GB":r=function(e){for(var t="[ABCDEFGHIJKLMNOPRSTUWYZ]",a="[ABCDEFGHKLMNOPQRSTUVWXY]",n="[ABDEFGHJLNPQRSTUWXYZ]",r=0,i=[new RegExp("^(".concat(t,"{1}").concat(a,"?[0-9]{1,2})(\\s*)([0-9]{1}").concat(n,"{2})$"),"i"),new RegExp("^(".concat(t,"{1}[0-9]{1}").concat("[ABCDEFGHJKPMNRSTUVWXY]","{1})(\\s*)([0-9]{1}").concat(n,"{2})$"),"i"),new RegExp("^(".concat(t,"{1}").concat(a,"{1}?[0-9]{1}").concat("[ABEHMNPRVWXY]","{1})(\\s*)([0-9]{1}").concat(n,"{2})$"),"i"),new RegExp("^(BF1)(\\s*)([0-6]{1}[ABDEFGHJLNPQRST]{1}[ABDEFGHJLNPQRSTUWZYZ]{1})$","i"),/^(GIR)(\s*)(0AA)$/i,/^(BFPO)(\s*)([0-9]{1,4})$/i,/^(BFPO)(\s*)(c\/o\s*[0-9]{1,3})$/i,/^([A-Z]{4})(\s*)(1ZZ)$/i,/^(AI-2640)$/i];r<i.length;r++)if(i[r].test(e))return!0;return!1}(t.value);break;case"IN":r=/^\d{3}\s?\d{3}$/.test(t.value);break;case"IE":r=/^(D6W|[ACDEFHKNPRTVWXY]\d{2})\s[0-9ACDEFHKNPRTVWXY]{4}$/.test(t.value);break;case"IT":r=/^(I-|IT-)?\d{5}$/i.test(t.value);break;case"MA":r=/^[1-9][0-9]{4}$/i.test(t.value);break;case"NL":r=/^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i.test(t.value);break;case"PL":r=/^[0-9]{2}\-[0-9]{3}$/.test(t.value);break;case"PT":r=/^[1-9]\d{3}-\d{3}$/.test(t.value);break;case"RO":r=/^(0[1-8]{1}|[1-9]{1}[0-5]{1})?[0-9]{4}$/i.test(t.value);break;case"RU":r=/^[0-9]{6}$/i.test(t.value);break;case"SE":r=/^(S-)?\d{3}\s?\d{2}$/i.test(t.value);break;case"SG":r=/^([0][1-9]|[1-6][0-9]|[7]([0-3]|[5-9])|[8][0-2])(\d{4})$/i.test(t.value);break;case"SK":r=/^(\d{3})([ ]?)(\d{2})$/.test(t.value);break;case"US":default:r=/^\d{4,5}([\-]?\d{4})?$/.test(t.value)}return{message:p(t.l10n?a.message||t.l10n.zipCode.country:a.message,t.l10n?t.l10n.zipCode.countries[n]:n),valid:r}}}}},L=function(){function e(t,a){s(this,e),this.elements={},this.ee={fns:{},clear:function(){this.fns={}},emit:function(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];(this.fns[e]||[]).map(function(e){return e.apply(e,a)})},off:function(e,t){if(this.fns[e]){var a=this.fns[e].indexOf(t);a>=0&&this.fns[e].splice(a,1)}},on:function(e,t){(this.fns[e]=this.fns[e]||[]).push(t)}},this.filter={filters:{},add:function(e,t){(this.filters[e]=this.filters[e]||[]).push(t)},clear:function(){this.filters={}},execute:function(e,t,a){if(!this.filters[e]||!this.filters[e].length)return t;for(var n=t,r=this.filters[e],i=r.length,s=0;s<i;s++)n=r[s].apply(n,a);return n},remove:function(e,t){this.filters[e]&&(this.filters[e]=this.filters[e].filter(function(e){return e!==t}))}},this.plugins={},this.results=new Map,this.validators={},this.form=t,this.fields=a}return o(e,[{key:"on",value:function(e,t){return this.ee.on(e,t),this}},{key:"off",value:function(e,t){return this.ee.off(e,t),this}},{key:"emit",value:function(e){for(var t,a=arguments.length,n=new Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];return(t=this.ee).emit.apply(t,[e].concat(n)),this}},{key:"registerPlugin",value:function(e,t){if(this.plugins[e])throw new Error("The plguin ".concat(e," is registered"));return t.setCore(this),t.install(),this.plugins[e]=t,this}},{key:"deregisterPlugin",value:function(e){var t=this.plugins[e];return t&&t.uninstall(),delete this.plugins[e],this}},{key:"registerValidator",value:function(e,t){if(this.validators[e])throw new Error("The validator ".concat(e," is registered"));return this.validators[e]=t,this}},{key:"registerFilter",value:function(e,t){return this.filter.add(e,t),this}},{key:"deregisterFilter",value:function(e,t){return this.filter.remove(e,t),this}},{key:"executeFilter",value:function(e,t,a){return this.filter.execute(e,t,a)}},{key:"addField",value:function(e,t){var a=Object.assign({},{selector:"",validators:{}},t);return this.fields[e]=this.fields[e]?{selector:a.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,a.validators)}:a,this.elements[e]=this.queryElements(e),this.emit("core.field.added",{elements:this.elements[e],field:e,options:this.fields[e]}),this}},{key:"removeField",value:function(e){if(!this.fields[e])throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"));var t=this.elements[e],a=this.fields[e];return delete this.elements[e],delete this.fields[e],this.emit("core.field.removed",{elements:t,field:e,options:a}),this}},{key:"validate",value:function(){var e=this;return this.emit("core.form.validating"),this.filter.execute("validate-pre",Promise.resolve(),[]).then(function(){return Promise.all(Object.keys(e.fields).map(function(t){return e.validateField(t)})).then(function(t){switch(!0){case-1!==t.indexOf("Invalid"):return e.emit("core.form.invalid"),Promise.resolve("Invalid");case-1!==t.indexOf("NotValidated"):return e.emit("core.form.notvalidated"),Promise.resolve("NotValidated");default:return e.emit("core.form.valid"),Promise.resolve("Valid")}})})}},{key:"validateField",value:function(e){var t=this,a=this.results.get(e);if("Valid"===a||"Invalid"===a)return Promise.resolve(a);this.emit("core.field.validating",e);var n=this.elements[e];if(0===n.length)return this.emit("core.field.valid",e),Promise.resolve("Valid");var r=n[0].getAttribute("type");return"radio"===r||"checkbox"===r||1===n.length?this.validateElement(e,n[0]):Promise.all(n.map(function(a){return t.validateElement(e,a)})).then(function(a){switch(!0){case-1!==a.indexOf("Invalid"):return t.emit("core.field.invalid",e),t.results.set(e,"Invalid"),Promise.resolve("Invalid");case-1!==a.indexOf("NotValidated"):return t.emit("core.field.notvalidated",e),t.results.delete(e),Promise.resolve("NotValidated");default:return t.emit("core.field.valid",e),t.results.set(e,"Valid"),Promise.resolve("Valid")}})}},{key:"validateElement",value:function(e,t){var a=this;this.results.delete(e);var n=this.elements[e];if(this.filter.execute("element-ignored",!1,[e,t,n]))return this.emit("core.element.ignored",{element:t,elements:n,field:e}),Promise.resolve("Ignored");var r=this.fields[e].validators;this.emit("core.element.validating",{element:t,elements:n,field:e});var i=Object.keys(r).map(function(n){return function(){return a.executeValidator(e,t,n,r[n])}});return this.waterfall(i).then(function(r){var i=-1===r.indexOf("Invalid");a.emit("core.element.validated",{element:t,elements:n,field:e,valid:i});var s=t.getAttribute("type");return("radio"===s||"checkbox"===s||1===n.length)&&a.emit(i?"core.field.valid":"core.field.invalid",e),Promise.resolve(i?"Valid":"Invalid")}).catch(function(r){return a.emit("core.element.notvalidated",{element:t,elements:n,field:e}),Promise.resolve(r)})}},{key:"executeValidator",value:function(e,t,a,n){var r=this,i=this.elements[e],s=this.filter.execute("validator-name",a,[a,e]);if(n.message=this.filter.execute("validator-message",n.message,[this.locale,e,s]),!this.validators[s]||!1===n.enabled)return this.emit("core.validator.validated",{element:t,elements:i,field:e,result:this.normalizeResult(e,s,{valid:!0}),validator:s}),Promise.resolve("Valid");var l=this.validators[s],o=this.getElementValue(e,t,s);if(!this.filter.execute("field-should-validate",!0,[e,t,o,a]))return this.emit("core.validator.notvalidated",{element:t,elements:i,field:e,validator:a}),Promise.resolve("NotValidated");this.emit("core.validator.validating",{element:t,elements:i,field:e,validator:a});var d=l().validate({element:t,elements:i,field:e,l10n:this.localization,options:n,value:o});if("function"==typeof d.then)return d.then(function(n){var s=r.normalizeResult(e,a,n);return r.emit("core.validator.validated",{element:t,elements:i,field:e,result:s,validator:a}),s.valid?"Valid":"Invalid"});var c=this.normalizeResult(e,a,d);return this.emit("core.validator.validated",{element:t,elements:i,field:e,result:c,validator:a}),Promise.resolve(c.valid?"Valid":"Invalid")}},{key:"getElementValue",value:function(e,t,a){var n=m(this.form,0,t,this.elements[e]);return this.filter.execute("field-value",n,[n,e,t,a])}},{key:"getElements",value:function(e){return this.elements[e]}},{key:"getFields",value:function(){return this.fields}},{key:"getFormElement",value:function(){return this.form}},{key:"getLocale",value:function(){return this.locale}},{key:"getPlugin",value:function(e){return this.plugins[e]}},{key:"updateFieldStatus",value:function(e,t,a){var n=this,r=this.elements[e],i=r[0].getAttribute("type");if(("radio"===i||"checkbox"===i?[r[0]]:r).forEach(function(r){return n.updateElementStatus(e,r,t,a)}),!a)switch(t){case"NotValidated":this.emit("core.field.notvalidated",e),this.results.delete(e);break;case"Validating":this.emit("core.field.validating",e),this.results.delete(e);break;case"Valid":this.emit("core.field.valid",e),this.results.set(e,"Valid");break;case"Invalid":this.emit("core.field.invalid",e),this.results.set(e,"Invalid")}return this}},{key:"updateElementStatus",value:function(e,t,a,n){var r=this,i=this.elements[e],s=this.fields[e].validators,l=n?[n]:Object.keys(s);switch(a){case"NotValidated":l.forEach(function(a){return r.emit("core.validator.notvalidated",{element:t,elements:i,field:e,validator:a})}),this.emit("core.element.notvalidated",{element:t,elements:i,field:e});break;case"Validating":l.forEach(function(a){return r.emit("core.validator.validating",{element:t,elements:i,field:e,validator:a})}),this.emit("core.element.validating",{element:t,elements:i,field:e});break;case"Valid":l.forEach(function(a){return r.emit("core.validator.validated",{element:t,field:e,result:{message:s[a].message,valid:!0},validator:a})}),this.emit("core.element.validated",{element:t,elements:i,field:e,valid:!0});break;case"Invalid":l.forEach(function(a){return r.emit("core.validator.validated",{element:t,field:e,result:{message:s[a].message,valid:!1},validator:a})}),this.emit("core.element.validated",{element:t,elements:i,field:e,valid:!1})}return this}},{key:"resetForm",value:function(e){var t=this;return Object.keys(this.fields).forEach(function(a){return t.resetField(a,e)}),this.emit("core.form.reset",{reset:e}),this}},{key:"resetField",value:function(e,t){if(t){var a=this.elements[e],n=a[0].getAttribute("type");a.forEach(function(e){"radio"===n||"checkbox"===n?(e.removeAttribute("selected"),e.removeAttribute("checked"),e.checked=!1):(e.setAttribute("value",""),(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(e.value=""))})}return this.updateFieldStatus(e,"NotValidated"),this.emit("core.field.reset",{field:e,reset:t}),this}},{key:"revalidateField",value:function(e){return this.updateFieldStatus(e,"NotValidated"),this.validateField(e)}},{key:"disableValidator",value:function(e,t){return this.toggleValidator(!1,e,t)}},{key:"enableValidator",value:function(e,t){return this.toggleValidator(!0,e,t)}},{key:"updateValidatorOption",value:function(e,t,a,n){return this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][a]=n),this}},{key:"destroy",value:function(){var e=this;return Object.keys(this.plugins).forEach(function(t){return e.plugins[t].uninstall()}),this.ee.clear(),this.filter.clear(),this.results.clear(),this.plugins={},this}},{key:"setLocale",value:function(e,t){return this.locale=e,this.localization=t,this}},{key:"waterfall",value:function(e){return e.reduce(function(e,t,a,n){return e.then(function(e){return t().then(function(t){return e.push(t),e})})},Promise.resolve([]))}},{key:"queryElements",value:function(e){var t=this.fields[e].selector?"#"===this.fields[e].selector.charAt(0)?'[id="'.concat(this.fields[e].selector.substring(1),'"]'):this.fields[e].selector:'[name="'.concat(e,'"]');return[].slice.call(this.form.querySelectorAll(t))}},{key:"normalizeResult",value:function(e,t,a){var n=this.fields[e].validators[t];return Object.assign({},a,{message:a.message||(n?n.message:"")||(this.localization&&this.localization[t]&&this.localization[t].default?this.localization[t].default:"")||"The field ".concat(e," is not valid")})}},{key:"toggleValidator",value:function(e,t,a){var n=this,r=this.fields[t].validators;return a&&r&&r[a]?this.fields[t].validators[a].enabled=e:a||Object.keys(r).forEach(function(a){return n.fields[t].validators[a].enabled=e}),this.updateFieldStatus(t,"NotValidated",a)}}]),e}();var R=function(){function e(t){s(this,e),this.opts=t}return o(e,[{key:"setCore",value:function(e){return this.core=e,this}},{key:"install",value:function(){}},{key:"uninstall",value:function(){}}]),e}(),B={getFieldValue:m},D=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).opts=e||{},a.validatorNameFilter=a.getValidatorName.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.registerFilter("validator-name",this.validatorNameFilter)}},{key:"uninstall",value:function(){this.core.deregisterFilter("validator-name",this.validatorNameFilter)}},{key:"getValidatorName",value:function(e,t){return this.opts[e]||e}}]),t}(),P=function(e){function t(){var e;return s(this,t),(e=h(this,u(t).call(this,{}))).elementValidatedHandler=e.onElementValidated.bind(v(e)),e.fieldValidHandler=e.onFieldValid.bind(v(e)),e.fieldInvalidHandler=e.onFieldInvalid.bind(v(e)),e.messageDisplayedHandler=e.onMessageDisplayed.bind(v(e)),e}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("core.field.valid",this.fieldValidHandler).on("core.field.invalid",this.fieldInvalidHandler).on("core.element.validated",this.elementValidatedHandler).on("plugins.message.displayed",this.messageDisplayedHandler)}},{key:"uninstall",value:function(){this.core.off("core.field.valid",this.fieldValidHandler).off("core.field.invalid",this.fieldInvalidHandler).off("core.element.validated",this.elementValidatedHandler).off("plugins.message.displayed",this.messageDisplayedHandler)}},{key:"onElementValidated",value:function(e){e.valid&&(e.element.setAttribute("aria-invalid","false"),e.element.removeAttribute("aria-describedby"))}},{key:"onFieldValid",value:function(e){var t=this.core.getElements(e);t&&t.forEach(function(e){e.setAttribute("aria-invalid","false"),e.removeAttribute("aria-describedby")})}},{key:"onFieldInvalid",value:function(e){var t=this.core.getElements(e);t&&t.forEach(function(e){return e.setAttribute("aria-invalid","true")})}},{key:"onMessageDisplayed",value:function(e){e.messageElement.setAttribute("role","alert"),e.messageElement.setAttribute("aria-hidden","false");var t=this.core.getElements(e.field),a=t.indexOf(e.element),n="js-fv-".concat(e.field,"-").concat(a,"-").concat(Date.now(),"-message");e.messageElement.setAttribute("id",n),e.element.setAttribute("aria-describedby",n);var r=e.element.getAttribute("type");("radio"===r||"checkbox"===r)&&t.forEach(function(e){return e.setAttribute("aria-describedby",n)})}}]),t}(),Z=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},e),a}return c(t,R),o(t,[{key:"install",value:function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach(function(a){return e.core.addField(a,t[a])})}},{key:"parseOptions",value:function(){var e=this,t=this.opts.prefix,a={},n=this.core.getFields(),r=this.core.getFormElement();return[].slice.call(r.querySelectorAll("[name], [".concat(t,"field]"))).forEach(function(n){var r=e.parseElement(n);if(!e.isEmptyOption(r)){var i=n.getAttribute("name")||n.getAttribute("".concat(t,"field"));a[i]=Object.assign({},a[i],r)}}),Object.keys(a).forEach(function(e){Object.keys(a[e].validators).forEach(function(t){a[e].validators[t].enabled=a[e].validators[t].enabled||!1,n[e]&&n[e].validators&&n[e].validators[t]&&Object.assign(a[e].validators[t],n[e].validators[t])})}),Object.assign({},n,a)}},{key:"createPluginInstance",value:function(e,t){for(var a=e.split("."),n=window||this,r=0,i=a.length;r<i;r++)n=n[a[r]];if("function"!=typeof n)throw new Error("the plugin ".concat(e," doesn't exist"));return new n(t)}},{key:"parsePlugins",value:function(){for(var e=this,t=this.core.getFormElement(),a=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),n=t.attributes.length,r={},i=0;i<n;i++){var s=t.attributes[i].name,l=t.attributes[i].value,o=a.exec(s);if(o&&4===o.length){var c=this.toCamelCase(o[1]);r[c]=Object.assign({},o[3]?d({},this.toCamelCase(o[3]),l):{enabled:""===l||"true"===l},r[c])}}Object.keys(r).forEach(function(t){var a=r[t],n=a.enabled,i=a.class;if(n&&i){delete a.enabled,delete a.clazz;var s=e.createPluginInstance(i,a);e.core.registerPlugin(t,s)}})}},{key:"isEmptyOption",value:function(e){var t=e.validators;return 0===Object.keys(t).length&&t.constructor===Object}},{key:"parseElement",value:function(e){for(var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),a=e.attributes.length,n={},r=e.getAttribute("type"),i=0;i<a;i++){var s=e.attributes[i].name,l=e.attributes[i].value;if(this.opts.html5Input)switch(!0){case"minlength"===s:n.stringLength=Object.assign({},{enabled:!0,min:parseInt(l,10)},n.stringLength);break;case"maxlength"===s:n.stringLength=Object.assign({},{enabled:!0,max:parseInt(l,10)},n.stringLength);break;case"pattern"===s:n.regexp=Object.assign({},{enabled:!0,regexp:l},n.regexp);break;case"required"===s:n.notEmpty=Object.assign({},{enabled:!0},n.notEmpty);break;case"type"===s&&"color"===l:n.color=Object.assign({},{enabled:!0,type:"hex"},n.color);break;case"type"===s&&"email"===l:n.emailAddress=Object.assign({},{enabled:!0},n.emailAddress);break;case"type"===s&&"url"===l:n.uri=Object.assign({},{enabled:!0},n.uri);break;case"type"===s&&"range"===l:n.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute("max")),min:parseFloat(e.getAttribute("min"))},n.between);break;case"min"===s&&"date"!==r&&"range"!==r:n.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(l)},n.greaterThan);break;case"max"===s&&"date"!==r&&"range"!==r:n.lessThan=Object.assign({},{enabled:!0,max:parseFloat(l)},n.lessThan)}var o=t.exec(s);if(o&&4===o.length){var c=this.toCamelCase(o[1]);n[c]=Object.assign({},o[3]?d({},this.toCamelCase(o[3]),l):{enabled:""===l||"true"===l},n[c])}}return{validators:n}}},{key:"toUpperCase",value:function(e){return e.charAt(1).toUpperCase()}},{key:"toCamelCase",value:function(e){return e.replace(/-./g,this.toUpperCase)}}]),t}(),U=function(e){function t(){var e;return s(this,t),(e=h(this,u(t).call(this,{}))).onValidHandler=e.onFormValid.bind(v(e)),e}return c(t,R),o(t,[{key:"install",value:function(){if(this.core.getFormElement().querySelectorAll('[type="submit"][name="submit"]').length)throw new Error("Do not use `submit` for the name attribute of submit button");this.core.on("core.form.valid",this.onValidHandler)}},{key:"uninstall",value:function(){this.core.off("core.form.valid",this.onValidHandler)}},{key:"onFormValid",value:function(){var e=this.core.getFormElement();e instanceof HTMLFormElement&&e.submit()}}]),t}(),G=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).opts=e||{},a.triggerExecutedHandler=a.onTriggerExecuted.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("plugins.trigger.executed",this.triggerExecutedHandler)}},{key:"uninstall",value:function(){this.core.off("plugins.trigger.executed",this.triggerExecutedHandler)}},{key:"onTriggerExecuted",value:function(e){if(this.opts[e.field]){var t=this.opts[e.field].split(" "),a=!0,n=!1,r=void 0;try{for(var i,s=t[Symbol.iterator]();!(a=(i=s.next()).done);a=!0){var l=i.value.trim();this.opts[l]&&this.core.revalidateField(l)}}catch(e){n=!0,r=e}finally{try{!a&&null!=s.return&&s.return()}finally{if(n)throw r}}}}}]),t}(),j=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).opts=Object.assign({},{excluded:t.defaultIgnore},e),a.ignoreValidationFilter=a.ignoreValidation.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.registerFilter("element-ignored",this.ignoreValidationFilter)}},{key:"uninstall",value:function(){this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)}},{key:"ignoreValidation",value:function(e,t,a){return this.opts.excluded.apply(this,[e,t,a])}}],[{key:"defaultIgnore",value:function(e,t,a){var n=!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length),r=t.getAttribute("disabled");return""===r||"disabled"===r||"hidden"===t.getAttribute("type")||!n}}]),t}(),K=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).statuses=new Map,a.opts=Object.assign({},{onStatusChanged:function(){}},e),a.elementValidatingHandler=a.onElementValidating.bind(v(a)),a.elementValidatedHandler=a.onElementValidated.bind(v(a)),a.elementNotValidatedHandler=a.onElementNotValidated.bind(v(a)),a.elementIgnoredHandler=a.onElementIgnored.bind(v(a)),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a.fieldRemovedHandler=a.onFieldRemoved.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)}},{key:"uninstall",value:function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)}},{key:"areFieldsValid",value:function(){return Array.from(this.statuses.values()).every(function(e){return"Valid"===e||"NotValidated"===e||"Ignored"===e})}},{key:"getStatuses",value:function(){return this.statuses}},{key:"onFieldAdded",value:function(e){this.statuses.set(e.field,"NotValidated")}},{key:"onFieldRemoved",value:function(e){this.statuses.has(e.field)&&this.statuses.delete(e.field),this.opts.onStatusChanged(this.areFieldsValid())}},{key:"onElementValidating",value:function(e){this.statuses.set(e.field,"Validating"),this.opts.onStatusChanged(!1)}},{key:"onElementValidated",value:function(e){this.statuses.set(e.field,e.valid?"Valid":"Invalid"),e.valid?this.opts.onStatusChanged(this.areFieldsValid()):this.opts.onStatusChanged(!1)}},{key:"onElementNotValidated",value:function(e){this.statuses.set(e.field,"NotValidated"),this.opts.onStatusChanged(!1)}},{key:"onElementIgnored",value:function(e){this.statuses.set(e.field,"Ignored"),this.opts.onStatusChanged(this.areFieldsValid())}}]),t}();function Y(e,t){var a=[],n=[];Object.keys(t).forEach(function(e){e&&(t[e]?a.push(e):n.push(e))}),n.forEach(function(t){return function(e,t){t.split(" ").forEach(function(t){e.classList?e.classList.remove(t):e.className=e.className.replace(t,"")})}(e,t)}),a.forEach(function(t){return function(e,t){t.split(" ").forEach(function(t){e.classList?e.classList.add(t):" ".concat(e.className," ").indexOf(" ".concat(t," "))&&(e.className+=" ".concat(t))})}(e,t)})}function z(e,t){var a=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return a?a.call(e,t):[].slice.call(e.parentElement.querySelectorAll(t)).indexOf(e)>=0}function W(e,t){for(var a=e;a&&!z(a,t);)a=a.parentElement;return a}var _=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).messages=new Map,a.defaultContainer=document.createElement("div"),a.opts=Object.assign({},{container:function(e,t){return a.defaultContainer}},e),a.elementIgnoredHandler=a.onElementIgnored.bind(v(a)),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a.fieldRemovedHandler=a.onFieldRemoved.bind(v(a)),a.validatorValidatedHandler=a.onValidatorValidated.bind(v(a)),a.validatorNotValidatedHandler=a.onValidatorNotValidated.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.getFormElement().appendChild(this.defaultContainer),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.validator.notvalidated",this.validatorNotValidatedHandler)}},{key:"uninstall",value:function(){this.core.getFormElement().removeChild(this.defaultContainer),this.messages.forEach(function(e){return e.parentNode.removeChild(e)}),this.messages.clear(),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.validator.notvalidated",this.validatorNotValidatedHandler)}},{key:"onFieldAdded",value:function(e){var t=this,a=e.elements;a&&(a.forEach(function(e){var a=t.messages.get(e);a&&(a.parentNode.removeChild(a),t.messages.delete(e))}),this.prepareFieldContainer(e.field,a))}},{key:"onFieldRemoved",value:function(e){var t=this;if(e.elements.length&&e.field){var a=e.elements[0].getAttribute("type");("radio"===a||"checkbox"===a?[e.elements[0]]:e.elements).forEach(function(e){if(t.messages.has(e)){var a=t.messages.get(e);a.parentNode.removeChild(a),t.messages.delete(e)}})}}},{key:"prepareFieldContainer",value:function(e,t){var a=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0],t):t.forEach(function(n){return a.prepareElementContainer(e,n,t)})}}},{key:"prepareElementContainer",value:function(e,t,a){var n;switch(!0){case"string"==typeof this.opts.container:var r=this.opts.container;r="#"===r.charAt(0)?'[id="'.concat(r.substring(1),'"]'):r,n=this.core.getFormElement().querySelector(r);break;default:n=this.opts.container(e,t)}var i=document.createElement("div");n.appendChild(i),Y(i,{"fv-plugins-message-container":!0}),this.core.emit("plugins.message.placed",{element:t,elements:a,field:e,messageElement:i}),this.messages.set(t,i)}},{key:"getMessage",value:function(e){return"string"==typeof e.message?e.message:e.message[this.core.getLocale()]}},{key:"onValidatorValidated",value:function(e){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;if(this.messages.has(n)){var r=this.messages.get(n),i=r.querySelector('[data-field="'.concat(e.field,'"][data-validator="').concat(e.validator,'"]'));if(i||e.result.valid)i&&!e.result.valid?(i.innerHTML=this.getMessage(e.result),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:i,meta:e.result.meta,validator:e.validator})):i&&e.result.valid&&r.removeChild(i);else{var s=document.createElement("div");s.innerHTML=this.getMessage(e.result),s.setAttribute("data-field",e.field),s.setAttribute("data-validator",e.validator),this.opts.clazz&&Y(s,d({},this.opts.clazz,!0)),r.appendChild(s),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:s,meta:e.result.meta,validator:e.validator})}}}},{key:"onValidatorNotValidated",value:function(e){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;if(this.messages.has(n)){var r=this.messages.get(n),i=r.querySelector('[data-field="'.concat(e.field,'"][data-validator="').concat(e.validator,'"]'));i&&r.removeChild(i)}}},{key:"onElementIgnored",value:function(e){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;if(this.messages.has(n)){var r=this.messages.get(n);[].slice.call(r.querySelectorAll('[data-field="'.concat(e.field,'"]'))).forEach(function(e){r.removeChild(e)})}}}],[{key:"getClosestContainer",value:function(e,t,a){for(var n=e;n&&n!==t&&(n=n.parentElement,!a.test(n.className)););return n}}]),t}(),J=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).results=new Map,a.containers=new Map,a.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},e),a.elementIgnoredHandler=a.onElementIgnored.bind(v(a)),a.elementValidatingHandler=a.onElementValidating.bind(v(a)),a.elementValidatedHandler=a.onElementValidated.bind(v(a)),a.elementNotValidatedHandler=a.onElementNotValidated.bind(v(a)),a.iconPlacedHandler=a.onIconPlaced.bind(v(a)),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a.fieldRemovedHandler=a.onFieldRemoved.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){var e,t=this;Y(this.core.getFormElement(),(d(e={},this.opts.formClass,!0),d(e,"fv-plugins-framework",!0),e)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&this.core.registerPlugin("___frameworkMessage",new _({clazz:this.opts.messageClass,container:function(e,a){var n=W(a,"string"==typeof t.opts.rowSelector?t.opts.rowSelector:t.opts.rowSelector(e,a));return _.getClosestContainer(a,n,t.opts.rowPattern)}}))}},{key:"uninstall",value:function(){var e;this.results.clear(),this.containers.clear(),Y(this.core.getFormElement(),(d(e={},this.opts.formClass,!1),d(e,"fv-plugins-framework",!1),e)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)}},{key:"onIconPlaced",value:function(e){}},{key:"onFieldAdded",value:function(e){var t=this,a=e.elements;a&&(a.forEach(function(e){var a,n=t.containers.get(e);n&&(Y(n,(d(a={},t.opts.rowInvalidClass,!1),d(a,t.opts.rowValidatingClass,!1),d(a,t.opts.rowValidClass,!1),d(a,"fv-plugins-icon-container",!1),a)),t.containers.delete(e))}),this.prepareFieldContainer(e.field,a))}},{key:"onFieldRemoved",value:function(e){var t=this;e.elements.forEach(function(e){var a,n=t.containers.get(e);n&&Y(n,(d(a={},t.opts.rowInvalidClass,!1),d(a,t.opts.rowValidatingClass,!1),d(a,t.opts.rowValidClass,!1),a))})}},{key:"prepareFieldContainer",value:function(e,t){var a=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0]):t.forEach(function(t){return a.prepareElementContainer(e,t)})}}},{key:"prepareElementContainer",value:function(e,t){var a,n=W(t,"string"==typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t));n!==t&&(Y(n,(d(a={},this.opts.rowClasses,!0),d(a,"fv-plugins-icon-container",!0),a)),this.containers.set(t,n))}},{key:"onElementValidating",value:function(e){var t,a=e.elements,n=e.element.getAttribute("type"),r="radio"===n||"checkbox"===n?a[0]:e.element,i=this.containers.get(r);i&&Y(i,(d(t={},this.opts.rowInvalidClass,!1),d(t,this.opts.rowValidatingClass,!0),d(t,this.opts.rowValidClass,!1),t))}},{key:"onElementNotValidated",value:function(e){this.removeClasses(e.element,e.elements)}},{key:"onElementIgnored",value:function(e){this.removeClasses(e.element,e.elements)}},{key:"removeClasses",value:function(e,t){var a,n=e.getAttribute("type"),r="radio"===n||"checkbox"===n?t[0]:e;Y(r,(d(a={},this.opts.eleValidClass,!1),d(a,this.opts.eleInvalidClass,!1),a));var i,s=this.containers.get(r);s&&Y(s,(d(i={},this.opts.rowInvalidClass,!1),d(i,this.opts.rowValidatingClass,!1),d(i,this.opts.rowValidClass,!1),i))}},{key:"onElementValidated",value:function(e){var t,a=this,n=e.elements,r=e.element.getAttribute("type"),i="radio"===r||"checkbox"===r?n[0]:e.element;Y(i,(d(t={},this.opts.eleValidClass,e.valid),d(t,this.opts.eleInvalidClass,!e.valid),t));var s=this.containers.get(i);if(s)if(e.valid){this.results.delete(i);var l,o=!0;if(this.containers.forEach(function(e,t){e===s&&!1===a.results.get(t)&&(o=!1)}),o)Y(s,(d(l={},this.opts.rowInvalidClass,!1),d(l,this.opts.rowValidatingClass,!1),d(l,this.opts.rowValidClass,!0),l))}else{var c;this.results.set(i,!1),Y(s,(d(c={},this.opts.rowInvalidClass,!0),d(c,this.opts.rowValidatingClass,!1),d(c,this.opts.rowValidClass,!1),c))}}}]),t}(),X=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).icons=new Map,a.opts=Object.assign({},{invalid:"fv-plugins-icon--invalid",onPlaced:function(){},onSet:function(){},valid:"fv-plugins-icon--valid",validating:"fv-plugins-icon--validating"},e),a.elementValidatingHandler=a.onElementValidating.bind(v(a)),a.elementValidatedHandler=a.onElementValidated.bind(v(a)),a.elementNotValidatedHandler=a.onElementNotValidated.bind(v(a)),a.elementIgnoredHandler=a.onElementIgnored.bind(v(a)),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler)}},{key:"uninstall",value:function(){this.icons.forEach(function(e){return e.parentNode.removeChild(e)}),this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler)}},{key:"onFieldAdded",value:function(e){var t=this,a=e.elements;a&&(a.forEach(function(e){var a=t.icons.get(e);a&&(a.parentNode.removeChild(a),t.icons.delete(e))}),this.prepareFieldIcon(e.field,a))}},{key:"prepareFieldIcon",value:function(e,t){var a=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementIcon(e,t[0]):t.forEach(function(t){return a.prepareElementIcon(e,t)})}}},{key:"prepareElementIcon",value:function(e,t){var a=document.createElement("i");a.setAttribute("data-field",e),t.parentNode.insertBefore(a,t.nextSibling),Y(a,{"fv-plugins-icon":!0});var n={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:a};this.core.emit("plugins.icon.placed",n),this.opts.onPlaced(n),this.icons.set(t,a)}},{key:"onElementValidating",value:function(e){var t,a=this.setClasses(e.field,e.element,e.elements,(d(t={},this.opts.invalid,!1),d(t,this.opts.valid,!1),d(t,this.opts.validating,!0),t)),n={element:e.element,field:e.field,iconElement:a,status:"Validating"};this.core.emit("plugins.icon.set",n),this.opts.onSet(n)}},{key:"onElementValidated",value:function(e){var t,a=this.setClasses(e.field,e.element,e.elements,(d(t={},this.opts.invalid,!e.valid),d(t,this.opts.valid,e.valid),d(t,this.opts.validating,!1),t)),n={element:e.element,field:e.field,iconElement:a,status:e.valid?"Valid":"Invalid"};this.core.emit("plugins.icon.set",n),this.opts.onSet(n)}},{key:"onElementNotValidated",value:function(e){var t,a=this.setClasses(e.field,e.element,e.elements,(d(t={},this.opts.invalid,!1),d(t,this.opts.valid,!1),d(t,this.opts.validating,!1),t)),n={element:e.element,field:e.field,iconElement:a,status:"NotValidated"};this.core.emit("plugins.icon.set",n),this.opts.onSet(n)}},{key:"onElementIgnored",value:function(e){var t,a=this.setClasses(e.field,e.element,e.elements,(d(t={},this.opts.invalid,!1),d(t,this.opts.valid,!1),d(t,this.opts.validating,!1),t)),n={element:e.element,field:e.field,iconElement:a,status:"Ignored"};this.core.emit("plugins.icon.set",n),this.opts.onSet(n)}},{key:"setClasses",value:function(e,t,a,n){var r=t.getAttribute("type"),i="radio"===r||"checkbox"===r?a[0]:t;if(this.icons.has(i)){var s=this.icons.get(i);return Y(s,n),s}return null}}]),t}(),q=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).invalidFields=new Map,a.opts=Object.assign({},{enabled:!0},e),a.validatorHandler=a.onValidatorValidated.bind(v(a)),a.shouldValidateFilter=a.shouldValidate.bind(v(a)),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a.elementNotValidatedHandler=a.onElementNotValidated.bind(v(a)),a.elementValidatingHandler=a.onElementValidating.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter)}},{key:"uninstall",value:function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter)}},{key:"shouldValidate",value:function(e,t,a,n){return!((!0===this.opts.enabled||!0===this.opts.enabled[e])&&this.invalidFields.has(t)&&!!this.invalidFields.get(t).length&&-1===this.invalidFields.get(t).indexOf(n))}},{key:"onValidatorValidated",value:function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],a=t.indexOf(e.validator);e.result.valid&&a>=0?t.splice(a,1):!e.result.valid&&-1===a&&t.push(e.validator),this.invalidFields.set(e.element,t)}},{key:"onFieldAdded",value:function(e){e.elements&&this.clearInvalidFields(e.elements)}},{key:"onElementNotValidated",value:function(e){this.clearInvalidFields(e.elements)}},{key:"onElementValidating",value:function(e){this.clearInvalidFields(e.elements)}},{key:"clearInvalidFields",value:function(e){var t=this;e.forEach(function(e){return t.invalidFields.delete(e)})}}]),t}(),Q=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).isFormValid=!1,a.opts=Object.assign({},{aspNetButton:!1,selector:'[type="submit"]:not([formnovalidate])'},e),a.submitHandler=a.handleSubmitEvent.bind(v(a)),a.buttonClickHandler=a.handleClickEvent.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){var e=this;if(this.core.getFormElement()instanceof HTMLFormElement){var t=this.core.getFormElement();this.selectorButtons=[].slice.call(t.querySelectorAll(this.opts.selector)),this.submitButtons=[].slice.call(t.querySelectorAll('[type="submit"]')),t.setAttribute("novalidate","novalidate"),t.addEventListener("submit",this.submitHandler),this.hiddenClickedEle=document.createElement("input"),this.hiddenClickedEle.setAttribute("type","hidden"),t.appendChild(this.hiddenClickedEle),this.submitButtons.forEach(function(t){t.addEventListener("click",e.buttonClickHandler)})}}},{key:"uninstall",value:function(){var e=this,t=this.core.getFormElement();t instanceof HTMLFormElement&&t.removeEventListener("submit",this.submitHandler),this.submitButtons.forEach(function(t){t.removeEventListener("click",e.buttonClickHandler)}),this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle)}},{key:"handleSubmitEvent",value:function(e){this.validateForm(e)}},{key:"handleClickEvent",value:function(e){var t=e.currentTarget;if(t instanceof HTMLElement&&-1!==this.selectorButtons.indexOf(t)&&(!this.opts.aspNetButton||!0!==this.isFormValid)){this.core.getFormElement().removeEventListener("submit",this.submitHandler),this.clickedButton=e.target;var a=this.clickedButton.getAttribute("name"),n=this.clickedButton.getAttribute("value");a&&n&&(this.hiddenClickedEle.setAttribute("name",a),this.hiddenClickedEle.setAttribute("value",n)),this.validateForm(e)}}},{key:"validateForm",value:function(e){var t=this;e.preventDefault(),this.core.validate().then(function(e){"Valid"===e&&t.opts.aspNetButton&&!t.isFormValid&&t.clickedButton&&(t.isFormValid=!0,t.clickedButton.removeEventListener("click",t.buttonClickHandler),t.clickedButton.click())})}}]),t}(),ee=function(e){function t(e){var a;return s(this,t),(a=h(this,u(t).call(this,e))).messages=new Map,a.opts=Object.assign({},{placement:"top",trigger:"click"},e),a.iconPlacedHandler=a.onIconPlaced.bind(v(a)),a.validatorValidatedHandler=a.onValidatorValidated.bind(v(a)),a.elementValidatedHandler=a.onElementValidated.bind(v(a)),a.documentClickHandler=a.onDocumentClicked.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.tip=document.createElement("div"),Y(this.tip,d({"fv-plugins-tooltip":!0},"fv-plugins-tooltip--".concat(this.opts.placement),!0)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.addEventListener("click",this.documentClickHandler)}},{key:"uninstall",value:function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.removeEventListener("click",this.documentClickHandler)}},{key:"onIconPlaced",value:function(e){var t=this;switch(Y(e.iconElement,{"fv-plugins-tooltip-icon":!0}),this.opts.trigger){case"hover":e.iconElement.addEventListener("mouseenter",function(a){return t.show(e.element,a)}),e.iconElement.addEventListener("mouseleave",function(e){return t.hide()});break;case"click":default:e.iconElement.addEventListener("click",function(a){return t.show(e.element,a)})}}},{key:"onValidatorValidated",value:function(e){if(!e.result.valid){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element,r="string"==typeof e.result.message?e.result.message:e.result.message[this.core.getLocale()];this.messages.set(n,r)}}},{key:"onElementValidated",value:function(e){if(e.valid){var t=e.elements,a=e.element.getAttribute("type"),n="radio"===a||"checkbox"===a?t[0]:e.element;this.messages.delete(n)}}},{key:"onDocumentClicked",value:function(e){this.hide()}},{key:"show",value:function(e,t){if(t.preventDefault(),t.stopPropagation(),this.messages.has(e)){Y(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML='<span class="fv-plugins-tooltip__content">'.concat(this.messages.get(e),"</span>");var a=t.target.getBoundingClientRect(),n=0,r=0;switch(this.opts.placement){case"top":default:n=a.top-a.height,r=a.left+a.width/2-this.tip.clientWidth/2;break;case"top-left":n=a.top-a.height,r=a.left;break;case"top-right":n=a.top-a.height,r=a.left+a.width-this.tip.clientWidth;break;case"bottom":n=a.top+a.height,r=a.left+a.width/2-this.tip.clientWidth/2;break;case"bottom-left":n=a.top+a.height,r=a.left;break;case"bottom-right":n=a.top+a.height,r=a.left+a.width-this.tip.clientWidth;break;case"left":n=a.top+a.height/2-this.tip.clientHeight/2,r=a.left-this.tip.clientWidth;break;case"right":n=a.top+a.height/2-this.tip.clientHeight/2,r=a.left+a.width}n+=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,r+=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(n,"px; left: ").concat(r,"px"))}}},{key:"hide",value:function(){Y(this.tip,{"fv-plugins-tooltip--hide":!0})}}]),t}(),te=function(e){function t(e){var a;s(this,t),(a=h(this,u(t).call(this,e))).handlers=[],a.timers=new Map,a.ieVersion=function(){for(var e=3,t=document.createElement("div"),a=t.all||[];t.innerHTML="\x3c!--[if gt IE "+ ++e+"]><br><![endif]--\x3e",a[0];);return e>4?e:document.documentMode}();var n=document.createElement("div");return a.defaultEvent=9!==a.ieVersion&&"oninput"in n?"input":"keyup",a.opts=Object.assign({},{delay:0,event:a.defaultEvent,threshold:0},e),a.fieldAddedHandler=a.onFieldAdded.bind(v(a)),a.fieldRemovedHandler=a.onFieldRemoved.bind(v(a)),a}return c(t,R),o(t,[{key:"install",value:function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)}},{key:"uninstall",value:function(){this.handlers.forEach(function(e){return e.element.removeEventListener(e.event,e.handler)}),this.handlers=[],this.timers.forEach(function(e){return window.clearTimeout(e)}),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)}},{key:"prepareHandler",value:function(e,t){var a=this;t.forEach(function(t){var n=[];switch(!0){case!!a.opts.event&&!1===a.opts.event[e]:n=[];break;case!!a.opts.event&&!!a.opts.event[e]:n=a.opts.event[e].split(" ");break;case"string"==typeof a.opts.event&&a.opts.event!==a.defaultEvent:n=a.opts.event.split(" ");break;default:var r=t.getAttribute("type"),i=t.tagName.toLowerCase();n=["radio"===r||"checkbox"===r||"file"===r||"select"===i?"change":a.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":a.defaultEvent]}n.forEach(function(n){var r=function(n){return a.handleEvent(n,e,t)};a.handlers.push({element:t,event:n,field:e,handler:r}),t.addEventListener(n,r)})})}},{key:"handleEvent",value:function(e,t,a){var n=this;if(this.exceedThreshold(t,a)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[t,a])){var r=function(){return n.core.validateElement(t,a).then(function(r){n.core.emit("plugins.trigger.executed",{element:a,event:e,field:t})})},i=this.opts.delay[t]||this.opts.delay;if(0===i)r();else{var s=this.timers.get(a);s&&window.clearTimeout(s),this.timers.set(a,window.setTimeout(r,1e3*i))}}}},{key:"onFieldAdded",value:function(e){this.handlers.filter(function(t){return t.field===e.field}).forEach(function(e){return e.element.removeEventListener(e.event,e.handler)}),this.prepareHandler(e.field,e.elements)}},{key:"onFieldRemoved",value:function(e){this.handlers.filter(function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0}).forEach(function(e){return e.element.removeEventListener(e.event,e.handler)})}},{key:"exceedThreshold",value:function(e,t){var a=0!==this.opts.threshold[e]&&0!==this.opts.threshold&&(this.opts.threshold[e]||this.opts.threshold);if(!a)return!0;var n=t.getAttribute("type");return-1!==["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(n)||this.core.getElementValue(e,t).length>=a}}]),t}(),ae={Alias:D,Aria:P,Declarative:Z,DefaultSubmit:U,Dependency:G,Excluded:j,FieldStatus:K,Framework:J,Icon:X,Message:_,Sequence:q,SubmitButton:Q,Tooltip:ee,Trigger:te};var ne={call:g,classSet:Y,closest:W,fetch:k,format:p,hasClass:function(e,t){return e.classList?e.classList.contains(t):new RegExp("(^| )".concat(t,"( |$)"),"gi").test(e.className)},isValidDate:A};e.Plugin=R,e.algorithms=i,e.filters=B,e.formValidation=function(e,t){var a=Object.assign({},{fields:{},locale:"en_US",plugins:{}},t),n=new L(e,a.fields);return n.setLocale(a.locale,a.localization),Object.keys(a.plugins).forEach(function(e){return n.registerPlugin(e,a.plugins[e])}),Object.keys(N).forEach(function(e){return n.registerValidator(e,N[e])}),Object.keys(a.fields).forEach(function(e){return n.addField(e,a.fields[e])}),n},e.locales={},e.plugins=ae,e.utils=ne,e.validators=N,Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e=e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Bootstrap=t())}(this,function(){"use strict";function e(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function t(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}function n(e){return(n=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function r(e,t){return(r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}var s=FormValidation.utils.classSet,l=FormValidation.utils.hasClass,o=FormValidation.plugins.Framework;return function(e){function r(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),i(this,n(r).call(this,Object.assign({},{eleInvalidClass:"is-invalid",eleValidClass:"is-valid",formClass:"fv-plugins-bootstrap",messageClass:"fv-help-block",rowInvalidClass:"has-danger",rowPattern:/^(.*)(col|offset)(-(sm|md|lg|xl))*-[0-9]+(.*)$/,rowSelector:".form-group",rowValidClass:"has-success"},e)))}return a(r,o),t(r,[{key:"onIconPlaced",value:function(e){var t=e.element.parentElement;l(t,"input-group")&&t.parentElement.insertBefore(e.iconElement,t.nextSibling);var a=e.element.getAttribute("type");if("checkbox"===a||"radio"===a){var n=t.parentElement;l(t,"form-check")?(s(e.iconElement,{"fv-plugins-icon-check":!0}),t.parentElement.insertBefore(e.iconElement,t.nextSibling)):l(t.parentElement,"form-check")&&(s(e.iconElement,{"fv-plugins-icon-check":!0}),n.parentElement.insertBefore(e.iconElement,n.nextSibling))}}}]),r}()});

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sweetalert2=e()}(this,function(){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function c(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(){return(s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function d(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}function i(t,e,n){return(i=d()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);var i=new(Function.bind.apply(t,o));return n&&l(i,n.prototype),i}).apply(null,arguments)}function p(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function f(t,e,n){return(f="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var o=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=u(t)););return t}(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(n):i.value}})(t,e,n||t)}function m(e){return Object.keys(e).map(function(t){return e[t]})}function h(t){return Array.prototype.slice.call(t)}function g(t,e){var n;n='"'.concat(t,'" is deprecated and will be removed in the next major release. Please use "').concat(e,'" instead.'),-1===z.indexOf(n)&&(z.push(n),_(n))}function v(t){return t&&"function"==typeof t.toPromise}function b(t){return v(t)?t.toPromise():Promise.resolve(t)}function y(t){return t&&Promise.resolve(t)===t}function w(t){return t instanceof Element||"object"===r(e=t)&&e.jquery;var e}function t(t){var e={};for(var n in t)e[t[n]]="swal2-"+t[n];return e}function C(t){var e=Q();return e?e.querySelector(t):null}function e(t){return C(".".concat(t))}function n(){var t=$();return h(t.querySelectorAll(".".concat(Y.icon)))}function k(){var t=n().filter(function(t){return vt(t)});return t.length?t[0]:null}function x(){return e(Y.title)}function P(){return e(Y.content)}function A(){return e(Y.image)}function B(){return e(Y["progress-steps"])}function S(){return e(Y["validation-message"])}function E(){return C(".".concat(Y.actions," .").concat(Y.confirm))}function O(){return C(".".concat(Y.actions," .").concat(Y.cancel))}function T(){return e(Y.actions)}function L(){return e(Y.header)}function j(){return e(Y.footer)}function q(){return e(Y["timer-progress-bar"])}function I(){return e(Y.close)}function V(){var t=h($().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(t,e){return t=parseInt(t.getAttribute("tabindex")),(e=parseInt(e.getAttribute("tabindex")))<t?1:t<e?-1:0}),e=h($().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter(function(t){return"-1"!==t.getAttribute("tabindex")});return function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(t.concat(e)).filter(function(t){return vt(t)})}function M(){return!J()&&!document.body.classList.contains(Y["no-backdrop"])}function R(){return $().hasAttribute("data-loading")}function H(e,t){var n;e.textContent="",t&&(n=(new DOMParser).parseFromString(t,"text/html"),h(n.querySelector("head").childNodes).forEach(function(t){e.appendChild(t)}),h(n.querySelector("body").childNodes).forEach(function(t){e.appendChild(t)}))}function D(t,e){if(e){for(var n=e.split(/\s+/),o=0;o<n.length;o++)if(!t.classList.contains(n[o]))return;return 1}}function N(t,e,n){var o,i;if(i=e,h((o=t).classList).forEach(function(t){-1===m(Y).indexOf(t)&&-1===m(Z).indexOf(t)&&-1===m(i.showClass).indexOf(t)&&o.classList.remove(t)}),e.customClass&&e.customClass[n]){if("string"!=typeof e.customClass[n]&&!e.customClass[n].forEach)return _("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(r(e.customClass[n]),'"'));mt(t,e.customClass[n])}}var U="SweetAlert2:",_=function(t){console.warn("".concat(U," ").concat(t))},F=function(t){console.error("".concat(U," ").concat(t))},z=[],W=function(t){return"function"==typeof t?t():t},K=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Y=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","toast-column","show","hide","close","title","header","content","html-container","actions","confirm","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),Z=t(["success","warning","info","question","error"]),Q=function(){return document.body.querySelector(".".concat(Y.container))},$=function(){return e(Y.popup)},J=function(){return document.body.classList.contains(Y["toast-shown"])},X={previousBodyPadding:null};function G(t,e){if(!e)return null;switch(e){case"select":case"textarea":case"file":return gt(t,Y[e]);case"checkbox":return t.querySelector(".".concat(Y.checkbox," input"));case"radio":return t.querySelector(".".concat(Y.radio," input:checked"))||t.querySelector(".".concat(Y.radio," input:first-child"));case"range":return t.querySelector(".".concat(Y.range," input"));default:return gt(t,Y.input)}}function tt(t){var e;t.focus(),"file"!==t.type&&(e=t.value,t.value="",t.value=e)}function et(t,e,n){t&&e&&("string"==typeof e&&(e=e.split(/\s+/).filter(Boolean)),e.forEach(function(e){t.forEach?t.forEach(function(t){n?t.classList.add(e):t.classList.remove(e)}):n?t.classList.add(e):t.classList.remove(e)}))}function nt(t,e,n){n||0===parseInt(n)?t.style[e]="number"==typeof n?"".concat(n,"px"):n:t.style.removeProperty(e)}function ot(t,e){var n=1<arguments.length&&void 0!==e?e:"flex";t.style.opacity="",t.style.display=n}function it(t){t.style.opacity="",t.style.display="none"}function rt(t,e,n){e?ot(t,n):it(t)}function at(t){return!!(t.scrollHeight>t.clientHeight)}function ct(t){var e=window.getComputedStyle(t),n=parseFloat(e.getPropertyValue("animation-duration")||"0"),o=parseFloat(e.getPropertyValue("transition-duration")||"0");return 0<n||0<o}function st(t,e){var n=1<arguments.length&&void 0!==e&&e,o=q();vt(o)&&(n&&(o.style.transition="none",o.style.width="100%"),setTimeout(function(){o.style.transition="width ".concat(t/1e3,"s linear"),o.style.width="0%"},10))}function ut(){return"undefined"==typeof window||"undefined"==typeof document}function lt(t){sn.isVisible()&&ft!==t.target.value&&sn.resetValidationMessage(),ft=t.target.value}function dt(t,e){t instanceof HTMLElement?e.appendChild(t):"object"===r(t)?wt(t,e):t&&H(e,t)}function pt(t,e){var n=T(),o=E(),i=O();e.showConfirmButton||e.showCancelButton||it(n),N(n,e,"actions"),xt(o,"confirm",e),xt(i,"cancel",e),e.buttonsStyling?function(t,e,n){mt([t,e],Y.styled),n.confirmButtonColor&&(t.style.backgroundColor=n.confirmButtonColor);n.cancelButtonColor&&(e.style.backgroundColor=n.cancelButtonColor);{var o;R()||(o=window.getComputedStyle(t).getPropertyValue("background-color"),t.style.borderLeftColor=o,t.style.borderRightColor=o)}}(o,i,e):(ht([o,i],Y.styled),o.style.backgroundColor=o.style.borderLeftColor=o.style.borderRightColor="",i.style.backgroundColor=i.style.borderLeftColor=i.style.borderRightColor=""),e.reverseButtons&&o.parentNode.insertBefore(i,o)}var ft,mt=function(t,e){et(t,e,!0)},ht=function(t,e){et(t,e,!1)},gt=function(t,e){for(var n=0;n<t.childNodes.length;n++)if(D(t.childNodes[n],e))return t.childNodes[n]},vt=function(t){return!(!t||!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))},bt='\n <div aria-labelledby="'.concat(Y.title,'" aria-describedby="').concat(Y.content,'" class="').concat(Y.popup,'" tabindex="-1">\n   <div class="').concat(Y.header,'">\n     <ul class="').concat(Y["progress-steps"],'"></ul>\n     <div class="').concat(Y.icon," ").concat(Z.error,'"></div>\n     <div class="').concat(Y.icon," ").concat(Z.question,'"></div>\n     <div class="').concat(Y.icon," ").concat(Z.warning,'"></div>\n     <div class="').concat(Y.icon," ").concat(Z.info,'"></div>\n     <div class="').concat(Y.icon," ").concat(Z.success,'"></div>\n     <img class="').concat(Y.image,'" />\n     <h2 class="').concat(Y.title,'" id="').concat(Y.title,'"></h2>\n     <button type="button" class="').concat(Y.close,'"></button>\n   </div>\n   <div class="').concat(Y.content,'">\n     <div id="').concat(Y.content,'" class="').concat(Y["html-container"],'"></div>\n     <input class="').concat(Y.input,'" />\n     <input type="file" class="').concat(Y.file,'" />\n     <div class="').concat(Y.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(Y.select,'"></select>\n     <div class="').concat(Y.radio,'"></div>\n     <label for="').concat(Y.checkbox,'" class="').concat(Y.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(Y.label,'"></span>\n     </label>\n     <textarea class="').concat(Y.textarea,'"></textarea>\n     <div class="').concat(Y["validation-message"],'" id="').concat(Y["validation-message"],'"></div>\n   </div>\n   <div class="').concat(Y.actions,'">\n     <button type="button" class="').concat(Y.confirm,'">OK</button>\n     <button type="button" class="').concat(Y.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(Y.footer,'"></div>\n   <div class="').concat(Y["timer-progress-bar-container"],'">\n     <div class="').concat(Y["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),yt=function(t){var e,n,o,i,r,a,c,s,u,l,d,p,f,m,h,g=!!(e=Q())&&(e.parentNode.removeChild(e),ht([document.documentElement,document.body],[Y["no-backdrop"],Y["toast-shown"],Y["has-column"]]),!0);ut()?F("SweetAlert2 requires document to initialize"):((n=document.createElement("div")).className=Y.container,g&&mt(n,Y["no-transition"]),H(n,bt),(o="string"==typeof(i=t.target)?document.querySelector(i):i).appendChild(n),r=t,(a=$()).setAttribute("role",r.toast?"alert":"dialog"),a.setAttribute("aria-live",r.toast?"polite":"assertive"),r.toast||a.setAttribute("aria-modal","true"),c=o,"rtl"===window.getComputedStyle(c).direction&&mt(Q(),Y.rtl),s=P(),u=gt(s,Y.input),l=gt(s,Y.file),d=s.querySelector(".".concat(Y.range," input")),p=s.querySelector(".".concat(Y.range," output")),f=gt(s,Y.select),m=s.querySelector(".".concat(Y.checkbox," input")),h=gt(s,Y.textarea),u.oninput=lt,l.onchange=lt,f.onchange=lt,m.onchange=lt,h.oninput=lt,d.oninput=function(t){lt(t),p.value=d.value},d.onchange=function(t){lt(t),d.nextSibling.value=d.value})},wt=function(t,e){t.jquery?Ct(e,t):H(e,t.toString())},Ct=function(t,e){if(t.textContent="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0))},kt=function(){if(ut())return!1;var t=document.createElement("div"),e={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&void 0!==t.style[n])return e[n];return!1}();function xt(t,e,n){var o;rt(t,n["show".concat((o=e).charAt(0).toUpperCase()+o.slice(1),"Button")],"inline-block"),H(t,n["".concat(e,"ButtonText")]),t.setAttribute("aria-label",n["".concat(e,"ButtonAriaLabel")]),t.className=Y[e],N(t,n,"".concat(e,"Button")),mt(t,n["".concat(e,"ButtonClass")])}function Pt(t,e){var n,o,i,r,a,c,s,u,l=Q();l&&(n=l,"string"==typeof(o=e.backdrop)?n.style.background=o:o||mt([document.documentElement,document.body],Y["no-backdrop"]),!e.backdrop&&e.allowOutsideClick&&_('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),i=l,(r=e.position)in Y?mt(i,Y[r]):(_('The "position" parameter is not valid, defaulting to "center"'),mt(i,Y.center)),a=l,!(c=e.grow)||"string"!=typeof c||(s="grow-".concat(c))in Y&&mt(a,Y[s]),N(l,e,"container"),(u=document.body.getAttribute("data-swal2-queue-step"))&&(l.setAttribute("data-queue-step",u),document.body.removeAttribute("data-swal2-queue-step")))}function At(t,e){t.placeholder&&!e.inputPlaceholder||(t.placeholder=e.inputPlaceholder)}var Bt={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap},St=["input","file","range","select","radio","checkbox","textarea"],Et=function(t){if(!jt[t.input])return F('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(t.input,'"'));var e=Lt(t.input),n=jt[t.input](e,t);ot(n),setTimeout(function(){tt(n)})},Ot=function(t,e){var n=G(P(),t);if(n)for(var o in!function(t){for(var e=0;e<t.attributes.length;e++){var n=t.attributes[e].name;-1===["type","value","style"].indexOf(n)&&t.removeAttribute(n)}}(n),e)"range"===t&&"placeholder"===o||n.setAttribute(o,e[o])},Tt=function(t){var e=Lt(t.input);t.customClass&&mt(e,t.customClass.input)},Lt=function(t){var e=Y[t]?Y[t]:Y.input;return gt(P(),e)},jt={};jt.text=jt.email=jt.password=jt.number=jt.tel=jt.url=function(t,e){return"string"==typeof e.inputValue||"number"==typeof e.inputValue?t.value=e.inputValue:y(e.inputValue)||_('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(r(e.inputValue),'"')),At(t,e),t.type=e.input,t},jt.file=function(t,e){return At(t,e),t},jt.range=function(t,e){var n=t.querySelector("input"),o=t.querySelector("output");return n.value=e.inputValue,n.type=e.input,o.value=e.inputValue,t},jt.select=function(t,e){var n;return t.textContent="",e.inputPlaceholder&&(n=document.createElement("option"),H(n,e.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,t.appendChild(n)),t},jt.radio=function(t){return t.textContent="",t},jt.checkbox=function(t,e){var n=G(P(),"checkbox");n.value=1,n.id=Y.checkbox,n.checked=Boolean(e.inputValue);var o=t.querySelector("span");return H(o,e.inputPlaceholder),t},jt.textarea=function(e,t){var n,o;return e.value=t.inputValue,At(e,t),"MutationObserver"in window&&(n=parseInt(window.getComputedStyle($()).width),o=parseInt(window.getComputedStyle($()).paddingLeft)+parseInt(window.getComputedStyle($()).paddingRight),new MutationObserver(function(){var t=e.offsetWidth+o;$().style.width=n<t?"".concat(t,"px"):null}).observe(e,{attributes:!0,attributeFilter:["style"]})),e};function qt(t,e){var n,o,i,r,a,c=P().querySelector("#".concat(Y.content));e.html?(dt(e.html,c),ot(c,"block")):e.text?(c.textContent=e.text,ot(c,"block")):it(c),n=t,o=e,i=P(),r=Bt.innerParams.get(n),a=!r||o.input!==r.input,St.forEach(function(t){var e=Y[t],n=gt(i,e);Ot(t,o.inputAttributes),n.className=e,a&&it(n)}),o.input&&(a&&Et(o),Tt(o)),N(P(),e,"content")}function It(){return Q()&&Q().getAttribute("data-queue-step")}function Vt(t,s){var u=B();if(!s.progressSteps||0===s.progressSteps.length)return it(u),0;ot(u),u.textContent="";var l=parseInt(void 0===s.currentProgressStep?It():s.currentProgressStep);l>=s.progressSteps.length&&_("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),s.progressSteps.forEach(function(t,e){var n,o,i,r,a,c=(n=t,o=document.createElement("li"),mt(o,Y["progress-step"]),H(o,n),o);u.appendChild(c),e===l&&mt(c,Y["active-progress-step"]),e!==s.progressSteps.length-1&&(r=s,a=document.createElement("li"),mt(a,Y["progress-step-line"]),r.progressStepsDistance&&(a.style.width=r.progressStepsDistance),i=a,u.appendChild(i))})}function Mt(t,e){var n,o,i,r,a,c,s,u,l=L();N(l,e,"header"),Vt(0,e),n=t,o=e,(r=Bt.innerParams.get(n))&&o.icon===r.icon&&k()?N(k(),o,"icon"):(Dt(),o.icon&&(-1!==Object.keys(Z).indexOf(o.icon)?(i=C(".".concat(Y.icon,".").concat(Z[o.icon])),ot(i),Ut(i,o),Nt(),N(i,o,"icon"),mt(i,o.showClass.icon)):F('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(o.icon,'"')))),function(t){var e=A();if(!t.imageUrl)return it(e);ot(e,""),e.setAttribute("src",t.imageUrl),e.setAttribute("alt",t.imageAlt),nt(e,"width",t.imageWidth),nt(e,"height",t.imageHeight),e.className=Y.image,N(e,t,"image")}(e),a=e,c=x(),rt(c,a.title||a.titleText),a.title&&dt(a.title,c),a.titleText&&(c.innerText=a.titleText),N(c,a,"title"),s=e,u=I(),H(u,s.closeButtonHtml),N(u,s,"closeButton"),rt(u,s.showCloseButton),u.setAttribute("aria-label",s.closeButtonAriaLabel)}function Rt(t,e){var n,o,i,r;n=e,o=$(),nt(o,"width",n.width),nt(o,"padding",n.padding),n.background&&(o.style.background=n.background),zt(o,n),Pt(0,e),Mt(t,e),qt(t,e),pt(0,e),i=e,r=j(),rt(r,i.footer),i.footer&&dt(i.footer,r),N(r,i,"footer"),"function"==typeof e.onRender&&e.onRender($())}function Ht(){return E()&&E().click()}var Dt=function(){for(var t=n(),e=0;e<t.length;e++)it(t[e])},Nt=function(){for(var t=$(),e=window.getComputedStyle(t).getPropertyValue("background-color"),n=t.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=e},Ut=function(t,e){t.textContent="",e.iconHtml?H(t,_t(e.iconHtml)):"success"===e.icon?H(t,'\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    '):"error"===e.icon?H(t,'\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    '):H(t,_t({question:"?",warning:"!",info:"i"}[e.icon]))},_t=function(t){return'<div class="'.concat(Y["icon-content"],'">').concat(t,"</div>")},Ft=[],zt=function(t,e){t.className="".concat(Y.popup," ").concat(vt(t)?e.showClass.popup:""),e.toast?(mt([document.documentElement,document.body],Y["toast-shown"]),mt(t,Y.toast)):mt(t,Y.modal),N(t,e,"popup"),"string"==typeof e.customClass&&mt(t,e.customClass),e.icon&&mt(t,Y["icon-".concat(e.icon)])};function Wt(){var t=$();t||sn.fire(),t=$();var e=T(),n=E();ot(e),ot(n,"inline-block"),mt([t,e],Y.loading),n.disabled=!0,t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()}function Kt(){return new Promise(function(t){var e=window.scrollX,n=window.scrollY;Xt.restoreFocusTimeout=setTimeout(function(){Xt.previousActiveElement&&Xt.previousActiveElement.focus?(Xt.previousActiveElement.focus(),Xt.previousActiveElement=null):document.body&&document.body.focus(),t()},100),void 0!==e&&void 0!==n&&window.scrollTo(e,n)})}function Yt(){if(Xt.timeout)return function(){var t=q(),e=parseInt(window.getComputedStyle(t).width);t.style.removeProperty("transition"),t.style.width="100%";var n=parseInt(window.getComputedStyle(t).width),o=parseInt(e/n*100);t.style.removeProperty("transition"),t.style.width="".concat(o,"%")}(),Xt.timeout.stop()}function Zt(){if(Xt.timeout){var t=Xt.timeout.start();return st(t),t}}function Qt(t){return Object.prototype.hasOwnProperty.call(Gt,t)}function $t(t){return ee[t]}function Jt(t){for(var e in t)Qt(i=e)||_('Unknown parameter "'.concat(i,'"')),t.toast&&(o=e,-1!==ne.indexOf(o)&&_('The parameter "'.concat(o,'" is incompatible with toasts'))),$t(n=e)&&g(n,$t(n));var n,o,i}var Xt={},Gt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconHtml:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:void 0,target:"body",backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,onBeforeOpen:void 0,onOpen:void 0,onRender:void 0,onClose:void 0,onAfterClose:void 0,onDestroy:void 0,scrollbarPadding:!0},te=["allowEscapeKey","allowOutsideClick","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","footer","hideClass","html","icon","imageAlt","imageHeight","imageUrl","imageWidth","onAfterClose","onClose","onDestroy","progressSteps","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","text","title","titleText"],ee={animation:'showClass" and "hideClass'},ne=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],oe=Object.freeze({isValidParameter:Qt,isUpdatableParameter:function(t){return-1!==te.indexOf(t)},isDeprecatedParameter:$t,argsToParams:function(o){var i={};return"object"!==r(o[0])||w(o[0])?["title","html","icon"].forEach(function(t,e){var n=o[e];"string"==typeof n||w(n)?i[t]=n:void 0!==n&&F("Unexpected type of ".concat(t,'! Expected "string" or "Element", got ').concat(r(n)))}):s(i,o[0]),i},isVisible:function(){return vt($())},clickConfirm:Ht,clickCancel:function(){return O()&&O().click()},getContainer:Q,getPopup:$,getTitle:x,getContent:P,getHtmlContainer:function(){return e(Y["html-container"])},getImage:A,getIcon:k,getIcons:n,getCloseButton:I,getActions:T,getConfirmButton:E,getCancelButton:O,getHeader:L,getFooter:j,getTimerProgressBar:q,getFocusableElements:V,getValidationMessage:S,isLoading:R,fire:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return i(this,e)},mixin:function(r){return function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&l(t,e)}(i,t);var n,o,e=(n=i,o=d(),function(){var t,e=u(n);return p(this,o?(t=u(this).constructor,Reflect.construct(e,arguments,t)):e.apply(this,arguments))});function i(){return a(this,i),e.apply(this,arguments)}return c(i,[{key:"_main",value:function(t){return f(u(i.prototype),"_main",this).call(this,s({},r,t))}}]),i}(this)},queue:function(t){var r=this;Ft=t;function a(t,e){Ft=[],t(e)}var c=[];return new Promise(function(i){!function e(n,o){n<Ft.length?(document.body.setAttribute("data-swal2-queue-step",n),r.fire(Ft[n]).then(function(t){void 0!==t.value?(c.push(t.value),e(n+1,o)):a(i,{dismiss:t.dismiss})})):a(i,{value:c})}(0)})},getQueueStep:It,insertQueueStep:function(t,e){return e&&e<Ft.length?Ft.splice(e,0,t):Ft.push(t)},deleteQueueStep:function(t){void 0!==Ft[t]&&Ft.splice(t,1)},showLoading:Wt,enableLoading:Wt,getTimerLeft:function(){return Xt.timeout&&Xt.timeout.getTimerLeft()},stopTimer:Yt,resumeTimer:Zt,toggleTimer:function(){var t=Xt.timeout;return t&&(t.running?Yt:Zt)()},increaseTimer:function(t){if(Xt.timeout){var e=Xt.timeout.increase(t);return st(e,!0),e}},isTimerRunning:function(){return Xt.timeout&&Xt.timeout.isRunning()}});function ie(){var t,e=Bt.innerParams.get(this);e&&(t=Bt.domCache.get(this),e.showConfirmButton||(it(t.confirmButton),e.showCancelButton||it(t.actions)),ht([t.popup,t.actions],Y.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.cancelButton.disabled=!1)}function re(){null===X.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(X.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(X.previousBodyPadding+function(){var t=document.createElement("div");t.className=Y["scrollbar-measure"],document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e}(),"px"))}function ae(){return!!window.MSInputMethodContext&&!!document.documentMode}function ce(){var t=Q(),e=$();t.style.removeProperty("align-items"),e.offsetTop<0&&(t.style.alignItems="flex-start")}var se=function(){navigator.userAgent.match(/(CriOS|FxiOS|EdgiOS|YaBrowser|UCBrowser)/i)||$().scrollHeight>window.innerHeight-44&&(Q().style.paddingBottom="".concat(44,"px"))},ue=function(){var e,t=Q();t.ontouchstart=function(t){e=le(t.target)},t.ontouchmove=function(t){e&&(t.preventDefault(),t.stopPropagation())}},le=function(t){var e=Q();return t===e||!(at(e)||"INPUT"===t.tagName||at(P())&&P().contains(t))},de={swalPromiseResolve:new WeakMap};function pe(t,e,n,o){var i;n?he(t,o):(Kt().then(function(){return he(t,o)}),Xt.keydownTarget.removeEventListener("keydown",Xt.keydownHandler,{capture:Xt.keydownListenerCapture}),Xt.keydownHandlerAdded=!1),e.parentNode&&!document.body.getAttribute("data-swal2-queue-step")&&e.parentNode.removeChild(e),M()&&(null!==X.previousBodyPadding&&(document.body.style.paddingRight="".concat(X.previousBodyPadding,"px"),X.previousBodyPadding=null),D(document.body,Y.iosfix)&&(i=parseInt(document.body.style.top,10),ht(document.body,Y.iosfix),document.body.style.top="",document.body.scrollTop=-1*i),"undefined"!=typeof window&&ae()&&window.removeEventListener("resize",ce),h(document.body.children).forEach(function(t){t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})),ht([document.documentElement,document.body],[Y.shown,Y["height-auto"],Y["no-backdrop"],Y["toast-shown"],Y["toast-column"]])}function fe(t){var e,n,o,i=$();i&&(e=Bt.innerParams.get(this))&&!D(i,e.hideClass.popup)&&(n=de.swalPromiseResolve.get(this),ht(i,e.showClass.popup),mt(i,e.hideClass.popup),o=Q(),ht(o,e.showClass.backdrop),mt(o,e.hideClass.backdrop),function(t,e,n){var o=Q(),i=kt&&ct(e),r=n.onClose,a=n.onAfterClose;if(r!==null&&typeof r==="function"){r(e)}if(i){me(t,e,o,a)}else{pe(t,o,J(),a)}}(this,i,e),void 0!==t?(t.isDismissed=void 0!==t.dismiss,t.isConfirmed=void 0===t.dismiss):t={isDismissed:!0,isConfirmed:!1},n(t||{}))}var me=function(t,e,n,o){Xt.swalCloseEventFinishedCallback=pe.bind(null,t,n,J(),o),e.addEventListener(kt,function(t){t.target===e&&(Xt.swalCloseEventFinishedCallback(),delete Xt.swalCloseEventFinishedCallback)})},he=function(t,e){setTimeout(function(){"function"==typeof e&&e(),t._destroy()})};function ge(t,e,n){var o=Bt.domCache.get(t);e.forEach(function(t){o[t].disabled=n})}function ve(t,e){if(!t)return!1;if("radio"===t.type)for(var n=t.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=e;else t.disabled=e}var be=function(){function n(t,e){a(this,n),this.callback=t,this.remaining=e,this.running=!1,this.start()}return c(n,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date-this.started),this.remaining}},{key:"increase",value:function(t){var e=this.running;return e&&this.stop(),this.remaining+=t,e&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),n}(),ye={email:function(t,e){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid email address")},url:function(t,e){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid URL")}};function we(t){var e,n;(e=t).inputValidator||Object.keys(ye).forEach(function(t){e.input===t&&(e.inputValidator=ye[t])}),t.showLoaderOnConfirm&&!t.preConfirm&&_("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),t.animation=W(t.animation),(n=t).target&&("string"!=typeof n.target||document.querySelector(n.target))&&("string"==typeof n.target||n.target.appendChild)||(_('Target parameter is not valid, defaulting to "body"'),n.target="body"),"string"==typeof t.title&&(t.title=t.title.split("\n").join("<br />")),yt(t)}function Ce(t){var e=Q(),n=$();"function"==typeof t.onBeforeOpen&&t.onBeforeOpen(n);var o=window.getComputedStyle(document.body).overflowY;je(e,n,t),Te(e,n),M()&&(Le(e,t.scrollbarPadding,o),h(document.body.children).forEach(function(t){t===Q()||function(t,e){if("function"==typeof t.contains)return t.contains(e)}(t,Q())||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")),t.setAttribute("aria-hidden","true"))})),J()||Xt.previousActiveElement||(Xt.previousActiveElement=document.activeElement),"function"==typeof t.onOpen&&setTimeout(function(){return t.onOpen(n)}),ht(e,Y["no-transition"])}function ke(t){var e,n=$();t.target===n&&(e=Q(),n.removeEventListener(kt,ke),e.style.overflowY="auto")}function xe(t,e){"select"===e.input||"radio"===e.input?Me(t,e):-1!==["text","email","number","tel","textarea"].indexOf(e.input)&&(v(e.inputValue)||y(e.inputValue))&&Re(t,e)}function Pe(t,e){t.disableButtons(),e.input?Ne(t,e):Ue(t,e,!0)}function Ae(t,e){t.disableButtons(),e(K.cancel)}function Be(t,e){t.closePopup({value:e})}function Se(e,t,n,o){t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=function(t){return ze(e,t,o)},t.keydownTarget=n.keydownListenerCapture?window:$(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)}function Ee(t,e,n){var o=V(),i=0;if(i<o.length)return(e+=n)===o.length?e=0:-1===e&&(e=o.length-1),o[e].focus();$().focus()}function Oe(t,e,n){Bt.innerParams.get(t).toast?Qe(t,e,n):(Je(e),Xe(e),Ge(t,e,n))}var Te=function(t,e){kt&&ct(e)?(t.style.overflowY="hidden",e.addEventListener(kt,ke)):t.style.overflowY="auto"},Le=function(t,e,n){var o;(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints)&&!D(document.body,Y.iosfix)&&(o=document.body.scrollTop,document.body.style.top="".concat(-1*o,"px"),mt(document.body,Y.iosfix),ue(),se()),"undefined"!=typeof window&&ae()&&(ce(),window.addEventListener("resize",ce)),e&&"hidden"!==n&&re(),setTimeout(function(){t.scrollTop=0})},je=function(t,e,n){mt(t,n.showClass.backdrop),ot(e),mt(e,n.showClass.popup),mt([document.documentElement,document.body],Y.shown),n.heightAuto&&n.backdrop&&!n.toast&&mt([document.documentElement,document.body],Y["height-auto"])},qe=function(t){return t.checked?1:0},Ie=function(t){return t.checked?t.value:null},Ve=function(t){return t.files.length?null!==t.getAttribute("multiple")?t.files:t.files[0]:null},Me=function(e,n){function o(t){return He[n.input](i,De(t),n)}var i=P();v(n.inputOptions)||y(n.inputOptions)?(Wt(),b(n.inputOptions).then(function(t){e.hideLoading(),o(t)})):"object"===r(n.inputOptions)?o(n.inputOptions):F("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(r(n.inputOptions)))},Re=function(e,n){var o=e.getInput();it(o),b(n.inputValue).then(function(t){o.value="number"===n.input?parseFloat(t)||0:"".concat(t),ot(o),o.focus(),e.hideLoading()}).catch(function(t){F("Error in inputValue promise: ".concat(t)),o.value="",ot(o),o.focus(),e.hideLoading()})},He={select:function(t,e,i){function r(t,e,n){var o=document.createElement("option");o.value=n,H(o,e),i.inputValue.toString()===n.toString()&&(o.selected=!0),t.appendChild(o)}var a=gt(t,Y.select);e.forEach(function(t){var e,n=t[0],o=t[1];Array.isArray(o)?((e=document.createElement("optgroup")).label=n,e.disabled=!1,a.appendChild(e),o.forEach(function(t){return r(e,t[1],t[0])})):r(a,o,n)}),a.focus()},radio:function(t,e,a){var c=gt(t,Y.radio);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("input"),i=document.createElement("label");o.type="radio",o.name=Y.radio,o.value=e,a.inputValue.toString()===e.toString()&&(o.checked=!0);var r=document.createElement("span");H(r,n),r.className=Y.label,i.appendChild(o),i.appendChild(r),c.appendChild(i)});var n=c.querySelectorAll("input");n.length&&n[0].focus()}},De=function o(n){var i=[];return"undefined"!=typeof Map&&n instanceof Map?n.forEach(function(t,e){var n=t;"object"===r(n)&&(n=o(n)),i.push([e,n])}):Object.keys(n).forEach(function(t){var e=n[t];"object"===r(e)&&(e=o(e)),i.push([t,e])}),i},Ne=function(e,n){var o=function(t,e){var n=t.getInput();if(!n)return null;switch(e.input){case"checkbox":return qe(n);case"radio":return Ie(n);case"file":return Ve(n);default:return e.inputAutoTrim?n.value.trim():n.value}}(e,n);n.inputValidator?(e.disableInput(),Promise.resolve().then(function(){return b(n.inputValidator(o,n.validationMessage))}).then(function(t){e.enableButtons(),e.enableInput(),t?e.showValidationMessage(t):Ue(e,n,o)})):e.getInput().checkValidity()?Ue(e,n,o):(e.enableButtons(),e.showValidationMessage(n.validationMessage))},Ue=function(e,t,n){t.showLoaderOnConfirm&&Wt(),t.preConfirm?(e.resetValidationMessage(),Promise.resolve().then(function(){return b(t.preConfirm(n,t.validationMessage))}).then(function(t){vt(S())||!1===t?e.hideLoading():Be(e,void 0===t?n:t)})):Be(e,n)},_e=["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"],Fe=["Escape","Esc"],ze=function(t,e,n){var o=Bt.innerParams.get(t);o.stopKeydownPropagation&&e.stopPropagation(),"Enter"===e.key?We(t,e,o):"Tab"===e.key?Ke(e,o):-1!==_e.indexOf(e.key)?Ye():-1!==Fe.indexOf(e.key)&&Ze(e,o,n)},We=function(t,e,n){if(!e.isComposing&&e.target&&t.getInput()&&e.target.outerHTML===t.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(n.input))return;Ht(),e.preventDefault()}},Ke=function(t){for(var e=t.target,n=V(),o=-1,i=0;i<n.length;i++)if(e===n[i]){o=i;break}t.shiftKey?Ee(0,o,-1):Ee(0,o,1),t.stopPropagation(),t.preventDefault()},Ye=function(){var t=E(),e=O();document.activeElement===t&&vt(e)?e.focus():document.activeElement===e&&vt(t)&&t.focus()},Ze=function(t,e,n){W(e.allowEscapeKey)&&(t.preventDefault(),n(K.esc))},Qe=function(e,t,n){t.popup.onclick=function(){var t=Bt.innerParams.get(e);t.showConfirmButton||t.showCancelButton||t.showCloseButton||t.input||n(K.close)}},$e=!1,Je=function(e){e.popup.onmousedown=function(){e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&($e=!0)}}},Xe=function(e){e.container.onmousedown=function(){e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,t.target!==e.popup&&!e.popup.contains(t.target)||($e=!0)}}},Ge=function(n,o,i){o.container.onclick=function(t){var e=Bt.innerParams.get(n);$e?$e=!1:t.target===o.container&&W(e.allowOutsideClick)&&i(K.backdrop)}};var tn=function(t,e,n){var o=q();it(o),e.timer&&(t.timeout=new be(function(){n("timer"),delete t.timeout},e.timer),e.timerProgressBar&&(ot(o),setTimeout(function(){t.timeout.running&&st(e.timer)})))},en=function(t,e){if(!e.toast)return W(e.allowEnterKey)?e.focusCancel&&vt(t.cancelButton)?t.cancelButton.focus():e.focusConfirm&&vt(t.confirmButton)?t.confirmButton.focus():void Ee(0,-1,1):nn()},nn=function(){document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};var on,rn=function(t){for(var e in t)t[e]=new WeakMap},an=Object.freeze({hideLoading:ie,disableLoading:ie,getInput:function(t){var e=Bt.innerParams.get(t||this),n=Bt.domCache.get(t||this);return n?G(n.content,e.input):null},close:fe,closePopup:fe,closeModal:fe,closeToast:fe,enableButtons:function(){ge(this,["confirmButton","cancelButton"],!1)},disableButtons:function(){ge(this,["confirmButton","cancelButton"],!0)},enableInput:function(){return ve(this.getInput(),!1)},disableInput:function(){return ve(this.getInput(),!0)},showValidationMessage:function(t){var e=Bt.domCache.get(this);H(e.validationMessage,t);var n=window.getComputedStyle(e.popup);e.validationMessage.style.marginLeft="-".concat(n.getPropertyValue("padding-left")),e.validationMessage.style.marginRight="-".concat(n.getPropertyValue("padding-right")),ot(e.validationMessage);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",Y["validation-message"]),tt(o),mt(o,Y.inputerror))},resetValidationMessage:function(){var t=Bt.domCache.get(this);t.validationMessage&&it(t.validationMessage);var e=this.getInput();e&&(e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedBy"),ht(e,Y.inputerror))},getProgressSteps:function(){return Bt.domCache.get(this).progressSteps},_main:function(t){Jt(t),Xt.currentInstance&&Xt.currentInstance._destroy(),Xt.currentInstance=this;var e=function(t){var e=s({},Gt.showClass,t.showClass),n=s({},Gt.hideClass,t.hideClass),o=s({},Gt,t);if(o.showClass=e,o.hideClass=n,t.animation===false){o.showClass={popup:"swal2-noanimation",backdrop:"swal2-noanimation"};o.hideClass={}}return o}(t);we(e),Object.freeze(e),Xt.timeout&&(Xt.timeout.stop(),delete Xt.timeout),clearTimeout(Xt.restoreFocusTimeout);var n=function(t){var e={popup:$(),container:Q(),content:P(),actions:T(),confirmButton:E(),cancelButton:O(),closeButton:I(),validationMessage:S(),progressSteps:B()};return Bt.domCache.set(t,e),e}(this);return Rt(this,e),Bt.innerParams.set(this,e),function(n,o,i){return new Promise(function(t){var e=function t(e){n.closePopup({dismiss:e})};de.swalPromiseResolve.set(n,t);o.confirmButton.onclick=function(){return Pe(n,i)};o.cancelButton.onclick=function(){return Ae(n,e)};o.closeButton.onclick=function(){return e(K.close)};Oe(n,o,e);Se(n,Xt,i,e);if(i.toast&&(i.input||i.footer||i.showCloseButton)){mt(document.body,Y["toast-column"])}else{ht(document.body,Y["toast-column"])}xe(n,i);Ce(i);tn(Xt,i,e);en(o,i);setTimeout(function(){o.container.scrollTop=0})})}(this,n,e)},update:function(e){var t=$(),n=Bt.innerParams.get(this);if(!t||D(t,n.hideClass.popup))return _("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");var o={};Object.keys(e).forEach(function(t){sn.isUpdatableParameter(t)?o[t]=e[t]:_('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js'))});var i=s({},n,o);Rt(this,i),Bt.innerParams.set(this,i),Object.defineProperties(this,{params:{value:s({},this.params,e),writable:!1,enumerable:!0}})},_destroy:function(){var t=Bt.domCache.get(this),e=Bt.innerParams.get(this);e&&(t.popup&&Xt.swalCloseEventFinishedCallback&&(Xt.swalCloseEventFinishedCallback(),delete Xt.swalCloseEventFinishedCallback),Xt.deferDisposalTimer&&(clearTimeout(Xt.deferDisposalTimer),delete Xt.deferDisposalTimer),"function"==typeof e.onDestroy&&e.onDestroy(),delete this.params,delete Xt.keydownHandler,delete Xt.keydownTarget,rn(Bt),rn(de))}}),cn=function(){function r(){if(a(this,r),"undefined"!=typeof window){"undefined"==typeof Promise&&F("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),on=this;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(e));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});var i=this._main(this.params);Bt.promise.set(this,i)}}return c(r,[{key:"then",value:function(t){return Bt.promise.get(this).then(t)}},{key:"finally",value:function(t){return Bt.promise.get(this).finally(t)}}]),r}();s(cn.prototype,an),s(cn,oe),Object.keys(an).forEach(function(t){cn[t]=function(){if(on)return on[t].apply(on,arguments)}}),cn.DismissReason=K,cn.version="9.17.2";var sn=cn;return sn.default=sn}),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2);
"use strict";
// Set defaults
swal.mixin({
    width: 400,
    heightAuto: false,
    padding: '2.5rem',
    buttonsStyling: false,
    confirmButtonClass: 'btn btn-success',
    confirmButtonColor: null,
    cancelButtonClass: 'btn btn-secondary',
    cancelButtonColor: null
});
