/*!
 * jQuery blockUI plugin
 * Version 2.70.0-2014.11.23
 * Requires jQuery v1.7 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2013 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 * Thanks to <PERSON><PERSON><PERSON><PERSON><PERSON> for some excellent contributions!
 */

;(function() {
    /*jshint eqeqeq:false curly:false latedef:false */
    "use strict";

    function setup($) {
        $.fn._fadeIn = $.fn.fadeIn;

        var noOp = $.noop || function() {};

        // this bit is to ensure we don't call setExpression when we shouldn't (with extra muscle to handle
        // confusing userAgent strings on Vista)
        var msie = /MSIE/.test(navigator.userAgent);
        var ie6  = /MSIE 6.0/.test(navigator.userAgent) && ! /MSIE 8.0/.test(navigator.userAgent);
        var mode = document.documentMode || 0;
        var setExpr = $.isFunction( document.createElement('div').style.setExpression );

        // global $ methods for blocking/unblocking the entire page
        $.blockUI   = function(opts) { install(window, opts); };
        $.unblockUI = function(opts) { remove(window, opts); };

        // convenience method for quick growl-like notifications  (http://www.google.com/search?q=growl)
        $.growlUI = function(title, message, timeout, onClose) {
            var $m = $('<div class="growlUI"></div>');
            if (title) $m.append('<h1>'+title+'</h1>');
            if (message) $m.append('<h2>'+message+'</h2>');
            if (timeout === undefined) timeout = 3000;

            // Added by konapun: Set timeout to 30 seconds if this growl is moused over, like normal toast notifications
            var callBlock = function(opts) {
                opts = opts || {};

                $.blockUI({
                    message: $m,
                    fadeIn : typeof opts.fadeIn  !== 'undefined' ? opts.fadeIn  : 700,
                    fadeOut: typeof opts.fadeOut !== 'undefined' ? opts.fadeOut : 1000,
                    timeout: typeof opts.timeout !== 'undefined' ? opts.timeout : timeout,
                    centerY: false,
                    showOverlay: false,
                    onUnblock: onClose,
                    css: $.blockUI.defaults.growlCSS
                });
            };

            callBlock();
            var nonmousedOpacity = $m.css('opacity');
            $m.mouseover(function() {
                callBlock({
                    fadeIn: 0,
                    timeout: 30000
                });

                var displayBlock = $('.blockMsg');
                displayBlock.stop(); // cancel fadeout if it has started
                displayBlock.fadeTo(300, 1); // make it easier to read the message by removing transparency
            }).mouseout(function() {
                $('.blockMsg').fadeOut(1000);
            });
            // End konapun additions
        };

        // plugin method for blocking element content
        $.fn.block = function(opts) {
            if ( this[0] === window ) {
                $.blockUI( opts );
                return this;
            }
            var fullOpts = $.extend({}, $.blockUI.defaults, opts || {});
            this.each(function() {
                var $el = $(this);
                if (fullOpts.ignoreIfBlocked && $el.data('blockUI.isBlocked'))
                    return;
                $el.unblock({ fadeOut: 0 });
            });

            return this.each(function() {
                if ($.css(this,'position') == 'static') {
                    this.style.position = 'relative';
                    $(this).data('blockUI.static', true);
                }
                this.style.zoom = 1; // force 'hasLayout' in ie
                install(this, opts);
            });
        };

        // plugin method for unblocking element content
        $.fn.unblock = function(opts) {
            if ( this[0] === window ) {
                $.unblockUI( opts );
                return this;
            }
            return this.each(function() {
                remove(this, opts);
            });
        };

        $.blockUI.version = 2.70; // 2nd generation blocking at no extra cost!

        // override these in your code to change the default behavior and style
        $.blockUI.defaults = {
            // message displayed when blocking (use null for no message)
            message:  '<h1>Please wait...</h1>',

            title: null,		// title string; only used when theme == true
            draggable: true,	// only used when theme == true (requires jquery-ui.js to be loaded)

            theme: false, // set to true to use with jQuery UI themes

            // styles for the message when blocking; if you wish to disable
            // these and use an external stylesheet then do this in your code:
            // $.blockUI.defaults.css = {};
            css: {
                padding:	0,
                margin:		0,
                width:		'30%',
                top:		'40%',
                left:		'35%',
                textAlign:	'center',
                color:		'#000',
                border:		'3px solid #aaa',
                backgroundColor:'#fff',
                cursor:		'wait'
            },

            // minimal style set used when themes are used
            themedCSS: {
                width:	'30%',
                top:	'40%',
                left:	'35%'
            },

            // styles for the overlay
            overlayCSS:  {
                backgroundColor:	'#000',
                opacity:			0.6,
                cursor:				'wait'
            },

            // style to replace wait cursor before unblocking to correct issue
            // of lingering wait cursor
            cursorReset: 'default',

            // styles applied when using $.growlUI
            growlCSS: {
                width:		'350px',
                top:		'10px',
                left:		'',
                right:		'10px',
                border:		'none',
                padding:	'5px',
                opacity:	0.6,
                cursor:		'default',
                color:		'#fff',
                backgroundColor: '#000',
                '-webkit-border-radius':'10px',
                '-moz-border-radius':	'10px',
                'border-radius':		'10px'
            },

            // IE issues: 'about:blank' fails on HTTPS and javascript:false is s-l-o-w
            // (hat tip to Jorge H. N. de Vasconcelos)
            /*jshint scripturl:true */
            iframeSrc: /^https/i.test(window.location.href || '') ? 'javascript:false' : 'about:blank',

            // force usage of iframe in non-IE browsers (handy for blocking applets)
            forceIframe: false,

            // z-index for the blocking overlay
            baseZ: 1000,

            // set these to true to have the message automatically centered
            centerX: true, // <-- only effects element blocking (page block controlled via css above)
            centerY: true,

            // allow body element to be stetched in ie6; this makes blocking look better
            // on "short" pages.  disable if you wish to prevent changes to the body height
            allowBodyStretch: true,

            // enable if you want key and mouse events to be disabled for content that is blocked
            bindEvents: true,

            // be default blockUI will supress tab navigation from leaving blocking content
            // (if bindEvents is true)
            constrainTabKey: true,

            // fadeIn time in millis; set to 0 to disable fadeIn on block
            fadeIn:  200,

            // fadeOut time in millis; set to 0 to disable fadeOut on unblock
            fadeOut:  400,

            // time in millis to wait before auto-unblocking; set to 0 to disable auto-unblock
            timeout: 0,

            // disable if you don't want to show the overlay
            showOverlay: true,

            // if true, focus will be placed in the first available input field when
            // page blocking
            focusInput: true,

            // elements that can receive focus
            focusableElements: ':input:enabled:visible',

            // suppresses the use of overlay styles on FF/Linux (due to performance issues with opacity)
            // no longer needed in 2012
            // applyPlatformOpacityRules: true,

            // callback method invoked when fadeIn has completed and blocking message is visible
            onBlock: null,

            // callback method invoked when unblocking has completed; the callback is
            // passed the element that has been unblocked (which is the window object for page
            // blocks) and the options that were passed to the unblock call:
            //	onUnblock(element, options)
            onUnblock: null,

            // callback method invoked when the overlay area is clicked.
            // setting this will turn the cursor to a pointer, otherwise cursor defined in overlayCss will be used.
            onOverlayClick: null,

            // don't ask; if you really must know: http://groups.google.com/group/jquery-en/browse_thread/thread/36640a8730503595/2f6a79a77a78e493#2f6a79a77a78e493
            quirksmodeOffsetHack: 4,

            // class name of the message block
            blockMsgClass: 'blockMsg',

            // if it is already blocked, then ignore it (don't unblock and reblock)
            ignoreIfBlocked: false
        };

        // private data and functions follow...

        var pageBlock = null;
        var pageBlockEls = [];

        function install(el, opts) {
            var css, themedCSS;
            var full = (el == window);
            var msg = (opts && opts.message !== undefined ? opts.message : undefined);
            opts = $.extend({}, $.blockUI.defaults, opts || {});

            if (opts.ignoreIfBlocked && $(el).data('blockUI.isBlocked'))
                return;

            opts.overlayCSS = $.extend({}, $.blockUI.defaults.overlayCSS, opts.overlayCSS || {});
            css = $.extend({}, $.blockUI.defaults.css, opts.css || {});
            if (opts.onOverlayClick)
                opts.overlayCSS.cursor = 'pointer';

            themedCSS = $.extend({}, $.blockUI.defaults.themedCSS, opts.themedCSS || {});
            msg = msg === undefined ? opts.message : msg;

            // remove the current block (if there is one)
            if (full && pageBlock)
                remove(window, {fadeOut:0});

            // if an existing element is being used as the blocking content then we capture
            // its current place in the DOM (and current display style) so we can restore
            // it when we unblock
            if (msg && typeof msg != 'string' && (msg.parentNode || msg.jquery)) {
                var node = msg.jquery ? msg[0] : msg;
                var data = {};
                $(el).data('blockUI.history', data);
                data.el = node;
                data.parent = node.parentNode;
                data.display = node.style.display;
                data.position = node.style.position;
                if (data.parent)
                    data.parent.removeChild(node);
            }

            $(el).data('blockUI.onUnblock', opts.onUnblock);
            var z = opts.baseZ;

            // blockUI uses 3 layers for blocking, for simplicity they are all used on every platform;
            // layer1 is the iframe layer which is used to supress bleed through of underlying content
            // layer2 is the overlay layer which has opacity and a wait cursor (by default)
            // layer3 is the message content that is displayed while blocking
            var lyr1, lyr2, lyr3, s;
            if (msie || opts.forceIframe)
                lyr1 = $('<iframe class="blockUI" style="z-index:'+ (z++) +';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+opts.iframeSrc+'"></iframe>');
            else
                lyr1 = $('<div class="blockUI" style="display:none"></div>');

            if (opts.theme)
                lyr2 = $('<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+ (z++) +';display:none"></div>');
            else
                lyr2 = $('<div class="blockUI blockOverlay" style="z-index:'+ (z++) +';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>');

            if (opts.theme && full) {
                s = '<div class="blockUI ' + opts.blockMsgClass + ' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+(z+10)+';display:none;position:fixed">';
                if ( opts.title ) {
                    s += '<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(opts.title || '&nbsp;')+'</div>';
                }
                s += '<div class="ui-widget-content ui-dialog-content"></div>';
                s += '</div>';
            }
            else if (opts.theme) {
                s = '<div class="blockUI ' + opts.blockMsgClass + ' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+(z+10)+';display:none;position:absolute">';
                if ( opts.title ) {
                    s += '<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(opts.title || '&nbsp;')+'</div>';
                }
                s += '<div class="ui-widget-content ui-dialog-content"></div>';
                s += '</div>';
            }
            else if (full) {
                s = '<div class="blockUI ' + opts.blockMsgClass + ' blockPage" style="z-index:'+(z+10)+';display:none;position:fixed"></div>';
            }
            else {
                s = '<div class="blockUI ' + opts.blockMsgClass + ' blockElement" style="z-index:'+(z+10)+';display:none;position:absolute"></div>';
            }
            lyr3 = $(s);

            // if we have a message, style it
            if (msg) {
                if (opts.theme) {
                    lyr3.css(themedCSS);
                    lyr3.addClass('ui-widget-content');
                }
                else
                    lyr3.css(css);
            }

            // style the overlay
            if (!opts.theme /*&& (!opts.applyPlatformOpacityRules)*/)
                lyr2.css(opts.overlayCSS);
            lyr2.css('position', full ? 'fixed' : 'absolute');

            // make iframe layer transparent in IE
            if (msie || opts.forceIframe)
                lyr1.css('opacity',0.0);

            //$([lyr1[0],lyr2[0],lyr3[0]]).appendTo(full ? 'body' : el);
            var layers = [lyr1,lyr2,lyr3], $par = full ? $('body') : $(el);
            $.each(layers, function() {
                this.appendTo($par);
            });

            if (opts.theme && opts.draggable && $.fn.draggable) {
                lyr3.draggable({
                    handle: '.ui-dialog-titlebar',
                    cancel: 'li'
                });
            }

            // ie7 must use absolute positioning in quirks mode and to account for activex issues (when scrolling)
            var expr = setExpr && (!$.support.boxModel || $('object,embed', full ? null : el).length > 0);
            if (ie6 || expr) {
                // give body 100% height
                if (full && opts.allowBodyStretch && $.support.boxModel)
                    $('html,body').css('height','100%');

                // fix ie6 issue when blocked element has a border width
                if ((ie6 || !$.support.boxModel) && !full) {
                    var t = sz(el,'borderTopWidth'), l = sz(el,'borderLeftWidth');
                    var fixT = t ? '(0 - '+t+')' : 0;
                    var fixL = l ? '(0 - '+l+')' : 0;
                }

                // simulate fixed position
                $.each(layers, function(i,o) {
                    var s = o[0].style;
                    s.position = 'absolute';
                    if (i < 2) {
                        if (full)
                            s.setExpression('height','Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.support.boxModel?0:'+opts.quirksmodeOffsetHack+') + "px"');
                        else
                            s.setExpression('height','this.parentNode.offsetHeight + "px"');
                        if (full)
                            s.setExpression('width','jQuery.support.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"');
                        else
                            s.setExpression('width','this.parentNode.offsetWidth + "px"');
                        if (fixL) s.setExpression('left', fixL);
                        if (fixT) s.setExpression('top', fixT);
                    }
                    else if (opts.centerY) {
                        if (full) s.setExpression('top','(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"');
                        s.marginTop = 0;
                    }
                    else if (!opts.centerY && full) {
                        var top = (opts.css && opts.css.top) ? parseInt(opts.css.top, 10) : 0;
                        var expression = '((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + '+top+') + "px"';
                        s.setExpression('top',expression);
                    }
                });
            }

            // show the message
            if (msg) {
                if (opts.theme)
                    lyr3.find('.ui-widget-content').append(msg);
                else
                    lyr3.append(msg);
                if (msg.jquery || msg.nodeType)
                    $(msg).show();
            }

            if ((msie || opts.forceIframe) && opts.showOverlay)
                lyr1.show(); // opacity is zero
            if (opts.fadeIn) {
                var cb = opts.onBlock ? opts.onBlock : noOp;
                var cb1 = (opts.showOverlay && !msg) ? cb : noOp;
                var cb2 = msg ? cb : noOp;
                if (opts.showOverlay)
                    lyr2._fadeIn(opts.fadeIn, cb1);
                if (msg)
                    lyr3._fadeIn(opts.fadeIn, cb2);
            }
            else {
                if (opts.showOverlay)
                    lyr2.show();
                if (msg)
                    lyr3.show();
                if (opts.onBlock)
                    opts.onBlock.bind(lyr3)();
            }

            // bind key and mouse events
            bind(1, el, opts);

            if (full) {
                pageBlock = lyr3[0];
                pageBlockEls = $(opts.focusableElements,pageBlock);
                if (opts.focusInput)
                    setTimeout(focus, 20);
            }
            else
                center(lyr3[0], opts.centerX, opts.centerY);

            if (opts.timeout) {
                // auto-unblock
                var to = setTimeout(function() {
                    if (full)
                        $.unblockUI(opts);
                    else
                        $(el).unblock(opts);
                }, opts.timeout);
                $(el).data('blockUI.timeout', to);
            }
        }

        // remove the block
        function remove(el, opts) {
            var count;
            var full = (el == window);
            var $el = $(el);
            var data = $el.data('blockUI.history');
            var to = $el.data('blockUI.timeout');
            if (to) {
                clearTimeout(to);
                $el.removeData('blockUI.timeout');
            }
            opts = $.extend({}, $.blockUI.defaults, opts || {});
            bind(0, el, opts); // unbind events

            if (opts.onUnblock === null) {
                opts.onUnblock = $el.data('blockUI.onUnblock');
                $el.removeData('blockUI.onUnblock');
            }

            var els;
            if (full) // crazy selector to handle odd field errors in ie6/7
                els = $('body').children().filter('.blockUI').add('body > .blockUI');
            else
                els = $el.find('>.blockUI');

            // fix cursor issue
            if ( opts.cursorReset ) {
                if ( els.length > 1 )
                    els[1].style.cursor = opts.cursorReset;
                if ( els.length > 2 )
                    els[2].style.cursor = opts.cursorReset;
            }

            if (full)
                pageBlock = pageBlockEls = null;

            if (opts.fadeOut) {
                count = els.length;
                els.stop().fadeOut(opts.fadeOut, function() {
                    if ( --count === 0)
                        reset(els,data,opts,el);
                });
            }
            else
                reset(els, data, opts, el);
        }

        // move blocking element back into the DOM where it started
        function reset(els,data,opts,el) {
            var $el = $(el);
            if ( $el.data('blockUI.isBlocked') )
                return;

            els.each(function(i,o) {
                // remove via DOM calls so we don't lose event handlers
                if (this.parentNode)
                    this.parentNode.removeChild(this);
            });

            if (data && data.el) {
                data.el.style.display = data.display;
                data.el.style.position = data.position;
                data.el.style.cursor = 'default'; // #59
                if (data.parent)
                    data.parent.appendChild(data.el);
                $el.removeData('blockUI.history');
            }

            if ($el.data('blockUI.static')) {
                $el.css('position', 'static'); // #22
            }

            if (typeof opts.onUnblock == 'function')
                opts.onUnblock(el,opts);

            // fix issue in Safari 6 where block artifacts remain until reflow
            var body = $(document.body), w = body.width(), cssW = body[0].style.width;
            body.width(w-1).width(w);
            body[0].style.width = cssW;
        }

        // bind/unbind the handler
        function bind(b, el, opts) {
            var full = el == window, $el = $(el);

            // don't bother unbinding if there is nothing to unbind
            if (!b && (full && !pageBlock || !full && !$el.data('blockUI.isBlocked')))
                return;

            $el.data('blockUI.isBlocked', b);

            // don't bind events when overlay is not in use or if bindEvents is false
            if (!full || !opts.bindEvents || (b && !opts.showOverlay))
                return;

            // bind anchors and inputs for mouse and key events
            var events = 'mousedown mouseup keydown keypress keyup touchstart touchend touchmove';
            if (b)
                $(document).bind(events, opts, handler);
            else
                $(document).unbind(events, handler);

            // former impl...
            //		var $e = $('a,:input');
            //		b ? $e.bind(events, opts, handler) : $e.unbind(events, handler);
        }

        // event handler to suppress keyboard/mouse events when blocking
        function handler(e) {
            // allow tab navigation (conditionally)
            if (e.type === 'keydown' && e.keyCode && e.keyCode == 9) {
                if (pageBlock && e.data.constrainTabKey) {
                    var els = pageBlockEls;
                    var fwd = !e.shiftKey && e.target === els[els.length-1];
                    var back = e.shiftKey && e.target === els[0];
                    if (fwd || back) {
                        setTimeout(function(){focus(back);},10);
                        return false;
                    }
                }
            }
            var opts = e.data;
            var target = $(e.target);
            if (target.hasClass('blockOverlay') && opts.onOverlayClick)
                opts.onOverlayClick(e);

            // allow events within the message content
            if (target.parents('div.' + opts.blockMsgClass).length > 0)
                return true;

            // allow events for content that is not being blocked
            return target.parents().children().filter('div.blockUI').length === 0;
        }

        function focus(back) {
            if (!pageBlockEls)
                return;
            var e = pageBlockEls[back===true ? pageBlockEls.length-1 : 0];
            if (e)
                e.focus();
        }

        function center(el, x, y) {
            var p = el.parentNode, s = el.style;
            var l = ((p.offsetWidth - el.offsetWidth)/2) - sz(p,'borderLeftWidth');
            var t = ((p.offsetHeight - el.offsetHeight)/2) - sz(p,'borderTopWidth');
            if (x) s.left = l > 0 ? (l+'px') : '0';
            if (y) s.top  = t > 0 ? (t+'px') : '0';
        }

        function sz(el, p) {
            return parseInt($.css(el,p),10)||0;
        }

    }


    /*global define:true */
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['jquery'], setup);
    } else if (typeof exports === 'object') {
        // Node/CommonJS
        setup(require('jquery'));
    } else {
        // Browser globals
        setup(jQuery);
    }

})();

/* Toastr */
!function(e){e(["jquery"],function(e){return function(){function t(e,t,n){return g({type:O.error,iconClass:m().iconClasses.error,message:e,optionsOverride:n,title:t})}function n(t,n){return t||(t=m()),v=e("#"+t.containerId),v.length?v:(n&&(v=d(t)),v)}function o(e,t,n){return g({type:O.info,iconClass:m().iconClasses.info,message:e,optionsOverride:n,title:t})}function s(e){C=e}function i(e,t,n){return g({type:O.success,iconClass:m().iconClasses.success,message:e,optionsOverride:n,title:t})}function a(e,t,n){return g({type:O.warning,iconClass:m().iconClasses.warning,message:e,optionsOverride:n,title:t})}function r(e,t){var o=m();v||n(o),u(e,o,t)||l(o)}function c(t){var o=m();return v||n(o),t&&0===e(":focus",t).length?void h(t):void(v.children().length&&v.remove())}function l(t){for(var n=v.children(),o=n.length-1;o>=0;o--)u(e(n[o]),t)}function u(t,n,o){var s=!(!o||!o.force)&&o.force;return!(!t||!s&&0!==e(":focus",t).length)&&(t[n.hideMethod]({duration:n.hideDuration,easing:n.hideEasing,complete:function(){h(t)}}),!0)}function d(t){return v=e("<div/>").attr("id",t.containerId).addClass(t.positionClass),v.appendTo(e(t.target)),v}function p(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1}}function f(e){C&&C(e)}function g(t){function o(e){return null==e&&(e=""),e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function s(){c(),u(),d(),p(),g(),C(),l(),i()}function i(){var e="";switch(t.iconClass){case"toast-success":case"toast-info":e="polite";break;default:e="assertive"}I.attr("aria-live",e)}function a(){E.closeOnHover&&I.hover(H,D),!E.onclick&&E.tapToDismiss&&I.click(b),E.closeButton&&j&&j.click(function(e){e.stopPropagation?e.stopPropagation():void 0!==e.cancelBubble&&e.cancelBubble!==!0&&(e.cancelBubble=!0),E.onCloseClick&&E.onCloseClick(e),b(!0)}),E.onclick&&I.click(function(e){E.onclick(e),b()})}function r(){I.hide(),I[E.showMethod]({duration:E.showDuration,easing:E.showEasing,complete:E.onShown}),E.timeOut>0&&(k=setTimeout(b,E.timeOut),F.maxHideTime=parseFloat(E.timeOut),F.hideEta=(new Date).getTime()+F.maxHideTime,E.progressBar&&(F.intervalId=setInterval(x,10)))}function c(){t.iconClass&&I.addClass(E.toastClass).addClass(y)}function l(){E.newestOnTop?v.prepend(I):v.append(I)}function u(){if(t.title){var e=t.title;E.escapeHtml&&(e=o(t.title)),M.append(e).addClass(E.titleClass),I.append(M)}}function d(){if(t.message){var e=t.message;E.escapeHtml&&(e=o(t.message)),B.append(e).addClass(E.messageClass),I.append(B)}}function p(){E.closeButton&&(j.addClass(E.closeClass).attr("role","button"),I.prepend(j))}function g(){E.progressBar&&(q.addClass(E.progressClass),I.prepend(q))}function C(){E.rtl&&I.addClass("rtl")}function O(e,t){if(e.preventDuplicates){if(t.message===w)return!0;w=t.message}return!1}function b(t){var n=t&&E.closeMethod!==!1?E.closeMethod:E.hideMethod,o=t&&E.closeDuration!==!1?E.closeDuration:E.hideDuration,s=t&&E.closeEasing!==!1?E.closeEasing:E.hideEasing;if(!e(":focus",I).length||t)return clearTimeout(F.intervalId),I[n]({duration:o,easing:s,complete:function(){h(I),clearTimeout(k),E.onHidden&&"hidden"!==P.state&&E.onHidden(),P.state="hidden",P.endTime=new Date,f(P)}})}function D(){(E.timeOut>0||E.extendedTimeOut>0)&&(k=setTimeout(b,E.extendedTimeOut),F.maxHideTime=parseFloat(E.extendedTimeOut),F.hideEta=(new Date).getTime()+F.maxHideTime)}function H(){clearTimeout(k),F.hideEta=0,I.stop(!0,!0)[E.showMethod]({duration:E.showDuration,easing:E.showEasing})}function x(){var e=(F.hideEta-(new Date).getTime())/F.maxHideTime*100;q.width(e+"%")}var E=m(),y=t.iconClass||E.iconClass;if("undefined"!=typeof t.optionsOverride&&(E=e.extend(E,t.optionsOverride),y=t.optionsOverride.iconClass||y),!O(E,t)){T++,v=n(E,!0);var k=null,I=e("<div/>"),M=e("<div/>"),B=e("<div/>"),q=e("<div/>"),j=e(E.closeHtml),F={intervalId:null,hideEta:null,maxHideTime:null},P={toastId:T,state:"visible",startTime:new Date,options:E,map:t};return s(),r(),a(),f(P),E.debug&&console&&console.log(P),I}}function m(){return e.extend({},p(),b.options)}function h(e){v||(v=n()),e.is(":visible")||(e.remove(),e=null,0===v.children().length&&(v.remove(),w=void 0))}var v,C,w,T=0,O={error:"error",info:"info",success:"success",warning:"warning"},b={clear:r,remove:c,error:t,getContainer:n,info:o,options:{},subscribe:s,success:i,version:"2.1.4",warning:a};return b}()})}("function"==typeof define&&define.amd?define:function(e,t){"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):window.toastr=t(window.jQuery)});

/* FormValidation */
!function(e,t){typeof exports==='object'&&typeof module!=='undefined'?t(exports):typeof define==='function'&&define.amd?define(['exports'],t):(e=e||self, t(e.FormValidation={}))}(this,(function(e){"use strict";function t(e){var t=e.length,n=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],r=0,a=0;while(t--)a+=n[r][parseInt(e.charAt(t),10)],r=1-r;return a%10===0&&a>0}function n(e){var t=e.length,n=5;for(var r=0;r<t;r++)n=((n||10)*2%11+parseInt(e.charAt(r),10))%10;return n===1}function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:'0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',n=e.length,r=t.length,a=Math.floor(r/2);for(var l=0;l<n;l++)a=((a||r)*2%(r+1)+t.indexOf(e.charAt(l)))%r;return a===1}function a(e){var t=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],n=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=e.reverse(),a=0;for(var l=0;l<r.length;l++)a=t[a][n[l%8][r[l]]];return a===0}var l={luhn:t,mod11And10:n,mod37And36:r,verhoeff:a};function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1;r.configurable=!0;"value" in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}function s(e,t,n){t&&o(e.prototype,t);n&&o(e,n);return e}function c(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):(e[t]=n);return e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function")}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}});t&&f(e,t)}function d(e){d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)};return d(e)}function f(e,t){f=Object.setPrototypeOf||function(e,t){e.__proto__=t;return e};return f(e,t)}function m(e){if(e===void 0){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}function g(e,t){if(t&&(typeof t==="object"||typeof t==="function")){return t}return m(e)}function p(){return{fns:{},clear:function(){this.fns={}},emit:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];(this.fns[e]||[]).map(function(e){return e.apply(e,n)})},off:function(e,t){if(this.fns[e]){var n=this.fns[e].indexOf(t);n>=0&&this.fns[e].splice(n,1)}},on:function(e,t){(this.fns[e]=this.fns[e]||[]).push(t)}}}function h(){return{filters:{},add:function(e,t){(this.filters[e]=this.filters[e]||[]).push(t)},clear:function(){this.filters={}},execute:function(e,t,n){if(!this.filters[e]||!this.filters[e].length){return t}var r=t,a=this.filters[e],l=a.length;for(var i=0;i<l;i++)r=a[i].apply(r,n);return r},remove:function(e,t){this.filters[e]&&(this.filters[e]=this.filters[e].filter(function(e){return e!==t}))}}}function v(e,t,n,r){var a=(n.getAttribute('type')||'').toLowerCase(),l=n.tagName.toLowerCase();switch(l){case 'textarea':return n.value;case 'select':var s=n;var o=s.selectedIndex;return o>=0?s.options.item(o).value:'';case 'input':if('radio'===a||'checkbox'===a){var i=r.filter(function(e){return e.checked}).length;return i===0?'':i+''}else{return n.value};default:return''}}function A(e,t){var n=Array.isArray(t)?t:[t],r=e;n.forEach(function(e){r=r.replace('%s',e)});return r}function b(){var e=function(e){return parseFloat("".concat(e).replace(',','.'))};return{validate:function(t){var n=t.value;if(n===''){return{valid:!0}}var r=Object.assign({},{inclusive:!0,message:''},t.options),a=e(r.min),l=e(r.max);return r.inclusive?{message:A(t.l10n?r.message||t.l10n.between["default"]:r.message,["".concat(a),"".concat(l)]),valid:parseFloat(n)>=a&&parseFloat(n)<=l}:{message:A(t.l10n?r.message||t.l10n.between.notInclusive:r.message,["".concat(a),"".concat(l)]),valid:parseFloat(n)>a&&parseFloat(n)<l}}}}function E(){return{validate:function(e){return{valid:!0}}}}function C(e,t){if('function'===typeof e){return e.apply(this,t)}else if('string'===typeof e){var n=e;'()'===n.substring(n.length-2)&&(n=n.substring(0,n.length-2));var r=n.split('.'),a=r.pop(),l=window,i=!0,o=!1,s=void 0;try{for(var c=r[Symbol.iterator](),u;!(i=(u=c.next()).done);i=!0){var d=u.value;l=l[d]}}catch(e){o=!0,s=e}finally{try{!i&&c["return"]!=null&&c["return"]()}finally{if(o){throw s}}};return typeof l[a]==='undefined'?null:l[a].apply(this,t)}}function V(){return{validate:function(e){var t=C(e.options.callback,[e]);return'boolean'===typeof t?{valid:t}:t}}}function S(){return{validate:function(e){var t='select'===e.element.tagName.toLowerCase()?e.element.querySelectorAll('option:checked').length:e.elements.filter(function(e){return e.checked}).length,n=e.options.min?"".concat(e.options.min):'',r=e.options.max?"".concat(e.options.max):'',a=e.l10n?e.options.message||e.l10n.choice["default"]:e.options.message,l=!(n&&t<parseInt(n,10)||r&&t>parseInt(r,10));switch(!0){case !!n&&!!r:a=A(e.l10n?e.l10n.choice.between:e.options.message,[n,r]);break;case !!n:a=A(e.l10n?e.l10n.choice.more:e.options.message,n);break;case !!r:a=A(e.l10n?e.l10n.choice.less:e.options.message,r);break}return{message:a,valid:l}}}}var H={AMERICAN_EXPRESS:{length:[15],prefix:['34','37']},DANKORT:{length:[16],prefix:['5019']},DINERS_CLUB:{length:[14],prefix:['300','301','302','303','304','305','36']},DINERS_CLUB_US:{length:[16],prefix:['54','55']},DISCOVER:{length:[16],prefix:['6011','622126','622127','622128','622129','62213','62214','62215','62216','62217','62218','62219','6222','6223','6224','6225','6226','6227','6228','62290','62291','622920','622921','622922','622923','622924','622925','644','645','646','647','648','649','65']},ELO:{length:[16],prefix:['4011','4312','4389','4514','4573','4576','5041','5066','5067','509','6277','6362','6363','650','6516','6550']},FORBRUGSFORENINGEN:{length:[16],prefix:['600722']},JCB:{length:[16],prefix:['3528','3529','353','354','355','356','357','358']},LASER:{length:[16,17,18,19],prefix:['6304','6706','6771','6709']},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:['5018','5020','5038','5868','6304','6759','6761','6762','6763','6764','6765','6766']},MASTERCARD:{length:[16],prefix:['51','52','53','54','55']},SOLO:{length:[16,18,19],prefix:['6334','6767']},UNIONPAY:{length:[16,17,18,19],prefix:['622126','622127','622128','622129','62213','62214','62215','62216','62217','62218','62219','6222','6223','6224','6225','6226','6227','6228','62290','62291','622920','622921','622922','622923','622924','622925']},VISA:{length:[16],prefix:['4']},VISA_ELECTRON:{length:[16],prefix:['4026','417500','4405','4508','4844','4913','4917']}};function y(){return{validate:function(e){if(e.value===''){return{meta:{type:null},valid:!0}}if(/[^0-9-\s]+/.test(e.value)){return{meta:{type:null},valid:!1}}var n=e.value.replace(/\D/g,'');if(!t(n)){return{meta:{type:null},valid:!1}}for(var r=0,a=Object.keys(H);r<a.length;r++){var l=a[r];for(var i in H[l].prefix){if(e.value.substr(0,H[l].prefix[i].length)===H[l].prefix[i]&&H[l].length.indexOf(n.length)!==-1){return{meta:{type:l},valid:!0}}}}return{meta:{type:null},valid:!1}}}}function F(e,t,n,r){if(isNaN(e)||isNaN(t)||isNaN(n)){return!1}if(e<1e3||e>9999||t<=0||t>12){return!1}var a=[31,e%400===0||e%100!==0&&e%4===0?29:28,31,30,31,30,31,31,30,31,30,31];if(n<=0||n>a[t-1]){return!1}if(r===!0){var l=new Date,i=l.getFullYear(),o=l.getMonth(),s=l.getDate();return e<i||e===i&&t-1<o||e===i&&t-1===o&&n<s}return!0}function I(){var e=function(e,t,n){var r=t.indexOf('YYYY'),a=t.indexOf('MM'),l=t.indexOf('DD');if(r===-1||a===-1||l===-1){return null}var i=e.split(' '),o=i[0].split(n);if(o.length<3){return null}var s=new Date(parseInt(o[r],10),parseInt(o[a],10)-1,parseInt(o[l],10));if(i.length>1){var c=i[1].split(':');s.setHours(c.length>0?parseInt(c[0],10):0);s.setMinutes(c.length>1?parseInt(c[1],10):0);s.setSeconds(c.length>2?parseInt(c[2],10):0)}return s},t=function(e,t){var n=t.replace(/Y/g,'y').replace(/M/g,'m').replace(/D/g,'d').replace(/:m/g,':M').replace(/:mm/g,':MM').replace(/:S/,':s').replace(/:SS/,':ss'),r=e.getDate(),a=r<10?"0".concat(r):r,l=e.getMonth()+1,i=l<10?"0".concat(l):l,o="".concat(e.getFullYear()).substr(2),s=e.getFullYear(),c=e.getHours()%12||12,u=c<10?"0".concat(c):c,d=e.getHours(),f=d<10?"0".concat(d):d,m=e.getMinutes(),g=m<10?"0".concat(m):m,p=e.getSeconds(),h=p<10?"0".concat(p):p,v={H:"".concat(d),HH:"".concat(f),M:"".concat(m),MM:"".concat(g),d:"".concat(r),dd:"".concat(a),h:"".concat(c),hh:"".concat(u),m:"".concat(l),mm:"".concat(i),s:"".concat(p),ss:"".concat(h),yy:"".concat(o),yyyy:"".concat(s)};return n.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\1?|"[^"]*"|'[^']*'/g,function(e){return v[e]?v[e]:e.slice(1,e.length-1)})};return{validate:function(n){if(n.value===''){return{meta:{date:null},valid:!0}}var r=Object.assign({},{format:n.element&&n.element.getAttribute('type')==='date'?'YYYY-MM-DD':'MM/DD/YYYY',message:''},n.options),a=n.l10n?n.l10n.date["default"]:r.message,l={message:"".concat(a),meta:{date:null},valid:!1},i=r.format.split(' '),o=i.length>1?i[1]:null,s=i.length>2?i[2]:null,c=n.value.split(' '),u=c[0],d=c.length>1?c[1]:null;if(i.length!==c.length){return l}var f=r.separator||(u.indexOf('/')!==-1?'/':u.indexOf('-')!==-1?'-':u.indexOf('.')!==-1?'.':'/');if(f===null||u.indexOf(f)===-1){return l}var m=u.split(f),g=i[0].split(f);if(m.length!==g.length){return l}var p=m[g.indexOf('YYYY')],h=m[g.indexOf('MM')],v=m[g.indexOf('DD')];if(!/^\d+$/.test(p)||!/^\d+$/.test(h)||!/^\d+$/.test(v)||p.length>4||h.length>2||v.length>2){return l}var b=parseInt(p,10),E=parseInt(h,10),C=parseInt(v,10);if(!F(b,E,C)){return l}var V=new Date(b,E-1,C);if(o){var S=d.split(':');if(o.split(':').length!==S.length){return l}var H=S.length>0?S[0].length<=2&&/^\d+$/.test(S[0])?parseInt(S[0],10):-1:0,y=S.length>1?S[1].length<=2&&/^\d+$/.test(S[1])?parseInt(S[1],10):-1:0,I=S.length>2?S[2].length<=2&&/^\d+$/.test(S[2])?parseInt(S[2],10):-1:0;if(H===-1||y===-1||I===-1){return l}if(I<0||I>60){return l}if(H<0||H>=24||s&&H>12){return l}if(y<0||y>59){return l}V.setHours(H);V.setMinutes(y);V.setSeconds(I)}var O=typeof r.min==='function'?r.min():r.min,w=O instanceof Date?O:O?e(O,g,f):V,L=typeof r.max==='function'?r.max():r.max,N=L instanceof Date?L:L?e(L,g,f):V,x=O instanceof Date?t(w,r.format):O,M=L instanceof Date?t(N,r.format):L;switch(!0){case !!x&&!M:return{message:A(n.l10n?n.l10n.date.min:a,x),meta:{date:V},valid:V.getTime()>=w.getTime()};case !!M&&!x:return{message:A(n.l10n?n.l10n.date.max:a,M),meta:{date:V},valid:V.getTime()<=N.getTime()};case !!M&&!!x:return{message:A(n.l10n?n.l10n.date.range:a,[x,M]),meta:{date:V},valid:V.getTime()<=N.getTime()&&V.getTime()>=w.getTime()};default:return{message:"".concat(a),meta:{date:V},valid:!0}}}}}function O(){return{validate:function(e){var t='function'===typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:t===''||e.value!==t}}}}function w(){return{validate:function(e){return{valid:e.value===''||/^\d+$/.test(e.value)}}}}function L(){var e=function(e,t){var n=e.split(/"/),r=n.length,a=[],l='';for(var i=0;i<r;i++){if(i%2===0){var o=n[i].split(t),s=o.length;if(s===1)l+=o[0];else{a.push(l+o[0]);for(var c=1;c<s-1;c++)a.push(o[c]);l=o[s-1]}}else l+='"'+n[i],i<r-1&&(l+='"')}a.push(l);return a};return{validate:function(t){if(t.value===''){return{valid:!0}}var n=Object.assign({},{multiple:!1,separator:/[,;]/},t.options),r=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,a=n.multiple===!0||"".concat(n.multiple)==='true';if(a){var l=n.separator||/[,;]/,i=e(t.value,l),o=i.length;for(var s=0;s<o;s++){if(!r.test(i[s])){return{valid:!1}}}return{valid:!0}}else{return{valid:r.test(t.value)}}}}}function N(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t,n=e.options.extension?e.options.extension.toLowerCase().split(','):null,r=e.options.type?e.options.type.toLowerCase().split(','):null,a=window.File&&window.FileList&&window.FileReader;if(a){var l=e.element.files,i=l.length,o=0;if(e.options.maxFiles&&i>parseInt("".concat(e.options.maxFiles),10)){return{meta:{error:'INVALID_MAX_FILES'},valid:!1}}if(e.options.minFiles&&i<parseInt("".concat(e.options.minFiles),10)){return{meta:{error:'INVALID_MIN_FILES'},valid:!1}}var s={};for(var c=0;c<i;c++){o+=l[c].size;t=l[c].name.substr(l[c].name.lastIndexOf('.')+1);s={ext:t,file:l[c],size:l[c].size,type:l[c].type};if(e.options.minSize&&l[c].size<parseInt("".concat(e.options.minSize),10)){return{meta:Object.assign({},{error:'INVALID_MIN_SIZE'},s),valid:!1}}if(e.options.maxSize&&l[c].size>parseInt("".concat(e.options.maxSize),10)){return{meta:Object.assign({},{error:'INVALID_MAX_SIZE'},s),valid:!1}}if(n&&n.indexOf(t.toLowerCase())===-1){return{meta:Object.assign({},{error:'INVALID_EXTENSION'},s),valid:!1}}if(l[c].type&&r&&r.indexOf(l[c].type.toLowerCase())===-1){return{meta:Object.assign({},{error:'INVALID_TYPE'},s),valid:!1}}}if(e.options.maxTotalSize&&o>parseInt("".concat(e.options.maxTotalSize),10)){return{meta:Object.assign({},{error:'INVALID_MAX_TOTAL_SIZE',totalSize:o},s),valid:!1}}if(e.options.minTotalSize&&o<parseInt("".concat(e.options.minTotalSize),10)){return{meta:Object.assign({},{error:'INVALID_MIN_TOTAL_SIZE',totalSize:o},s),valid:!1}}}else{t=e.value.substr(e.value.lastIndexOf('.')+1);if(n&&n.indexOf(t.toLowerCase())===-1){return{meta:{error:'INVALID_EXTENSION',ext:t},valid:!1}}}return{valid:!0}}}}function x(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{inclusive:!0,message:''},e.options),n=parseFloat("".concat(t.min).replace(',','.'));return t.inclusive?{message:A(e.l10n?t.message||e.l10n.greaterThan["default"]:t.message,"".concat(n)),valid:parseFloat(e.value)>=n}:{message:A(e.l10n?t.message||e.l10n.greaterThan.notInclusive:t.message,"".concat(n)),valid:parseFloat(e.value)>n}}}}function M(){return{validate:function(e){var t='function'===typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:t===''||e.value===t}}}}function T(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{decimalSeparator:'.',thousandsSeparator:''},e.options),n=t.decimalSeparator==='.'?'\\.':t.decimalSeparator,r=t.thousandsSeparator==='.'?'\\.':t.thousandsSeparator,a=new RegExp("^-?[0-9]{1,3}(".concat(r,"[0-9]{3})*(").concat(n,"[0-9]+)?$")),l=new RegExp(r,'g'),i="".concat(e.value);if(!a.test(i)){return{valid:!1}}r&&(i=i.replace(l,''));n&&(i=i.replace(n,'.'));var o=parseFloat(i);return{valid:!isNaN(o)&&isFinite(o)&&Math.floor(o)===o}}}}function k(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{ipv4:!0,ipv6:!0},e.options),n=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/([0-9]|[1-2][0-9]|3[0-2]))?$/,r=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*(\/(\d|\d\d|1[0-1]\d|12[0-8]))?$/;switch(!0){case t.ipv4&&!t.ipv6:return{message:e.l10n?t.message||e.l10n.ip.ipv4:t.message,valid:n.test(e.value)};case !t.ipv4&&t.ipv6:return{message:e.l10n?t.message||e.l10n.ip.ipv6:t.message,valid:r.test(e.value)};case t.ipv4&&t.ipv6:;default:return{message:e.l10n?t.message||e.l10n.ip["default"]:t.message,valid:n.test(e.value)||r.test(e.value)}}}}}function R(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{inclusive:!0,message:''},e.options),n=parseFloat("".concat(t.max).replace(',','.'));return t.inclusive?{message:A(e.l10n?t.message||e.l10n.lessThan["default"]:t.message,"".concat(n)),valid:parseFloat(e.value)<=n}:{message:A(e.l10n?t.message||e.l10n.lessThan.notInclusive:t.message,"".concat(n)),valid:parseFloat(e.value)<n}}}}function B(){return{validate:function(e){return{valid:e.value!==''}}}}function Z(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{decimalSeparator:'.',thousandsSeparator:''},e.options),n="".concat(e.value);n.substr(0,1)===t.decimalSeparator?(n="0".concat(t.decimalSeparator).concat(n.substr(1))):n.substr(0,2)==="-".concat(t.decimalSeparator)&&(n="-0".concat(t.decimalSeparator).concat(n.substr(2)));var r=t.decimalSeparator==='.'?'\\.':t.decimalSeparator,a=t.thousandsSeparator==='.'?'\\.':t.thousandsSeparator,l=new RegExp("^-?[0-9]{1,3}(".concat(a,"[0-9]{3})*(").concat(r,"[0-9]+)?$")),i=new RegExp(a,'g');if(!l.test(n)){return{valid:!1}}a&&(n=n.replace(i,''));r&&(n=n.replace(r,'.'));var o=parseFloat(n);return{valid:!isNaN(o)&&isFinite(o)}}}}function G(){return{validate:function(e){return C(e.options.promise,[e])}}}function P(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=e.options.regexp;if(t instanceof RegExp){return{valid:t.test(e.value)}}else{var n=t.toString(),r=e.options.flags?new RegExp(n,e.options.flags):new RegExp(n);return{valid:r.test(e.value)}}}}}function D(e,t){var n=function(e){return Object.keys(e).map(function(t){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e[t]))}).join('&')};return new Promise(function(r,a){var l=Object.assign({},{crossDomain:!1,headers:{},method:'GET',params:{}},t),i=Object.keys(l.params).map(function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(l.params[e]))}).join('&'),o=e.indexOf('?'),s='GET'===l.method?"".concat(e).concat(o?'?':'&').concat(i):e;if(l.crossDomain){var c=document.createElement('script'),u="___fetch".concat(Date.now(),"___");window[u]=function(e){delete window[u];r(e)};c.src="".concat(s).concat(o?'&':'?',"callback=").concat(u);c.async=!0;c.addEventListener('load',function(){c.parentNode.removeChild(c)});c.addEventListener('error',function(){return a});document.head.appendChild(c)}else{var d=new XMLHttpRequest;d.open(l.method,s);d.setRequestHeader('X-Requested-With','XMLHttpRequest');'POST'===l.method&&d.setRequestHeader('Content-Type','application/x-www-form-urlencoded');Object.keys(l.headers).forEach(function(e){return d.setRequestHeader(e,l.headers[e])});d.addEventListener('load',function(){r(JSON.parse(this.responseText))});d.addEventListener('error',function(){return a});d.send(n(l.params))}})}function K(){var e={crossDomain:!1,data:{},headers:{},method:'GET',validKey:'valid'};return{validate:function(t){if(t.value===''){return Promise.resolve({valid:!0})}var n=Object.assign({},e,t.options),r=n.data;'function'===typeof n.data&&(r=n.data.call(this,t));'string'===typeof r&&(r=JSON.parse(r));r[n.name||t.field]=t.value;var a='function'===typeof n.url?n.url.call(this,t):n.url;return D(a,{crossDomain:n.crossDomain,headers:n.headers,method:n.method,params:r}).then(function(e){return Promise.resolve({message:e.message,meta:e,valid:"".concat(e[n.validKey])==='true'})})["catch"](function(e){return Promise.reject({valid:!1})})}}}function U(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{"case":'lower'},e.options),n=(t["case"]||'lower').toLowerCase();return{message:t.message||(e.l10n?'upper'===n?e.l10n.stringCase.upper:e.l10n.stringCase["default"]:t.message),valid:'upper'===n?e.value===e.value.toUpperCase():e.value===e.value.toLowerCase()}}}}function Y(){var e=function(e){var t=e.length;for(var n=e.length-1;n>=0;n--){var r=e.charCodeAt(n);r>127&&r<=2047?t++:r>2047&&r<=65535&&(t+=2);r>=56320&&r<=57343&&n--}return"".concat(t)};return{validate:function(t){var n=Object.assign({},{message:'',trim:!1,utf8Bytes:!1},t.options),r=n.trim===!0||"".concat(n.trim)==='true'?t.value.trim():t.value;if(r===''){return{valid:!0}}var a=n.min?"".concat(n.min):'',l=n.max?"".concat(n.max):'',i=n.utf8Bytes?e(r):r.length,o=!0,s=t.l10n?n.message||t.l10n.stringLength["default"]:n.message;(a&&i<parseInt(a,10)||l&&i>parseInt(l,10))&&(o=!1);switch(!0){case !!a&&!!l:s=A(t.l10n?n.message||t.l10n.stringLength.between:n.message,[a,l]);break;case !!a:s=A(t.l10n?n.message||t.l10n.stringLength.more:n.message,"".concat(parseInt(a,10)-1));break;case !!l:s=A(t.l10n?n.message||t.l10n.stringLength.less:n.message,"".concat(parseInt(l,10)+1));break}return{message:s,valid:o}}}}function J(){var e={allowEmptyProtocol:!1,allowLocal:!1,protocol:'http, https, ftp'};return{validate:function(t){if(t.value===''){return{valid:!0}}var n=Object.assign({},e,t.options),r=n.allowLocal===!0||"".concat(n.allowLocal)==='true',a=n.allowEmptyProtocol===!0||"".concat(n.allowEmptyProtocol)==='true',l=n.protocol.split(',').join('|').replace(/\s/g,''),i=new RegExp("^(?:(?:"+l+")://)"+(a?'?':'')+"(?:\\S+(?::\\S*)?@)?"+"(?:"+(r?'':"(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})")+"(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])"+"(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}"+"(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))"+"|"+"(?:(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9]+)"+"(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9])*"+"(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))"+(r?'?':'')+")"+"(?::\\d{2,5})?"+"(?:/[^\\s]*)?$","i");return{valid:i.test(t.value)}}}}function W(){return{validate:function(e){return{valid:e.value===''||/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})$/.test(e.value)}}}}function z(){return{validate:function(e){return{valid:e.value===''||/^[a-zA-Z]{6}[a-zA-Z0-9]{2}([a-zA-Z0-9]{3})?$/.test(e.value)}}}}function _(){var e=['hex','rgb','rgba','hsl','hsla','keyword'],t=['aliceblue','antiquewhite','aqua','aquamarine','azure','beige','bisque','black','blanchedalmond','blue','blueviolet','brown','burlywood','cadetblue','chartreuse','chocolate','coral','cornflowerblue','cornsilk','crimson','cyan','darkblue','darkcyan','darkgoldenrod','darkgray','darkgreen','darkgrey','darkkhaki','darkmagenta','darkolivegreen','darkorange','darkorchid','darkred','darksalmon','darkseagreen','darkslateblue','darkslategray','darkslategrey','darkturquoise','darkviolet','deeppink','deepskyblue','dimgray','dimgrey','dodgerblue','firebrick','floralwhite','forestgreen','fuchsia','gainsboro','ghostwhite','gold','goldenrod','gray','green','greenyellow','grey','honeydew','hotpink','indianred','indigo','ivory','khaki','lavender','lavenderblush','lawngreen','lemonchiffon','lightblue','lightcoral','lightcyan','lightgoldenrodyellow','lightgray','lightgreen','lightgrey','lightpink','lightsalmon','lightseagreen','lightskyblue','lightslategray','lightslategrey','lightsteelblue','lightyellow','lime','limegreen','linen','magenta','maroon','mediumaquamarine','mediumblue','mediumorchid','mediumpurple','mediumseagreen','mediumslateblue','mediumspringgreen','mediumturquoise','mediumvioletred','midnightblue','mintcream','mistyrose','moccasin','navajowhite','navy','oldlace','olive','olivedrab','orange','orangered','orchid','palegoldenrod','palegreen','paleturquoise','palevioletred','papayawhip','peachpuff','peru','pink','plum','powderblue','purple','red','rosybrown','royalblue','saddlebrown','salmon','sandybrown','seagreen','seashell','sienna','silver','skyblue','slateblue','slategray','slategrey','snow','springgreen','steelblue','tan','teal','thistle','tomato','transparent','turquoise','violet','wheat','white','whitesmoke','yellow','yellowgreen'],n=function(e){return /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(e)},r=function(e){return /^hsl\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},a=function(e){return /^hsla\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)},l=function(e){return t.indexOf(e)>=0},i=function(e){return /^rgb\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){2}(\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*)\)$/.test(e)||/^rgb\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},o=function(e){return /^rgba\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)||/^rgba\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)};return{validate:function(t){if(t.value===''){return{valid:!0}}var s=typeof t.options.type==='string'?t.options.type.toString().replace(/s/g,'').split(','):t.options.type||e,c=!0,u=!1,d=void 0;try{for(var f=s[Symbol.iterator](),m;!(c=(m=f.next()).done);c=!0){var g=m.value,p=g.toLowerCase();if(e.indexOf(p)===-1){continue}var h=!0;switch(p){case 'hex':h=n(t.value);break;case 'hsl':h=r(t.value);break;case 'hsla':h=a(t.value);break;case 'keyword':h=l(t.value);break;case 'rgb':h=i(t.value);break;case 'rgba':h=o(t.value);break}if(h){return{valid:!0}}}}catch(e){u=!0,d=e}finally{try{!c&&f["return"]!=null&&f["return"]()}finally{if(u){throw d}}};return{valid:!1}}}}function X(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=e.value.toUpperCase();if(!/^[0-9A-Z]{9}$/.test(t)){return{valid:!1}}var n=t.split('').map(function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-65+10+'':e}),r=n.length,a=0;for(var l=0;l<r-1;l++){var i=parseInt(n[l],10);l%2!==0&&(i*=2);i>9&&(i-=9);a+=i}a=(10-a%10)%10;return{valid:a===parseInt(n[r-1],10)}}}}function Q(){return{validate:function(e){if(e.value===''){return{valid:!0}}if(!/^(\d{8}|\d{12}|\d{13}|\d{14})$/.test(e.value)){return{valid:!1}}var t=e.value.length,n=0,r=t===8?[3,1]:[1,3];for(var a=0;a<t-1;a++)n+=parseInt(e.value.charAt(a),10)*r[a%2];n=(10-n%10)%10;return{valid:"".concat(n)===e.value.charAt(t-1)}}}}function q(){var e={ANDOVER:['10','12'],ATLANTA:['60','67'],AUSTIN:['50','53'],BROOKHAVEN:['01','02','03','04','05','06','11','13','14','16','21','22','23','25','34','51','52','54','55','56','57','58','59','65'],CINCINNATI:['30','32','35','36','37','38','61'],FRESNO:['15','24'],INTERNET:['20','26','27','45','46','47'],KANSAS_CITY:['40','44'],MEMPHIS:['94','95'],OGDEN:['80','90'],PHILADELPHIA:['33','39','41','42','43','48','62','63','64','66','68','71','72','73','74','75','76','77','81','82','83','84','85','86','87','88','91','92','93','98','99'],SMALL_BUSINESS_ADMINISTRATION:['31']};return{validate:function(t){if(t.value===''){return{meta:null,valid:!0}}if(!/^[0-9]{2}-?[0-9]{7}$/.test(t.value)){return{meta:null,valid:!1}}var n="".concat(t.value.substr(0,2));for(var r in e){if(e[r].indexOf(n)!==-1){return{meta:{campus:r},valid:!0}}}return{meta:null,valid:!1}}}}function $(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=e.value.toUpperCase();if(!/^[GRID:]*([0-9A-Z]{2})[-\s]*([0-9A-Z]{5})[-\s]*([0-9A-Z]{10})[-\s]*([0-9A-Z]{1})$/g.test(t)){return{valid:!1}}t=t.replace(/\s/g,'').replace(/-/g,'');'GRID:'===t.substr(0,5)&&(t=t.substr(5));return{valid:r(t)}}}}function j(){return{validate:function(e){return{valid:e.value===''||/^[0-9a-fA-F]+$/.test(e.value)}}}}function ee(){var e={AD:'AD[0-9]{2}[0-9]{4}[0-9]{4}[A-Z0-9]{12}',AE:'AE[0-9]{2}[0-9]{3}[0-9]{16}',AL:'AL[0-9]{2}[0-9]{8}[A-Z0-9]{16}',AO:'AO[0-9]{2}[0-9]{21}',AT:'AT[0-9]{2}[0-9]{5}[0-9]{11}',AZ:'AZ[0-9]{2}[A-Z]{4}[A-Z0-9]{20}',BA:'BA[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{8}[0-9]{2}',BE:'BE[0-9]{2}[0-9]{3}[0-9]{7}[0-9]{2}',BF:'BF[0-9]{2}[0-9]{23}',BG:'BG[0-9]{2}[A-Z]{4}[0-9]{4}[0-9]{2}[A-Z0-9]{8}',BH:'BH[0-9]{2}[A-Z]{4}[A-Z0-9]{14}',BI:'BI[0-9]{2}[0-9]{12}',BJ:'BJ[0-9]{2}[A-Z]{1}[0-9]{23}',BR:'BR[0-9]{2}[0-9]{8}[0-9]{5}[0-9]{10}[A-Z][A-Z0-9]',CH:'CH[0-9]{2}[0-9]{5}[A-Z0-9]{12}',CI:'CI[0-9]{2}[A-Z]{1}[0-9]{23}',CM:'CM[0-9]{2}[0-9]{23}',CR:'CR[0-9]{2}[0-9][0-9]{3}[0-9]{14}',CV:'CV[0-9]{2}[0-9]{21}',CY:'CY[0-9]{2}[0-9]{3}[0-9]{5}[A-Z0-9]{16}',CZ:'CZ[0-9]{2}[0-9]{20}',DE:'DE[0-9]{2}[0-9]{8}[0-9]{10}',DK:'DK[0-9]{2}[0-9]{14}',DO:'DO[0-9]{2}[A-Z0-9]{4}[0-9]{20}',DZ:'DZ[0-9]{2}[0-9]{20}',EE:'EE[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{11}[0-9]{1}',ES:'ES[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{1}[0-9]{1}[0-9]{10}',FI:'FI[0-9]{2}[0-9]{6}[0-9]{7}[0-9]{1}',FO:'FO[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}',FR:'FR[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}',GB:'GB[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}',GE:'GE[0-9]{2}[A-Z]{2}[0-9]{16}',GI:'GI[0-9]{2}[A-Z]{4}[A-Z0-9]{15}',GL:'GL[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}',GR:'GR[0-9]{2}[0-9]{3}[0-9]{4}[A-Z0-9]{16}',GT:'GT[0-9]{2}[A-Z0-9]{4}[A-Z0-9]{20}',HR:'HR[0-9]{2}[0-9]{7}[0-9]{10}',HU:'HU[0-9]{2}[0-9]{3}[0-9]{4}[0-9]{1}[0-9]{15}[0-9]{1}',IE:'IE[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}',IL:'IL[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{13}',IR:'IR[0-9]{2}[0-9]{22}',IS:'IS[0-9]{2}[0-9]{4}[0-9]{2}[0-9]{6}[0-9]{10}',IT:'IT[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}',JO:'JO[0-9]{2}[A-Z]{4}[0-9]{4}[0]{8}[A-Z0-9]{10}',KW:'KW[0-9]{2}[A-Z]{4}[0-9]{22}',KZ:'KZ[0-9]{2}[0-9]{3}[A-Z0-9]{13}',LB:'LB[0-9]{2}[0-9]{4}[A-Z0-9]{20}',LI:'LI[0-9]{2}[0-9]{5}[A-Z0-9]{12}',LT:'LT[0-9]{2}[0-9]{5}[0-9]{11}',LU:'LU[0-9]{2}[0-9]{3}[A-Z0-9]{13}',LV:'LV[0-9]{2}[A-Z]{4}[A-Z0-9]{13}',MC:'MC[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}',MD:'MD[0-9]{2}[A-Z0-9]{20}',ME:'ME[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}',MG:'MG[0-9]{2}[0-9]{23}',MK:'MK[0-9]{2}[0-9]{3}[A-Z0-9]{10}[0-9]{2}',ML:'ML[0-9]{2}[A-Z]{1}[0-9]{23}',MR:'MR13[0-9]{5}[0-9]{5}[0-9]{11}[0-9]{2}',MT:'MT[0-9]{2}[A-Z]{4}[0-9]{5}[A-Z0-9]{18}',MU:'MU[0-9]{2}[A-Z]{4}[0-9]{2}[0-9]{2}[0-9]{12}[0-9]{3}[A-Z]{3}',MZ:'MZ[0-9]{2}[0-9]{21}',NL:'NL[0-9]{2}[A-Z]{4}[0-9]{10}',NO:'NO[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{1}',PK:'PK[0-9]{2}[A-Z]{4}[A-Z0-9]{16}',PL:'PL[0-9]{2}[0-9]{8}[0-9]{16}',PS:'PS[0-9]{2}[A-Z]{4}[A-Z0-9]{21}',PT:'PT[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{11}[0-9]{2}',QA:'QA[0-9]{2}[A-Z]{4}[A-Z0-9]{21}',RO:'RO[0-9]{2}[A-Z]{4}[A-Z0-9]{16}',RS:'RS[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}',SA:'SA[0-9]{2}[0-9]{2}[A-Z0-9]{18}',SE:'SE[0-9]{2}[0-9]{3}[0-9]{16}[0-9]{1}',SI:'SI[0-9]{2}[0-9]{5}[0-9]{8}[0-9]{2}',SK:'SK[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{10}',SM:'SM[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}',SN:'SN[0-9]{2}[A-Z]{1}[0-9]{23}',TL:'TL38[0-9]{3}[0-9]{14}[0-9]{2}',TN:'TN59[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}',TR:'TR[0-9]{2}[0-9]{5}[A-Z0-9]{1}[A-Z0-9]{16}',VG:'VG[0-9]{2}[A-Z]{4}[0-9]{16}',XK:'XK[0-9]{2}[0-9]{4}[0-9]{10}[0-9]{2}'},t=['AT','BE','BG','CH','CY','CZ','DE','DK','EE','ES','FI','FR','GB','GI','GR','HR','HU','IE','IS','IT','LI','LT','LU','LV','MC','MT','NL','NO','PL','PT','RO','SE','SI','SK','SM'];return{validate:function(n){if(n.value===''){return{valid:!0}}var r=Object.assign({},{message:''},n.options),a=n.value.replace(/[^a-zA-Z0-9]/g,'').toUpperCase(),l=r.country||a.substr(0,2);if(!e[l]){return{message:r.message,valid:!1}}if(r.sepa!==void 0){var i=t.indexOf(l)!==-1;if((r.sepa==='true'||r.sepa===!0)&&!i||(r.sepa==='false'||r.sepa===!1)&&i){return{message:r.message,valid:!1}}}var o=A(n.l10n?r.message||n.l10n.iban.country:r.message,n.l10n?n.l10n.iban.countries[l]:l);if(!new RegExp("^".concat(e[l],"$")).test(n.value)){return{message:o,valid:!1}}a="".concat(a.substr(4)).concat(a.substr(0,4));a=a.split('').map(function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-65+10:e}).join('');var s=parseInt(a.substr(0,1),10),c=a.length;for(var u=1;u<c;++u)s=(s*10+parseInt(a.substr(u,1),10))%97;return{message:o,valid:s===1}}}}function te(e){var t=e.replace(/\./g,'');return{meta:{},valid:/^\d{7,8}$/.test(t)}}function ne(e,t){if(!/^\d{13}$/.test(e)){return!1}var n=parseInt(e.substr(0,2),10),r=parseInt(e.substr(2,2),10),a=parseInt(e.substr(7,2),10),l=parseInt(e.substr(12,1),10);if(n>31||r>12){return!1}var i=0;for(var o=0;o<6;o++)i+=(7-o)*(parseInt(e.charAt(o),10)+parseInt(e.charAt(o+6),10));i=11-i%11;(i===10||i===11)&&(i=0);if(i!==l){return!1}switch(t.toUpperCase()){case 'BA':return 10<=a&&a<=19;case 'MK':return 41<=a&&a<=49;case 'ME':return 20<=a&&a<=29;case 'RS':return 70<=a&&a<=99;case 'SI':return 50<=a&&a<=59;default:return!0}}function re(e){return{meta:{},valid:ne(e,'BA')}}function ae(e){if(!/^\d{10}$/.test(e)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(e)){return{meta:{},valid:!1}}var t=e.replace(/\s/g,''),n=parseInt(t.substr(0,2),10)+1900,r=parseInt(t.substr(2,2),10),a=parseInt(t.substr(4,2),10);r>40?(n+=100,r-=40):r>20&&(n-=100,r-=20);if(!F(n,r,a)){return{meta:{},valid:!1}}var l=0,i=[2,4,8,5,10,9,7,3,6];for(var o=0;o<9;o++)l+=parseInt(t.charAt(o),10)*i[o];l=l%11%10;return{meta:{},valid:"".concat(l)===t.substr(9,1)}}function le(e){var t=e.replace(/\D/g,'');if(!/^\d{11}$/.test(t)||/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(t)){return{meta:{},valid:!1}}var n=0,r;for(r=0;r<9;r++)n+=(10-r)*parseInt(t.charAt(r),10);n=11-n%11;(n===10||n===11)&&(n=0);if("".concat(n)!==t.charAt(9)){return{meta:{},valid:!1}}var a=0;for(r=0;r<10;r++)a+=(11-r)*parseInt(t.charAt(r),10);a=11-a%11;(a===10||a===11)&&(a=0);return{meta:{},valid:"".concat(a)===t.charAt(10)}}function ie(e){if(!/^756[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{4}[\.]{0,1}[0-9]{2}$/.test(e)){return{meta:{},valid:!1}}var t=e.replace(/\D/g,'').substr(3),n=t.length,r=n===8?[3,1]:[1,3],a=0;for(var l=0;l<n-1;l++)a+=parseInt(t.charAt(l),10)*r[l%2];a=10-a%10;return{meta:{},valid:"".concat(a)===t.charAt(n-1)}}function oe(e){if(!/^\d{7,8}[-]{0,1}[0-9K]$/i.test(e)){return{meta:{},valid:!1}}var t=e.replace(/\-/g,'');while(t.length<9)t="0".concat(t);var n=[3,2,7,6,5,4,3,2],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r=11-r%11;var l="".concat(r);r===11?(l='0'):r===10&&(l='K');return{meta:{},valid:l===t.charAt(8).toUpperCase()}}function se(e){var t=e.trim();if(!/^\d{15}$/.test(t)&&!/^\d{17}[\dXx]{1}$/.test(t)){return{meta:{},valid:!1}}var n={11:{0:[0],1:[[0,9],[11,17]],2:[0,28,29]},12:{0:[0],1:[[0,16]],2:[0,21,23,25]},13:{0:[0],1:[[0,5],7,8,21,[23,33],[81,85]],2:[[0,5],[7,9],[23,25],27,29,30,81,83],3:[[0,4],[21,24]],4:[[0,4],6,21,[23,35],81],5:[[0,3],[21,35],81,82],6:[[0,4],[21,38],[81,84]],7:[[0,3],5,6,[21,33]],8:[[0,4],[21,28]],9:[[0,3],[21,30],[81,84]],10:[[0,3],[22,26],28,81,82],11:[[0,2],[21,28],81,82]},14:{0:[0],1:[0,1,[5,10],[21,23],81],2:[[0,3],11,12,[21,27]],3:[[0,3],11,21,22],4:[[0,2],11,21,[23,31],81],5:[[0,2],21,22,24,25,81],6:[[0,3],[21,24]],7:[[0,2],[21,29],81],8:[[0,2],[21,30],81,82],9:[[0,2],[21,32],81],10:[[0,2],[21,34],81,82],11:[[0,2],[21,30],81,82],23:[[0,3],22,23,[25,30],32,33]},15:{0:[0],1:[[0,5],[21,25]],2:[[0,7],[21,23]],3:[[0,4]],4:[[0,4],[21,26],[28,30]],5:[[0,2],[21,26],81],6:[[0,2],[21,27]],7:[[0,3],[21,27],[81,85]],8:[[0,2],[21,26]],9:[[0,2],[21,29],81],22:[[0,2],[21,24]],25:[[0,2],[22,31]],26:[[0,2],[24,27],[29,32],34],28:[0,1,[22,27]],29:[0,[21,23]]},21:{0:[0],1:[[0,6],[11,14],[22,24],81],2:[[0,4],[11,13],24,[81,83]],3:[[0,4],11,21,23,81],4:[[0,4],11,[21,23]],5:[[0,5],21,22],6:[[0,4],24,81,82],7:[[0,3],11,26,27,81,82],8:[[0,4],11,81,82],9:[[0,5],11,21,22],10:[[0,5],11,21,81],11:[[0,3],21,22],12:[[0,2],4,21,23,24,81,82],13:[[0,3],21,22,24,81,82],14:[[0,4],21,22,81]},22:{0:[0],1:[[0,6],12,22,[81,83]],2:[[0,4],11,21,[81,84]],3:[[0,3],22,23,81,82],4:[[0,3],21,22],5:[[0,3],21,23,24,81,82],6:[[0,2],4,5,[21,23],25,81],7:[[0,2],[21,24],81],8:[[0,2],21,22,81,82],24:[[0,6],24,26]},23:{0:[0],1:[[0,12],21,[23,29],[81,84]],2:[[0,8],21,[23,25],27,[29,31],81],3:[[0,7],21,81,82],4:[[0,7],21,22],5:[[0,3],5,6,[21,24]],6:[[0,6],[21,24]],7:[[0,16],22,81],8:[[0,5],11,22,26,28,33,81,82],9:[[0,4],21],10:[[0,5],24,25,81,[83,85]],11:[[0,2],21,23,24,81,82],12:[[0,2],[21,26],[81,83]],27:[[0,4],[21,23]]},31:{0:[0],1:[0,1,[3,10],[12,20]],2:[0,30]},32:{0:[0],1:[[0,7],11,[13,18],24,25],2:[[0,6],11,81,82],3:[[0,5],11,12,[21,24],81,82],4:[[0,2],4,5,11,12,81,82],5:[[0,9],[81,85]],6:[[0,2],11,12,21,23,[81,84]],7:[0,1,3,5,6,[21,24]],8:[[0,4],11,26,[29,31]],9:[[0,3],[21,25],28,81,82],10:[[0,3],11,12,23,81,84,88],11:[[0,2],11,12,[81,83]],12:[[0,4],[81,84]],13:[[0,2],11,[21,24]]},33:{0:[0],1:[[0,6],[8,10],22,27,82,83,85],2:[0,1,[3,6],11,12,25,26,[81,83]],3:[[0,4],22,24,[26,29],81,82],4:[[0,2],11,21,24,[81,83]],5:[[0,3],[21,23]],6:[[0,2],21,24,[81,83]],7:[[0,3],23,26,27,[81,84]],8:[[0,3],22,24,25,81],9:[[0,3],21,22],10:[[0,4],[21,24],81,82],11:[[0,2],[21,27],81]},34:{0:[0],1:[[0,4],11,[21,24],81],2:[[0,4],7,8,[21,23],25],3:[[0,4],11,[21,23]],4:[[0,6],21],5:[[0,4],6,[21,23]],6:[[0,4],21],7:[[0,3],11,21],8:[[0,3],11,[22,28],81],10:[[0,4],[21,24]],11:[[0,3],22,[24,26],81,82],12:[[0,4],21,22,25,26,82],13:[[0,2],[21,24]],14:[[0,2],[21,24]],15:[[0,3],[21,25]],16:[[0,2],[21,23]],17:[[0,2],[21,23]],18:[[0,2],[21,25],81]},35:{0:[0],1:[[0,5],11,[21,25],28,81,82],2:[[0,6],[11,13]],3:[[0,5],22],4:[[0,3],21,[23,30],81],5:[[0,5],21,[24,27],[81,83]],6:[[0,3],[22,29],81],7:[[0,2],[21,25],[81,84]],8:[[0,2],[21,25],81],9:[[0,2],[21,26],81,82]},36:{0:[0],1:[[0,5],11,[21,24]],2:[[0,3],22,81],3:[[0,2],13,[21,23]],4:[[0,3],21,[23,30],81,82],5:[[0,2],21],6:[[0,2],22,81],7:[[0,2],[21,35],81,82],8:[[0,3],[21,30],81],9:[[0,2],[21,26],[81,83]],10:[[0,2],[21,30]],11:[[0,2],[21,30],81]},37:{0:[0],1:[[0,5],12,13,[24,26],81],2:[[0,3],5,[11,14],[81,85]],3:[[0,6],[21,23]],4:[[0,6],81],5:[[0,3],[21,23]],6:[[0,2],[11,13],34,[81,87]],7:[[0,5],24,25,[81,86]],8:[[0,2],11,[26,32],[81,83]],9:[[0,3],11,21,23,82,83],10:[[0,2],[81,83]],11:[[0,3],21,22],12:[[0,3]],13:[[0,2],11,12,[21,29]],14:[[0,2],[21,28],81,82],15:[[0,2],[21,26],81],16:[[0,2],[21,26]],17:[[0,2],[21,28]]},41:{0:[0],1:[[0,6],8,22,[81,85]],2:[[0,5],11,[21,25]],3:[[0,7],11,[22,29],81],4:[[0,4],11,[21,23],25,81,82],5:[[0,3],5,6,22,23,26,27,81],6:[[0,3],11,21,22],7:[[0,4],11,21,[24,28],81,82],8:[[0,4],11,[21,23],25,[81,83]],9:[[0,2],22,23,[26,28]],10:[[0,2],[23,25],81,82],11:[[0,4],[21,23]],12:[[0,2],21,22,24,81,82],13:[[0,3],[21,30],81],14:[[0,3],[21,26],81],15:[[0,3],[21,28]],16:[[0,2],[21,28],81],17:[[0,2],[21,29]],90:[0,1]},42:{0:[0],1:[[0,7],[11,17]],2:[[0,5],22,81],3:[[0,3],[21,25],81],5:[[0,6],[25,29],[81,83]],6:[[0,2],6,7,[24,26],[82,84]],7:[[0,4]],8:[[0,2],4,21,22,81],9:[[0,2],[21,23],81,82,84],10:[[0,3],[22,24],81,83,87],11:[[0,2],[21,27],81,82],12:[[0,2],[21,24],81],13:[[0,3],21,81],28:[[0,2],22,23,[25,28]],90:[0,[4,6],21]},43:{0:[0],1:[[0,5],11,12,21,22,24,81],2:[[0,4],11,21,[23,25],81],3:[[0,2],4,21,81,82],4:[0,1,[5,8],12,[21,24],26,81,82],5:[[0,3],11,[21,25],[27,29],81],6:[[0,3],11,21,23,24,26,81,82],7:[[0,3],[21,26],81],8:[[0,2],11,21,22],9:[[0,3],[21,23],81],10:[[0,3],[21,28],81],11:[[0,3],[21,29]],12:[[0,2],[21,30],81],13:[[0,2],21,22,81,82],31:[0,1,[22,27],30]},44:{0:[0],1:[[0,7],[11,16],83,84],2:[[0,5],21,22,24,29,32,33,81,82],3:[0,1,[3,8]],4:[[0,4]],5:[0,1,[6,15],23,82,83],6:[0,1,[4,8]],7:[0,1,[3,5],81,[83,85]],8:[[0,4],11,23,25,[81,83]],9:[[0,3],23,[81,83]],12:[[0,3],[23,26],83,84],13:[[0,3],[22,24],81],14:[[0,2],[21,24],26,27,81],15:[[0,2],21,23,81],16:[[0,2],[21,25]],17:[[0,2],21,23,81],18:[[0,3],21,23,[25,27],81,82],19:[0],20:[0],51:[[0,3],21,22],52:[[0,3],21,22,24,81],53:[[0,2],[21,23],81]},45:{0:[0],1:[[0,9],[21,27]],2:[[0,5],[21,26]],3:[[0,5],11,12,[21,32]],4:[0,1,[3,6],11,[21,23],81],5:[[0,3],12,21],6:[[0,3],21,81],7:[[0,3],21,22],8:[[0,4],21,81],9:[[0,3],[21,24],81],10:[[0,2],[21,31]],11:[[0,2],[21,23]],12:[[0,2],[21,29],81],13:[[0,2],[21,24],81],14:[[0,2],[21,25],81]},46:{0:[0],1:[0,1,[5,8]],2:[0,1],3:[0,[21,23]],90:[[0,3],[5,7],[21,39]]},50:{0:[0],1:[[0,19]],2:[0,[22,38],[40,43]],3:[0,[81,84]]},51:{0:[0],1:[0,1,[4,8],[12,15],[21,24],29,31,32,[81,84]],3:[[0,4],11,21,22],4:[[0,3],11,21,22],5:[[0,4],21,22,24,25],6:[0,1,3,23,26,[81,83]],7:[0,1,3,4,[22,27],81],8:[[0,2],11,12,[21,24]],9:[[0,4],[21,23]],10:[[0,2],11,24,25,28],11:[[0,2],[11,13],23,24,26,29,32,33,81],13:[[0,4],[21,25],81],14:[[0,2],[21,25]],15:[[0,3],[21,29]],16:[[0,3],[21,23],81],17:[[0,3],[21,25],81],18:[[0,3],[21,27]],19:[[0,3],[21,23]],20:[[0,2],21,22,81],32:[0,[21,33]],33:[0,[21,38]],34:[0,1,[22,37]]},52:{0:[0],1:[[0,3],[11,15],[21,23],81],2:[0,1,3,21,22],3:[[0,3],[21,30],81,82],4:[[0,2],[21,25]],5:[[0,2],[21,27]],6:[[0,3],[21,28]],22:[0,1,[22,30]],23:[0,1,[22,28]],24:[0,1,[22,28]],26:[0,1,[22,36]],27:[[0,2],22,23,[25,32]]},53:{0:[0],1:[[0,3],[11,14],21,22,[24,29],81],3:[[0,2],[21,26],28,81],4:[[0,2],[21,28]],5:[[0,2],[21,24]],6:[[0,2],[21,30]],7:[[0,2],[21,24]],8:[[0,2],[21,29]],9:[[0,2],[21,27]],23:[0,1,[22,29],31],25:[[0,4],[22,32]],26:[0,1,[21,28]],27:[0,1,[22,30]],28:[0,1,22,23],29:[0,1,[22,32]],31:[0,2,3,[22,24]],34:[0,[21,23]],33:[0,21,[23,25]],35:[0,[21,28]]},54:{0:[0],1:[[0,2],[21,27]],21:[0,[21,29],32,33],22:[0,[21,29],[31,33]],23:[0,1,[22,38]],24:[0,[21,31]],25:[0,[21,27]],26:[0,[21,27]]},61:{0:[0],1:[[0,4],[11,16],22,[24,26]],2:[[0,4],22],3:[[0,4],[21,24],[26,31]],4:[[0,4],[22,31],81],5:[[0,2],[21,28],81,82],6:[[0,2],[21,32]],7:[[0,2],[21,30]],8:[[0,2],[21,31]],9:[[0,2],[21,29]],10:[[0,2],[21,26]]},62:{0:[0],1:[[0,5],11,[21,23]],2:[0,1],3:[[0,2],21],4:[[0,3],[21,23]],5:[[0,3],[21,25]],6:[[0,2],[21,23]],7:[[0,2],[21,25]],8:[[0,2],[21,26]],9:[[0,2],[21,24],81,82],10:[[0,2],[21,27]],11:[[0,2],[21,26]],12:[[0,2],[21,28]],24:[0,21,[24,29]],26:[0,21,[23,30]],29:[0,1,[21,27]],30:[0,1,[21,27]]},63:{0:[0],1:[[0,5],[21,23]],2:[0,2,[21,25]],21:[0,[21,23],[26,28]],22:[0,[21,24]],23:[0,[21,24]],25:[0,[21,25]],26:[0,[21,26]],27:[0,1,[21,26]],28:[[0,2],[21,23]]},64:{0:[0],1:[0,1,[4,6],21,22,81],2:[[0,3],5,[21,23]],3:[[0,3],[21,24],81],4:[[0,2],[21,25]],5:[[0,2],21,22]},65:{0:[0],1:[[0,9],21],2:[[0,5]],21:[0,1,22,23],22:[0,1,22,23],23:[[0,3],[23,25],27,28],28:[0,1,[22,29]],29:[0,1,[22,29]],30:[0,1,[22,24]],31:[0,1,[21,31]],32:[0,1,[21,27]],40:[0,2,3,[21,28]],42:[[0,2],21,[23,26]],43:[0,1,[21,26]],90:[[0,4]],27:[[0,2],22,23]},71:{0:[0]},81:{0:[0]},82:{0:[0]}},r=parseInt(t.substr(0,2),10),a=parseInt(t.substr(2,2),10),l=parseInt(t.substr(4,2),10);if(!n[r]||!n[r][a]){return{meta:{},valid:!1}}var i=!1,o=n[r][a],s;for(s=0;s<o.length;s++){if(Array.isArray(o[s])&&o[s][0]<=l&&l<=o[s][1]||!Array.isArray(o[s])&&l===o[s]){i=!0;break}}if(!i){return{meta:{},valid:!1}}var c;t.length===18?(c=t.substr(6,8)):(c="19".concat(t.substr(6,6)));var u=parseInt(c.substr(0,4),10),d=parseInt(c.substr(4,2),10),f=parseInt(c.substr(6,2),10);if(!F(u,d,f)){return{meta:{},valid:!1}}if(t.length===18){var m=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],g=0;for(s=0;s<17;s++)g+=parseInt(t.charAt(s),10)*m[s];g=(12-g%11)%11;var p=t.charAt(17).toUpperCase()!=='X'?parseInt(t.charAt(17),10):10;return{meta:{},valid:p===g}}return{meta:{},valid:!0}}function ce(e){var t=e.replace(/\./g,'').replace('-','');if(!/^\d{8,16}$/.test(t)){return{meta:{},valid:!1}}var n=t.length,r=[3,7,13,17,19,23,29,37,41,43,47,53,59,67,71],a=0;for(var l=n-2;l>=0;l--)a+=parseInt(t.charAt(l),10)*r[l];a%=11;a>=2&&(a=11-a);return{meta:{},valid:"".concat(a)===t.substr(n-1)}}function ue(e){if(!/^\d{9,10}$/.test(e)){return{meta:{},valid:!1}}var t=1900+parseInt(e.substr(0,2),10),n=parseInt(e.substr(2,2),10)%50%20,r=parseInt(e.substr(4,2),10);if(e.length===9){t>=1980&&(t-=100);if(t>1953){return{meta:{},valid:!1}}}else t<1954&&(t+=100);if(!F(t,n,r)){return{meta:{},valid:!1}}if(e.length===10){var a=parseInt(e.substr(0,9),10)%11;t<1985&&(a%=10);return{meta:{},valid:"".concat(a)===e.substr(9,1)}}return{meta:{},valid:!0}}function de(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e)){return{meta:{},valid:!1}}var t=e.replace(/-/g,''),n=parseInt(t.substr(0,2),10),r=parseInt(t.substr(2,2),10),a=parseInt(t.substr(4,2),10);switch(!0){case '5678'.indexOf(t.charAt(6))!==-1&&a>=58:a+=1800;break;case '0123'.indexOf(t.charAt(6))!==-1:;case '49'.indexOf(t.charAt(6))!==-1&&a>=37:a+=1900;break;default:a+=2e3;break}return{meta:{},valid:F(a,r,n)}}function fe(e){var t=/^[0-9]{8}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),n=/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),r=/^[A-HNPQS][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-J]$/.test(e);if(!t&&!n&&!r){return{meta:{},valid:!1}}var a=e.replace(/-/g,''),l,i,o=!0;if(t||n){i='DNI';var s='XYZ'.indexOf(a.charAt(0));s!==-1&&(a=s+a.substr(1)+'',i='NIE');l=parseInt(a.substr(0,8),10);l='TRWAGMYFPDXBNJZSQVHLCKE'[l%23];return{meta:{type:i},valid:l===a.substr(8,1)}}else{l=a.substr(1,7);i='CIF';var c=a[0],u=a.substr(-1),d=0;for(var f=0;f<l.length;f++){if(f%2!==0)d+=parseInt(l[f],10);else{var m=''+parseInt(l[f],10)*2;d+=parseInt(m[0],10);m.length===2&&(d+=parseInt(m[1],10))}}var g=d-Math.floor(d/10)*10;g!==0&&(g=10-g);'KQS'.indexOf(c)!==-1?(o=u==='JABCDEFGHI'[g]):'ABEH'.indexOf(c)!==-1?(o=u===''+g):(o=u===''+g||u==='JABCDEFGHI'[g]);return{meta:{type:i},valid:o}}}function me(e){if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(e)){return{meta:{},valid:!1}}var t=parseInt(e.substr(0,2),10),n=parseInt(e.substr(2,2),10),r=parseInt(e.substr(4,2),10),a={'+':1800,'-':1900,'A':2e3};r=a[e.charAt(6)]+r;if(!F(r,n,t)){return{meta:{},valid:!1}}var l=parseInt(e.substr(7,3),10);if(l<2){return{meta:{},valid:!1}}var i=parseInt(e.substr(0,6)+e.substr(7,3)+'',10);return{meta:{},valid:'0123456789ABCDEFHJKLMNPRSTUVWXY'.charAt(i%31)===e.charAt(10)}}function ge(e){var t=e.toUpperCase();if(!/^(1|2)\d{2}\d{2}(\d{2}|\d[A-Z]|\d{3})\d{2,3}\d{3}\d{2}$/.test(t)){return{meta:{},valid:!1}}var n=t.substr(5,2);switch(!0){case /^\d{2}$/.test(n):t=e;break;case n==='2A':t="".concat(e.substr(0,5),"19").concat(e.substr(7));break;case n==='2B':t="".concat(e.substr(0,5),"18").concat(e.substr(7));break;default:return{meta:{},valid:!1}}var r=97-parseInt(t.substr(0,13),10)%97,a=r<10?"0".concat(r):"".concat(r);return{meta:{},valid:a===t.substr(13)}}function pe(e){var t=e.toUpperCase();if(!/^[A-MP-Z]{1,2}[0-9]{6}[0-9A]$/.test(t)){return{meta:{},valid:!1}}var n='ABCDEFGHIJKLMNOPQRSTUVWXYZ',r=t.charAt(0),a=t.charAt(1),l=0,i=t;/^[A-Z]$/.test(a)?(l+=9*(10+n.indexOf(r)),l+=8*(10+n.indexOf(a)),i=t.substr(2)):(l+=324,l+=8*(10+n.indexOf(r)),i=t.substr(1));var o=i.length;for(var s=0;s<o-1;s++)l+=(7-s)*parseInt(i.charAt(s),10);var c=l%11,u=c===0?'0':11-c===10?'A':"".concat(11-c);return{meta:{},valid:u===i.charAt(o-1)}}function he(e){return{meta:{},valid:/^[0-9]{11}$/.test(e)&&n(e)}}function ve(e){if(!/^[2-9]\d{11}$/.test(e)){return{meta:{},valid:!1}}var t=e.split("").map(function(e){return parseInt(e,10)});return{meta:{},valid:a(t)}}function Ae(e){if(!/^\d{7}[A-W][AHWTX]?$/.test(e)){return{meta:{},valid:!1}}var t=function(e){var t=e;while(t.length<7)t="0".concat(t);var n='WABCDEFGHIJKLMNOPQRSTUV',r=0;for(var a=0;a<7;a++)r+=parseInt(t.charAt(a),10)*(8-a);r+=9*n.indexOf(t.substr(7));return n[r%23]},n=e.length===9&&('A'===e.charAt(8)||'H'===e.charAt(8))?e.charAt(7)===t(e.substr(0,7)+e.substr(8)+''):e.charAt(7)===t(e.substr(0,7));return{meta:{},valid:n}}function be(e){if(!/^\d{1,9}$/.test(e)){return{meta:{},valid:!1}}return{meta:{},valid:t(e)}}function Ee(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e)){return{meta:{},valid:!1}}var t=e.replace(/-/g,''),n=parseInt(t.substr(0,2),10),r=parseInt(t.substr(2,2),10),a=parseInt(t.substr(4,2),10),l=parseInt(t.charAt(9),10);a=l===9?1900+a:(20+l)*100+a;if(!F(a,r,n,!0)){return{meta:{},valid:!1}}var i=[3,2,7,6,5,4,3,2],o=0;for(var s=0;s<8;s++)o+=parseInt(t.charAt(s),10)*i[s];o=11-o%11;return{meta:{},valid:"".concat(o)===t.charAt(8)}}function Ce(e){var t=e.replace('-','');if(!/^\d{13}$/.test(t)){return{meta:{},valid:!1}}var n=t.charAt(6),r=parseInt(t.substr(0,2),10),a=parseInt(t.substr(2,2),10),l=parseInt(t.substr(4,2),10);switch(n){case '1':;case '2':;case '5':;case '6':r+=1900;break;case '3':;case '4':;case '7':;case '8':r+=2e3;break;default:r+=1800;break}if(!F(r,a,l)){return{meta:{},valid:!1}}var i=[2,3,4,5,6,7,8,9,2,3,4,5],o=t.length,s=0;for(var c=0;c<o-1;c++)s+=i[c]*parseInt(t.charAt(c),10);var u=(11-s%11)%10;return{meta:{},valid:"".concat(u)===t.charAt(o-1)}}function Ve(e){if(!/^[0-9]{11}$/.test(e)){return{meta:{},valid:!1}}var t=parseInt(e.charAt(0),10),n=parseInt(e.substr(1,2),10),r=parseInt(e.substr(3,2),10),a=parseInt(e.substr(5,2),10),l=t%2===0?17+t/2:17+(t+1)/2;n=l*100+n;if(!F(n,r,a,!0)){return{meta:{},valid:!1}}var i=[1,2,3,4,5,6,7,8,9,1],o=0,s;for(s=0;s<10;s++)o+=parseInt(e.charAt(s),10)*i[s];o%=11;if(o!==10){return{meta:{},valid:"".concat(o)===e.charAt(10)}}o=0;i=[3,4,5,6,7,8,9,1,2,3];for(s=0;s<10;s++)o+=parseInt(e.charAt(s),10)*i[s];o%=11;o===10&&(o=0);return{meta:{},valid:"".concat(o)===e.charAt(10)}}function Se(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(e)){return{meta:{},valid:!1}}var t=e.replace(/\D/g,''),n=parseInt(t.substr(0,2),10),r=parseInt(t.substr(2,2),10),a=parseInt(t.substr(4,2),10);a=a+1800+parseInt(t.charAt(6),10)*100;if(!F(a,r,n,!0)){return{meta:{},valid:!1}}var l=0,i=[10,5,8,4,2,1,6,3,7,9];for(var o=0;o<10;o++)l+=parseInt(t.charAt(o),10)*i[o];l=(l+1)%11%10;return{meta:{},valid:"".concat(l)===t.charAt(10)}}function He(e){return{meta:{},valid:ne(e,'ME')}}function ye(e){return{meta:{},valid:ne(e,'MK')}}function Fe(e){var t=e.toUpperCase();if(!/^[A-Z]{4}\d{6}[A-Z]{6}[0-9A-Z]\d$/.test(t)){return{meta:{},valid:!1}}var n=['BACA','BAKA','BUEI','BUEY','CACA','CACO','CAGA','CAGO','CAKA','CAKO','COGE','COGI','COJA','COJE','COJI','COJO','COLA','CULO','FALO','FETO','GETA','GUEI','GUEY','JETA','JOTO','KACA','KACO','KAGA','KAGO','KAKA','KAKO','KOGE','KOGI','KOJA','KOJE','KOJI','KOJO','KOLA','KULO','LILO','LOCA','LOCO','LOKA','LOKO','MAME','MAMO','MEAR','MEAS','MEON','MIAR','MION','MOCO','MOKO','MULA','MULO','NACA','NACO','PEDA','PEDO','PENE','PIPI','PITO','POPO','PUTA','PUTO','QULO','RATA','ROBA','ROBE','ROBO','RUIN','SENO','TETA','VACA','VAGA','VAGO','VAKA','VUEI','VUEY','WUEI','WUEY'],r=t.substr(0,4);if(n.indexOf(r)>=0){return{meta:{},valid:!1}}var a=parseInt(t.substr(4,2),10),l=parseInt(t.substr(6,2),10),i=parseInt(t.substr(6,2),10);/^[0-9]$/.test(t.charAt(16))?(a+=1900):(a+=2e3);if(!F(a,l,i)){return{meta:{},valid:!1}}var o=t.charAt(10);if(o!=='H'&&o!=='M'){return{meta:{},valid:!1}}var s=t.substr(11,2),c=['AS','BC','BS','CC','CH','CL','CM','CS','DF','DG','GR','GT','HG','JC','MC','MN','MS','NE','NL','NT','OC','PL','QR','QT','SL','SP','SR','TC','TL','TS','VZ','YN','ZS'];if(c.indexOf(s)===-1){return{meta:{},valid:!1}}var u='0123456789ABCDEFGHIJKLMN&OPQRSTUVWXYZ',d=0,f=t.length;for(var m=0;m<f-1;m++)d+=(18-m)*u.indexOf(t.charAt(m));d=(10-d%10)%10;return{meta:{},valid:"".concat(d)===t.charAt(f-1)}}function Ie(e){if(!/^\d{12}$/.test(e)){return{meta:{},valid:!1}}var t=parseInt(e.substr(0,2),10),n=parseInt(e.substr(2,2),10),r=parseInt(e.substr(4,2),10);if(!F(t+1900,n,r)&&!F(t+2e3,n,r)){return{meta:{},valid:!1}}var a=e.substr(6,2),l=["17","18","19","20","69","70","73","80","81","94","95","96","97"];return{meta:{},valid:l.indexOf(a)===-1}}function Oe(e){if(e.length<8){return{meta:{},valid:!1}}var t=e;t.length===8&&(t="0".concat(t));if(!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t)){return{meta:{},valid:!1}}t=t.replace(/\./g,'');if(parseInt(t,10)===0){return{meta:{},valid:!1}}var n=0,r=t.length;for(var a=0;a<r-1;a++)n+=(9-a)*parseInt(t.charAt(a),10);n%=11;n===10&&(n=0);return{meta:{},valid:"".concat(n)===t.charAt(r-1)}}function we(e){if(!/^\d{11}$/.test(e)){return{meta:{},valid:!1}}var t=function(e){var t=[3,7,6,1,8,9,4,5,2],n=0;for(var r=0;r<9;r++)n+=t[r]*parseInt(e.charAt(r),10);return 11-n%11},n=function(e){var t=[5,4,3,2,7,6,5,4,3,2],n=0;for(var r=0;r<10;r++)n+=t[r]*parseInt(e.charAt(r),10);return 11-n%11};return{meta:{},valid:"".concat(t(e))===e.substr(-2,1)&&"".concat(n(e))===e.substr(-1)}}function Le(e){if(!/^\d{8}[0-9A-Z]*$/.test(e)){return{meta:{},valid:!1}}if(e.length===8){return{meta:{},valid:!0}}var t=[3,2,7,6,5,4,3,2],n=0;for(var r=0;r<8;r++)n+=t[r]*parseInt(e.charAt(r),10);var a=n%11,l=[6,5,4,3,2,1,1,0,9,8,7][a],i="KJIHGFEDCBA".charAt(a);return{meta:{},valid:e.charAt(8)==="".concat(l)||e.charAt(8)===i}}function Ne(e){if(!/^[0-9]{11}$/.test(e)){return{meta:{},valid:!1}}var t=0,n=e.length,r=[1,3,7,9,1,3,7,9,1,3,7];for(var a=0;a<n-1;a++)t+=r[a]*parseInt(e.charAt(a),10);t%=10;t===0&&(t=10);t=10-t;return{meta:{},valid:"".concat(t)===e.charAt(n-1)}}function xe(e){if(!/^[0-9]{13}$/.test(e)){return{meta:{},valid:!1}}var t=parseInt(e.charAt(0),10);if(t===0||t===7||t===8){return{meta:{},valid:!1}}var n=parseInt(e.substr(1,2),10),r=parseInt(e.substr(3,2),10),a=parseInt(e.substr(5,2),10),l={1:1900,2:1900,3:1800,4:1800,5:2e3,6:2e3};if(a>31&&r>12){return{meta:{},valid:!1}}if(t!==9){n=l[t+'']+n;if(!F(n,r,a)){return{meta:{},valid:!1}}}var i=0,o=[2,7,9,1,4,6,3,5,8,2,7,9],s=e.length;for(var c=0;c<s-1;c++)i+=parseInt(e.charAt(c),10)*o[c];i%=11;i===10&&(i=1);return{meta:{},valid:"".concat(i)===e.charAt(s-1)}}function Me(e){return{meta:{},valid:ne(e,'RS')}}function Te(e){if(!/^[0-9]{10}$/.test(e)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(e)){return{meta:{},valid:!1}}var n=e.replace(/[^0-9]/g,''),r=parseInt(n.substr(0,2),10)+1900,a=parseInt(n.substr(2,2),10),l=parseInt(n.substr(4,2),10);if(!F(r,a,l)){return{meta:{},valid:!1}}return{meta:{},valid:t(n)}}function ke(e){return{meta:{},valid:ne(e,'SI')}}function Re(e){return{meta:{},valid:/^\d{5}$/.test(e)}}function Be(e){if(e.length!==13){return{meta:{},valid:!1}}var t=0;for(var n=0;n<12;n++)t+=parseInt(e.charAt(n),10)*(13-n);return{meta:{},valid:(11-t%11)%10===parseInt(e.charAt(12),10)}}function Ze(e){if(e.length!==11){return{meta:{},valid:!1}}var t=0;for(var n=0;n<10;n++)t+=parseInt(e.charAt(n),10);return{meta:{},valid:t%10===parseInt(e.charAt(10),10)}}function Ge(e){var t=e.toUpperCase();if(!/^[A-Z][12][0-9]{8}$/.test(t)){return{meta:{},valid:!1}}var n=t.length,r='ABCDEFGHJKLMNPQRSTUVXYWZIO',a=r.indexOf(t.charAt(0))+10,l=Math.floor(a/10)+a%10*(n-1),i=0;for(var o=1;o<n-1;o++)i+=parseInt(t.charAt(o),10)*(n-1-o);return{meta:{},valid:(l+i+parseInt(t.charAt(n-1),10))%10===0}}function Pe(e){if(!/^\d{8}$/.test(e)){return{meta:{},valid:!1}}var t=[2,9,8,7,6,3,4],n=0;for(var r=0;r<7;r++)n+=parseInt(e.charAt(r),10)*t[r];n%=10;n>0&&(n=10-n);return{meta:{},valid:"".concat(n)===e.charAt(7)}}function De(e){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(e)){return{meta:{},valid:!1}}var n=parseInt(e.substr(0,2),10),r=new Date.getFullYear()%100,a=parseInt(e.substr(2,2),10),l=parseInt(e.substr(4,2),10);n=n>=r?n+1900:n+2e3;if(!F(n,a,l)){return{meta:{},valid:!1}}return{meta:{},valid:t(e)}}function Ke(){var e=['AR','BA','BG','BR','CH','CL','CN','CO','CZ','DK','EE','ES','FI','FR','HK','HR','ID','IE','IL','IS','KR','LT','LV','ME','MK','MX','MY','NL','NO','PE','PL','RO','RS','SE','SI','SK','SM','TH','TR','TW','UY','ZA'];return{validate:function(t){if(t.value===''){return{valid:!0}}var n=Object.assign({},{message:''},t.options),r=t.value.substr(0,2);'function'===typeof n.country?(r=n.country.call(this)):(r=n.country);if(e.indexOf(r)===-1){return{valid:!0}}var a={meta:{},valid:!0};switch(r.toLowerCase()){case 'ar':a=te(t.value);break;case 'ba':a=re(t.value);break;case 'bg':a=ae(t.value);break;case 'br':a=le(t.value);break;case 'ch':a=ie(t.value);break;case 'cl':a=oe(t.value);break;case 'cn':a=se(t.value);break;case 'co':a=ce(t.value);break;case 'cz':a=ue(t.value);break;case 'dk':a=de(t.value);break;case 'ee':a=Ve(t.value);break;case 'es':a=fe(t.value);break;case 'fi':a=me(t.value);break;case 'fr':a=ge(t.value);break;case 'hk':a=pe(t.value);break;case 'hr':a=he(t.value);break;case 'id':a=ve(t.value);break;case 'ie':a=Ae(t.value);break;case 'il':a=be(t.value);break;case 'is':a=Ee(t.value);break;case 'kr':a=Ce(t.value);break;case 'lt':a=Ve(t.value);break;case 'lv':a=Se(t.value);break;case 'me':a=He(t.value);break;case 'mk':a=ye(t.value);break;case 'mx':a=Fe(t.value);break;case 'my':a=Ie(t.value);break;case 'nl':a=Oe(t.value);break;case 'no':a=we(t.value);break;case 'pe':a=Le(t.value);break;case 'pl':a=Ne(t.value);break;case 'ro':a=xe(t.value);break;case 'rs':a=Me(t.value);break;case 'se':a=Te(t.value);break;case 'si':a=ke(t.value);break;case 'sk':a=ue(t.value);break;case 'sm':a=Re(t.value);break;case 'th':a=Be(t.value);break;case 'tr':a=Ze(t.value);break;case 'tw':a=Ge(t.value);break;case 'uy':a=Pe(t.value);break;case 'za':a=De(t.value);break}var l=A(t.l10n?n.message||t.l10n.id.country:n.message,t.l10n?t.l10n.id.countries[r.toUpperCase()]:r.toUpperCase());return Object.assign({},{message:l},a)}}}function Ue(){return{validate:function(e){if(e.value===''){return{valid:!0}}switch(!0){case /^\d{15}$/.test(e.value):;case /^\d{2}-\d{6}-\d{6}-\d{1}$/.test(e.value):;case /^\d{2}\s\d{6}\s\d{6}\s\d{1}$/.test(e.value):var n=e.value.replace(/[^0-9]/g,'');return{valid:t(n)};case /^\d{14}$/.test(e.value):;case /^\d{16}$/.test(e.value):;case /^\d{2}-\d{6}-\d{6}(|-\d{2})$/.test(e.value):;case /^\d{2}\s\d{6}\s\d{6}(|\s\d{2})$/.test(e.value):return{valid:!0};default:return{valid:!1}}}}}function Ye(){return{validate:function(e){if(e.value===''){return{valid:!0}}if(!/^IMO \d{7}$/i.test(e.value)){return{valid:!1}}var t=e.value.replace(/^.*(\d{7})$/,'$1'),n=0;for(var r=6;r>=1;r--)n+=parseInt(t.slice(6-r,-r),10)*(r+1);return{valid:n%10===parseInt(t.charAt(6),10)}}}}function Je(){return{validate:function(e){if(e.value===''){return{meta:{type:null},valid:!0}}var t;switch(!0){case /^\d{9}[\dX]$/.test(e.value):;case e.value.length===13&&/^(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):;case e.value.length===13&&/^(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t='ISBN10';break;case /^(978|979)\d{9}[\dX]$/.test(e.value):;case e.value.length===17&&/^(978|979)-(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):;case e.value.length===17&&/^(978|979)\s(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t='ISBN13';break;default:return{meta:{type:null},valid:!1}}var n=e.value.replace(/[^0-9X]/gi,'').split(''),r=n.length,a=0,l,i;switch(t){case 'ISBN10':a=0;for(l=0;l<r-1;l++)a+=parseInt(n[l],10)*(10-l);i=11-a%11;i===11?(i=0):i===10&&(i='X');return{meta:{type:t},valid:"".concat(i)===n[r-1]};case 'ISBN13':a=0;for(l=0;l<r-1;l++)a+=l%2===0?parseInt(n[l],10):parseInt(n[l],10)*3;i=10-a%10;i===10&&(i='0');return{meta:{type:t},valid:"".concat(i)===n[r-1]}}}}}function We(){var e="AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|KH|CM|CA|CV|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MK|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|RE|RO|RU|RW|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SZ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW";return{validate:function(t){if(t.value===''){return{valid:!0}}var n=t.value.toUpperCase(),r=new RegExp('^('+e+')[0-9A-Z]{10}$');if(!r.test(t.value)){return{valid:!1}}var a=n.length,l='',i;for(i=0;i<a-1;i++){var o=n.charCodeAt(i);l+=o>57?(o-55).toString():n.charAt(i)}var s='',c=l.length,u=c%2!==0?0:1;for(i=0;i<c;i++)s+=parseInt(l[i],10)*(i%2===u?2:1)+'';var d=0;for(i=0;i<s.length;i++)d+=parseInt(s.charAt(i),10);d=(10-d%10)%10;return{valid:"".concat(d)===n.charAt(a-1)}}}}function ze(){return{validate:function(e){if(e.value===''){return{meta:null,valid:!0}}var t;switch(!0){case /^M\d{9}$/.test(e.value):;case /^M-\d{4}-\d{4}-\d{1}$/.test(e.value):;case /^M\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t='ISMN10';break;case /^9790\d{9}$/.test(e.value):;case /^979-0-\d{4}-\d{4}-\d{1}$/.test(e.value):;case /^979\s0\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t='ISMN13';break;default:return{meta:null,valid:!1}}var n=e.value;'ISMN10'===t&&(n="9790".concat(n.substr(1)));n=n.replace(/[^0-9]/gi,'');var r=0,a=n.length,l=[1,3];for(var i=0;i<a-1;i++)r+=parseInt(n.charAt(i),10)*l[i%2];r=(10-r%10)%10;return{meta:{type:t},valid:"".concat(r)===n.charAt(a-1)}}}}function _e(){return{validate:function(e){if(e.value===''){return{valid:!0}}if(!/^\d{4}\-\d{3}[\dX]$/.test(e.value)){return{valid:!1}}var t=e.value.replace(/[^0-9X]/gi,'').split(''),n=t.length,r=0;t[7]==='X'&&(t[7]='10');for(var a=0;a<n;a++)r+=parseInt(t[a],10)*(8-a);return{valid:r%11===0}}}}function Xe(){return{validate:function(e){return{valid:e.value===''||/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(e.value)||/^([0-9A-Fa-f]{4}\.){2}([0-9A-Fa-f]{4})$/.test(e.value)}}}}function Qe(){return{validate:function(e){if(e.value===''){return{valid:!0}}var n=e.value;switch(!0){case /^[0-9A-F]{15}$/i.test(n):;case /^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}[- ][0-9A-F]$/i.test(n):;case /^\d{19}$/.test(n):;case /^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}[- ]\d$/.test(n):var i=n.charAt(n.length-1).toUpperCase();n=n.replace(/[- ]/g,'');if(n.match(/^\d*$/i)){return{valid:t(n)}};n=n.slice(0,-1);var l='';var a;for(a=1;a<=13;a+=2)l+=(parseInt(n.charAt(a),16)*2).toString(16);var r=0;for(a=0;a<l.length;a++)r+=parseInt(l.charAt(a),16);return{valid:r%10===0?i==='0':i===((Math.floor((r+10)/10)*10-r)*2).toString(16).toUpperCase()};case /^[0-9A-F]{14}$/i.test(n):;case /^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}$/i.test(n):;case /^\d{18}$/.test(n):;case /^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}$/.test(n):return{valid:!0};default:return{valid:!1}}}}}function qe(){var e=['AE','BG','BR','CN','CZ','DE','DK','ES','FR','GB','IN','MA','NL','PK','RO','RU','SK','TH','US','VE'];return{validate:function(t){if(t.value===''){return{valid:!0}}var n=Object.assign({},{message:''},t.options),r=t.value.trim(),a=r.substr(0,2);'function'===typeof n.country?(a=n.country.call(this)):(a=n.country);if(!a||e.indexOf(a.toUpperCase())===-1){return{valid:!0}}var l=!0;switch(a.toUpperCase()){case 'AE':l=/^(((\+|00)?971[\s\.-]?(\(0\)[\s\.-]?)?|0)(\(5(0|2|5|6)\)|5(0|2|5|6)|2|3|4|6|7|9)|60)([\s\.-]?[0-9]){7}$/.test(r);break;case 'BG':l=/^(0|359|00)(((700|900)[0-9]{5}|((800)[0-9]{5}|(800)[0-9]{4}))|(87|88|89)([0-9]{7})|((2[0-9]{7})|(([3-9][0-9])(([0-9]{6})|([0-9]{5})))))$/.test(r.replace(/\+|\s|-|\/|\(|\)/gi,''));break;case 'BR':l=/^(([\d]{4}[-.\s]{1}[\d]{2,3}[-.\s]{1}[\d]{2}[-.\s]{1}[\d]{2})|([\d]{4}[-.\s]{1}[\d]{3}[-.\s]{1}[\d]{4})|((\(?\+?[0-9]{2}\)?\s?)?(\(?\d{2}\)?\s?)?\d{4,5}[-.\s]?\d{4}))$/.test(r);break;case 'CN':l=/^((00|\+)?(86(?:-| )))?((\d{11})|(\d{3}[- ]{1}\d{4}[- ]{1}\d{4})|((\d{2,4}[- ]){1}(\d{7,8}|(\d{3,4}[- ]{1}\d{4}))([- ]{1}\d{1,4})?))$/.test(r);break;case 'CZ':l=/^(((00)([- ]?)|\+)(420)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(r);break;case 'DE':l=/^(((((((00|\+)49[ \-/]?)|0)[1-9][0-9]{1,4})[ \-/]?)|((((00|\+)49\()|\(0)[1-9][0-9]{1,4}\)[ \-/]?))[0-9]{1,7}([ \-/]?[0-9]{1,5})?)$/.test(r);break;case 'DK':l=/^(\+45|0045|\(45\))?\s?[2-9](\s?\d){7}$/.test(r);break;case 'ES':l=/^(?:(?:(?:\+|00)34\D?))?(?:5|6|7|8|9)(?:\d\D?){8}$/.test(r);break;case 'FR':l=/^(?:(?:(?:\+|00)33[ ]?(?:\(0\)[ ]?)?)|0){1}[1-9]{1}([ .-]?)(?:\d{2}\1?){3}\d{2}$/.test(r);break;case 'GB':l=/^\(?(?:(?:0(?:0|11)\)?[\s-]?\(?|\+)44\)?[\s-]?\(?(?:0\)?[\s-]?\(?)?|0)(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}|\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4}|\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3})|\d{5}\)?[\s-]?\d{4,5}|8(?:00[\s-]?11[\s-]?11|45[\s-]?46[\s-]?4\d))(?:(?:[\s-]?(?:x|ext\.?\s?|\#)\d+)?)$/.test(r);break;case 'IN':l=/((\+?)((0[ -]+)*|(91 )*)(\d{12}|\d{10}))|\d{5}([- ]*)\d{6}/.test(r);break;case 'MA':l=/^(?:(?:(?:\+|00)212[\s]?(?:[\s]?\(0\)[\s]?)?)|0){1}(?:5[\s.-]?[2-3]|6[\s.-]?[13-9]){1}[0-9]{1}(?:[\s.-]?\d{2}){3}$/.test(r);break;case 'NL':l=/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9])((\s|\s?-\s?)?[0-9])((\s|\s?-\s?)?[0-9])\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]$/gm.test(r);break;case 'PK':l=/^0?3[0-9]{2}[0-9]{7}$/.test(r);break;case 'RO':l=/^(\+4|)?(07[0-8]{1}[0-9]{1}|02[0-9]{2}|03[0-9]{2}){1}?(\s|\.|\-)?([0-9]{3}(\s|\.|\-|)){2}$/g.test(r);break;case 'RU':l=/^((8|\+7|007)[\-\.\/ ]?)?([\(\/\.]?\d{3}[\)\/\.]?[\-\.\/ ]?)?[\d\-\.\/ ]{7,10}$/g.test(r);break;case 'SK':l=/^(((00)([- ]?)|\+)(421)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(r);break;case 'TH':l=/^0\(?([6|8-9]{2})*-([0-9]{3})*-([0-9]{4})$/.test(r);break;case 'VE':l=/^0(?:2(?:12|4[0-9]|5[1-9]|6[0-9]|7[0-8]|8[1-35-8]|9[1-5]|3[45789])|4(?:1[246]|2[46]))\d{7}$/.test(r);break;case 'US':;default:l=/^(?:(1\-?)|(\+1 ?))?\(?\d{3}\)?[\-\.\s]?\d{3}[\-\.\s]?\d{4}$/.test(r);break}return{message:A(t.l10n?n.message||t.l10n.phone.country:n.message,t.l10n?t.l10n.phone.countries[a]:a),valid:l}}}}function $e(){return{validate:function(e){if(e.value===''){return{valid:!0}}if(!/^\d{9}$/.test(e.value)){return{valid:!1}}var t=0;for(var n=0;n<e.value.length;n+=3)t+=parseInt(e.value.charAt(n),10)*3+parseInt(e.value.charAt(n+1),10)*7+parseInt(e.value.charAt(n+2),10);return{valid:t!==0&&t%10===0}}}}function je(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=e.value.toUpperCase();if(!/^[0-9A-Z]{7}$/.test(t)){return{valid:!1}}var n=[1,3,1,7,3,9,1],r=t.length,a=0;for(var l=0;l<r-1;l++)a+=n[l]*parseInt(t.charAt(l),36);a=(10-a%10)%10;return{valid:"".concat(a)===t.charAt(r-1)}}}}function et(){return{validate:function(e){return{valid:e.value===''||/^\d{9}$/.test(e.value)&&t(e.value)}}}}function tt(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=e.value.length,n=0,r;for(var a=0;a<t;a++)r=parseInt(e.value.charAt(a),10),a%2===0&&(r*=2,r>9&&(r-=9)),n+=r;return{valid:n%10===0}}}}function nt(){var e=function(e,t){var n=Math.pow(10,t),r=e*n,a;switch(!0){case r===0:a=0;break;case r>0:a=1;break;case r<0:a=-1;break}var l=r%1===.5*a;return l?(Math.floor(r)+(a>0?1:0))/n:Math.round(r)/n},t=function(t,n){if(n===0){return 1}var r="".concat(t).split('.'),a="".concat(n).split('.'),l=(r.length===1?0:r[1].length)+(a.length===1?0:a[1].length);return e(t-n*Math.floor(t/n),l)};return{validate:function(e){if(e.value===''){return{valid:!0}}var n=parseFloat(e.value);if(isNaN(n)||!isFinite(n)){return{valid:!1}}var r=Object.assign({},{baseValue:0,message:'',step:1},e.options),a=t(n-r.baseValue,r.step);return{message:A(e.l10n?r.message||e.l10n.step["default"]:r.message,"".concat(r.step)),valid:a===0||a===r.step}}}}function rt(){return{validate:function(e){if(e.value===''){return{valid:!0}}var t=Object.assign({},{message:''},e.options),n={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i},r=t.version?"".concat(t.version):'all';return{message:t.version?A(e.l10n?t.message||e.l10n.uuid.version:t.message,t.version):e.l10n?e.l10n.uuid["default"]:t.message,valid:null===n[r]?!0:n[r].test(e.value)}}}}function at(e){var t=e.replace('-','');/^AR[0-9]{11}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{11}$/.test(t)){return{meta:{},valid:!1}}var n=[5,4,3,2,7,6,5,4,3,2],r=0;for(var a=0;a<10;a++)r+=parseInt(t.charAt(a),10)*n[a];r=11-r%11;r===11&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(10)}}function lt(e){var t=e;/^ATU[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^U[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}t=t.substr(1);var n=[1,2,1,2,1,2,1],r=0,a=0;for(var l=0;l<7;l++)a=parseInt(t.charAt(l),10)*n[l],a>9&&(a=Math.floor(a/10)+a%10),r+=a;r=10-(r+4)%10;r===10&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(7,1)}}function it(e){var t=e;/^BE[0]?[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0]?[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}t.length===9&&(t="0".concat(t));if(t.substr(1,1)==='0'){return{meta:{},valid:!1}}var n=parseInt(t.substr(0,8),10)+parseInt(t.substr(8,2),10);return{meta:{},valid:n%97===0}}function ot(e){var t=e;/^BG[0-9]{9,10}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9,10}$/.test(t)){return{meta:{},valid:!1}}var n=0,r=0;if(t.length===9){for(r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*(r+1);n%=11;if(n===10){n=0;for(r=0;r<8;r++)n+=parseInt(t.charAt(r),10)*(r+3)}n%=10;return{meta:{},valid:"".concat(n)===t.substr(8)}}else{var a=function(e){var t=parseInt(e.substr(0,2),10)+1900,n=parseInt(e.substr(2,2),10),r=parseInt(e.substr(4,2),10);n>40?(t+=100,n-=40):n>20&&(t-=100,n-=20);if(!F(t,n,r)){return!1}var a=[2,4,8,5,10,9,7,3,6],l=0;for(var i=0;i<9;i++)l+=parseInt(e.charAt(i),10)*a[i];l=l%11%10;return"".concat(l)===e.substr(9,1)},l=function(e){var t=[21,19,17,13,11,9,7,3,1],n=0;for(var r=0;r<9;r++)n+=parseInt(e.charAt(r),10)*t[r];n%=10;return"".concat(n)===e.substr(9,1)},i=function(e){var t=[4,3,2,7,6,5,4,3,2],n=0;for(var r=0;r<9;r++)n+=parseInt(e.charAt(r),10)*t[r];n=11-n%11;if(n===10){return!1}n===11&&(n=0);return"".concat(n)===e.substr(9,1)};return{meta:{},valid:a(t)||l(t)||i(t)}}}function st(e){if(e===''){return{meta:{},valid:!0}}var t=e.replace(/[^\d]+/g,'');if(t===''||t.length!==14){return{meta:{},valid:!1}}if(t==='00000000000000'||t==='11111111111111'||t==='22222222222222'||t==='33333333333333'||t==='44444444444444'||t==='55555555555555'||t==='66666666666666'||t==='77777777777777'||t==='88888888888888'||t==='99999999999999'){return{meta:{},valid:!1}}var n=t.length-2,r=t.substring(0,n),a=t.substring(n),l=0,i=n-7,o;for(o=n;o>=1;o--)l+=parseInt(r.charAt(n-o),10)*i--,i<2&&(i=9);var s=l%11<2?0:11-l%11;if(s!==parseInt(a.charAt(0),10)){return{meta:{},valid:!1}}n+=1;r=t.substring(0,n);l=0;i=n-7;for(o=n;o>=1;o--)l+=parseInt(r.charAt(n-o),10)*i--,i<2&&(i=9);s=l%11<2?0:11-l%11;return{meta:{},valid:s===parseInt(a.charAt(1),10)}}function ct(e){var t=e;/^CHE[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t)&&(t=t.substr(2));if(!/^E[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t)){return{meta:{},valid:!1}}t=t.substr(1);var n=[5,4,3,2,7,6,5,4],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r=11-r%11;if(r===10){return{meta:{},valid:!1}}r===11&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(8,1)}}function ut(e){var t=e;/^CY[0-5|9][0-9]{7}[A-Z]$/.test(t)&&(t=t.substr(2));if(!/^[0-5|9][0-9]{7}[A-Z]$/.test(t)){return{meta:{},valid:!1}}if(t.substr(0,2)==='12'){return{meta:{},valid:!1}}var n=0,r={0:1,1:0,2:5,3:7,4:9,5:13,6:15,7:17,8:19,9:21};for(var a=0;a<8;a++){var l=parseInt(t.charAt(a),10);a%2===0&&(l=r["".concat(l)]);n+=l}return{meta:{},valid:"".concat('ABCDEFGHIJKLMNOPQRSTUVWXYZ'[n%26])===t.substr(8,1)}}function dt(e){var t=e;/^CZ[0-9]{8,10}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8,10}$/.test(t)){return{meta:{},valid:!1}}var n=0,r=0;if(t.length===8){if("".concat(t.charAt(0))==='9'){return{meta:{},valid:!1}}n=0;for(r=0;r<7;r++)n+=parseInt(t.charAt(r),10)*(8-r);n=11-n%11;n===10&&(n=0);n===11&&(n=1);return{meta:{},valid:"".concat(n)===t.substr(7,1)}}else if(t.length===9&&"".concat(t.charAt(0))==='6'){n=0;for(r=0;r<7;r++)n+=parseInt(t.charAt(r+1),10)*(8-r);n=11-n%11;n===10&&(n=0);n===11&&(n=1);n=[8,7,6,5,4,3,2,1,0,9,10][n-1];return{meta:{},valid:"".concat(n)===t.substr(8,1)}}else if(t.length===9||t.length===10){var a=1900+parseInt(t.substr(0,2),10),l=parseInt(t.substr(2,2),10)%50%20,i=parseInt(t.substr(4,2),10);if(t.length===9){a>=1980&&(a-=100);if(a>1953){return{meta:{},valid:!1}}}else a<1954&&(a+=100);if(!F(a,l,i)){return{meta:{},valid:!1}}if(t.length===10){var o=parseInt(t.substr(0,9),10)%11;a<1985&&(o%=10);return{meta:{},valid:"".concat(o)===t.substr(9,1)}}return{meta:{},valid:!0}}return{meta:{},valid:!1}}function ft(e){var t=e;/^DE[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}return{meta:{},valid:n(t)}}function mt(e){var t=e;/^DK[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}var n=0,r=[2,7,6,5,4,3,2,1];for(var a=0;a<8;a++)n+=parseInt(t.charAt(a),10)*r[a];return{meta:{},valid:n%11===0}}function gt(e){var t=e;/^EE[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}var n=0,r=[3,7,1,3,7,1,3,7,1];for(var a=0;a<9;a++)n+=parseInt(t.charAt(a),10)*r[a];return{meta:{},valid:n%10===0}}function pt(e){var t=e;/^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t)&&(t=t.substr(2));if(!/^[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t)){return{meta:{},valid:!1}}var n=function(e){var t=parseInt(e.substr(0,8),10);return"".concat('TRWAGMYFPDXBNJZSQVHLCKE'[t%23])===e.substr(8,1)},r=function(e){var t=['XYZ'.indexOf(e.charAt(0)),e.substr(1)].join(''),n='TRWAGMYFPDXBNJZSQVHLCKE'[parseInt(t,10)%23];return"".concat(n)===e.substr(8,1)},a=function(e){var t=e.charAt(0),n;if('KLM'.indexOf(t)!==-1){n=parseInt(e.substr(1,8),10);n='TRWAGMYFPDXBNJZSQVHLCKE'[n%23];return"".concat(n)===e.substr(8,1)}else if('ABCDEFGHJNPQRSUVW'.indexOf(t)!==-1){var r=[2,1,2,1,2,1,2],a=0,l=0;for(var i=0;i<7;i++)l=parseInt(e.charAt(i+1),10)*r[i],l>9&&(l=Math.floor(l/10)+l%10),a+=l;a=10-a%10;a===10&&(a=0);return"".concat(a)===e.substr(8,1)||'JABCDEFGHI'[a]===e.substr(8,1)}return!1},l=t.charAt(0);if(/^[0-9]$/.test(l)){return{meta:{type:'DNI'},valid:n(t)}}else if(/^[XYZ]$/.test(l)){return{meta:{type:'NIE'},valid:r(t)}}else{return{meta:{type:'CIF'},valid:a(t)}}}function ht(e){var t=e;/^FI[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}var n=[7,9,10,5,8,4,2,1],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];return{meta:{},valid:r%11===0}}function vt(e){var n=e;/^FR[0-9A-Z]{2}[0-9]{9}$/.test(n)&&(n=n.substr(2));if(!/^[0-9A-Z]{2}[0-9]{9}$/.test(n)){return{meta:{},valid:!1}}if(!t(n.substr(2))){return{meta:{},valid:!1}}if(/^[0-9]{2}$/.test(n.substr(0,2))){return{meta:{},valid:n.substr(0,2)==="".concat(parseInt(n.substr(2)+'12',10)%97)}}else{var r='0123456789ABCDEFGHJKLMNPQRSTUVWXYZ',a;/^[0-9]$/.test(n.charAt(0))?(a=r.indexOf(n.charAt(0))*24+r.indexOf(n.charAt(1))-10):(a=r.indexOf(n.charAt(0))*34+r.indexOf(n.charAt(1))-100);return{meta:{},valid:(parseInt(n.substr(2),10)+1+Math.floor(a/11))%11===a%11}}}function At(e){var t=e;(/^GB[0-9]{9}$/.test(t)||/^GB[0-9]{12}$/.test(t)||/^GBGD[0-9]{3}$/.test(t)||/^GBHA[0-9]{3}$/.test(t)||/^GB(GD|HA)8888[0-9]{5}$/.test(t))&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)&&!/^[0-9]{12}$/.test(t)&&!/^GD[0-9]{3}$/.test(t)&&!/^HA[0-9]{3}$/.test(t)&&!/^(GD|HA)8888[0-9]{5}$/.test(t)){return{meta:{},valid:!1}}var n=t.length;if(n===5){var r=t.substr(0,2),a=parseInt(t.substr(2),10);return{meta:{},valid:'GD'===r&&a<500||'HA'===r&&a>=500}}else if(n===11&&('GD8888'===t.substr(0,6)||'HA8888'===t.substr(0,6))){if('GD'===t.substr(0,2)&&parseInt(t.substr(6,3),10)>=500||'HA'===t.substr(0,2)&&parseInt(t.substr(6,3),10)<500){return{meta:{},valid:!1}}return{meta:{},valid:parseInt(t.substr(6,3),10)%97===parseInt(t.substr(9,2),10)}}else if(n===9||n===12){var l=[8,7,6,5,4,3,2,10,1],i=0;for(var o=0;o<9;o++)i+=parseInt(t.charAt(o),10)*l[o];i%=97;var s=parseInt(t.substr(0,3),10)>=100?i===0||i===42||i===55:i===0;return{meta:{},valid:s}}return{meta:{},valid:!0}}function bt(e){var t=e;/^(GR|EL)[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}t.length===8&&(t="0".concat(t));var n=[256,128,64,32,16,8,4,2],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r=r%11%10;return{meta:{},valid:"".concat(r)===t.substr(8,1)}}function Et(e){var t=e;/^HR[0-9]{11}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{11}$/.test(t)){return{meta:{},valid:!1}}return{meta:{},valid:n(t)}}function Ct(e){var t=e;/^HU[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}var n=[9,7,3,1,9,7,3,1],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];return{meta:{},valid:r%10===0}}function Vt(e){var t=e;/^IE[0-9][0-9A-Z\*\+][0-9]{5}[A-Z]{1,2}$/.test(t)&&(t=t.substr(2));if(!/^[0-9][0-9A-Z\*\+][0-9]{5}[A-Z]{1,2}$/.test(t)){return{meta:{},valid:!1}}var n=function(e){var t=e;while(t.length<7)t="0".concat(t);var n='WABCDEFGHIJKLMNOPQRSTUV',r=0;for(var a=0;a<7;a++)r+=parseInt(t.charAt(a),10)*(8-a);r+=9*n.indexOf(t.substr(7));return n[r%23]};if(/^[0-9]+$/.test(t.substr(0,7))){return{meta:{},valid:t.charAt(7)===n("".concat(t.substr(0,7)).concat(t.substr(8)))}}else if('ABCDEFGHIJKLMNOPQRSTUVWXYZ+*'.indexOf(t.charAt(1))!==-1){return{meta:{},valid:t.charAt(7)===n("".concat(t.substr(2,5)).concat(t.substr(0,1)))}}return{meta:{},valid:!0}}function St(e){var t=e;/^IS[0-9]{5,6}$/.test(t)&&(t=t.substr(2));return{meta:{},valid:/^[0-9]{5,6}$/.test(t)}}function Ht(e){var n=e;/^IT[0-9]{11}$/.test(n)&&(n=n.substr(2));if(!/^[0-9]{11}$/.test(n)){return{meta:{},valid:!1}}if(parseInt(n.substr(0,7),10)===0){return{meta:{},valid:!1}}var r=parseInt(n.substr(7,3),10);if(r<1||r>201&&r!==999&&r!==888){return{meta:{},valid:!1}}return{meta:{},valid:t(n)}}function yt(e){var t=e;/^LT([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t)&&(t=t.substr(2));if(!/^([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t)){return{meta:{},valid:!1}}var n=t.length,r=0,a;for(a=0;a<n-1;a++)r+=parseInt(t.charAt(a),10)*(1+a%9);var l=r%11;if(l===10){r=0;for(a=0;a<n-1;a++)r+=parseInt(t.charAt(a),10)*(1+(a+2)%9)}l=l%11%10;return{meta:{},valid:"".concat(l)===t.charAt(n-1)}}function Ft(e){var t=e;/^LU[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}return{meta:{},valid:"".concat(parseInt(t.substr(0,6),10)%89)===t.substr(6,2)}}function It(e){var t=e;/^LV[0-9]{11}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{11}$/.test(t)){return{meta:{},valid:!1}}var n=parseInt(t.charAt(0),10),r=t.length,a=0,l=[],i;if(n>3){a=0;l=[9,1,4,8,3,10,2,5,7,6,1];for(i=0;i<r;i++)a+=parseInt(t.charAt(i),10)*l[i];a%=11;return{meta:{},valid:a===3}}else{var o=parseInt(t.substr(0,2),10),s=parseInt(t.substr(2,2),10),c=parseInt(t.substr(4,2),10);c=c+1800+parseInt(t.charAt(6),10)*100;if(!F(c,s,o)){return{meta:{},valid:!1}}a=0;l=[10,5,8,4,2,1,6,3,7,9];for(i=0;i<r-1;i++)a+=parseInt(t.charAt(i),10)*l[i];a=(a+1)%11%10;return{meta:{},valid:"".concat(a)===t.charAt(r-1)}}}function Ot(e){var t=e;/^MT[0-9]{8}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{8}$/.test(t)){return{meta:{},valid:!1}}var n=[3,4,6,7,8,9,10,1],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];return{meta:{},valid:r%37===0}}function wt(e){var t=e;/^NL[0-9]{9}B[0-9]{2}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}B[0-9]{2}$/.test(t)){return{meta:{},valid:!1}}var n=[9,8,7,6,5,4,3,2],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r%=11;r>9&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(8,1)}}function Lt(e){var t=e;/^NO[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}var n=[3,2,7,6,5,4,3,2],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r=11-r%11;r===11&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(8,1)}}function Nt(e){var t=e;/^PL[0-9]{10}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{10}$/.test(t)){return{meta:{},valid:!1}}var n=[6,5,7,2,3,4,5,6,7,-1],r=0;for(var a=0;a<10;a++)r+=parseInt(t.charAt(a),10)*n[a];return{meta:{},valid:r%11===0}}function xt(e){var t=e;/^PT[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}var n=[9,8,7,6,5,4,3,2],r=0;for(var a=0;a<8;a++)r+=parseInt(t.charAt(a),10)*n[a];r=11-r%11;r>9&&(r=0);return{meta:{},valid:"".concat(r)===t.substr(8,1)}}function Mt(e){var t=e;/^RO[1-9][0-9]{1,9}$/.test(t)&&(t=t.substr(2));if(!/^[1-9][0-9]{1,9}$/.test(t)){return{meta:{},valid:!1}}var n=t.length,r=[7,5,3,2,1,7,5,3,2].slice(10-n),a=0;for(var l=0;l<n-1;l++)a+=parseInt(t.charAt(l),10)*r[l];a=10*a%11%10;return{meta:{},valid:"".concat(a)===t.substr(n-1,1)}}function Tt(e){var t=e;/^RS[0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[0-9]{9}$/.test(t)){return{meta:{},valid:!1}}var n=10,r=0;for(var a=0;a<8;a++)r=(parseInt(t.charAt(a),10)+n)%10,r===0&&(r=10),n=2*r%11;return{meta:{},valid:(n+parseInt(t.substr(8,1),10))%10===1}}function kt(e){var t=e;/^RU([0-9]{10}|[0-9]{12})$/.test(t)&&(t=t.substr(2));if(!/^([0-9]{10}|[0-9]{12})$/.test(t)){return{meta:{},valid:!1}}var n=0;if(t.length===10){var r=[2,4,10,3,5,9,4,6,8,0],a=0;for(n=0;n<10;n++)a+=parseInt(t.charAt(n),10)*r[n];a%=11;a>9&&(a%=10);return{meta:{},valid:"".concat(a)===t.substr(9,1)}}else if(t.length===12){var l=[7,2,4,10,3,5,9,4,6,8,0],i=[3,7,2,4,10,3,5,9,4,6,8,0],o=0,s=0;for(n=0;n<11;n++)o+=parseInt(t.charAt(n),10)*l[n],s+=parseInt(t.charAt(n),10)*i[n];o%=11;o>9&&(o%=10);s%=11;s>9&&(s%=10);return{meta:{},valid:"".concat(o)===t.substr(10,1)&&"".concat(s)===t.substr(11,1)}}return{meta:{},valid:!0}}function Rt(e){var n=e;/^SE[0-9]{10}01$/.test(n)&&(n=n.substr(2));if(!/^[0-9]{10}01$/.test(n)){return{meta:{},valid:!1}}n=n.substr(0,10);return{meta:{},valid:t(n)}}function Bt(e){var t=e.match(/^(SI)?([1-9][0-9]{7})$/);if(!t){return{meta:{},valid:!1}}var n=t[1]?e.substr(2):e,r=[8,7,6,5,4,3,2],a=0;for(var l=0;l<7;l++)a+=parseInt(n.charAt(l),10)*r[l];a=11-a%11;a===10&&(a=0);return{meta:{},valid:"".concat(a)===n.substr(7,1)}}function Zt(e){var t=e;/^SK[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)&&(t=t.substr(2));if(!/^[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)){return{meta:{},valid:!1}}return{meta:{},valid:parseInt(t,10)%11===0}}function Gt(e){var t=e;/^VE[VEJPG][0-9]{9}$/.test(t)&&(t=t.substr(2));if(!/^[VEJPG][0-9]{9}$/.test(t)){return{meta:{},valid:!1}}var n={E:8,G:20,J:12,P:16,V:4},r=[3,2,7,6,5,4,3,2],a=n[t.charAt(0)];for(var l=0;l<8;l++)a+=parseInt(t.charAt(l+1),10)*r[l];a=11-a%11;(a===11||a===10)&&(a=0);return{meta:{},valid:"".concat(a)===t.substr(9,1)}}function Pt(e){var t=e;/^ZA4[0-9]{9}$/.test(t)&&(t=t.substr(2));return{meta:{},valid:/^4[0-9]{9}$/.test(t)}}function Dt(){var e=['AR','AT','BE','BG','BR','CH','CY','CZ','DE','DK','EE','EL','ES','FI','FR','GB','GR','HR','HU','IE','IS','IT','LT','LU','LV','MT','NL','NO','PL','PT','RO','RU','RS','SE','SK','SI','VE','ZA'];return{validate:function(t){var n=t.value;if(n===''){return{valid:!0}}var r=Object.assign({},{message:''},t.options),a=n.substr(0,2);'function'===typeof r.country?(a=r.country.call(this)):(a=r.country);if(e.indexOf(a)===-1){return{valid:!0}}var l={meta:{},valid:!0};switch(a.toLowerCase()){case 'ar':l=at(n);break;case 'at':l=lt(n);break;case 'be':l=it(n);break;case 'bg':l=ot(n);break;case 'br':l=st(n);break;case 'ch':l=ct(n);break;case 'cy':l=ut(n);break;case 'cz':l=dt(n);break;case 'de':l=ft(n);break;case 'dk':l=mt(n);break;case 'ee':l=gt(n);break;case 'el':l=bt(n);break;case 'es':l=pt(n);break;case 'fi':l=ht(n);break;case 'fr':l=vt(n);break;case 'gb':l=At(n);break;case 'gr':l=bt(n);break;case 'hr':l=Et(n);break;case 'hu':l=Ct(n);break;case 'ie':l=Vt(n);break;case 'is':l=St(n);break;case 'it':l=Ht(n);break;case 'lt':l=yt(n);break;case 'lu':l=Ft(n);break;case 'lv':l=It(n);break;case 'mt':l=Ot(n);break;case 'nl':l=wt(n);break;case 'no':l=Lt(n);break;case 'pl':l=Nt(n);break;case 'pt':l=xt(n);break;case 'ro':l=Mt(n);break;case 'rs':l=Tt(n);break;case 'ru':l=kt(n);break;case 'se':l=Rt(n);break;case 'si':l=Bt(n);break;case 'sk':l=Zt(n);break;case 've':l=Gt(n);break;case 'za':l=Pt(n);break}var i=A(t.l10n?r.message||t.l10n.vat.country:r.message,t.l10n?t.l10n.vat.countries[a.toUpperCase()]:a.toUpperCase());return Object.assign({},{message:i},l)}}}function Kt(){return{validate:function(e){if(e.value===''){return{valid:!0}}if(!/^[a-hj-npr-z0-9]{8}[0-9xX][a-hj-npr-z0-9]{8}$/i.test(e.value)){return{valid:!1}}var t=e.value.toUpperCase(),n={A:1,B:2,C:3,D:4,E:5,F:6,G:7,H:8,J:1,K:2,L:3,M:4,N:5,P:7,R:9,S:2,T:3,U:4,V:5,W:6,X:7,Y:8,Z:9,0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9},r=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],a=t.length,l=0;for(var i=0;i<a;i++)l+=n["".concat(t.charAt(i))]*r[i];var o="".concat(l%11);o==='10'&&(o='X');return{valid:o===t.charAt(8)}}}}function Ut(){var e=['AT','BG','BR','CA','CH','CZ','DE','DK','ES','FR','GB','IE','IN','IT','MA','NL','PL','PT','RO','RU','SE','SG','SK','US'],t=function(e){var t='[ABCDEFGHIJKLMNOPRSTUWYZ]',n='[ABCDEFGHKLMNOPQRSTUVWXY]',r='[ABCDEFGHJKPMNRSTUVWXY]',a='[ABEHMNPRVWXY]',l='[ABDEFGHJLNPQRSTUWXYZ]',i=[new RegExp("^(".concat(t,"{1}").concat(n,"?[0-9]{1,2})(\\s*)([0-9]{1}").concat(l,"{2})$"),'i'),new RegExp("^(".concat(t,"{1}[0-9]{1}").concat(r,"{1})(\\s*)([0-9]{1}").concat(l,"{2})$"),'i'),new RegExp("^(".concat(t,"{1}").concat(n,"{1}?[0-9]{1}").concat(a,"{1})(\\s*)([0-9]{1}").concat(l,"{2})$"),'i'),new RegExp('^(BF1)(\\s*)([0-6]{1}[ABDEFGHJLNPQRST]{1}[ABDEFGHJLNPQRSTUWZYZ]{1})$','i'),/^(GIR)(\s*)(0AA)$/i,/^(BFPO)(\s*)([0-9]{1,4})$/i,/^(BFPO)(\s*)(c\/o\s*[0-9]{1,3})$/i,/^([A-Z]{4})(\s*)(1ZZ)$/i,/^(AI-2640)$/i];for(var o=0,s=i;o<s.length;o++){var c=s[o];if(c.test(e)){return!0}}return!1};return{validate:function(n){var r=Object.assign({},n.options);if(n.value===''||!r.country){return{valid:!0}}var a=n.value.substr(0,2);'function'===typeof r.country?(a=r.country.call(this)):(a=r.country);if(!a||e.indexOf(a.toUpperCase())===-1){return{valid:!0}}var l=!1;a=a.toUpperCase();switch(a){case 'AT':l=/^([1-9]{1})(\d{3})$/.test(n.value);break;case 'BG':l=/^([1-9]{1}[0-9]{3})$/.test(n.value);break;case 'BR':l=/^(\d{2})([\.]?)(\d{3})([\-]?)(\d{3})$/.test(n.value);break;case 'CA':l=/^(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|X|Y){1}[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}\s?[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}[0-9]{1}$/i.test(n.value);break;case 'CH':l=/^([1-9]{1})(\d{3})$/.test(n.value);break;case 'CZ':l=/^(\d{3})([ ]?)(\d{2})$/.test(n.value);break;case 'DE':l=/^(?!01000|99999)(0[1-9]\d{3}|[1-9]\d{4})$/.test(n.value);break;case 'DK':l=/^(DK(-|\s)?)?\d{4}$/i.test(n.value);break;case 'ES':l=/^(?:0[1-9]|[1-4][0-9]|5[0-2])\d{3}$/.test(n.value);break;case 'FR':l=/^[0-9]{5}$/i.test(n.value);break;case 'GB':l=t(n.value);break;case 'IN':l=/^\d{3}\s?\d{3}$/.test(n.value);break;case 'IE':l=/^(D6W|[ACDEFHKNPRTVWXY]\d{2})\s[0-9ACDEFHKNPRTVWXY]{4}$/.test(n.value);break;case 'IT':l=/^(I-|IT-)?\d{5}$/i.test(n.value);break;case 'MA':l=/^[1-9][0-9]{4}$/i.test(n.value);break;case 'NL':l=/^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i.test(n.value);break;case 'PL':l=/^[0-9]{2}\-[0-9]{3}$/.test(n.value);break;case 'PT':l=/^[1-9]\d{3}-\d{3}$/.test(n.value);break;case 'RO':l=/^(0[1-8]{1}|[1-9]{1}[0-5]{1})?[0-9]{4}$/i.test(n.value);break;case 'RU':l=/^[0-9]{6}$/i.test(n.value);break;case 'SE':l=/^(S-)?\d{3}\s?\d{2}$/i.test(n.value);break;case 'SG':l=/^([0][1-9]|[1-6][0-9]|[7]([0-3]|[5-9])|[8][0-2])(\d{4})$/i.test(n.value);break;case 'SK':l=/^(\d{3})([ ]?)(\d{2})$/.test(n.value);break;case 'US':;default:l=/^\d{4,5}([\-]?\d{4})?$/.test(n.value);break}return{message:A(n.l10n?r.message||n.l10n.zipCode.country:r.message,n.l10n?n.l10n.zipCode.countries[a]:a),valid:l}}}}var Yt={between:b,blank:E,callback:V,choice:S,creditCard:y,date:I,different:O,digits:w,emailAddress:L,file:N,greaterThan:x,identical:M,integer:T,ip:k,lessThan:R,notEmpty:B,numeric:Z,promise:G,regexp:P,remote:K,stringCase:U,stringLength:Y,uri:J,base64:W,bic:z,color:_,cusip:X,ean:Q,ein:q,grid:$,hex:j,iban:ee,id:Ke,imei:Ue,imo:Ye,isbn:Je,isin:We,ismn:ze,issn:_e,mac:Xe,meid:Qe,phone:qe,rtn:$e,sedol:je,siren:et,siret:tt,step:nt,uuid:rt,vat:Dt,vin:Kt,zipCode:Ut},Jt=function(){function e(t,n){i(this,e);this.elements={};this.ee=p();this.filter=h();this.plugins={};this.results=new Map;this.validators={};this.form=t;this.fields=n}s(e,[{key:"on",value:function(e,t){this.ee.on(e,t);return this}},{key:"off",value:function(e,t){this.ee.off(e,t);return this}},{key:"emit",value:function(e){var t;for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];(t=this.ee).emit.apply(t,[e].concat(r));return this}},{key:"registerPlugin",value:function(e,t){if(this.plugins[e]){throw new Error("The plguin ".concat(e," is registered"))}t.setCore(this);t.install();this.plugins[e]=t;return this}},{key:"deregisterPlugin",value:function(e){var t=this.plugins[e];t&&t.uninstall();delete this.plugins[e];return this}},{key:"registerValidator",value:function(e,t){if(this.validators[e]){throw new Error("The validator ".concat(e," is registered"))}this.validators[e]=t;return this}},{key:"registerFilter",value:function(e,t){this.filter.add(e,t);return this}},{key:"deregisterFilter",value:function(e,t){this.filter.remove(e,t);return this}},{key:"executeFilter",value:function(e,t,n){return this.filter.execute(e,t,n)}},{key:"addField",value:function(e,t){var n=Object.assign({},{selector:'',validators:{}},t);this.fields[e]=this.fields[e]?{selector:n.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,n.validators)}:n;this.elements[e]=this.queryElements(e);this.emit('core.field.added',{elements:this.elements[e],field:e,options:this.fields[e]});return this}},{key:"removeField",value:function(e){if(!this.fields[e]){throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"))}var t=this.elements[e],n=this.fields[e];delete this.elements[e];delete this.fields[e];this.emit('core.field.removed',{elements:t,field:e,options:n});return this}},{key:"validate",value:function(){var e=this;this.emit('core.form.validating');return this.filter.execute('validate-pre',Promise.resolve(),[]).then(function(){return Promise.all(Object.keys(e.fields).map(function(t){return e.validateField(t)})).then(function(t){switch(!0){case t.indexOf('Invalid')!==-1:e.emit('core.form.invalid');return Promise.resolve('Invalid');case t.indexOf('NotValidated')!==-1:e.emit('core.form.notvalidated');return Promise.resolve('NotValidated');default:e.emit('core.form.valid');return Promise.resolve('Valid')}})})}},{key:"validateField",value:function(e){var t=this,n=this.results.get(e);if(n==='Valid'||n==='Invalid'){return Promise.resolve(n)}this.emit('core.field.validating',e);var r=this.elements[e];if(r.length===0){this.emit('core.field.valid',e);return Promise.resolve('Valid')}var a=r[0].getAttribute('type');if('radio'===a||'checkbox'===a||r.length===1){return this.validateElement(e,r[0])}else{return Promise.all(r.map(function(n){return t.validateElement(e,n)})).then(function(n){switch(!0){case n.indexOf('Invalid')!==-1:t.emit('core.field.invalid',e);t.results.set(e,'Invalid');return Promise.resolve('Invalid');case n.indexOf('NotValidated')!==-1:t.emit('core.field.notvalidated',e);t.results["delete"](e);return Promise.resolve('NotValidated');default:t.emit('core.field.valid',e);t.results.set(e,'Valid');return Promise.resolve('Valid')}})}}},{key:"validateElement",value:function(e,t){var n=this;this.results["delete"](e);var r=this.elements[e],a=this.filter.execute('element-ignored',!1,[e,t,r]);if(a){this.emit('core.element.ignored',{element:t,elements:r,field:e});return Promise.resolve('Ignored')}var l=this.fields[e].validators;this.emit('core.element.validating',{element:t,elements:r,field:e});var i=Object.keys(l).map(function(r){return function(){return n.executeValidator(e,t,r,l[r])}});return this.waterfall(i).then(function(a){var l=a.indexOf('Invalid')===-1;n.emit('core.element.validated',{element:t,elements:r,field:e,valid:l});var i=t.getAttribute('type');('radio'===i||'checkbox'===i||r.length===1)&&n.emit(l?'core.field.valid':'core.field.invalid',e);return Promise.resolve(l?'Valid':'Invalid')})["catch"](function(a){n.emit('core.element.notvalidated',{element:t,elements:r,field:e});return Promise.resolve(a)})}},{key:"executeValidator",value:function(e,t,n,r){var a=this,l=this.elements[e],i=this.filter.execute('validator-name',n,[n,e]);r.message=this.filter.execute('validator-message',r.message,[this.locale,e,i]);if(!this.validators[i]||r.enabled===!1){this.emit('core.validator.validated',{element:t,elements:l,field:e,result:this.normalizeResult(e,i,{valid:!0}),validator:i});return Promise.resolve('Valid')}var o=this.validators[i],s=this.getElementValue(e,t,i),c=this.filter.execute('field-should-validate',!0,[e,t,s,n]);if(!c){this.emit('core.validator.notvalidated',{element:t,elements:l,field:e,validator:n});return Promise.resolve('NotValidated')}this.emit('core.validator.validating',{element:t,elements:l,field:e,validator:n});var u=o().validate({element:t,elements:l,field:e,l10n:this.localization,options:r,value:s}),d='function'===typeof u.then;if(d){return u.then(function(r){var i=a.normalizeResult(e,n,r);a.emit('core.validator.validated',{element:t,elements:l,field:e,result:i,validator:n});return i.valid?'Valid':'Invalid'})}else{var f=this.normalizeResult(e,n,u);this.emit('core.validator.validated',{element:t,elements:l,field:e,result:f,validator:n});return Promise.resolve(f.valid?'Valid':'Invalid')}}},{key:"getElementValue",value:function(e,t,n){var r=v(this.form,e,t,this.elements[e]);return this.filter.execute('field-value',r,[r,e,t,n])}},{key:"getElements",value:function(e){return this.elements[e]}},{key:"getFields",value:function(){return this.fields}},{key:"getFormElement",value:function(){return this.form}},{key:"getLocale",value:function(){return this.locale}},{key:"getPlugin",value:function(e){return this.plugins[e]}},{key:"updateFieldStatus",value:function(e,t,n){var r=this,a=this.elements[e],l=a[0].getAttribute('type'),i='radio'===l||'checkbox'===l?[a[0]]:a;i.forEach(function(a){return r.updateElementStatus(e,a,t,n)});if(!n){switch(t){case 'NotValidated':this.emit('core.field.notvalidated',e);this.results["delete"](e);break;case 'Validating':this.emit('core.field.validating',e);this.results["delete"](e);break;case 'Valid':this.emit('core.field.valid',e);this.results.set(e,'Valid');break;case 'Invalid':this.emit('core.field.invalid',e);this.results.set(e,'Invalid');break}}return this}},{key:"updateElementStatus",value:function(e,t,n,r){var a=this,l=this.elements[e],i=this.fields[e].validators,o=r?[r]:Object.keys(i);switch(n){case 'NotValidated':o.forEach(function(n){return a.emit('core.validator.notvalidated',{element:t,elements:l,field:e,validator:n})});this.emit('core.element.notvalidated',{element:t,elements:l,field:e});break;case 'Validating':o.forEach(function(n){return a.emit('core.validator.validating',{element:t,elements:l,field:e,validator:n})});this.emit('core.element.validating',{element:t,elements:l,field:e});break;case 'Valid':o.forEach(function(n){return a.emit('core.validator.validated',{element:t,field:e,result:{message:i[n].message,valid:!0},validator:n})});this.emit('core.element.validated',{element:t,elements:l,field:e,valid:!0});break;case 'Invalid':o.forEach(function(n){return a.emit('core.validator.validated',{element:t,field:e,result:{message:i[n].message,valid:!1},validator:n})});this.emit('core.element.validated',{element:t,elements:l,field:e,valid:!1});break}return this}},{key:"resetForm",value:function(e){var t=this;Object.keys(this.fields).forEach(function(n){return t.resetField(n,e)});this.emit('core.form.reset',{reset:e});return this}},{key:"resetField",value:function(e,t){if(t){var n=this.elements[e],r=n[0].getAttribute('type');n.forEach(function(e){'radio'===r||'checkbox'===r?(e.removeAttribute('selected'),e.removeAttribute('checked'),e.checked=!1):(e.setAttribute('value',''),(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(e.value=''))})}this.updateFieldStatus(e,'NotValidated');this.emit('core.field.reset',{field:e,reset:t});return this}},{key:"revalidateField",value:function(e){this.updateFieldStatus(e,'NotValidated');return this.validateField(e)}},{key:"disableValidator",value:function(e,t){return this.toggleValidator(!1,e,t)}},{key:"enableValidator",value:function(e,t){return this.toggleValidator(!0,e,t)}},{key:"updateValidatorOption",value:function(e,t,n,r){this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][n]=r);return this}},{key:"destroy",value:function(){var e=this;Object.keys(this.plugins).forEach(function(t){return e.plugins[t].uninstall()});this.ee.clear();this.filter.clear();this.results.clear();this.plugins={};return this}},{key:"setLocale",value:function(e,t){this.locale=e;this.localization=t;return this}},{key:"waterfall",value:function(e){return e.reduce(function(e,t,n,r){return e.then(function(e){return t().then(function(t){e.push(t);return e})})},Promise.resolve([]))}},{key:"queryElements",value:function(e){var t=this.fields[e].selector?'#'===this.fields[e].selector.charAt(0)?"[id=\"".concat(this.fields[e].selector.substring(1),"\"]"):this.fields[e].selector:"[name=\"".concat(e,"\"]");return[].slice.call(this.form.querySelectorAll(t))}},{key:"normalizeResult",value:function(e,t,n){var r=this.fields[e].validators[t];return Object.assign({},n,{message:n.message||(r?r.message:'')||(this.localization&&this.localization[t]&&this.localization[t]["default"]?this.localization[t]["default"]:'')||"The field ".concat(e," is not valid")})}},{key:"toggleValidator",value:function(e,t,n){var r=this,a=this.fields[t].validators;n&&a&&a[n]?(this.fields[t].validators[n].enabled=e):n||Object.keys(a).forEach(function(n){return r.fields[t].validators[n].enabled=e});return this.updateFieldStatus(t,'NotValidated',n)}}]);return e}();function Wt(e,t){var n=Object.assign({},{fields:{},locale:'en_US',plugins:{}},t),r=new Jt(e,n.fields);r.setLocale(n.locale,n.localization);Object.keys(n.plugins).forEach(function(e){return r.registerPlugin(e,n.plugins[e])});Object.keys(Yt).forEach(function(e){return r.registerValidator(e,Yt[e])});Object.keys(n.fields).forEach(function(e){return r.addField(e,n.fields[e])});return r}var zt=function(){function e(t){i(this,e);this.opts=t}s(e,[{key:"setCore",value:function(e){this.core=e;return this}},{key:"install",value:function(){}},{key:"uninstall",value:function(){}}]);return e}(),_t={getFieldValue:v},Xt=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.opts=e||{};n.validatorNameFilter=n.getValidatorName.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.registerFilter('validator-name',this.validatorNameFilter)}},{key:"uninstall",value:function(){this.core.deregisterFilter('validator-name',this.validatorNameFilter)}},{key:"getValidatorName",value:function(e,t){return this.opts[e]||e}}]);return t}(zt),Qt=function(e){u(t,e);function t(){var e;i(this,t);e=g(this,d(t).call(this,{}));e.elementValidatedHandler=e.onElementValidated.bind(m(e));e.fieldValidHandler=e.onFieldValid.bind(m(e));e.fieldInvalidHandler=e.onFieldInvalid.bind(m(e));e.messageDisplayedHandler=e.onMessageDisplayed.bind(m(e));return e}s(t,[{key:"install",value:function(){this.core.on('core.field.valid',this.fieldValidHandler).on('core.field.invalid',this.fieldInvalidHandler).on('core.element.validated',this.elementValidatedHandler).on('plugins.message.displayed',this.messageDisplayedHandler)}},{key:"uninstall",value:function(){this.core.off('core.field.valid',this.fieldValidHandler).off('core.field.invalid',this.fieldInvalidHandler).off('core.element.validated',this.elementValidatedHandler).off('plugins.message.displayed',this.messageDisplayedHandler)}},{key:"onElementValidated",value:function(e){e.valid&&(e.element.setAttribute('aria-invalid','false'),e.element.removeAttribute('aria-describedby'))}},{key:"onFieldValid",value:function(e){var t=this.core.getElements(e);t&&t.forEach(function(e){e.setAttribute('aria-invalid','false');e.removeAttribute('aria-describedby')})}},{key:"onFieldInvalid",value:function(e){var t=this.core.getElements(e);t&&t.forEach(function(e){return e.setAttribute('aria-invalid','true')})}},{key:"onMessageDisplayed",value:function(e){e.messageElement.setAttribute('role','alert');e.messageElement.setAttribute('aria-hidden','false');var t=this.core.getElements(e.field),n=t.indexOf(e.element),r="js-fv-".concat(e.field,"-").concat(n,"-").concat(Date.now(),"-message");e.messageElement.setAttribute('id',r);e.element.setAttribute('aria-describedby',r);var a=e.element.getAttribute('type');('radio'===a||'checkbox'===a)&&t.forEach(function(e){return e.setAttribute('aria-describedby',r)})}}]);return t}(zt),qt=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.opts=Object.assign({},{html5Input:!1,pluginPrefix:'data-fvp-',prefix:'data-fv-'},e);return n}s(t,[{key:"install",value:function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach(function(n){return e.core.addField(n,t[n])})}},{key:"parseOptions",value:function(){var e=this,t=this.opts.prefix,n={},r=this.core.getFields(),a=this.core.getFormElement(),l=[].slice.call(a.querySelectorAll("[name], [".concat(t,"field]")));l.forEach(function(r){var a=e.parseElement(r);if(!e.isEmptyOption(a)){var l=r.getAttribute('name')||r.getAttribute("".concat(t,"field"));n[l]=Object.assign({},n[l],a)}});Object.keys(n).forEach(function(e){Object.keys(n[e].validators).forEach(function(t){n[e].validators[t].enabled=n[e].validators[t].enabled||!1;r[e]&&r[e].validators&&r[e].validators[t]&&Object.assign(n[e].validators[t],r[e].validators[t])})});return Object.assign({},r,n)}},{key:"createPluginInstance",value:function(e,t){var n=e.split('.'),r=window||this;for(var a=0,l=n.length;a<l;a++)r=r[n[a]];if(typeof r!=='function'){throw new Error("the plugin ".concat(e," doesn't exist"))}return new r(t)}},{key:"parsePlugins",value:function(){var e=this,t=this.core.getFormElement(),n=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),r=t.attributes.length,a={};for(var l=0;l<r;l++){var i=t.attributes[l].name,o=t.attributes[l].value,s=n.exec(i);if(s&&s.length===4){var u=this.toCamelCase(s[1]);a[u]=Object.assign({},s[3]?c({},this.toCamelCase(s[3]),o):{enabled:''===o||'true'===o},a[u])}}Object.keys(a).forEach(function(t){var n=a[t],r=n.enabled,l=n['class'];if(r&&l){delete n.enabled;delete n.clazz;var i=e.createPluginInstance(l,n);e.core.registerPlugin(t,i)}})}},{key:"isEmptyOption",value:function(e){var t=e.validators;return Object.keys(t).length===0&&t.constructor===Object}},{key:"parseElement",value:function(e){var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),n=e.attributes.length,r={},a=e.getAttribute('type');for(var l=0;l<n;l++){var i=e.attributes[l].name,o=e.attributes[l].value;if(this.opts.html5Input){switch(!0){case 'minlength'===i:r.stringLength=Object.assign({},{enabled:!0,min:parseInt(o,10)},r.stringLength);break;case 'maxlength'===i:r.stringLength=Object.assign({},{enabled:!0,max:parseInt(o,10)},r.stringLength);break;case 'pattern'===i:r.regexp=Object.assign({},{enabled:!0,regexp:o},r.regexp);break;case 'required'===i:r.notEmpty=Object.assign({},{enabled:!0},r.notEmpty);break;case 'type'===i&&'color'===o:r.color=Object.assign({},{enabled:!0,type:'hex'},r.color);break;case 'type'===i&&'email'===o:r.emailAddress=Object.assign({},{enabled:!0},r.emailAddress);break;case 'type'===i&&'url'===o:r.uri=Object.assign({},{enabled:!0},r.uri);break;case 'type'===i&&'range'===o:r.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute('max')),min:parseFloat(e.getAttribute('min'))},r.between);break;case 'min'===i&&a!=='date'&&a!=='range':r.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(o)},r.greaterThan);break;case 'max'===i&&a!=='date'&&a!=='range':r.lessThan=Object.assign({},{enabled:!0,max:parseFloat(o)},r.lessThan);break}}var s=t.exec(i);if(s&&s.length===4){var u=this.toCamelCase(s[1]);r[u]=Object.assign({},s[3]?c({},this.toCamelCase(s[3]),o):{enabled:''===o||'true'===o},r[u])}}return{validators:r}}},{key:"toUpperCase",value:function(e){return e.charAt(1).toUpperCase()}},{key:"toCamelCase",value:function(e){return e.replace(/-./g,this.toUpperCase)}}]);return t}(zt),$t=function(e){u(t,e);function t(){var e;i(this,t);e=g(this,d(t).call(this,{}));e.onValidHandler=e.onFormValid.bind(m(e));return e}s(t,[{key:"install",value:function(){var e=this.core.getFormElement();if(e.querySelectorAll('[type="submit"][name="submit"]').length){throw new Error('Do not use `submit` for the name attribute of submit button')}this.core.on('core.form.valid',this.onValidHandler)}},{key:"uninstall",value:function(){this.core.off('core.form.valid',this.onValidHandler)}},{key:"onFormValid",value:function(){var e=this.core.getFormElement();e instanceof HTMLFormElement&&e.submit()}}]);return t}(zt),jt=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.opts=e||{};n.triggerExecutedHandler=n.onTriggerExecuted.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.on('plugins.trigger.executed',this.triggerExecutedHandler)}},{key:"uninstall",value:function(){this.core.off('plugins.trigger.executed',this.triggerExecutedHandler)}},{key:"onTriggerExecuted",value:function(e){if(this.opts[e.field]){var t=this.opts[e.field].split(' '),n=!0,r=!1,a=void 0;try{for(var l=t[Symbol.iterator](),i;!(n=(i=l.next()).done);n=!0){var o=i.value,s=o.trim();this.opts[s]&&this.core.revalidateField(s)}}catch(e){r=!0,a=e}finally{try{!n&&l["return"]!=null&&l["return"]()}finally{if(r){throw a}}}}}}]);return t}(zt),en=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.opts=Object.assign({},{excluded:t.defaultIgnore},e);n.ignoreValidationFilter=n.ignoreValidation.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.registerFilter('element-ignored',this.ignoreValidationFilter)}},{key:"uninstall",value:function(){this.core.deregisterFilter('element-ignored',this.ignoreValidationFilter)}},{key:"ignoreValidation",value:function(e,t,n){return this.opts.excluded.apply(this,[e,t,n])}}],[{key:"defaultIgnore",value:function(e,t,n){var r=!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length),a=t.getAttribute('disabled');return a===''||a==='disabled'||t.getAttribute('type')==='hidden'||!r}}]);return t}(zt),tn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.statuses=new Map;n.opts=Object.assign({},{onStatusChanged:function(){}},e);n.elementValidatingHandler=n.onElementValidating.bind(m(n));n.elementValidatedHandler=n.onElementValidated.bind(m(n));n.elementNotValidatedHandler=n.onElementNotValidated.bind(m(n));n.elementIgnoredHandler=n.onElementIgnored.bind(m(n));n.fieldAddedHandler=n.onFieldAdded.bind(m(n));n.fieldRemovedHandler=n.onFieldRemoved.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.on('core.element.validating',this.elementValidatingHandler).on('core.element.validated',this.elementValidatedHandler).on('core.element.notvalidated',this.elementNotValidatedHandler).on('core.element.ignored',this.elementIgnoredHandler).on('core.field.added',this.fieldAddedHandler).on('core.field.removed',this.fieldRemovedHandler)}},{key:"uninstall",value:function(){this.statuses.clear();this.core.off('core.element.validating',this.elementValidatingHandler).off('core.element.validated',this.elementValidatedHandler).off('core.element.notvalidated',this.elementNotValidatedHandler).off('core.element.ignored',this.elementIgnoredHandler).off('core.field.added',this.fieldAddedHandler).off('core.field.removed',this.fieldRemovedHandler)}},{key:"areFieldsValid",value:function(){return Array.from(this.statuses.values()).every(function(e){return e==='Valid'||e==='NotValidated'||e==='Ignored'})}},{key:"getStatuses",value:function(){return this.statuses}},{key:"onFieldAdded",value:function(e){this.statuses.set(e.field,'NotValidated')}},{key:"onFieldRemoved",value:function(e){this.statuses.has(e.field)&&this.statuses["delete"](e.field);this.opts.onStatusChanged(this.areFieldsValid())}},{key:"onElementValidating",value:function(e){this.statuses.set(e.field,'Validating');this.opts.onStatusChanged(!1)}},{key:"onElementValidated",value:function(e){this.statuses.set(e.field,e.valid?'Valid':'Invalid');e.valid?this.opts.onStatusChanged(this.areFieldsValid()):this.opts.onStatusChanged(!1)}},{key:"onElementNotValidated",value:function(e){this.statuses.set(e.field,'NotValidated');this.opts.onStatusChanged(!1)}},{key:"onElementIgnored",value:function(e){this.statuses.set(e.field,'Ignored');this.opts.onStatusChanged(this.areFieldsValid())}}]);return t}(zt);function nn(e,t){t.split(' ').forEach(function(t){e.classList?e.classList.add(t):" ".concat(e.className," ").indexOf(" ".concat(t," "))&&(e.className+=" ".concat(t))})}function rn(e,t){t.split(' ').forEach(function(t){e.classList?e.classList.remove(t):e.className=e.className.replace(t,'')})}function an(e,t){var n=[],r=[];Object.keys(t).forEach(function(e){e&&(t[e]?n.push(e):r.push(e))});r.forEach(function(t){return rn(e,t)});n.forEach(function(t){return nn(e,t)})}function ln(e,t){var n=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;if(n){return n.call(e,t)}var r=[].slice.call(e.parentElement.querySelectorAll(t));return r.indexOf(e)>=0}function on(e,t){var n=e;while(n){if(ln(n,t)){break}n=n.parentElement}return n}var sn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.messages=new Map;n.defaultContainer=document.createElement('div');n.opts=Object.assign({},{container:function(e,t){return n.defaultContainer}},e);n.elementIgnoredHandler=n.onElementIgnored.bind(m(n));n.fieldAddedHandler=n.onFieldAdded.bind(m(n));n.fieldRemovedHandler=n.onFieldRemoved.bind(m(n));n.validatorValidatedHandler=n.onValidatorValidated.bind(m(n));n.validatorNotValidatedHandler=n.onValidatorNotValidated.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.getFormElement().appendChild(this.defaultContainer);this.core.on('core.element.ignored',this.elementIgnoredHandler).on('core.field.added',this.fieldAddedHandler).on('core.field.removed',this.fieldRemovedHandler).on('core.validator.validated',this.validatorValidatedHandler).on('core.validator.notvalidated',this.validatorNotValidatedHandler)}},{key:"uninstall",value:function(){this.core.getFormElement().removeChild(this.defaultContainer);this.messages.forEach(function(e){return e.parentNode.removeChild(e)});this.messages.clear();this.core.off('core.element.ignored',this.elementIgnoredHandler).off('core.field.added',this.fieldAddedHandler).off('core.field.removed',this.fieldRemovedHandler).off('core.validator.validated',this.validatorValidatedHandler).off('core.validator.notvalidated',this.validatorNotValidatedHandler)}},{key:"onFieldAdded",value:function(e){var t=this,n=e.elements;n&&(n.forEach(function(e){var n=t.messages.get(e);n&&(n.parentNode.removeChild(n),t.messages["delete"](e))}),this.prepareFieldContainer(e.field,n))}},{key:"onFieldRemoved",value:function(e){var t=this;if(!e.elements.length||!e.field){return}var n=e.elements[0].getAttribute('type'),r='radio'===n||'checkbox'===n?[e.elements[0]]:e.elements;r.forEach(function(e){if(t.messages.has(e)){var n=t.messages.get(e);n.parentNode.removeChild(n);t.messages["delete"](e)}})}},{key:"prepareFieldContainer",value:function(e,t){var n=this;if(t.length){var r=t[0].getAttribute('type');'radio'===r||'checkbox'===r?this.prepareElementContainer(e,t[0],t):t.forEach(function(r){return n.prepareElementContainer(e,r,t)})}}},{key:"prepareElementContainer",value:function(e,t,n){var r;switch(!0){case 'string'===typeof this.opts.container:var a=this.opts.container;a='#'===a.charAt(0)?"[id=\"".concat(a.substring(1),"\"]"):a;r=this.core.getFormElement().querySelector(a);break;default:r=this.opts.container(e,t);break}var l=document.createElement('div');r.appendChild(l);an(l,{'fv-plugins-message-container':!0});this.core.emit('plugins.message.placed',{element:t,elements:n,field:e,messageElement:l});this.messages.set(t,l)}},{key:"getMessage",value:function(e){return typeof e.message==='string'?e.message:e.message[this.core.getLocale()]}},{key:"onValidatorValidated",value:function(e){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element;if(this.messages.has(r)){var a=this.messages.get(r),l=a.querySelector("[data-field=\"".concat(e.field,"\"][data-validator=\"").concat(e.validator,"\"]"));if(!l&&!e.result.valid){var i=document.createElement('div');i.innerHTML=this.getMessage(e.result);i.setAttribute('data-field',e.field);i.setAttribute('data-validator',e.validator);this.opts.clazz&&an(i,c({},this.opts.clazz,!0));a.appendChild(i);this.core.emit('plugins.message.displayed',{element:e.element,field:e.field,message:e.result.message,messageElement:i,meta:e.result.meta,validator:e.validator})}else l&&!e.result.valid?(l.innerHTML=this.getMessage(e.result),this.core.emit('plugins.message.displayed',{element:e.element,field:e.field,message:e.result.message,messageElement:l,meta:e.result.meta,validator:e.validator})):l&&e.result.valid&&a.removeChild(l)}}},{key:"onValidatorNotValidated",value:function(e){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element;if(this.messages.has(r)){var a=this.messages.get(r),l=a.querySelector("[data-field=\"".concat(e.field,"\"][data-validator=\"").concat(e.validator,"\"]"));l&&a.removeChild(l)}}},{key:"onElementIgnored",value:function(e){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element;if(this.messages.has(r)){var a=this.messages.get(r),l=[].slice.call(a.querySelectorAll("[data-field=\"".concat(e.field,"\"]")));l.forEach(function(e){a.removeChild(e)})}}}],[{key:"getClosestContainer",value:function(e,t,n){var r=e;while(r){if(r===t){break}r=r.parentElement;if(n.test(r.className)){break}}return r}}]);return t}(zt),cn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.results=new Map;n.containers=new Map;n.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:'',eleValidClass:'',rowClasses:'',rowValidatingClass:''},e);n.elementIgnoredHandler=n.onElementIgnored.bind(m(n));n.elementValidatingHandler=n.onElementValidating.bind(m(n));n.elementValidatedHandler=n.onElementValidated.bind(m(n));n.elementNotValidatedHandler=n.onElementNotValidated.bind(m(n));n.iconPlacedHandler=n.onIconPlaced.bind(m(n));n.fieldAddedHandler=n.onFieldAdded.bind(m(n));n.fieldRemovedHandler=n.onFieldRemoved.bind(m(n));return n}s(t,[{key:"install",value:function(){var e,t=this;an(this.core.getFormElement(),(e={}, c(e,this.opts.formClass,!0), c(e,'fv-plugins-framework',!0), e));this.core.on('core.element.ignored',this.elementIgnoredHandler).on('core.element.validating',this.elementValidatingHandler).on('core.element.validated',this.elementValidatedHandler).on('core.element.notvalidated',this.elementNotValidatedHandler).on('plugins.icon.placed',this.iconPlacedHandler).on('core.field.added',this.fieldAddedHandler).on('core.field.removed',this.fieldRemovedHandler);this.opts.defaultMessageContainer&&this.core.registerPlugin('___frameworkMessage',new sn({clazz:this.opts.messageClass,container:function(e,n){var r='string'===typeof t.opts.rowSelector?t.opts.rowSelector:t.opts.rowSelector(e,n),a=on(n,r);return sn.getClosestContainer(n,a,t.opts.rowPattern)}}))}},{key:"uninstall",value:function(){var e;this.results.clear();this.containers.clear();an(this.core.getFormElement(),(e={}, c(e,this.opts.formClass,!1), c(e,'fv-plugins-framework',!1), e));this.core.off('core.element.ignored',this.elementIgnoredHandler).off('core.element.validating',this.elementValidatingHandler).off('core.element.validated',this.elementValidatedHandler).off('core.element.notvalidated',this.elementNotValidatedHandler).off('plugins.icon.placed',this.iconPlacedHandler).off('core.field.added',this.fieldAddedHandler).off('core.field.removed',this.fieldRemovedHandler)}},{key:"onIconPlaced",value:function(e){}},{key:"onFieldAdded",value:function(e){var t=this,n=e.elements;n&&(n.forEach(function(e){var n=t.containers.get(e);if(n){var r;an(n,(r={}, c(r,t.opts.rowInvalidClass,!1), c(r,t.opts.rowValidatingClass,!1), c(r,t.opts.rowValidClass,!1), c(r,'fv-plugins-icon-container',!1), r));t.containers["delete"](e)}}),this.prepareFieldContainer(e.field,n))}},{key:"onFieldRemoved",value:function(e){var t=this;e.elements.forEach(function(e){var n=t.containers.get(e);if(n){var r;an(n,(r={}, c(r,t.opts.rowInvalidClass,!1), c(r,t.opts.rowValidatingClass,!1), c(r,t.opts.rowValidClass,!1), r))}})}},{key:"prepareFieldContainer",value:function(e,t){var n=this;if(t.length){var r=t[0].getAttribute('type');'radio'===r||'checkbox'===r?this.prepareElementContainer(e,t[0]):t.forEach(function(t){return n.prepareElementContainer(e,t)})}}},{key:"prepareElementContainer",value:function(e,t){var n='string'===typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t),r=on(t,n);if(r!==t){var a;an(r,(a={}, c(a,this.opts.rowClasses,!0), c(a,'fv-plugins-icon-container',!0), a));this.containers.set(t,r)}}},{key:"onElementValidating",value:function(e){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element,a=this.containers.get(r);if(a){var l;an(a,(l={}, c(l,this.opts.rowInvalidClass,!1), c(l,this.opts.rowValidatingClass,!0), c(l,this.opts.rowValidClass,!1), l))}}},{key:"onElementNotValidated",value:function(e){this.removeClasses(e.element,e.elements)}},{key:"onElementIgnored",value:function(e){this.removeClasses(e.element,e.elements)}},{key:"removeClasses",value:function(e,t){var n,r=e.getAttribute('type'),a='radio'===r||'checkbox'===r?t[0]:e;an(a,(n={}, c(n,this.opts.eleValidClass,!1), c(n,this.opts.eleInvalidClass,!1), n));var l=this.containers.get(a);if(l){var i;an(l,(i={}, c(i,this.opts.rowInvalidClass,!1), c(i,this.opts.rowValidatingClass,!1), c(i,this.opts.rowValidClass,!1), i))}}},{key:"onElementValidated",value:function(e){var t,n=this,r=e.elements,a=e.element.getAttribute('type'),l='radio'===a||'checkbox'===a?r[0]:e.element;an(l,(t={}, c(t,this.opts.eleValidClass,e.valid), c(t,this.opts.eleInvalidClass,!e.valid), t));var i=this.containers.get(l);if(i){if(!e.valid){var o;this.results.set(l,!1);an(i,(o={}, c(o,this.opts.rowInvalidClass,!0), c(o,this.opts.rowValidatingClass,!1), c(o,this.opts.rowValidClass,!1), o))}else{this.results["delete"](l);var s=!0;this.containers.forEach(function(e,t){e===i&&n.results.get(t)===!1&&(s=!1)});if(s){var u;an(i,(u={}, c(u,this.opts.rowInvalidClass,!1), c(u,this.opts.rowValidatingClass,!1), c(u,this.opts.rowValidClass,!0), u))}}}}}]);return t}(zt),un=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.icons=new Map;n.opts=Object.assign({},{invalid:'fv-plugins-icon--invalid',onPlaced:function(){},onSet:function(){},valid:'fv-plugins-icon--valid',validating:'fv-plugins-icon--validating'},e);n.elementValidatingHandler=n.onElementValidating.bind(m(n));n.elementValidatedHandler=n.onElementValidated.bind(m(n));n.elementNotValidatedHandler=n.onElementNotValidated.bind(m(n));n.elementIgnoredHandler=n.onElementIgnored.bind(m(n));n.fieldAddedHandler=n.onFieldAdded.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.on('core.element.validating',this.elementValidatingHandler).on('core.element.validated',this.elementValidatedHandler).on('core.element.notvalidated',this.elementNotValidatedHandler).on('core.element.ignored',this.elementIgnoredHandler).on('core.field.added',this.fieldAddedHandler)}},{key:"uninstall",value:function(){this.icons.forEach(function(e){return e.parentNode.removeChild(e)});this.icons.clear();this.core.off('core.element.validating',this.elementValidatingHandler).off('core.element.validated',this.elementValidatedHandler).off('core.element.notvalidated',this.elementNotValidatedHandler).off('core.element.ignored',this.elementIgnoredHandler).off('core.field.added',this.fieldAddedHandler)}},{key:"onFieldAdded",value:function(e){var t=this,n=e.elements;n&&(n.forEach(function(e){var n=t.icons.get(e);n&&(n.parentNode.removeChild(n),t.icons["delete"](e))}),this.prepareFieldIcon(e.field,n))}},{key:"prepareFieldIcon",value:function(e,t){var n=this;if(t.length){var r=t[0].getAttribute('type');'radio'===r||'checkbox'===r?this.prepareElementIcon(e,t[0]):t.forEach(function(t){return n.prepareElementIcon(e,t)})}}},{key:"prepareElementIcon",value:function(e,t){var n=document.createElement('i');n.setAttribute('data-field',e);t.parentNode.insertBefore(n,t.nextSibling);an(n,{'fv-plugins-icon':!0});var r={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:n};this.core.emit('plugins.icon.placed',r);this.opts.onPlaced(r);this.icons.set(t,n)}},{key:"onElementValidating",value:function(e){var t,n=this.setClasses(e.field,e.element,e.elements,(t={}, c(t,this.opts.invalid,!1), c(t,this.opts.valid,!1), c(t,this.opts.validating,!0), t)),r={element:e.element,field:e.field,iconElement:n,status:'Validating'};this.core.emit('plugins.icon.set',r);this.opts.onSet(r)}},{key:"onElementValidated",value:function(e){var t,n=this.setClasses(e.field,e.element,e.elements,(t={}, c(t,this.opts.invalid,!e.valid), c(t,this.opts.valid,e.valid), c(t,this.opts.validating,!1), t)),r={element:e.element,field:e.field,iconElement:n,status:e.valid?'Valid':'Invalid'};this.core.emit('plugins.icon.set',r);this.opts.onSet(r)}},{key:"onElementNotValidated",value:function(e){var t,n=this.setClasses(e.field,e.element,e.elements,(t={}, c(t,this.opts.invalid,!1), c(t,this.opts.valid,!1), c(t,this.opts.validating,!1), t)),r={element:e.element,field:e.field,iconElement:n,status:'NotValidated'};this.core.emit('plugins.icon.set',r);this.opts.onSet(r)}},{key:"onElementIgnored",value:function(e){var t,n=this.setClasses(e.field,e.element,e.elements,(t={}, c(t,this.opts.invalid,!1), c(t,this.opts.valid,!1), c(t,this.opts.validating,!1), t)),r={element:e.element,field:e.field,iconElement:n,status:'Ignored'};this.core.emit('plugins.icon.set',r);this.opts.onSet(r)}},{key:"setClasses",value:function(e,t,n,r){var a=t.getAttribute('type'),l='radio'===a||'checkbox'===a?n[0]:t;if(this.icons.has(l)){var i=this.icons.get(l);an(i,r);return i}else{return null}}}]);return t}(zt),dn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.invalidFields=new Map;n.opts=Object.assign({},{enabled:!0},e);n.validatorHandler=n.onValidatorValidated.bind(m(n));n.shouldValidateFilter=n.shouldValidate.bind(m(n));n.fieldAddedHandler=n.onFieldAdded.bind(m(n));n.elementNotValidatedHandler=n.onElementNotValidated.bind(m(n));n.elementValidatingHandler=n.onElementValidating.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.on('core.validator.validated',this.validatorHandler).on('core.field.added',this.fieldAddedHandler).on('core.element.notvalidated',this.elementNotValidatedHandler).on('core.element.validating',this.elementValidatingHandler).registerFilter('field-should-validate',this.shouldValidateFilter)}},{key:"uninstall",value:function(){this.invalidFields.clear();this.core.off('core.validator.validated',this.validatorHandler).off('core.field.added',this.fieldAddedHandler).off('core.element.notvalidated',this.elementNotValidatedHandler).off('core.element.validating',this.elementValidatingHandler).deregisterFilter('field-should-validate',this.shouldValidateFilter)}},{key:"shouldValidate",value:function(e,t,n,r){var a=(this.opts.enabled===!0||this.opts.enabled[e]===!0)&&this.invalidFields.has(t)&&!!this.invalidFields.get(t).length&&this.invalidFields.get(t).indexOf(r)===-1;return!a}},{key:"onValidatorValidated",value:function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],n=t.indexOf(e.validator);e.result.valid&&n>=0?t.splice(n,1):!e.result.valid&&n===-1&&t.push(e.validator);this.invalidFields.set(e.element,t)}},{key:"onFieldAdded",value:function(e){e.elements&&this.clearInvalidFields(e.elements)}},{key:"onElementNotValidated",value:function(e){this.clearInvalidFields(e.elements)}},{key:"onElementValidating",value:function(e){this.clearInvalidFields(e.elements)}},{key:"clearInvalidFields",value:function(e){var t=this;e.forEach(function(e){return t.invalidFields["delete"](e)})}}]);return t}(zt),fn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.isFormValid=!1;n.opts=Object.assign({},{aspNetButton:!1,selector:'[type="submit"]:not([formnovalidate])'},e);n.submitHandler=n.handleSubmitEvent.bind(m(n));n.buttonClickHandler=n.handleClickEvent.bind(m(n));return n}s(t,[{key:"install",value:function(){var e=this;if(!(this.core.getFormElement() instanceof HTMLFormElement)){return}var t=this.core.getFormElement();this.selectorButtons=[].slice.call(t.querySelectorAll(this.opts.selector));this.submitButtons=[].slice.call(t.querySelectorAll('[type="submit"]'));t.setAttribute('novalidate','novalidate');t.addEventListener('submit',this.submitHandler);this.hiddenClickedEle=document.createElement('input');this.hiddenClickedEle.setAttribute('type','hidden');t.appendChild(this.hiddenClickedEle);this.submitButtons.forEach(function(t){t.addEventListener('click',e.buttonClickHandler)})}},{key:"uninstall",value:function(){var e=this,t=this.core.getFormElement();t instanceof HTMLFormElement&&t.removeEventListener('submit',this.submitHandler);this.submitButtons.forEach(function(t){t.removeEventListener('click',e.buttonClickHandler)});this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle)}},{key:"handleSubmitEvent",value:function(e){this.validateForm(e)}},{key:"handleClickEvent",value:function(e){var t=e.currentTarget;if(t instanceof HTMLElement&&this.selectorButtons.indexOf(t)!==-1){if(!(this.opts.aspNetButton&&this.isFormValid===!0)){var n=this.core.getFormElement();n.removeEventListener('submit',this.submitHandler);this.clickedButton=e.target;var r=this.clickedButton.getAttribute('name'),a=this.clickedButton.getAttribute('value');r&&a&&(this.hiddenClickedEle.setAttribute('name',r),this.hiddenClickedEle.setAttribute('value',a));this.validateForm(e)}}}},{key:"validateForm",value:function(e){var t=this;e.preventDefault();this.core.validate().then(function(e){e==='Valid'&&t.opts.aspNetButton&&!t.isFormValid&&t.clickedButton&&(t.isFormValid=!0,t.clickedButton.removeEventListener('click',t.buttonClickHandler),t.clickedButton.click())})}}]);return t}(zt),mn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.messages=new Map;n.opts=Object.assign({},{placement:'top',trigger:'click'},e);n.iconPlacedHandler=n.onIconPlaced.bind(m(n));n.validatorValidatedHandler=n.onValidatorValidated.bind(m(n));n.elementValidatedHandler=n.onElementValidated.bind(m(n));n.documentClickHandler=n.onDocumentClicked.bind(m(n));return n}s(t,[{key:"install",value:function(){this.tip=document.createElement('div');an(this.tip,c({'fv-plugins-tooltip':!0},"fv-plugins-tooltip--".concat(this.opts.placement),!0));document.body.appendChild(this.tip);this.core.on('plugins.icon.placed',this.iconPlacedHandler).on('core.validator.validated',this.validatorValidatedHandler).on('core.element.validated',this.elementValidatedHandler);'click'===this.opts.trigger&&document.addEventListener('click',this.documentClickHandler)}},{key:"uninstall",value:function(){this.messages.clear();document.body.removeChild(this.tip);this.core.off('plugins.icon.placed',this.iconPlacedHandler).off('core.validator.validated',this.validatorValidatedHandler).off('core.element.validated',this.elementValidatedHandler);'click'===this.opts.trigger&&document.removeEventListener('click',this.documentClickHandler)}},{key:"onIconPlaced",value:function(e){var t=this;an(e.iconElement,{'fv-plugins-tooltip-icon':!0});switch(this.opts.trigger){case 'hover':e.iconElement.addEventListener('mouseenter',function(n){return t.show(e.element,n)});e.iconElement.addEventListener('mouseleave',function(e){return t.hide()});break;case 'click':;default:e.iconElement.addEventListener('click',function(n){return t.show(e.element,n)});break}}},{key:"onValidatorValidated",value:function(e){if(!e.result.valid){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element,a=typeof e.result.message==='string'?e.result.message:e.result.message[this.core.getLocale()];this.messages.set(r,a)}}},{key:"onElementValidated",value:function(e){if(e.valid){var t=e.elements,n=e.element.getAttribute('type'),r='radio'===n||'checkbox'===n?t[0]:e.element;this.messages["delete"](r)}}},{key:"onDocumentClicked",value:function(e){this.hide()}},{key:"show",value:function(e,t){t.preventDefault();t.stopPropagation();if(!this.messages.has(e)){return}an(this.tip,{'fv-plugins-tooltip--hide':!1});this.tip.innerHTML="<span class=\"fv-plugins-tooltip__content\">".concat(this.messages.get(e),"</span>");var n=t.target,r=n.getBoundingClientRect(),a=0,l=0;switch(this.opts.placement){case 'top':;default:a=r.top-r.height;l=r.left+r.width/2-this.tip.clientWidth/2;break;case 'top-left':a=r.top-r.height;l=r.left;break;case 'top-right':a=r.top-r.height;l=r.left+r.width-this.tip.clientWidth;break;case 'bottom':a=r.top+r.height;l=r.left+r.width/2-this.tip.clientWidth/2;break;case 'bottom-left':a=r.top+r.height;l=r.left;break;case 'bottom-right':a=r.top+r.height;l=r.left+r.width-this.tip.clientWidth;break;case 'left':a=r.top+r.height/2-this.tip.clientHeight/2;l=r.left-this.tip.clientWidth;break;case 'right':a=r.top+r.height/2-this.tip.clientHeight/2;l=r.left+r.width;break}var i=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,o=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;a+=i;l+=o;this.tip.setAttribute('style',"top: ".concat(a,"px; left: ").concat(l,"px"))}},{key:"hide",value:function(){an(this.tip,{'fv-plugins-tooltip--hide':!0})}}]);return t}(zt),gn=function(e){u(t,e);function t(e){var n;i(this,t);n=g(this,d(t).call(this,e));n.handlers=[];n.timers=new Map;n.ieVersion=function(){var e=3,t=document.createElement('div'),n=t.all||[];while(t.innerHTML='<!--[if gt IE '+ ++e+']><br><![endif]-->', n[0]);;return e>4?e:document.documentMode}();var r=document.createElement('div');n.defaultEvent=n.ieVersion===9||!('oninput' in r)?'keyup':'input';n.opts=Object.assign({},{delay:0,event:n.defaultEvent,threshold:0},e);n.fieldAddedHandler=n.onFieldAdded.bind(m(n));n.fieldRemovedHandler=n.onFieldRemoved.bind(m(n));return n}s(t,[{key:"install",value:function(){this.core.on('core.field.added',this.fieldAddedHandler).on('core.field.removed',this.fieldRemovedHandler)}},{key:"uninstall",value:function(){this.handlers.forEach(function(e){return e.element.removeEventListener(e.event,e.handler)});this.handlers=[];this.timers.forEach(function(e){return window.clearTimeout(e)});this.timers.clear();this.core.off('core.field.added',this.fieldAddedHandler).off('core.field.removed',this.fieldRemovedHandler)}},{key:"prepareHandler",value:function(e,t){var n=this;t.forEach(function(t){var r=[];switch(!0){case !!n.opts.event&&n.opts.event[e]===!1:r=[];break;case !!n.opts.event&&!!n.opts.event[e]:r=n.opts.event[e].split(' ');break;case 'string'===typeof n.opts.event&&n.opts.event!==n.defaultEvent:r=n.opts.event.split(' ');break;default:var i=t.getAttribute('type');var l=t.tagName.toLowerCase();var a='radio'===i||'checkbox'===i||'file'===i||'select'===l?'change':n.ieVersion>=10&&t.getAttribute('placeholder')?'keyup':n.defaultEvent;r=[a];break}r.forEach(function(r){var a=function(r){return n.handleEvent(r,e,t)};n.handlers.push({element:t,event:r,field:e,handler:a});t.addEventListener(r,a)})})}},{key:"handleEvent",value:function(e,t,n){var r=this;if(this.exceedThreshold(t,n)&&this.core.executeFilter('plugins-trigger-should-validate',!0,[t,n])){var a=function(){return r.core.validateElement(t,n).then(function(a){r.core.emit('plugins.trigger.executed',{element:n,event:e,field:t})})},l=this.opts.delay[t]||this.opts.delay;if(l===0)a();else{var i=this.timers.get(n);i&&window.clearTimeout(i);this.timers.set(n,window.setTimeout(a,l*1e3))}}}},{key:"onFieldAdded",value:function(e){this.handlers.filter(function(t){return t.field===e.field}).forEach(function(e){return e.element.removeEventListener(e.event,e.handler)});this.prepareHandler(e.field,e.elements)}},{key:"onFieldRemoved",value:function(e){this.handlers.filter(function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0}).forEach(function(e){return e.element.removeEventListener(e.event,e.handler)})}},{key:"exceedThreshold",value:function(e,t){var n=this.opts.threshold[e]===0||this.opts.threshold===0?!1:this.opts.threshold[e]||this.opts.threshold;if(!n){return!0}var r=t.getAttribute('type');if(['button','checkbox','file','hidden','image','radio','reset','submit'].indexOf(r)!==-1){return!0}var a=this.core.getElementValue(e,t);return a.length>=n}}]);return t}(zt),pn={Alias:Xt,Aria:Qt,Declarative:qt,DefaultSubmit:$t,Dependency:jt,Excluded:en,FieldStatus:tn,Framework:cn,Icon:un,Message:sn,Sequence:dn,SubmitButton:fn,Tooltip:mn,Trigger:gn};function hn(e,t){return e.classList?e.classList.contains(t):new RegExp("(^| )".concat(t,"( |$)"),'gi').test(e.className)}var vn={call:C,classSet:an,closest:on,fetch:D,format:A,hasClass:hn,isValidDate:F},An={};e.Plugin=zt;e.algorithms=l;e.filters=_t;e.formValidation=Wt;e.locales=An;e.plugins=pn;e.utils=vn;e.validators=Yt;Object.defineProperty(e,'__esModule',{value:!0})}))

!function(e,n){typeof exports==='object'&&typeof module!=='undefined'?module.exports=n():typeof define==='function'&&define.amd?define(n):(e=e||self, (e.FormValidation=e.FormValidation||{}, e.FormValidation.plugins=e.FormValidation.plugins||{}, e.FormValidation.plugins.Bootstrap=n()))}(this,(function(){"use strict";function e(e,n){if(!(e instanceof n)){throw new TypeError("Cannot call a class as a function")}}function n(e,n){for(var t=0;t<n.length;t++){var i=n[t];i.enumerable=i.enumerable||!1;i.configurable=!0;"value" in i&&(i.writable=!0);Object.defineProperty(e,i.key,i)}}function t(e,t,i){t&&n(e.prototype,t);i&&n(e,i);return e}function i(e,n){if(typeof n!=="function"&&n!==null){throw new TypeError("Super expression must either be null or a function")}e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}});n&&r(e,n)}function o(e){o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)};return o(e)}function r(e,n){r=Object.setPrototypeOf||function(e,n){e.__proto__=n;return e};return r(e,n)}function l(e){if(e===void 0){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}function c(e,n){if(n&&(typeof n==="object"||typeof n==="function")){return n}return l(e)}var u=FormValidation.utils.classSet,a=FormValidation.utils.hasClass,s=FormValidation.plugins.Framework,p=function(n){i(r,n);function r(n){e(this,r);return c(this,o(r).call(this,Object.assign({},{eleInvalidClass:'is-invalid',eleValidClass:'is-valid',formClass:'fv-plugins-bootstrap',messageClass:'fv-help-block',rowInvalidClass:'has-danger',rowPattern:/^(.*)(col|offset)(-(sm|md|lg|xl))*-[0-9]+(.*)$/,rowSelector:'.form-group',rowValidClass:'has-success'},n)))}t(r,[{key:"onIconPlaced",value:function(e){var n=e.element.parentElement;a(n,'input-group')&&n.parentElement.insertBefore(e.iconElement,n.nextSibling);var t=e.element.getAttribute('type');if('checkbox'===t||'radio'===t){var i=n.parentElement;a(n,'form-check')?(u(e.iconElement,{'fv-plugins-icon-check':!0}),n.parentElement.insertBefore(e.iconElement,n.nextSibling)):a(n.parentElement,'form-check')&&(u(e.iconElement,{'fv-plugins-icon-check':!0}),i.parentElement.insertBefore(e.iconElement,i.nextSibling))}}}]);return r}(s);return p}))