$(document).ready(function() {
    let inputs = ['site_emailsmtp', 'site_emailport', 'site_emailssl'];
    $('input[name="smtp_provider"]').on('change', function() {
        let smtpProvider = $('input[name="smtp_provider"]:checked').val();
        $('.help-content').addClass('d-none');
        $('.help-' + smtpProvider).removeClass('d-none');
        if (smtpProvider == 'custom') {
            inputs.forEach(function (input) {
                $('input[name="' + input + '"]').closest('.form-group').removeClass('d-none');
            });
            $('input[name="site_emailusername"]').closest('.form-group').removeClass('d-none');
            $('input[name="site_emailpassword"]').closest('.form-group').removeClass('d-none');
        } else {
            inputs.forEach(function(input) {
                $('input[name="' + input + '"]').closest('.form-group').addClass('d-none');
            });

            if (smtpProvider == 'outlook') {
                $('input[name="site_emailusername"]').closest('.form-group').addClass('d-none');
                $('input[name="site_emailpassword"]').closest('.form-group').addClass('d-none');
            } else {
                $('input[name="site_emailusername"]').closest('.form-group').removeClass('d-none');
                $('input[name="site_emailpassword"]').closest('.form-group').removeClass('d-none');
            }
        }
    });

    $('input[name="smtp_provider"]').trigger('change');

    $('#site_emailpassword').on('input', function() {
        $(this).val($(this).val().replace(/\s/g,''));
    });
});
