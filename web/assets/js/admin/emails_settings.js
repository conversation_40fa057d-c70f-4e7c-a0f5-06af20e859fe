$(document).ready(function() {
    let inputs = ['site_emailsmtp', 'site_emailport', 'site_emailssl'];
    $('input[name="smtp_provider"]').on('change', function() {
        let smtpProvider = $('input[name="smtp_provider"]:checked').val();
        $('.help-content').addClass('d-none');
        $('.help-' + smtpProvider).removeClass('d-none');
        if (smtpProvider == 'custom') {
            inputs.forEach(function(input) {
                $('input[name="' + input + '"]').closest('.form-group').removeClass('d-none');
            });
        } else {
            inputs.forEach(function(input) {
                $('input[name="' + input + '"]').closest('.form-group').addClass('d-none');
            });
        }
    });

    $('input[name="smtp_provider"]').trigger('change');

    $('#site_emailpassword').on('input', function() {
        $(this).val($(this).val().replace(/\s/g,''));
    });

    // OAuth functionality
    function toggleEmailMethod() {
        let smtpGroup = $('.form-group-smtp');
        let oauthGroup = $('.form-group-oauth');

        // Check if OAuth is connected
        let isOAuthConnected = $('.alert-success').length > 0 && $('.alert-success').text().includes('Connexion OAuth active');

        if (isOAuthConnected) {
            // If OAuth is connected, suggest using it
            smtpGroup.find('.card-header').append('<div class="badge badge-warning ml-2">OAuth disponible</div>');
        }
    }

    // Initialize OAuth status check
    toggleEmailMethod();

    // Handle OAuth connection status updates
    $(document).on('click', '[href*="oauth/microsoft"]', function(e) {
        if ($(this).attr('href').includes('authorize')) {
            // Show loading state
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Connexion en cours...');
        }
    });
});
