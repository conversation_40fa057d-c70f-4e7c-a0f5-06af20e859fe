function CalcPrice(id, type) {
    var tva = $('input[name="products['+id+'][vat]"]').val();
    var ht = $('input[name="products['+id+'][price_tax_excl]"]').val();
    var ttc = $('input[name="products['+id+'][price_tax_incl]"]').val();
    var qty = $('input[name="products['+id+'][quantity]"]').val();
    if (!qty || qty <= 0) {
        qty = 1;
    }
    qty = parseFloat(qty);

    if (type == 'ht') {
        if (tva == 0) {
            ttc = ht * qty;
        } else {
            ttc = ht * qty * (1+tva/100);
        }
        ttc = parseFloat(ttc);
        ttc = roundUpto(ttc, 2);
        $('input[name="products['+id+'][price_tax_incl]"]').val(ttc);
    } else {
        if (tva == 0) {
            ht = ttc / qty;
        } else {
            ht = ttc / qty / (1+tva/100);
        }
        ht = parseFloat(ht);
        ht = roundUpto(ht, 2);
        $('input[name="products['+id+'][price_tax_excl]"]').val(ht);
    }

    var amount_tax_excl = 0;
    var amount_tax_incl = 0;
    var total_vat = 0;

    $('input.price_tax_excl').each(function() {
        var qty = parseFloat($('input[name="products['+$(this).parent().parent().data('id')+'][quantity]"]').val());
        amount_tax_excl += parseFloat(qty * $(this).val());
    });
    $('input.price_tax_incl').each(function() {
        amount_tax_incl += parseFloat($(this).val());
    });

    amount_tax_excl = roundUpto(amount_tax_excl, 2);
    amount_tax_incl = roundUpto(amount_tax_incl, 2);
    total_vat = roundUpto(amount_tax_incl - amount_tax_excl, 2);

    // Mise à jour des totaux
    $('#amount_tax_excl').html(amount_tax_excl);
    $('#amount_tax_incl').html(amount_tax_incl);
    $('#amount_tax').html(total_vat);

    calcPayments();
}

$(document).ready(function() {
    autoCalcPrice();

    if ($('.table-payments tbody tr').length) {
        handlePayments();
    }

    if ($('select#client_id').length) {
        $('select#client_id').on('change', function () {
            fillClientInfos($(this).val(), '#cardinfos');
        });
    }
});

function autoCalcPrice() {
    $('input.quantity').unbind('change');
    $('input.price_tax_excl').unbind('change');
    $('input.tva').unbind('change');
    $('input.price_tax_incl').unbind('change');

    $('input.quantity').bind('change', function(event) {
        //CalcPrice($(this).parent().parent().data('id'), 'ttc');
        CalcPrice($(this).parent().parent().data('id'), 'ht');
    });
    $('input.price_tax_excl').bind('change', function(event) {
        CalcPrice($(this).parent().parent().data('id'), 'ht');
    });
    $('input.price_tax_incl').on('change', function(event) {
        CalcPrice($(this).parent().parent().data('id'), 'ttc');
    });
    $('input.tva').on('change', function(event) {
        CalcPrice($(this).parent().parent().data('id'), 'ht');
    });
    $('input.quantity').trigger('change');
}

function AddProduct() {
    var last_id = $('.table-products tbody tr:last').data('id');
    if (isNaN(last_id)) {
        last_id = -1;
    }
    last_id++;

    var row = '<tr data-id="'+last_id+'">'+
        '<td class="w-80px"><input name="products['+last_id+'][quantity]" type="number" min="0" step="any" class="form-control input-small quantity w-80px mb-1" value="1"></td>'+
        '<td><textarea name="products['+last_id+'][name]" class="form-control input" rows="2"></textarea></td>'+
        '<td class="w-80px"><input name="products['+last_id+'][price_tax_excl]" type="number" min="0" step="any" class="form-control input-small price_tax_excl w-80px" value="0"></td>'+
        '<td class="w-60px"><input name="products['+last_id+'][vat]" type="number" min="0" step="any" class="form-control input-small tva w-60px" value="20"></td>'+
        '<td class="w-80px"><input name="products['+last_id+'][price_tax_incl]" type="number" min="0" step="any" class="form-control input-small price_tax_incl w-80px" value="0"></td>'+
        '<td class="w-30px"><a onclick="RemoveProduct('+last_id+');" class="btn btn-clean btn-icon btn-sm btn-hover-light-danger cursor-pointer mt-1"><i class="fas fa-minus-circle icon-nm"></i></a></td>'+
    '</tr>';

    $('.table-products tbody').append(row);
    autoCalcPrice();
}

function RemoveProduct(id) {
    $('.table-products tbody tr[data-id="' + id + '"]').remove();
    autoCalcPrice();
}

function AddSurcharge(name, price, comment) {
    var last_id = $('.table-products tbody tr:last').data('id');
    if (isNaN(last_id)) {
        last_id = -1;
    }
    last_id++;

    let priceTaxExcl = parseFloat(price / 1.2).toFixed(2);
    let priceTaxIncl = parseFloat(price).toFixed(2);

    let title = name;
    if (comment) {
        title += "\n" + comment.replaceAll('<br/>', "\n");
    }
    var row = '<tr data-id="'+last_id+'">'+
        '<td class="w-80px"><input name="products['+last_id+'][quantity]" type="number" min="0" step="any" class="form-control input-small quantity w-80px mb-1" value="1"></td>'+
        '<td><textarea name="products['+last_id+'][name]" id="product_name_' + last_id + '" class="form-control autosize" rows="2">' + title + '</textarea></td>'+
        '<td class="w-80px"><input name="products['+last_id+'][price_tax_excl]" type="number" min="0" step="any" class="form-control input-small price_tax_excl w-80px" value="' + priceTaxExcl + '"></td>'+
        '<td class="w-60px"><input name="products['+last_id+'][vat]" type="number" min="0" step="any" class="form-control input-small tva w-60px" value="20"></td>'+
        '<td class="w-80px"><input name="products['+last_id+'][price_tax_incl]" type="number" min="0" step="any" class="form-control input-small price_tax_incl w-80px" value="' + priceTaxIncl + '"></td>'+
        '<td class="w-30px"><a onclick="RemoveProduct('+last_id+');" class="btn btn-clean btn-icon btn-sm btn-hover-light-danger cursor-pointer mt-1"><i class="fas fa-minus-circle icon-nm"></i></a></td>'+
        '</tr>';

    $('.table-products tbody').append(row);
    autoCalcPrice();

    let textarea = $('#product_name_' + last_id);
    autosize(textarea);
}

function roundUpto(number, upto) {
    return Number(number.toFixed(upto));
}

function handlePayments() {
    $('.table-payments input[name="payments[]"]').on('change', function() {
        calcPayments();
    });
}

function calcPayments() {
    let amount = parseFloat($('#amount_tax_incl').html());
    let paid = 0;
    $('.table-payments input[name="payments[]"]:checked').each(function() {
        paid += parseFloat($(this).data('amount'));
    });
    let rest = amount - paid;
    if (rest < 0) {
        rest = 0;
    }
    $('#payments_amount').html(roundUpto(paid, 2));
    $('#rest').html(roundUpto(rest, 2));
}

function fillClientInfos(clientId, block) {
    if (!clientId) {
        $(block + ' #first_name').val('');
        $(block + ' #last_name').val('');
        $(block + ' #company').val('');
        $(block + ' #email').val('');
        $(block + ' #address').val('');
        $(block + ' #address2').val('');
        $(block + ' #zip').val('');
        $(block + ' #city').val('');
        $(block + ' #telephone').val('');
        $(block + ' #tva_intracom').val('');
        return;
    }

    KTApp.block(block, {});
    $.post(
        baseDir + '/ajax/client/get/',
        { clientId: clientId, CSRFGuard_token: CSRFGuard_token },
        function (data) {
            KTApp.unblock(block);
            if (data.status) {
                $(block + ' #first_name').val(data.address.firstName);
                $(block + ' #last_name').val(data.address.lastName);
                $(block + ' #company').val(data.address.company);
                $(block + ' #email').val(data.address.email);
                $(block + ' #address').val(data.address.address);
                $(block + ' #address2').val(data.address.address2);
                $(block + ' #zip').val(data.address.zip);
                $(block + ' #city').val(data.address.city);
                $(block + ' #telephone').val(data.address.telephone);
                $(block + ' #tva_intracom').val(data.address.tvaIntracom);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}
