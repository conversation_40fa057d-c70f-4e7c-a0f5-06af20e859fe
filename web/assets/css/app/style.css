:root {
    --blue: #265bc3;
    --rose: #bc4877;
}
#kt_header,
#kt_header_mobile {
    background: #0E2072;
}
a {
    color: var(--blue);
}
.bg-primary {
    background-color: var(--blue) !important;
}
.btn.btn-primary {
    color: #FFFFFF;
    background-color: var(--blue);
    border-color: var(--blue);
}
.btn.btn-light-primary {
    color: var(--blue);
    background-color: #d6e4ff;
    border-color: transparent;
}
.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-light-primary:focus:not(.btn-text),
.btn.btn-light-primary.focus:not(.btn-text) {
    background-color: var(--blue);
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean:focus:not(.btn-text),
.btn.btn-clean.focus:not(.btn-text) {
    color: var(--blue);
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean:focus:not(.btn-text) i,
.btn.btn-clean.focus:not(.btn-text) i {
    color: var(--blue);
}
.btn.btn-clean.btn-hover-light-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-light-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-light-danger.focus:not(.btn-text) {
    color: #F64E60;
}
.btn.btn-clean.btn-hover-light-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-light-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-light-danger.focus:not(.btn-text) i {
    color: #F64E60;
}
.label.label-primary {
    background-color: var(--blue);
}
.label.label-outline-primary {
    background-color: transparent;
    color: var(--blue);
    border: 1px solid var(--blue);
}
.navi .navi-item .navi-link.active .navi-icon svg g [fill] {
    fill: var(--blue);
}
.svg-icon.svg-icon-primary svg g [fill] {
    fill: var(--blue) !important;
}
.dataTables_wrapper .dataTable th.sorting_desc,
.dataTables_wrapper .dataTable td.sorting_desc {
    color: var(--blue) !important;
}
.navi .navi-item .navi-text {
    padding-right: 10px;
}
.navi .navi-item .navi-link.active .navi-text {
    color: var(--blue);
}
.navi .navi-item .navi-link:hover,
.navi .navi-item .navi-link:hover .navi-text,
.navi .navi-item .navi-link:hover .navi-icon i {
    color: var(--blue);
}
.text-primary {
    color: var(--blue) !important;
}
a.text-hover-primary:hover, .text-hover-primary:hover {
    color: var(--blue) !important;
}
.gap-4 {
    gap: 1rem;
}

.btn.btn-info {
    color: #ffffff;
    background-color: var(--rose);
    border-color: var(--rose);
}
.btn.btn-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-info:focus:not(.btn-text),
.btn.btn-info.focus:not(.btn-text) {
    background-color: #ba2361;
    border-color: #ba2361;
}
.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) {
    color: #F64E60 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) i {
    color: #F64E60 !important;
}

.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-success:focus:not(.btn-text),
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) {
    color: #1BC5BD !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-success:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) i {
    color: #1BC5BD !important;
}

.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text),
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) {
    color: #FFA800 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) i {
    color: #FFA800 !important;
}

.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-info:focus:not(.btn-text),
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) {
    color: #8950FC !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-info:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) i {
    color: #8950FC !important;
}

.btn.disabled, .btn:disabled {
    cursor: not-allowed;
}
.image-input [data-action="ckfinder"] {
    position: absolute;
    right: -10px;
    top: -10px;
}


div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}
div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0;
    width: 250px;
}
.dataTables_wrapper .dataTable td {
    vertical-align: top;
}


.switch-left {
    display: inline-block;
    width: 70px;
    vertical-align: top;
    margin-bottom: 0;
}
.switch-right {
    display: inline-block;
    margin-bottom: 0;
    width: calc(100% - 75px);
}
.switch-right .switch-infos {
    margin-top: 7px;
}
.switch-right .switch-infos label {
    margin-bottom: 0;
}


.input-group.date-time-picker,
.input-group.date-picker {
    max-width: 250px;
}

.navi-item a.navi-link {
    cursor: pointer;
}
.navi-link.text-danger:hover,
.navi-link.text-danger:hover .navi-icon i,
.navi-link.text-danger:hover .navi-text {
    color: #F64E60 !important;
}
.navi-link.text-success:hover,
.navi-link.text-success:hover .navi-icon i,
.navi-link.text-success:hover .navi-text {
    color: #1BC5BD !important;
}
.navi-link.text-warning:hover,
.navi-link.text-warning:hover .navi-icon i,
.navi-link.text-warning:hover .navi-text {
    color: #FFA800 !important;
}
.navi-link.text-info:hover,
.navi-link.text-info:hover .navi-icon i,
.navi-link.text-info:hover .navi-text {
    color: #8950FC !important;
}

.hidden {
    display: none !important;
}
/*.header-fixed[data-header-scroll="on"] .content {
    padding-top: 0 !important;
}*/
body.card-sticky-on .card-sticky {
    padding-top: 70px;
}

ul.navi.no-wrap li .navi-text {
    white-space: nowrap;
    padding-right: 1rem;
}

.symbol-h-50 {
    height: 50px;
    align-items: center;
    display: flex;
}
.symbol-h-50 > img {
    max-width: 100%;
    width: auto;
    height: auto;
    max-height: 50px;
    margin: 0 auto;
}

td .dropdown .dropdown-menu {
    min-width: 175px;
}

.swal2-icon.swal2-warning {
    color: #F64E60;
    border-color: #F64E60;
}
.swal2-cancel.swal2-styled {
    color: #3F4254;
    background-color: #E4E6EF;
    border-color: #E4E6EF;
    font-weight: normal;
}
.swal2-cancel.swal2-styled:hover {
    color: #3F4254;
    background-color: #d7dae7;
    border-color: #d7dae7;
}

#topbar_notifications_notifications .notification a {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

#toast-container .toast {
    opacity: 1 !important;
}

.select2-container {
    min-width: 300px;
    max-width: 100%;
}
.filters .select2-container {
    min-width: 100px;
}
.select2-container .select2-selection--multiple {
    min-height: 40px;
}
.select2-container .select2-selection--multiple .select2-search__field {
    min-width: 100%;
}
.select2-container .select2-search--inline .select2-search__field {
    margin-top: 0;
}
.modal .select2-container {
    min-width: 100%;
}
.image-input-wrapper {
    display: flex;
}
.image-input img {
    max-width: 100%;
    max-height: 120px;
}
input.form-control.input-sm {
    width: auto;
}
.input-group.input-group-small {
    width: 200px;
}

#ckf-modal-header {
    background: #fafafa !important;
}

.border-hover-primary:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #6993FF !important;
}
.border-hover-info:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #8950FC !important;
}
.border-hover-success:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #1BC5BD !important;
}
.border-hover-warning:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #FFA800 !important;
}
.border-hover-danger:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #F64E60 !important;
}

.topbar-item.topbar-event .btn.btn-hover-transparent-light:hover {
    color: #F3F6F9 !important;
    background-color: rgba(243, 246, 249, 0.1) !important;
    border-color: transparent !important;
}

.btn-create-modal-content:hover > * {
    opacity: .7;
    cursor: pointer;
}
.btn-create-modal-content:after {
    content: "\f002";
    position: absolute;
    left: 50%;
    top: 50%;
    font-size: 1.25rem;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    opacity: .8;
    background: #B5B5C3;
    border-radius: 50%;
    padding: 10px;
    color: white;
    transform: translateX(-50%) translateY(-50%);
    cursor: pointer;
}
.btn-create-modal-content:hover:after {
    opacity: 1;
}
.swal2-lg {
    width: 50em;
}
.swal2-xl {
    width: 75em;
}

#kt_quick_search_dropdown .card-blank-state {
    box-shadow: none !important;
}
#kt_quick_search_dropdown .blank-state {
    text-align: center;
}
#kt_quick_search_dropdown .blank-state .blank-state-image {
    margin: 30px 0;
}
#kt_quick_search_dropdown .blank-state .blank-state-image img {
    max-height: 100px;
}
#kt_quick_search_dropdown .blank-state p {
    margin: 0 auto 30px auto;
    opacity: 0.8;
    font-size: 16px;
    line-height: 24px;
}


.notification-inbox {
    border-radius: 0 !important;
    border-bottom: 1px solid #ebedf3;
    min-height: 75px;
}
.notification-container {
    width: 100%;
}
.notification-inbox.new .notification-subject {
    font-weight: 600 !important;
}
.notification {
    padding-top: 10px;
}

.apexcharts-legend-marker {
    border-radius: 50% !important;
}

#ModalVideo .modal-content {
    background: transparent;
    box-shadow: none;
}
#ModalVideo .modal-header {
    border: none;
    padding-right: 0;
}
#ModalVideo .modal-header i {
    color: white;
    font-size: 16px;
}
#ModalVideo iframe {
    border-color: black !important;
}
.modal-backdrop.show {
    opacity: .6;
}

/* ckeditor5 media embed */
figure.media {
    display: block;
}

.card-bg {
    background-position: center center;
    height: 200px;
    background-size: cover;
}
.card-bg-icon {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.navi-item .accordion.accordion-toggle-arrow .card .card-header .card-title:after {
    right: 20px;
}
.navi-label.navi-label-rounded .label {
    border-radius: 50%;
    padding: 0.5rem;
    width: 24px;
    height: 24px;
}
.navi-item .accordion.accordion-toggle-arrow > .card > .card-header > .navi-link .navi-label {
    margin-right: 20px;
}

.nouislider-car-level {
    max-width: 300px;
    background: rgb(255,0,0);
    background: linear-gradient(90deg, rgba(255,0,0,1) 0%, rgba(255,0,0,1) 30%, #008000 30%, #008000 70%, rgba(255,0,0,1) 70%, rgba(255,0,0,1) 100%);
}
.nouislider-car-level.car-level-fuel {
    max-width: 300px;
    background: rgb(255,0,0);
    background: linear-gradient(90deg, rgba(255,0,0,1) 0%, rgba(255,0,0,1) 30%, #008000 30%, #008000 100%);
}
.nouislider-car-level .noUi-marker-horizontal.noUi-marker {
    width: 1px;
    height: 7px;
}
.noUi-horizontal .noUi-handle {
    right: -11px;
}
.nouislider-car-level .noUi-marker-horizontal.noUi-marker:first-of-type,
.nouislider-car-level .noUi-marker-horizontal.noUi-marker:last-of-type {
    /*display: none;*/
}

.header-stat {
    border-style: dashed;
    border-color: #E1E3EA;
    min-width: 100px;
    padding: 0.75rem 1rem;
    border-radius: 0.475rem;
}

.label {
    min-height: 20px;
    height: auto;
}

.radio-bordered {
    border: 1px solid #d9dade;
    padding: 8px 12px;
    border-radius: .42rem;
}
.radio-inline .radio-bordered {
    margin-bottom: 1rem;
}


.card.card-custom.card-sm > .card-header {
    min-height: 50px;
    padding: 0 15px !important;
}
.card.card-custom.card-sm > .card-header .card-title {
    margin: 0;
}
.card.card-custom.card-sm > .card-header .card-title .card-label {
    font-size: 1rem;
}
.card.card-custom.card-sm .card-spacer .btn {
    padding: 0.55rem 0.75rem;
    font-size: 0.925rem;
    line-height: 1.35;
    border-radius: 0.42rem;
}


div.controls {
    position: relative;
}
.address_results {
    border-radius: 4px;
    background: white;
    padding: 10px 0;
    width: 100%;
    max-height: 250px;
    position:absolute;
    top: calc(100% + 10px);
    display: none;
    overflow-y: auto;
    box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, .1);
    z-index: 1;
}
.address_results li {
    padding: 10px 20px;
    cursor: pointer;
    list-style: none;
}
.address_results li:hover {
    background-color: var(--blue);
    color: white;
}

.filters .checkbox-list .checkbox > span {
    background-color: white;
    border: 1px solid #D1D3E0;
}
.filters .checkbox-list .checkbox > input:checked ~ span {
    background-color: var(--blue);
    border: none;
}

.show-more {
    position: relative;
}
.show-more p:last-child {
    margin-bottom: 0;
}
.show-more.stretched .show-more-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 25;
    overflow: hidden;
    position: relative;
}
.show-more.stretched .show-more-content.lines-5 {
    -webkit-line-clamp: 5;
}
.show-more-button {
    position: absolute;
    bottom: 0;
    left: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, .5), #ffffff);
    width: 100%;
    padding-top: 40px;
}


.card-empty {
    background: transparent;
}
.card-empty.border-dashed {
    border: 1px dashed #ebedf3;
}
.card-empty .card-spacer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mobile-nav {
    background: #FFFFFF;
    position: fixed;
    bottom: 0;
    height: 78px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 10px;
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
    z-index: 1200;
}
.mobile-nav .mobile-nav-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    color: #1BC5BD;
    flex: 1;
}
.mobile-nav .mobile-nav-item.mobile-nav-item-active {
    color: #6993FF;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon {
    font-size: 2rem;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon svg {
    height: 24px !important;
    width: 24px !important;
}
.mobile-nav .mobile-nav-item .mobile-nav-text {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    text-align: center;
}
@media screen and (min-width: 768px) {
    .mobile-nav {
        display: none;
    }
}
@media screen and (max-width: 767px) {
    #help-icon {
        display: none;
    }
    .flex-root {
        padding-bottom: 80px;
    }
    .offcanvas-mobile {
        height: calc(100% - 78px);
    }
}

.form-group .controls {
    position: relative;
}
.form-group .toggle-password {
    position: absolute;
    right: 35px;
    top: 0;
    cursor: pointer;
    height: 100%;
    display: flex;
    align-items: center;
}


.dropzone.dz-started .select-document {
    display: none;
}

.ck.ck-powered-by {
    display: none !important;
}

.fc .fc-event-danger .fc-list-event-dot {
    border-color: #F64E60 !important;
}
.fc .fc-event-facts-done .fc-list-event-dot {
    border-color: #1BC5BD !important;
}
.fc .fc-event-convocations-done .fc-list-event-dot {
    border-color: #0E2072 !important;
}

.card.card-custom.card-document > .card-header {
    min-height: 0;
    padding: 10px;
}
.card.card-custom.card-document > .card-header h3 {
    font-size: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin: 0;
}
.card.card-custom.card-document .card-body {
    padding: 0;
}

.minicolors-theme-bootstrap .minicolors-input-swatch {
    width: 38.4px !important;
    height: 38.4px !important;
}

@media (max-width: 767px) {
    .header-actions-mobile {
        position: absolute;
        top: 2rem;
        right: 2rem;
        z-index: 1;
    }
}

.dropzone .dz-preview .dz-details .dz-rotate {
    opacity: 0;
}
.dropzone .dz-preview.dz-image-preview .dz-details .dz-rotate {
    opacity: 1;
}
.dropzone .dz-preview .dz-details .dz-rotate {
    margin-top: 20px;
    display: inline-block;
}
.dropzone .dz-preview .dz-details .dz-rotate a {
    margin: 0 5px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 5px 10px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.9);
}
.dropzone .dz-preview .dz-details .dz-rotate a i {
    cursor: pointer;
}

@media (max-width: 767px) {
    .modal {
        max-height: calc(100vh - 100px);
    }
}


#modalNotes .dropdown {
    display: none;
}
.scrolltop {
    bottom: 100px;
}

.card-child {
    margin-left: 4rem;
    border-color: #d6e4ff;
}
.card-child:before {
    content: '';
    position: absolute;
    top: -26px;
    left: calc(50% - 1.25rem);
    width: 2px;
    height: 25px;
    background: #d6e4ff;
}


.cameraContainer {
    padding: 20px;
    text-align: center;
    cursor: pointer;
    border: 1px dashed #009ef7;
    background: #f1faff;
    border-radius: 0.42rem;
    position: relative;
}
.cameraContainer .btn-take-picture .btn {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 18px;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, .1);
}
.cameraContainer .btn-take-picture .btn i {
    font-size: 18px;
}
.cameraContainer .btn-close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    background: white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    padding-top: 2px;
    box-shadow: 0 0 8px rgba(0,0,0,.05);
}

.cameraContainer .select-camera {
    position: absolute;
    top: 10px;
    right: 70px;
    border-radius: 40px;
    width: 150px;
    height: 50px;
    z-index: 10;
    border: none;
    padding: 0 10px;
    box-shadow: 0 0 8px rgba(0,0,0,.05);
}

.img-container {
    position: relative;
    max-width: 140px !important;
}
.img-container .overlay-options {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .3);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
}
.img-container:hover .overlay-options {
    opacity: 1;
}
.img-container .overlay-options a {
    width: 30px;
    height: 30px;
    text-align: center;
    color: rgba(255,255,255,.7);
    padding-top: 4px;
    display: inline-block;
    font-size: 16px;
    cursor: pointer;
}
.img-container .overlay-options a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255,255,255,1);
    border-radius: 4px;
}


body.card-sticky-on .card-sticky .tox.tox-tinymce--toolbar-sticky-on .tox-editor-header {
    top: 150px !important;
}
.dataTables_wrapper table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before {
    display: inline-block;
}
@media (max-width: 767px) {
    .card.card-custom > .card-header:not(.flex-nowrap) {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    .dataTables_wrapper table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child i.fa-sort {
        display: none;
    }
}


@media (max-width: 767px) {
    .list.list-hover.min-w-500px {
        min-width: 0 !important;
    }
    .min-w-200px {
        min-width: 100px !important;
    }
}

/*@media (min-width: 992px) {
    #kt_aside_menu {
        position: sticky;
        max-height: calc(100vh - 130px);
        overflow-y: auto;
        background: white;
        top: 106px;
        border-radius: 0.42rem;
    }

    #kt_aside_menu .card {
        box-shadow: none;
    }

    #kt_aside_menu .navi {
        padding-bottom: 20px;
    }
}
*/


.table.table-separate.table-bordered th:first-child,
.table.table-separate.table-bordered td:first-child {
    padding-left: 0.75rem !important;
}
.table.table-separate.table-bordered th,
.table.table-separate.table-bordered td {
    border: 1px solid #EBEDF3;
}

.dropzone .dz-remove-container {
    display: flex;
    flex-direction: row;
    width: 100%;
}
.dropzone .dz-remove-container .dz-remove {
    padding: 4px;
}
.dropzone .dz-remove-container .dz-remove i {
    margin-right: 4px;
}
.dropzone .dz-rotate {
    margin-left: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.dropzone .dz-rotate a {
    margin: 0;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 5px 10px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.9);
}
.dropzone .dz-rotate a:hover {
    color: #6993FF;
}
.dropzone .dz-rotate a i {
    cursor: pointer;
}

