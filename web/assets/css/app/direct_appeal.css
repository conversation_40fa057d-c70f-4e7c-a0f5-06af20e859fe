.shock-location-container {
    width: 172px;
    height: 273px;
    position: relative;
}
.shock-location-container img {
    width: 100%;
    height: 100%;
}
.shock-location-container .location {
    position: absolute;
    top: 0;
    left: 0;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    font-size: 16px;
    background: transparent;
    color: var(--blue);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.shock-location-container .location.active {
    background: var(--blue);
    color: white;
}
.shock-location-container .location.front_left {
    top: 55px;
    left: 25px;
}
.shock-location-container .location.front_right {
    top: 55px;
    left: 125px;
}
.shock-location-container .location.left_lateral {
    top: 125px;
    left: 25px;
}
.shock-location-container .location.right_lateral {
    top: 125px;
    left: 125px;
}
.shock-location-container .location.rear_left {
    top: 190px;
    left: 25px;
}
.shock-location-container .location.rear_right {
    top: 190px;
    left: 125px;
}
.shock-location-container .location.front {
    top: 20px;
    left: 75px;
}
.shock-location-container .location.rear {
    top: 225px;
    left: 75px;
}