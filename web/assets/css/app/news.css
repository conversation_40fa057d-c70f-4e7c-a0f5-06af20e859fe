.news-container {
    border-left: 10px solid transparent;
}
.news-container.new {
    border-left: 10px solid #21A3FF;
}
.card.news-container .card-content {
    float: none;
    height: auto;
    opacity: 1;
}
.news-container h4,
#ModalNews h4 {
    font-weight: bold;
    cursor: pointer;
}
.news-container.new h4,
.news-container.opened h4 {
    color: #f27a24;
}
.news-container .icone {
    float: right;
    color:#ccc;
    font-size: 20px;
    cursor: pointer;
}
.news-container .icone i:before {
    content: "\f107";
}
.news-container.opened .icone i:before {
    content: "\f106";
}

.card.news-container .card-content .card-content-text {
    height: 20px;
    overflow: hidden;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -ms-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
    margin-top: 10px;
}
.card.news-container.opened .card-content  .card-content-text {
    height: auto;
}

.news-container .date {
    color: #777e85;
    display: inline-block;
    margin: 0;
    margin-right: 10px;
}
.news-container .flash {
    display: inline-block;
    margin: 0;
    margin-right: 10px;
    border-radius: 16px;
    background-color: #21A3FF;
    padding: 2px 15px;
    font-size: 12px;
    color: white;
    text-transform: uppercase;
    vertical-align: top;
}

.news #gallery {
    height: auto !important;
    margin-right: 0px !important;
    margin-bottom: 0px !important;
}
.news #gallery .thumbnail {
    height: auto !important;
    margin-bottom: 0px !important;
}
.news #gallery .pic {
    min-height: 0 !important;
}
.news #gallery img.pic {
    min-height: 0 !important;
}

span.flash {
    background-color: #EFCC2B;
    border-radius: 31px;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    padding: 5px 12px 4px;
    font-size: 12px;
    text-transform: uppercase;
}

#ModalNews span.flash {
    float: right;
    margin-right: 10px;
}
#ModalNews .modal-body {
    text-align: center;
}
#ModalNews img.news {
    max-width: 300px;
}
#ModalNews h4 {
    font-size: 36px;
    text-align: center;
    line-height: 44px;
    color: #403D50;
    margin-bottom: 20px;
    font-weight: normal;
}
#ModalNews p.date {
    font-size: 14px;
    text-align: center;
    line-height: 17px;
    color: #90969E;
    margin-bottom: 30px;
}
#ModalNews .preview-image {
    margin-bottom: 40px;
}
#ModalNews .preview-image img {
    max-width: 75%;
}
#ModalNews .btn-info {
    background: #1D93E6;
    box-shadow: none;
    border: none;
    font-size: 14px;
    padding: 15px 38px;
}
#ModalNews .btn-info:hover {
    background: #2088d2;
}

#ModalNewsContent .media {
    margin-top: 0px;
}
#ModalNewsContent .media-left {
    padding-right: 15px;
}
#ModalNewsContent img.news {
    max-width: 95px;
}
#ModalNewsContent span.flash {
    float: left;
    margin-top: -2px;
}
#ModalNewsContent p.date {
    font-size: 14px;
    text-align: center;
    line-height: 17px;
    color: #90969E;
    float:left;
    margin-right: 10px;
}
#ModalNewsContent .media-body {
    padding-top: 20px;
}
#ModalNewsContent .close-news {
    float: none;
    position: absolute;
    right: 15px;
    top: 10px;
}
#ModalNewsContent h4 {
    font-size: 18px;
    line-height: 22px;
    color: #403D50;
}
#ModalNewsContent .modal-body img {
    max-width: 100%;
}
#ModalNewsContent .modal-btn {
    text-align: center;
    margin: 20px 0;
    margin-bottom: 50px;
}
#ModalNewsContent .btn-info {
    background: #1D93E6;
    box-shadow: none;
    border: none;
    font-size: 14px;
    padding: 15px 38px;
}
#ModalNewsContent .btn-info:hover {
    background: #2088d2;
}
.news-container img {
    max-width: 100%;
}
