.card-blank-state {
	box-shadow: none !important;
}
.card-note.inactive {
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
}
#article img {
	max-width: 100%;
}

/* ckeditor5 media embed */
figure.media {
	display: block;
}
.input-group-prepend .btn,
.input-group-append .btn {
	height: 100%;
}
.article-smileys {
	padding: 2rem 2.25rem;
	border-top: 1px solid #EBEDF3;
	margin-top: 1.25rem;
}
.article-smileys a {
	margin: 0 0.5rem;
}