:root {
    --blue: #265bc3;
    --rose: #bc4877;
    --dark-blue: #0E2072;
}

.header {
    height: 120px;
    background: white;
}

.switch-left {
    display: inline-block;
    width: 70px;
    vertical-align: top;
    margin-bottom: 0;
}
.switch-right {
    display: inline-block;
    margin-bottom: 0;
    width: calc(100% - 75px);
}
.switch-right .switch-infos {
    margin-top: 7px;
}
.switch-right .switch-infos label {
    margin-bottom: 0;
}

.card-note.inactive {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
}
#kt_footer p {
    margin-bottom: 0;
    color: #7E8299;
}

.select2-container {
    min-width: 200px;
    max-width: 100%;
}
.modal .select2-container {
    min-width: 100%;
}

.card-bg {
    background-position: center center;
    height: 200px;
    background-size: cover;
}
.card-bg-icon {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

a {
    color: var(--dark-blue);
}
.bg-primary {
    background-color: var(--dark-blue) !important;
}
.btn.btn-primary {
    color: #FFFFFF;
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
    border-radius: 100px;
}
.btn.btn-light-primary {
    color: var(--dark-blue);
    background-color: #d6e4ff;
    border-color: transparent;
}
.btn.btn-outline-primary {
    color: var(--dark-blue);
    background-color: transparent;
    border-color: var(--dark-blue);
}
.btn.btn-outline-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-outline-primary:focus:not(.btn-text),
.btn.btn-outline-primary.focus:not(.btn-text) {
    color: #FFFFFF;
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
}
.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-light-primary:focus:not(.btn-text),
.btn.btn-light-primary.focus:not(.btn-text) {
    background-color: var(--dark-blue);
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean:focus:not(.btn-text),
.btn.btn-clean.focus:not(.btn-text) {
    color: var(--dark-blue);
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean:focus:not(.btn-text) i,
.btn.btn-clean.focus:not(.btn-text) i {
    color: var(--dark-blue);
}
.label.label-primary {
    background-color: var(--dark-blue);
}
.label.label-outline-primary {
    background-color: transparent;
    color: var(--dark-blue);
    border: 1px solid var(--dark-blue);
}
.navi .navi-item .navi-link.active .navi-icon svg g [fill] {
    fill: var(--dark-blue);
}
.svg-icon.svg-icon-primary svg g [fill] {
    fill: var(--dark-blue) !important;
}
.dataTables_wrapper .dataTable th.sorting_desc,
.dataTables_wrapper .dataTable td.sorting_desc {
    color: var(--dark-blue) !important;
}
.navi .navi-item .navi-link.active .navi-text {
    color: var(--dark-blue);
}
.navi .navi-item .navi-link:hover,
.navi .navi-item .navi-link:hover .navi-text,
.navi .navi-item .navi-link:hover .navi-icon i {
    color: var(--dark-blue);
}
.text-primary {
    color: var(--dark-blue) !important;
}
a.text-hover-primary:hover, .text-hover-primary:hover {
    color: var(--dark-blue) !important;
}



.btn.btn-info {
    color: #ffffff;
    background-color: var(--rose);
    border-color: var(--rose);
}
.btn.btn-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-info:focus:not(.btn-text),
.btn.btn-info.focus:not(.btn-text) {
    background-color: #ba2361;
    border-color: #ba2361;
}

.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) {
    color: #F64E60 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) i {
    color: #F64E60 !important;
}

.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-success:focus:not(.btn-text),
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) {
    color: #1BC5BD !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-success:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) i {
    color: #1BC5BD !important;
}

.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text),
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) {
    color: #FFA800 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) i {
    color: #FFA800 !important;
}

.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-info:focus:not(.btn-text),
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) {
    color: #8950FC !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-info:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) i {
    color: #8950FC !important;
}

input.form-control.input-sm {
    width: auto;
}
.input-group.input-group-small {
    width: 240px;
}
.form-group .controls {
    position: relative;
}
.form-group .toggle-password {
    position: absolute;
    right: 35px;
    top: 0;
    cursor: pointer;
    height: 100%;
    display: flex;
    align-items: center;
}

div.controls {
    position: relative;
}
.address_results {
    border-radius: 4px;
    background: white;
    padding: 10px 0;
    width: 100%;
    max-height: 250px;
    position:absolute;
    top: calc(100% + 10px);
    display: none;
    overflow-y: auto;
    box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, .1);
    z-index: 1;
}
.address_results li {
    padding: 10px 20px;
    cursor: pointer;
    list-style: none;
}
.address_results li:hover {
    background-color: var(--dark-blue);
    color: white;
}

.header-stat {
    border-style: dashed;
    border-color: #E1E3EA;
    min-width: 125px;
    padding: 0.75rem 1rem;
    border-radius: 0.475rem;
}

.select2-container .select2-selection--multiple {
    min-height: 48px;
}
.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    margin-bottom: 0;
}

.date-container.w-180px {
    width: 230px !important;
}


body.no-menu .header-middle,
body.no-menu .header-right-side {
    display: none !important;
}
body.no-footer .footer-middle {
    display: none !important;
}
