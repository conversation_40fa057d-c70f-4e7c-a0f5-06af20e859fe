.w-nav {
    z-index: 1;
}
.bg-accent-3 {
    background: #fcfcfc !important;
}
.header-logo-link {
    touch-action: none;
    pointer-events: none;
}
.header-middle, .header-right-side {
    display: none;
}
.top-section.pd-120px---120px {
    background: #fcfcfc !important;
    padding-top: 0;
    /*font-size: 13px;*/
    line-height: 1.5;
}
.top-section.pd-120px---120px a {
    text-decoration: none;
}
.form .card-header {
    display: flex!important;
}
.form-group label {
    font-size: 18px;
    line-height: 1.111em;
    color: var(--neutral--800);
    margin-bottom: 12px;
    font-weight: 500;
}
.form-group label.radio,
.form-group label.checkbox {
    font-weight: 400;
}
.navi .navi-item .navi-link .navi-text {
    font-size: 18px;
}
.card.card-custom > .card-header .card-title .card-label,
.card.card-custom > .card-header h3.card-title {
    font-weight: 700;
    font-size: 18px;
    color: #181C32;
}
.card-toolbar button {
    font-size: 16px;
}
.btn {
    font-size: 18px;
}
.btn.btn-sm {
    font-size: 12px;
    line-height: 1.35;
    padding: 0.55rem 0.75rem;
}
.form-group select {
    min-height: 72px;
    border-radius: 100px;
    margin-bottom: 0;
    font-size: 18px;
    padding: 16px 24px;
    transition: box-shadow .3s,color .3s,border-color .3s;
    background-color: var(--neutral--100);
}


.card-document {
    border: 1px solid var(--dark-blue) !important;
}
.card.card-custom.card-document > .card-header {
    background: transparent !important;
    border-bottom: 1px solid #EBEDF3 !important;
}
.card.card-custom.card-document > .card-header .card-title .card-label,
.card.card-custom.card-document > .card-header h3.card-title {
    overflow: visible;
    text-overflow: initial;
    white-space: initial;
}
.card-document .card-bg-icon {
    background: white !important;
}
.card-document .svg-icon.svg-icon-white svg g [fill] {
    fill: var(--dark-blue) !important;
}
.card-document.card-link {
    cursor: pointer;
}

.table-amounts {
    background: white !important;
}
.footer-quote-signature {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: #F3F6F9;
    box-shadow: 0 -10px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}
.footer-middle {
    display: none !important;
}

@media screen and (max-width: 767px) {
    .card-toolbar .btn-primary {
        padding: 0.65rem 1rem;
    }
}

.card-body .checkbox-list .checkbox span {
    border: 1px solid var(--neutral--600);
}
.card-body .checkbox-list .checkbox>input:checked~span {
    border: 1px solid var(--accent--primary-1);
}

.input-group .form-control {
    height: auto;
}

.footer-wrapper {
    display: none;
}