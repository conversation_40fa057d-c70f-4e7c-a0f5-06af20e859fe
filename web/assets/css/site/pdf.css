#kt_header, #kt_footer, .top-header, .footer-wrapper, .loading-bar-wrapper {
    display: none !important;
}
#kt_content {
    padding: 0 !important;
}
body {
    background: white;
    font-size: 14px !important;
}
p {
    font-size: 14px;
}
h1 {
    font-size: 3.5rem;
}
h2 {
    font-size: 2.75rem;
}
h3 {
    font-size: 2.35rem;
}
h4 {
    font-size: 1.75rem;
}
.table thead tr td,
.table thead tr th,
.table tr td {
    font-size: 14px;
}
div.page {
    page-break-after: always;
    page-break-inside: avoid;
}
.top-section.pd-120px---120px {
    padding: 0 !important;
}
.text-uppercase {
    letter-spacing: 0;
}
section, .section, .content-block, .keep-together {
    page-break-inside: avoid;
    break-inside: avoid;
}

#watermark {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
#watermark p {
    font-size: 48px;
    color: #ccc;
    opacity: .3;
    text-transform: uppercase;
    transform: rotate(-25deg);
    margin-bottom: 150px;
}
#watermark p:last-child {
    margin-bottom: 0;
}

.table>:not(caption)>*>* {
    background: transparent !important;
}
.table .thead-light th {
    color: #3F4254 !important;
    background-color: #F3F6F9 !important;
    border-color: #EBEDF3 !important;
}

.without-margin p {
    margin-bottom: 0;
}
