.blank-state {
    text-align: center;
    margin-bottom: 20px;
}
.blank-state .blank-state-image {
    margin: 40px 0;
}
.blank-state .blank-state-image img {
    max-height: 150px;
    max-width: 100%; /*for mobile*/
}
.blank-state .blank-state-icon {
    margin: 40px 0 20px 0;
}
.blank-state h1 {
    margin-bottom: 1rem;
}
.blank-state p {
    margin: 0 auto 30px auto;
    opacity: 0.8;
    font-size: 16px;
    line-height: 24px;
    max-width: 450px;
}
.blank-state li {
    font-size: 16px;
    line-height: 24px;
}
.blank-state .stretch {
    margin: 0 auto 30px auto;
    max-width: 450px;
}
.blank-state .stretch small {
    font-size: 13px;
}
.blank-state .links {
    text-align: center;
}

.blank-state form .card {
    box-shadow: none;
    background: transparent;
}
.blank-state form .card .card-header {
    border: none;
}
.blank-state form .card .card-header .card-toolbar {
    margin: 0 auto;
}
.blank-state form .card .card-body {
    display: none;
}

.card-body .card-blank-state {
    box-shadow: none;
}
