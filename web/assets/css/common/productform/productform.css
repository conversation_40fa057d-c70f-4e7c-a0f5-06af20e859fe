#card-errors {
    color: #f44336;
}
.cards_icons {
    border-top: 1px dashed #EBEDF3;
    border-bottom: 1px dashed #EBEDF3;
    margin: 15px 0;
    padding: 10px 0;
}
.cards_icons .lock_img {
    display: inline-block;
    vertical-align: top;
}
.cards_icons .lock_img img {
    width: 25px;
}
.cards_icons .secured_payment {
    display: inline-block;
    vertical-align: top;
    font-size: 12px;
    color: #677991;
    padding-top: 4px;
}
.cards_icons .cards_imgs {
    display: inline-block;
    vertical-align: top;
    text-align: right;
    float: right;
    padding-top: 2px;
}
.cards_icons .cards_imgs img {
    width: 20px;
}
.brand-icon i {
    font-size: 18px;
}
.brand-icon i.fa-credit-card {
    color: #8898AA;
}
.brand-icon i.fa-cc-visa {
    color: #1f50ba;
}
.brand-icon i.fa-cc-mastercard {
    color: #353a48;
}
.brand-icon i.fa-cc-amex {
    color: #007fcd;
}
.brand-icon i.fa-cc-discover {
    color: #ffa100;
}
.brand-icon i.fa-cc-diners-club {
    color: #1984cb;
}
.brand-icon i.fa-cc-jcb {
    color: #0464b0;
}

@media (max-width: 767px) {
    .cards_icons {
        padding: 0;
        text-align: center;
    }
    .cards_icons .cards_imgs {
        float: none;
        text-align: center;
    }
}

.product-attribute-color {
    display: inline-block;
    cursor: pointer;
}
.product-attribute-color .symbol {
    padding: 1px;
    border-radius: 0.52rem;
}

.login.login-3 .block_products #formAddDiscount .form-group input {
    background-color: #F3F6F9;
    border-color: #F3F6F9;
}
.login.login-3 .block_products .form-group select {
    height: calc(2rem + 1.3rem + 2px);
    border: 1px solid #E4E6EF;
    padding: 0.65rem 1rem;
    min-width: 200px;
}

.block_products ul {
    padding-left: 20px;
    margin-bottom: 0;
}
.block_products ul li {
    margin-bottom: 6px;
    line-height: 1.2;
}

.payment .form-control {
    height: auto;
}
