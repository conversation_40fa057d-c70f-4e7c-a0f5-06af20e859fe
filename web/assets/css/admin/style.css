#kt_header, #kt_header_mobile {
    background-color: #071437;
}

.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) {
    color: #F64E60 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) i {
    color: #F64E60 !important;
}

.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-success:focus:not(.btn-text),
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) {
    color: #1BC5BD !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-success:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) i {
    color: #1BC5BD !important;
}

.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text),
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) {
    color: #FFA800 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) i {
    color: #FFA800 !important;
}

.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-info:focus:not(.btn-text),
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) {
    color: #8950FC !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-info:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) i {
    color: #8950FC !important;
}

.btn.disabled, .btn:disabled {
    cursor: not-allowed;
}


div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}
div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0;
    width: 250px;
}


.switch-left {
    display: inline-block;
    width: 70px;
    vertical-align: top;
    margin-bottom: 0;
}
.switch-right {
    display: inline-block;
    margin-bottom: 0;
    width: calc(100% - 75px);
}
.switch-right .switch-infos {
    margin-top: 7px;
}
.switch-right .switch-infos label {
    margin-bottom: 0;
}


.input-group.date-time-picker,
.input-group.date-picker {
    max-width: 200px;
}

.navi-item a.navi-link {
    cursor: pointer;
}
.navi-link.text-danger:hover,
.navi-link.text-danger:hover .navi-icon i,
.navi-link.text-danger:hover .navi-text {
    color: #F64E60 !important;
}
.navi-link.text-success:hover,
.navi-link.text-success:hover .navi-icon i,
.navi-link.text-success:hover .navi-text {
    color: #1BC5BD !important;
}
.navi-link.text-warning:hover,
.navi-link.text-warning:hover .navi-icon i,
.navi-link.text-warning:hover .navi-text {
    color: #FFA800 !important;
}
.navi-link.text-info:hover,
.navi-link.text-info:hover .navi-icon i,
.navi-link.text-info:hover .navi-text {
    color: #8950FC !important;
}

.hidden {
    display: none !important;
}
/*.header-fixed[data-header-scroll="on"] .content {
    padding-top: 0 !important;
}*/
body.card-sticky-on .card-sticky {
    padding-top: 70px;
}

ul.navi.no-wrap li .navi-text {
    white-space: nowrap;
    padding-right: 1rem;
}

.symbol-h-50 {
    height: 50px;
    align-items: center;
    display: flex;
}
.symbol-h-50 > img {
    max-width: 100%;
    width: auto;
    height: auto;
    max-height: 50px;
    margin: 0 auto;
}

.image-input [data-action="ckfinder"] {
    position: absolute;
    right: -10px;
    top: -10px;
}

td .dropdown .dropdown-menu {
    min-width: 175px;
}

.swal2-icon.swal2-warning {
    color: #F64E60;
    border-color: #F64E60;
}
.swal2-cancel.swal2-styled {
    color: #3F4254;
    background-color: #E4E6EF;
    border-color: #E4E6EF;
    font-weight: normal;
}
.swal2-cancel.swal2-styled:hover {
    color: #3F4254;
    background-color: #d7dae7;
    border-color: #d7dae7;
}

#topbar_notifications_notifications .notification a {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
#topbar_notifications_logs .log .log-title {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-inbox {
    border-radius: 0 !important;
    border-bottom: 1px solid #ebedf3;
    min-height: 75px;
}
.notification-container {
    width: 100%;
}
.notification-inbox.new .notification-subject {
    font-weight: 600 !important;
}
.notification {
    padding-top: 10px;
}

#toast-container .toast {
    opacity: 1 !important;
}

.select2-container {
    min-width: 200px;
    max-width: 100%;
}
.modal .select2-container {
    min-width: 100%;
}

input.form-control.input-sm {
    width: auto;
}
.input-group.input-group-small {
    width: 200px;
}
.image-input-wrapper {
    display: flex;
}
.image-input img {
    max-width: 100%;
    max-height: 120px;
}

#ckf-modal-header {
    background: #fafafa !important;
}

.border-hover-primary:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #6993FF !important;
}
.border-hover-info:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #8950FC !important;
}
.border-hover-success:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #1BC5BD !important;
}
.border-hover-warning:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #FFA800 !important;
}
.border-hover-danger:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #F64E60 !important;
}

.btn-create-modal-content:hover > * {
    opacity: .7;
    cursor: pointer;
}
.btn-create-modal-content:after {
    content: "\f002";
    position: absolute;
    left: 50%;
    top: 50%;
    font-size: 1.25rem;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    opacity: .8;
    background: #B5B5C3;
    border-radius: 50%;
    padding: 10px;
    color: white;
    transform: translateX(-50%) translateY(-50%);
    cursor: pointer;
}
.btn-create-modal-content:hover:after {
    opacity: 1;
}
.swal2-lg {
    width: 50em;
}
.swal2-xl {
    width: 75em;
}

#kt_quick_search_dropdown .card-blank-state {
    box-shadow: none !important;
}
#kt_quick_search_dropdown .blank-state {
    text-align: center;
}
#kt_quick_search_dropdown .blank-state .blank-state-image {
    margin: 30px 0;
}
#kt_quick_search_dropdown .blank-state .blank-state-image img {
    max-height: 100px;
}
#kt_quick_search_dropdown .blank-state p {
    margin: 0 auto 30px auto;
    opacity: 0.8;
    font-size: 16px;
    line-height: 24px;
}

.apexcharts-legend-marker {
    border-radius: 50% !important;
}

.card-bg {
    background-position: top center;
    height: 200px;
    background-size: cover;
}
.card-bg-icon {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.card-empty {
    background: transparent;
}
.card-empty.border-dashed {
    border: 1px dashed #ebedf3;
}
.card-empty .card-spacer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-group .controls {
    position: relative;
}
.form-group .toggle-password {
    position: absolute;
    right: 35px;
    top: 0;
    cursor: pointer;
    height: 100%;
    display: flex;
    align-items: center;
}


.mobile-nav {
    background: #FFFFFF;
    position: fixed;
    bottom: 0;
    height: 78px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 10px;
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
    z-index: 1200;
}
.mobile-nav .mobile-nav-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    color: #1BC5BD;
    flex: 1;
}
.mobile-nav .mobile-nav-item.mobile-nav-item-active {
    color: #6993FF;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon {
    font-size: 2rem;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon svg {
    height: 24px !important;
    width: 24px !important;
}
.mobile-nav .mobile-nav-item .mobile-nav-text {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    text-align: center;
}
@media screen and (min-width: 768px) {
    .mobile-nav {
        display: none;
    }
}
@media screen and (max-width: 767px) {
    #help-icon {
        display: none;
    }
    .flex-root {
        padding-bottom: 80px;
    }
    .offcanvas-mobile {
        height: calc(100% - 78px);
    }
}
