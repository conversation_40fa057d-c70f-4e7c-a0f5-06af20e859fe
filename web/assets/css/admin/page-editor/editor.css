::-webkit-scrollbar {
    height: 13px;
    width: 13px;
}
::-webkit-scrollbar-track {
    background: rgba(0,0,0,0);
    width: 5px;
}
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb:hover {
    background: rgba(114,120,128, 0.3);
    background-clip: padding-box;
    border-radius: 10px;
    border: 4px solid rgba(0, 0, 0, 0);
    width: 5px;
}

#content {
    padding: 0;
}
#main-container {
    background: transparent;
}
#main-container {
    overflow: visible;
}
.header-container {
    display: flex;
    flex: 1;
    flex-direction: column;
}
.page-container .form-actions {
    position: relative;
    box-shadow: none;
}
.page-container .form {
    margin: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0;
}
.page-container .form-group div.checker {
    margin-left: 0;
}
.page-container .form-group div.checker span {
    border-radius: 0;
    border: none;
}
.page-container .container {
    width: 1170px;
}
@media (max-width: 1600px) {
    .page-container .container {
        width: 90%;
    }
}
@media (max-width: 1070px) {
    .page-container .container {
        width: 100%;
    }
    .page-container .row {
        display: block;
    }
    .page-container .col-md-6 {
        display: block;
        width: 100%;
    }
    .page-container .left-side {
        border-radius: 0;
        margin: 0;
        padding: 20px;
    }
    .page-container .right-side {
        padding: 20px;
    }
    .page-container .form-actions {
        margin-bottom: 0;
    }
    .page-container .right-side .card-form-footer {
        margin-top: 20px;
    }
    .page-container .footer {
        margin: 20px;
    }
    .page-container .footer img {
        max-width: 100% !important;
        height: auto !important;
    }
}

#content .editor-sidebar {
    height: 100%;
    background: #F9FAFB;
    padding: 0;
    position: fixed;
    width: 400px;
    box-shadow: 0 1px 4px 0 rgba(20,27,58,0.14), 0 2px 16px 0 rgba(0,0,0,0.15);
    transition: all 0.3s ease-out;
    z-index: 10;
}
#content .editor-sidebar .editor-sidebar-header {
    height: 73px;
}
#content.full-width .editor-sidebar,
#content.full-width .editor-sidebar form:after {
    margin-left: -400px;
}
#content .editor-sidebar .accordion .card {
    background: transparent;
}
#content .editor-sidebar form {
    height: calc(100% - 75px);
    overflow-y: auto;
}
#content .editor-sidebar form:after {
    display: table;
    line-height: 0;
    content: "";
    position: fixed;
    bottom: 0;
    left: 0;
    width: 400px;
    height: 50px;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(24,28,50,1) 100%);
    transition: all 0.3s ease-out;
}
#content .editor-sidebar .btn-cancel {
    display: block;
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
}
#content .editor-sidebar label {
    color: white;
}
#content .editor-sidebar .element-bloc,
#content .editor-sidebar .element label {
    cursor: pointer;
}
#content .editor-sidebar #accordionElements label {
    margin-bottom: 0;
}
#content .editor-sidebar #accordionElements .element.highlight {
    background-color: rgba(105, 147, 255, 1);
}
#content .editor-sidebar #accordionElements .element.highlight.highlight-fade {
    background-color: rgba(105, 147, 255, 0);
    -webkit-transition:background-color .6s linear;
    -moz-transition:background-color .6s linear;
    transition:background-color .6s linear;
}
#content .editor-sidebar .accordion.accordion-toggle-arrow .card .card-header .card-title.collapsed {
    color: #7E8299;
}
#content .editor-sidebar .lists-container select {
    min-width: 0 !important;
}
#content .toggle-sidebar {
    position: fixed;
    left: 400px;
    bottom: 0;
    height: 50px;
    width: 50px;
    transition: all 0.3s ease-out;
    z-index: 10;
    padding: 5px;
}
#content .toggle-sidebar a {
    display: block;
    text-align: center;
    height: 40px;
    width: 40px;
    cursor: pointer;
    padding-top: 7px;
    border-radius: 4px;
}
#content .toggle-sidebar a:hover,
#content.full-width .toggle-sidebar a {
    background: white;
}
#content .toggle-sidebar a:hover svg g [fill],
#content.full-width .toggle-sidebar a svg g [fill] {
    -webkit-transition: fill 0.3s ease;
    transition: fill 0.3s ease;
    fill: #6993FF !important;
}
#content .toggle-sidebar svg {
    transition: all 0.4s linear;
}
#content.full-width .toggle-sidebar svg {
    transform: rotate(180deg);
}
#content.full-width .toggle-sidebar {
    left: 0;
}
#content.full-width .toggle-sidebar img {
    transform: rotate(180deg);
}

#content .editor-sidebar #collapseElements .form-group {
    margin-bottom: 5px;
}
#content .editor-sidebar #collapseElements .form-group label i {
    vertical-align: middle;
    margin-left: 4px;
    opacity: .5;
}
#content .editor-sidebar #collapseElements .form-group label:hover i {
    opacity: 1;
}
#content .editor-sidebar #collapseElements .form-group.off .item-title label {
    opacity: .5;
}
#content .editor-sidebar #collapseElements .form-group .form-group-content {
    min-height: 38px;
    margin-bottom: 0 !important;
}
#content .editor-sidebar #collapseElements .form-group .minicolors-input-swatch .minicolors-swatch-color {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
#content .editor-sidebar #collapseElements .form-group .switch-left {
    width: calc(100% - 96px);
}
#content .editor-sidebar #collapseElements .form-group.no-edit .switch-left {
    width: calc(100% - 40px);
}
#content .editor-sidebar #collapseElements .form-group .switch-right {
    width: 40px;
}
#content .editor-sidebar #collapseElements .form-group .switch-right input.ios-toggle-round.ios-toggle-mini + label {
    width: 36px;
}
#content .editor-sidebar #collapseElements .form-group .switch-right label {
    margin-bottom: 0;
}
#content .editor-sidebar #collapseElements .form-group .switch-edit {
    display: inline-block;
    vertical-align: top;
    padding-top: 2px;
    text-align: right;
    width: 56px;
}
#content .editor-sidebar #collapseElements .form-group.no-edit .switch-edit {
    display: none;
}
#content .editor-sidebar #collapseElements .form-group .switch-edit a:hover {
    color: #212B36;
    opacity: 0.5;
}
#content .editor-sidebar #collapseElements .form-group .switch-edit a:hover {
    opacity: 1;
}
#content .editor-sidebar #collapseElements .form-group .edit-element {
    cursor: pointer;
}
#content .editor-sidebar #collapseElements .form-group.off .edit-element {
    display: none;
}
#content .editor-sidebar #collapseElements .form-group .form-group-content .switch {
    display: none;
}
#content .editor-sidebar #collapseElements .form-group:hover .form-group-content .switch {
    display: flex;
}

#content .editor-sidebar .minicolors-panel {
    background: #181C32;
    border: solid 1px #181C32;
}

#content .editor-sidebar .form-group-image-container {
    width: 100%;
    height: 185px;
    background: white;
    position: relative;
    border-radius: 4px;
}
#content .editor-sidebar .form-group-image-container .form-group-image-user {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-size: cover;
    background-repeat: no-repeat;
}
#content .editor-sidebar .form-group-image-container .form-group-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    background: rgba(33, 43, 54, 0.8);
    display: none;
}
#content .editor-sidebar .form-group-image-container:hover .form-group-image-overlay {
    display: block;
}
#content .editor-sidebar .form-group-image-container .form-group-image-buttons {
    position: absolute;
    top: 50%;
    width: 100%;
    text-align: center;
    transform: translateY(-50%);
    z-index: 3;
}
#content .editor-sidebar .form-group-image-container .form-group-image-buttons button {
    opacity: .5;
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0 5px;
    margin-bottom: 8px;
    display: none;
    color: white;
}
#content .editor-sidebar .form-group-image-container:hover .form-group-image-buttons button {
    display: inline-block;
}
#content .editor-sidebar .form-group-image-container .form-group-image-buttons button:hover {
    opacity: 1;
}
#content .editor-sidebar .form-group-image-container .form-group-image-buttons button:last-child {
    margin-right: 0;
    padding-right: 0;
}
#content .editor-sidebar .form-group-image-container .form-group-image-buttons button i {
    padding-right: 5px;
    vertical-align: top;
    padding-top: 5px;
    font-size: 12px;
    color: white;
}


#content .editor-sidebar .form-group-logo {

}
#content .editor-sidebar .form-group-logo .form-group-logo-left,
#content .editor-sidebar .form-group-logo .form-group-logo-content {
    display: inline-block;
    vertical-align: top;
}
#content .editor-sidebar .form-group-logo .form-group-logo-left {
    width: 160px;
    height: 160px;
    border-radius: 4px;
    background-color: #FFFFFF;
    margin-right: 16px;
    text-align: center;
    position: relative;
    overflow: hidden;
}
#content .editor-sidebar .form-group-logo .form-group-logo-left img {
    max-width: 160px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}
#content .editor-sidebar .form-group-logo .form-group-logo-content {
    width: calc(100% - 180px);
}
#content .editor-sidebar .form-group-logo .form-group-logo-content select {
    margin-bottom: 8px;
}
#content .editor-sidebar .form-group-logo .form-group-logo-content button {
    opacity: .5;
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0 5px;
    display: block;
    margin-bottom: 8px;
}
#content .editor-sidebar .form-group-logo .form-group-logo-content button:hover {
    opacity: 1;
}
#content .editor-sidebar .form-group-logo .form-group-logo-content button:last-child {
    margin-right: 0;
    padding-right: 0;
}
#content .editor-sidebar #collapseLogo button i {
    padding-right: 5px;
    vertical-align: top;
    padding-top: 5px;
    font-size: 12px;
    color: #424B54;
}
#content .editor-sidebar .select2-container {
    min-width: 367px;
    max-width: 100%;
}
.select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1.6em;
}
#content .editor-sidebar .lists-container .select2-container,
#content .editor-sidebar .tags-container .select2-container {
    min-width: 315px;
    max-width: 315px;
    margin-right: 5px;
}
#content .editor-sidebar .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    width: 100% !important;
}
#content .editor-sidebar .select2-container--default .select2-search--inline .select2-search__field {
    width: 100% !important;
}

#content .page-content {
    width: calc(100% - 400px);
    margin-left: 400px;
    transition: all 0.3s ease-out;
}
#content.full-width .page-content {
    width: 100%;
    margin-left: 0;
}
#content .page-content div.radio span {
    border: none;
}
#content .page-content div.checker {
    margin-top: 0 !important;
}
#content .page-content div.checker span.checked:before {
    display: none;
}

.card-similar-pages {
    border-radius: 0;
    position: fixed;
    left: 400px;
    top: 0;
    width: calc(100% - 400px);
    height: 73px;
    z-index: 10;
    white-space: nowrap;
    border: none;
}
.card-similar-pages.tour-highlight-element {
    position: fixed !important;
}
.card-similar-pages .card-header {
    border-bottom: none;
    padding: 0 1.25rem;
    background: transparent;
    height: 73px;
}
.card-similar-pages > .card-header.card-header-tabs-line .card-toolbar {
    overflow-x: auto;
    overflow-y: hidden;
    height: 73px;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav {
    border-bottom: none;
    flex: none;
    flex-wrap: nowrap;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link {
    cursor: pointer;
    padding-top: 2.1rem;
    padding-bottom: 2rem;
    border-bottom: none !important;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link.inactive {
    opacity: .5;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link.active.inactive {
    opacity: 1;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-text {
    color: #E4E6EF;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .dropdown-toggle {
    color: #E4E6EF;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link.active .nav-text {
    color: #6993FF;
    font-weight: bold;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link.active + .dropdown-toggle {
    color: #6993FF;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .dropdown-menu .navi-text {
    color: #E4E6EF;
    cursor: pointer;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .dropdown-menu .navi.navi-hover .navi-item .navi-link:hover {
    background: transparent;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .dropdown-menu .navi.navi-hover .navi-item .navi-link:hover .navi-text {
    color: #6993FF;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link:hover .nav-text {
    color: #6993FF;
}
.card-similar-pages .nav.nav-tabs.nav-tabs-line .nav-item:first-child .nav-link {
    margin-left: 1rem;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link-icon {
    position: relative;
    margin: 0 0.5rem;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link-icon:before {
    content: '';
    background: #B5B5C3;
    width: 1px;
    height: 47px;
    position: absolute;
    top: 13px;
    left: 8px;
    z-index: 0;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link-icon .nav-icon {
    width: auto;
    z-index: 1;
    margin-top: 2px;
    background: #181C32 !important;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link-icon:hover .nav-icon {
    color: #6993FF;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link-icon:hover:before {
    background: #6993FF;
}
.card-similar-pages .nav.nav-tabs .nav-item:last-child {
    padding-right: 20px;
}

.card-similar-pages .card-toolbar:after {
    display: table;
    line-height: 0;
    content: "";
    position: fixed;
    top: 0;
    right: 0;
    width: 50px;
    height: 73px;
    background: linear-gradient(to right, transparent, #181C32);
}


.switch-page-status {
    margin-left: auto;
    position: relative;
}
.switch-page-status:after {
    display: table;
    line-height: 0;
    content: "";
    position: absolute;
    top: -23px;
    left: -50px;
    width: 50px;
    height: 73px;
    background: linear-gradient(to right, transparent, #181C32);
}

#content.full-width .card-similar-pages {
    left: 0;
    width: 100%;
}
.card-similar-pages + div {
    margin-top: 73px;
}

.navi-similar-pages .navi-item .navi-link .navi-text {
    color: #E4E6EF;
}
.navi-similar-pages .navi-item .navi-link.active .navi-text,
.navi-similar-pages .navi-item .navi-link:hover .navi-text {
    color: #6993FF;
}
.navi-similar-pages.navi-hover .navi-item .navi-link {
    cursor: pointer;
}
.navi-similar-pages.navi-hover .navi-item .navi-link:hover {
    background-color: rgba(105, 147, 255, 0.1);
}


.card-header .bloc {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

*[contenteditable="true"] {
    border-bottom: 1px dashed #EBEDF3;
}

.swal2-icon.swal2-warning {
    color: #F64E60;
    border-color: #F64E60;
}

.ck-editor__editable_inline {
    min-height: 0;
}

.video-responsive {
    overflow:hidden;
    padding-bottom:56.25%;
    position:relative;
    height:0;
}

.video-responsive iframe {
    left:0;
    top:0;
    height:100%;
    width:100%;
    position:absolute;
}

.page-content .modal {
    transform: translateX(192px);
}
.page-content .modal .modal-header {
    border-bottom: none;
}
.page-content .modal .modal-body {
    padding-top: 0;
}

.page-container .btn {
    border: none;
    -webkit-user-select: auto; /* fix for safari when editing button */
}

#ckf-modal-header {
    background: #fafafa !important;
}

i {
    font-size: inherit;
    color: inherit;
}

#ace-editor {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 300px;
}

.tox-tinymce-inline {
    margin-top: -6px;
    z-index: 2;
}
.mce-edit-focus {
    outline: blue solid 1px;
    outline-offset: 5px;
}

.modal.modal-element .modal-header {
    border-bottom: 1px solid #EBEDF3;
}
.modal.modal-element .modal-body {
    padding-top: 1.75rem;
}
#Modalicon .modal-body {
    overflow-y: scroll;
    height: calc(100vh - 200px);
}

.rounded-colors .minicolors input[type=hidden] + .minicolors-swatch {
    width: 25px;
    height: 25px;
    border-radius: 50%;
}
.rounded-colors .minicolors input[type=hidden] + .minicolors-swatch .minicolors-swatch-color {
    width: 25px;
    height: 25px;
}
.rounded-colors .form-group .minicolors-input-swatch .minicolors-swatch-color {
    border-top-right-radius: 50% !important;
    border-bottom-right-radius: 50% !important;
}
.rounded-colors .form-group .minicolors-panel .minicolors-swatches {
    background: white;
    margin: 0;
    padding: 5px 0 3px 5px;
}
.rounded-colors .form-group .minicolors-theme-bootstrap .minicolors-panel .minicolors-swatches .minicolors-swatch {
    border-radius: 0.42rem;
}
.rounded-colors .form-group .minicolors-theme-bootstrap .minicolors-panel .minicolors-swatches .minicolors-swatch-color {
    border-radius: 0;
}
.rounded-colors .form-group-slider {
    width: 150px;
    padding-top: 10px;
    margin-left: 5px;
}
.rounded-colors .noUi-target.noUi-horizontal {
    border: none;
    height: 5px;
}
.rounded-colors .nouislider-handle-primary .noUi-handle {
    border: 0;
    background: #6993FF;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.rounded-colors .noUi-target.noUi-horizontal .noUi-handle {
    top: -7px;
    width: 18px;
    height: 18px;
}
.rounded-colors.button-colors {
    display: inline-block;
}

.form-color {
    position: relative;
}
.form-color .form-color-remove {
    position: absolute;
    top: -5px;
    right: -3px;
    color: white;
    opacity: .7;
    cursor: pointer;
}
.form-color .form-color-remove:hover {
    opacity: 1;
}

.bootstrap-datetimepicker-widget .timepicker .timepicker-picker table td {
    background: transparent;
}

.bloc-border {
    border: 1px solid transparent;
}

.order-bump .ck.ck-editor__editable_inline,
.bloc-product-description.ck.ck-editor__editable_inline {
    padding: 0;
    margin-bottom: 0;
}
.order-bump-image-editor {
    width: 60px;
    position: absolute;
    top: 10px;
    left: 10px;
}
.order-bump .ck.ck-editor__editable_inline>:first-child {
    margin-top: 0;
    text-indent: 65px;
    float: none;
}
.bloc-product-description.ck.ck-editor__editable_inline>:first-child {
    margin-top: 0;
}
.order-bump .ck.ck-editor__editable_inline p:last-of-type,
.bloc-product-description.ck.ck-editor__editable_inline p:last-of-type {
    margin-bottom: 0;
}


.modal-edit-product .modal-body .card .card-toolbar {
    display: none;
}
.modal-edit-product .card.card-custom.card-collapsed>.card-body,
.modal-edit-product .card.card-custom.card-collapsed>form {
    display: block;
}

#modalPost .modal-body .card,
#modalCategory .modal-body .card,
#modalAttribute .modal-body .card,
#modalProductAttribute .modal-body .card,
#modalProduct .modal-body .card:first-child {
    padding: 0 !important;
    box-shadow: none;
}
#modalPost .modal-body .card-header,
#modalCategory .modal-body .card-header,
#modalAttribute .modal-body .card-header,
#modalProductAttribute .modal-body .card-header,
#modalProduct .modal-body .card:first-child .card-header {
    display: none !important;
}
#modalPost .modal-body .card .card-body,
#modalCategory .modal-body .card .card-body,
#modalAttribute .modal-body .card .card-body,
#modalProductAttribute .modal-body .card .card-body,
#modalProduct .modal-body .card:first-child .card-body {
    padding: 0 !important;
}
.image-input img {
    max-width: 100%;
    max-height: 120px;
}
.image-input [data-action="ckfinder"] {
    position: absolute;
    right: -10px;
    top: -10px;
}
.image-input [data-action="cancel"],
.image-input [data-action="remove"] {
    position: absolute;
    right: -10px;
    bottom: -5px;
}
.image-input [data-action="ckfinder"] i,
.image-input [data-action="cancel"] i,
.image-input [data-action="remove"] i {
    margin-left: 2px;
}

@media (max-width: 767px) {
    #content .editor-sidebar {
        width: 100%;
        z-index: 20;
    }
    #content .toggle-sidebar {
        left: 0;
        z-index: 21;
    }
    #content .page-content {
        width: 100%;
        margin-left: 0;
    }
    #content.full-width .editor-sidebar,
    #content.full-width .editor-sidebar form:after {
        margin-left: -1000px;
    }
    .card-similar-pages {
        left: 0;
    }
}

.ck-content .image>figcaption {
    background: transparent !important;
}
