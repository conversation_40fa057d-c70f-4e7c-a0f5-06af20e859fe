@charset "UTF-8";
.fc {
    direction: ltr;
    text-align: left; }

.fc-rtl {
    text-align: right; }

body .fc {
    /* extra precedence to overcome jqui */
    font-size: 1em; }

/* Colors
--------------------------------------------------------------------------------------------------*/
.fc-highlight {
    /* when user is selecting cells */
    background: #bce8f1;
    opacity: 0.3; }

.fc-bgevent {
    /* default look for background events */
    background: #8fdf82;
    opacity: 0.3; }

.fc-nonbusiness {
    /* default look for non-business-hours areas */
    /* will inherit .fc-bgevent's styles */
    background: #d7d7d7; }

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc .fc-popover {
    position: absolute;
    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    z-index: 99;
}

.fc-popover .fc-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 2px 4px; }

.fc-rtl .fc-popover .fc-header {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse; }

.fc-popover .fc-header .fc-title {
    margin: 0 2px; }

.fc-popover .fc-header .fc-close {
    cursor: pointer;
    opacity: 0.65;
    font-size: 1.1em; }

/* Misc Reusable Components
--------------------------------------------------------------------------------------------------*/
.fc-divider {
    border-style: solid;
    border-width: 1px; }

.fc-timegrid-divider {
    background: #F3F6F9;
}

.fc-bg,
.fc-bgevent-skeleton,
.fc-highlight-skeleton,
.fc-mirror-skeleton {
    /* these element should always cling to top-left/right corners */
    position: absolute;
    top: 0;
    left: 0;
    right: 0; }

.fc-bg {
    bottom: 0;
    /* strech bg to bottom edge */ }

.fc-bg table {
    height: 100%;
    /* strech bg to bottom edge */ }

.fc-toolbar .fc-button.fc-button-primary {
    color: #B5B5C3;
    background: transparent;
    border: 1px solid #EBEDF3;
    text-shadow: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    outline: none !important;
    height: 2.75rem;
    padding: 0 1rem;
    font-size: 1rem;
}
.fc-toolbar .fc-button.fc-button-primary:disabled {
    color: #B5B5C3;
    background: transparent;
    border: 1px solid #EBEDF3;
    text-shadow: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}
.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active{
    background-color: #1a252f;
    border-color: #151e27;
    color: #FFFFFF;
    -webkit-box-shadow: none;
    box-shadow: none;
    text-shadow: none;
}
.fc-toolbar .fc-button:hover {
    background: #F3F6F9;
    border: 1px solid #EBEDF3;
    color: #B5B5C3;
}

/* Tables
--------------------------------------------------------------------------------------------------*/
.fc table {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* fix scrollbar issue in firefox */
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em;
    /* normalize cross-browser */ }

.fc th {
    text-align: center; }

.fc th,
.fc td {
    border-style: solid;
    border-width: 1px;
    padding: 0;
    vertical-align: top;
    border-color: #EBEDF3;
}

.fc .fc-timegrid-axis {
    padding: 0.5rem 4px;
}
.fc-theme-standard .fc-scrollgrid {
    border-color: #EBEDF3;
}
.fc th.fc-col-header-cell {
    padding: 0.75rem 0.5rem;
    font-size: 1rem;
    font-weight: 500;
}
th.fc-col-header-cell a,
th.fc-col-header-cell span {
    color: #B5B5C3;
}
th.fc-col-header-cell a:hover {
    color: inherit;
}
.fc .fc-daygrid-day.fc-day-today,
.fc .fc-timegrid-col.fc-day-today {
    background: rgba(105, 147, 255, 0.025);
}

.fc td.fc-today {
    border-style: double;
    /* overcome neighboring borders */
}

.fc .fc-timegrid-slot {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.fc .fc-toolbar-title {
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 0;
}

.fc .fc-daygrid-body-natural .fc-daygrid-day-events {
    margin: 0.2rem 0.5rem;
}
.fc-event,
.fc-event-dot,
.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror, .fc-timegrid-more-link {
    background: #fff;
    -webkit-box-shadow: 0px 0px 9px 0px rgb(0 0 0 / 6%);
    box-shadow: 0px 0px 9px 0px rgb(0 0 0 / 6%);
    border-radius: 0.42rem;
    margin: 0.5rem;
    border: 5px solid #EBEDF3;
    border-right: 0;
    border-top: 0;
    border-bottom: 0;
}
.fc-event .fc-event-main,
.fc-event-dot .fc-event-main {
    padding: 0.5rem 0.5rem 0.5rem 2rem;
}
.fc-daygrid-dot-event {
    padding: 0.5rem;
}
.fc-daygrid-event-dot {
    border: 5px solid #3788d8;
    border-radius: 5px;
}
.fc-daygrid-dot-event.fc-event-light.fc-event-start .fc-daygrid-event-dot {
    border-color: #F3F6F9;
}
.fc-daygrid-dot-event.fc-event-danger.fc-event-start .fc-daygrid-event-dot {
    border-color: #F64E60;
}
.fc-daygrid-dot-event.fc-event-success.fc-event-start .fc-daygrid-event-dot {
    border-color: #1BC5BD;
}

.fc-remove {
    position: absolute;
    right: 5px;
    top: 3px;
    color: #EBEDF3;
}
.fc-remove:hover {
    color: #B5B5C3;
}


.fc-event.fc-event-solid-primary.fc-event-start,
.fc-event.fc-event-solid-primary.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-primary.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-primary.fc-event-start,
.fc-event-dot.fc-event-solid-primary.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-primary.fc-not-start.fc-end {
  background: #6993FF
}
.fc-event.fc-event-solid-info.fc-event-start,
.fc-event.fc-event-solid-info.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-info.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-info.fc-event-start,
.fc-event-dot.fc-event-solid-info.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-info.fc-not-start.fc-end {
    background: #8950FC;
}
.fc-event.fc-event-solid-danger.fc-event-start,
.fc-event.fc-event-solid-danger.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-danger.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-danger.fc-event-start,
.fc-event-dot.fc-event-solid-danger.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-danger.fc-not-start.fc-end {
    background: #F64E60;
}
.fc-event.fc-event-solid-warning.fc-event-start,
.fc-event.fc-event-solid-warning.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-warning.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-warning.fc-event-start,
.fc-event-dot.fc-event-solid-warning.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-warning.fc-not-start.fc-end {
    background: #FFA800;
}
.fc-event.fc-event-solid-success.fc-event-start,
.fc-event.fc-event-solid-success.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-success.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-success.fc-event-start,
.fc-event-dot.fc-event-solid-success.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-success.fc-not-start.fc-end {
    background: #1BC5BD;
}
.fc-event.fc-event-solid-secondary.fc-event-start,
.fc-event.fc-event-solid-secondary.fc-not-start.fc-not-end,
.fc-event.fc-event-solid-secondary.fc-not-start.fc-end,
.fc-event-dot.fc-event-solid-secondary.fc-event-start,
.fc-event-dot.fc-event-solid-secondary.fc-not-start.fc-not-end,
.fc-event-dot.fc-event-solid-secondary.fc-not-start.fc-end {
    background: #E4E6EF;
}

.fc-event .fc-event-main:before,
.fc-event-dot .fc-event-main:before {
    display: block;
    content: " ";
    position: absolute;
    height: 10px;
    width: 10px;
    border-radius: 50%;
    top: 0.85rem;
    left: 0.75rem;
}
.fc-event.fc-event-start .fc-event-main:before,
.fc-event-dot.fc-event-start .fc-event-main:before {
    background: #EBEDF3;
}
.fc-event.fc-event-primary.fc-event-start .fc-event-main:before,
.fc-event-dot.fc-event-primary.fc-event-start .fc-event-main:before {
    background: #6993FF;
}
.fc-event.fc-event-light.fc-event-start .fc-event-main:before,
.fc-event-dot.fc-event-light.fc-event-start .fc-event-main:before {
    background: #F3F6F9;
}
.fc-event.fc-event-info.fc-event-start .fc-event-main:before,
.fc-event-dot.fc-event-info.fc-event-start .fc-event-main:before {
    background: #8950FC;
}
.fc-event.fc-event-warning.fc-event-start .fc-event-main:before,
.fc-event-dot.fc-event-warning.fc-event-start .fc-event-main:before {
    background: #FFA800;
}

.fc-event .fc-event-title,
.fc-event-dot .fc-event-title {
    font-size: 0.9rem;
    font-weight: 400;
}
.fc-event.fc-event-solid-info.fc-event-start .fc-event-title,
.fc-event.fc-event-solid-info.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event.fc-event-solid-info.fc-event-not-start.fc-event-end .fc-event-title,
.fc-event-dot.fc-event-solid-info.fc-event-start .fc-event-title,
.fc-event-dot.fc-event-solid-info.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event-dot.fc-event-solid-info.fc-event-not-start.fc-event-end .fc-event-title {
    color: #ffffff;
}
.fc-event.fc-event-solid-danger.fc-event-start .fc-event-title,
.fc-event.fc-event-solid-danger.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event.fc-event-solid-danger.fc-event-not-start.fc-event-end .fc-event-title,
.fc-event-dot.fc-event-solid-danger.fc-event-start .fc-event-title,
.fc-event-dot.fc-event-solid-danger.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event-dot.fc-event-solid-danger.fc-event-not-start.fc-event-end .fc-event-title {
    color: #ffffff;
}
.fc-event.fc-event-solid-primary.fc-event-start .fc-event-title,
.fc-event.fc-event-solid-primary.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event.fc-event-solid-primary.fc-event-not-start.fc-event-end .fc-event-title,
.fc-event-dot.fc-event-solid-primary.fc-event-start .fc-event-title,
.fc-event-dot.fc-event-solid-primary.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event-dot.fc-event-solid-primary.fc-event-not-start.fc-event-end .fc-event-title {
    color: #ffffff;
}
.fc-event.fc-event-solid-warning.fc-event-start .fc-event-title,
.fc-event.fc-event-solid-warning.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event.fc-event-solid-warning.fc-event-not-start.fc-event-end .fc-event-title,
.fc-event-dot.fc-event-solid-warning.fc-event-start .fc-event-title,
.fc-event-dot.fc-event-solid-warning.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event-dot.fc-event-solid-warning.fc-event-not-start.fc-event-end .fc-event-title {
    color: #ffffff;
}
.fc-event.fc-event-solid-success.fc-event-start .fc-event-title,
.fc-event.fc-event-solid-success.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event.fc-event-solid-success.fc-event-not-start.fc-event-end .fc-event-title,
.fc-event-dot.fc-event-solid-success.fc-event-start .fc-event-title,
.fc-event-dot.fc-event-solid-success.fc-event-not-start.fc-event-not-end .fc-event-title,
.fc-event-dot.fc-event-solid-success.fc-event-not-start.fc-event-end .fc-event-title {
    color: #ffffff;
}

.fc-event .fc-event-main,
.fc-event-dot .fc-event-main {
    color: #3F4254;
}
.fc-event .fc-event-time,
.fc-event-dot .fc-event-time {
    font-size: 0.9rem;
    text-transform: uppercase;
    font-weight: 500;
}
.fc-event.fc-event-solid-info.fc-event-start .fc-event-time,
.fc-event.fc-event-solid-danger.fc-event-start .fc-event-time,
.fc-event.fc-event-solid-primary.fc-event-start .fc-event-time,
.fc-event.fc-event-solid-warning.fc-event-start .fc-event-time,
.fc-event.fc-event-solid-success.fc-event-start .fc-event-time {
    color: #FFFFFF;
}


.fc-description {
    color: #B5B5C3;
    font-size: 0.9rem;
    margin-top: 0;
    font-weight: normal;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
/*.fc-event.fc-event-solid-info.fc-event-start .fc-description,
.fc-event.fc-event-solid-danger.fc-event-start .fc-description,
.fc-event.fc-event-solid-primary.fc-event-start .fc-description,
.fc-event.fc-event-solid-warning.fc-event-start .fc-description,
.fc-event.fc-event-solid-success.fc-event-start .fc-description {
    color: #FFFFFF;
}*/


.fc-calendar-availabilities td.fc-timegrid-col {
    border-color: transparent;
}
.fc-calendar-availabilities .fc-event {
    margin: 0 0 2px 0;
}
.fc-calendar-availabilities .fc-event-time {
    display: none;
}
.fc-calendar-availabilities .fc-event .fc-event-main,
.fc-calendar-availabilities .fc-event-dot .fc-event-main {
    padding: 0;
}
.fc-calendar-availabilities .fc-event-title-container {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.fc-calendar-availabilities .fc-event.fc-event-start .fc-event-main:before,
.fc-calendar-availabilities .fc-event-dot.fc-event-start .fc-event-main:before {
    display: none;
}
.fc-calendar-availabilities .fc-event.selected .fc-event-main {
    color: white;
}
.fc-calendar-availabilities .fc-timegrid-slot-label {
    display: none;
}
.fc-calendar-availabilities .fc-timegrid-slot,
.fc-calendar-availabilities th.fc-col-header-cell,
.fc-calendar-availabilities .fc-scrollgrid,
.fc-calendar-availabilities th,
.fc-calendar-availabilities td {
    border: none;
}

@media (max-width: 767px) {
    .fc .fc-toolbar {
        flex-direction: column;
    }
    .fc-toolbar-chunk {
        margin: 10px 0;
    }
}
