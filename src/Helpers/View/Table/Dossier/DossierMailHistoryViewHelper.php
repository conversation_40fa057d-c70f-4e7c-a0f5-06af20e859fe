<?php
namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Dossier;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Mail\History\MailHistory;
use Mat<PERSON><PERSON>ver\Helpers\Tools;
use MatG<PERSON>ver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Services\Users\UsersService;

class DossierMailHistoryViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_mail_histories');
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('subject', __('Sujet')));
        $this->table->addTableColumn(new Column('users', __('Destinataires')));
        $this->table->addTableColumn(new Column('status', __('État')));
        $this->table->addTableColumn(new Column('opened', __('Ouvert')));
        $this->table->addTableColumn(new Column('actions', __('Action')));
        $this->table->setTableSort(0, 'desc');
    }

    /**
     * @param MailHistory[] $dossierMailsHistories
     */
    public function getContent(array $dossierMailsHistories)
    {
        $this->setContentRows($dossierMailsHistories);
        return $this->table->getContent();
    }

    /**
     * @param MailHistory[] $dossierMailsHistories
     */
    public function setContentRows(array $dossierMailsHistories)
    {
        foreach ($dossierMailsHistories as $dossierMailHistory) {
            $row = new Row();
            $row->addColumn(new Column('date', dateTimeFr($dossierMailHistory->getDate()->format('Y-m-d H:i:s')), '', $dossierMailHistory->getDate()->getTimestamp()));
            $row->addColumn(new Column('subject', $dossierMailHistory->getSubject()));
            $row->addColumn(new Column('users', $this->getUsers($dossierMailHistory)));

            if ($dossierMailHistory->getSent() and $dossierMailHistory->getError()) {
                $status = '<span class="label label-warning label-inline" data-rel="tooltip" data-html="true" data-placement="bottom" data-title="' . $dossierMailHistory->getErrorMessage() . '">' . __('Envoyé avec des erreurs') . '<i class="fa fa-info-circle text-white ml-2" style="font-size: 1rem"></i></span>';
            } else if ($dossierMailHistory->getSent()) {
                $status = '<span class="label label-success label-inline">' . __('Envoyé') . '</span>';
            } else if ($dossierMailHistory->getError()) {
                $status = '<span class="label label-danger label-inline" data-rel="tooltip" data-html="true" data-placement="bottom" data-title="' . $dossierMailHistory->getErrorMessage() . '">' . __('Erreur') . '<i class="fa fa-info-circle text-white ml-2" style="font-size: 1rem"></i></span>';
            } else {
                $status = '<span class="label label-info label-inline">' . __('En attente') . '</span>';
            }
            $row->addColumn(new Column('status', $status));

            $row->addColumn(new Column('opened', $this->getUsersOpened($dossierMailHistory)));

            $row->addColumn((new Column('actions'))->setActions($this->getActions($dossierMailHistory)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param MailHistory $dossierMailHistory
     * @return string
     */
    public function getUsers(MailHistory $dossierMailHistory): string
    {
        $users = $dossierMailHistory->getUsers();
        if (!count($users)) {
            return '<em>' . __('Aucun destinataire') . '</em>';
        }

        $content = '';
        foreach ($users as $user) {
            $userEntity = $user->getUser();
            if ($userEntity) {
                try {
                    $userEntity->getFirstName();
                } catch (\Exception $e) {
                    $userEntity = null;
                }
            }
            $userName = ($userEntity ?: $user->getFirstName() . ' ' . $user->getLastName());
            if (!trim($userName)) {
                $userName = $user->getEmail();
            }
            $content .= '
                <span data-toggle="tooltip" data-html="true" data-title="' . ($userEntity ? $userEntity->getEmail() : $user->getEmail()) . '">
                    ' . $userName . '
                </span>';
        }

        return $content;
    }

    /**
     * @param MailHistory $dossierMailHistory
     * @return string
     */
    public function getUsersOpened(MailHistory $dossierMailHistory): string
    {
        $users = $dossierMailHistory->getUsers();
        if (!count($users)) {
            return '';
        }

        if (count($users) == 1) {
            $user = $users[0];
            if ($user->getOpen()) {
                return '<span class="label label-inline label-success" data-toggle="tooltip" data-title="' . __('Ouvert le %s', dateTimeFr($user->getDateOpen()->format('Y-m-d H:i:s'))) . '">' . __('Ouvert') . '<i class="fas fa-info-circle ml-2"></i></span>';
            }
            return __('Non ouvert');
        }

        $nbOpened = 0;
        $nbUsers = count($users);
        foreach ($users as $user) {
            if ($user->getOpen()) {
                $nbOpened++;
            }
        }
        return n__('%d/%d ouverture', '%d/%d ouvertures', $nbOpened, $nbOpened, $nbUsers);
    }

    /**
     * @param MailHistory $dossierMailHistory
     * @return ColumnAction[]
     */
    public function getActions(MailHistory $dossierMailHistory): array
    {
        $action = new ColumnAction();
        $action->setHref(Tools::makeLink('app', 'dossier', 'mail/send/' . $dossierMailHistory->getDossier()->getId(), 'from_history=' . $dossierMailHistory->getId()));
        $action->setTooltip(__('Créer un nouvel email à partir de celui-ci'));
        $action->setIcon('fas fa-share');

        return [
            new ColumnActionView(Tools::makeLink('app', 'dossier', 'mail/view/' . $dossierMailHistory->getId() . '/' . $dossierMailHistory->getDossier()->getId())),
            $action
        ];
    }
}
