<?php
namespace MatGyver\Helpers;

use Imagick;
use ImagickException;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class Imagick
 * @package MatGyver\Helpers
 */
class ImagickHelper
{
    public const TEMP_SUFFIX = '-temp';
    public const THUMBNAIL_SUFFIX = '-thumbnail';

    public static function convertPdf(string $file, bool $firstPage = false, bool $resize = true, bool $addTempSuffix = true, bool $addThumbnailSuffix = false, string $extension = 'png'): array
    {
        // Check if Imagick extension is loaded
        if (!extension_loaded('imagick')) {
            LoggerService::logError('Imagick extension is not loaded');
            return [];
        }

        $fileName = basename($file, '.pdf');
        $directory = dirname($file);
        $images = [];

        try {
            $imagick = new Imagick();
            $imagick->setCompressionQuality(100);
            $imagick->setResolution(144, 144);
            $imagick->readImage($file . ($firstPage ? '[0]' : ''));
            $imagick->setImageFormat(($extension === 'png' ? "png" : "jpeg"));
            $imagick->setBackgroundColor('white');
            $imagick->setImageBackgroundColor('#ffffff');
            $imagick->setImageAlphaChannel(Imagick::ALPHACHANNEL_REMOVE);
            $nbPages = $imagick->getNumberImages();
            if ($nbPages == 1 or $firstPage) {
                $imageFile = self::getFileName($directory . '/' . $fileName . '.' . $extension, $addTempSuffix, $addThumbnailSuffix);
                $imagick->writeImage($imageFile);
                $images[] = str_replace(WEB_PATH . '/medias/', '', $imageFile);
            } else {
                foreach ($imagick as $i => $page) {
                    $imageFile = self::getFileName($directory . '/' . $fileName . '-' . $i . '.' . $extension, $addTempSuffix, $addThumbnailSuffix);
                    $imagick->writeImage($imageFile);
                    $images[] = str_replace(WEB_PATH . '/medias/', '', $imageFile);
                }
            }
            $imagick->clear();
        } catch (ImagickException $e) {
            LoggerService::logError('Imagick error: ' . $e->getMessage());
        } catch (\Exception $e) {
            LoggerService::logError('General error in convertPdf: ' . $e->getMessage());
        }

        if ($resize) {
            self::resize($images);
        }

        return $images;
    }

    public static function getFileName(string $filePath, bool $addTempSuffix = true, bool $addThumbnailSuffix = true): string
    {
        $extension = getExtension($filePath);
        $file = str_replace('.' . $extension, '', $filePath);
        if ($addThumbnailSuffix) {
            $file .= self::THUMBNAIL_SUFFIX;
        }
        if ($addTempSuffix) {
            $file .= self::TEMP_SUFFIX;
        }

        return $file . '.' . $extension;
    }

    public static function resize(array $images): void
    {
        foreach ($images as $image) {
            $imageFile = WEB_PATH . '/medias/' . $image;
            list($width, $height) = getimagesize($imageFile);
            if ($width > 2000) {
                try {
                    ThumbnailService::resizeToWidth($imageFile, $imageFile, 2000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                }
            } elseif ($height > 1000) {
                try {
                    ThumbnailService::resizeToHeight($imageFile, $imageFile, 1000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                }
            }
        }
    }
}
