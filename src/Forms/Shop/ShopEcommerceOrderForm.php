<?php
namespace MatGyver\Forms\Shop;

use MatGyver\Enums\ProductsEnum;
use MatGyver\Forms\AbstractForm;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\IpAddress;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use MatGyver\Services\Autoresponders\AutorespondersProductService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\PasswordService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Ecommerce\ShopEcommerceCartService;
use MatGyver\Services\Shop\Ecommerce\ShopEcommerceFormService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopCartProductsService;
use MatGyver\Services\Shop\ShopCartService;
use MatGyver\Services\Shop\ShopCustomersService;
use MatGyver\Services\Shop\ShopPaymentsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use MatGyver\Services\Users\UsersLoginService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Class ShopEcommerceOrderForm
 * @package MatGyver\Forms\Shop
 */
class ShopEcommerceOrderForm extends AbstractForm
{
    /**
     * @var ShopEcommerceCartService
     */
    private $shopEcommerceCartService;

    /**
     * @var ShopEcommerceFormService
     */
    private $shopEcommerceFormService;

    /**
     * @var ShopCartService
     */
    private $shopCartService;

    /**
     * @var ShopCartProductsService
     */
    private $shopCartProductsService;

    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * @var ShopPaymentsService
     */
    private $shopPaymentsService;

    /**
     * @var ShopProductsService
     */
    private $shopProductsService;

    /**
     * @var ShopVatRulesService
     */
    private $shopVatRulesService;

    /**
     * @var AutorespondersProductService
     */
    private $autorespondersService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var UsersLoginService
     */
    private $usersLoginService;

    /**
     * ShopEcommerceOrderForm constructor.
     * @param ShopEcommerceCartService $shopEcommerceCartService
     * @param ShopEcommerceFormService $shopEcommerceFormService
     * @param ShopCartService $shopCartService
     * @param ShopCartProductsService $shopCartProductsService
     * @param ShopCustomersService $shopCustomersService
     * @param ShopPaymentsService $shopPaymentsService
     * @param ShopProductsService $shopProductsService
     * @param ShopVatRulesService $shopVatRulesService
     * @param AutorespondersProductService $autorespondersService
     * @param UsersService $usersService
     * @param UsersLoginService $usersLoginService
     */
    public function __construct(
        ShopEcommerceCartService $shopEcommerceCartService,
        ShopEcommerceFormService $shopEcommerceFormService,
        ShopCartService $shopCartService,
        ShopCartProductsService $shopCartProductsService,
        ShopCustomersService $shopCustomersService,
        ShopPaymentsService $shopPaymentsService,
        ShopProductsService $shopProductsService,
        ShopVatRulesService $shopVatRulesService,
        AutorespondersProductService $autorespondersService,
        UsersService $usersService,
        UsersLoginService $usersLoginService
    ) {
        $this->shopEcommerceCartService = $shopEcommerceCartService;
        $this->shopEcommerceFormService = $shopEcommerceFormService;
        $this->shopCartService = $shopCartService;
        $this->shopCartProductsService = $shopCartProductsService;
        $this->shopCustomersService = $shopCustomersService;
        $this->shopPaymentsService = $shopPaymentsService;
        $this->shopProductsService = $shopProductsService;
        $this->shopVatRulesService = $shopVatRulesService;
        $this->autorespondersService = $autorespondersService;
        $this->usersService = $usersService;
        $this->usersLoginService = $usersLoginService;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostOrder(array $submittedData): array
    {
        $params = $this->getParamsFromPost($submittedData);

        $cart = $this->shopEcommerceCartService->getCart();
        if (!$cart->getProducts()) {
            return $this->sendResponse([__('Aucun produit dans votre panier.')]);
        }

        $rules = [
            'first_name' => [new NotBlank(['message' => __('Veuillez indiquer votre prénom.')])],
            'last_name' => [new NotBlank(['message' => __('Veuillez indiquer votre nom.')])],
            'email' => [new NotBlank(['message' => __('Veuillez indiquer votre adresse email.')])],
        ];
        $errors = $this->validateForm($rules, $submittedData);
        if ($errors) {
            return $this->sendResponse($errors, false, ['step' => 'customer']);
        }

        //get order form data
        $orderFormData = $this->shopEcommerceFormService->prepareOrderForm();
        $totalTaxIncl = $orderFormData['amounts']['amount_tax_incl'];
        if ($totalTaxIncl) {
            $rules = [
                'address' => [new NotBlank(['message' => __('Veuillez indiquer votre adresse.')])],
                'zip' => [new NotBlank(['message' => __('Veuillez indiquer votre code postal.')])],
                'city' => [new NotBlank(['message' => __('Veuillez indiquer votre ville.')])],
                'country' => [new NotBlank(['message' => __('Veuillez indiquer votre pays.')])],
            ];
            $errors = $this->validateForm($rules, $submittedData);
            if ($errors) {
                return $this->sendResponse($errors, false, ['step' => 'customer']);
            }
        }

        /*if (!RightsService::isIndividual() and !RightsService::isEditor()) {
            $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);

            $rules = [
                'password' => [new NotBlank(['message' => __('Veuillez indiquer un mot de passe.')])],
                'password2' => [new NotBlank(['message' => __('Veuillez retaper votre mot de passe.')])],
            ];
            $errors = $this->validateForm($rules, $submittedData);
            if ($errors) {
                return $this->sendResponse($errors, false, ['step' => 'customer']);
            }

            $user = $this->usersService->adminGetUserByEmail($email);
            if ($user) {
                $user = null;
            }
            if ($user) {
                //try to log in with password
                $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);

                if (!$user->getValidated()) {
                    $userRandomId = base64_encode('u=' . $user->getId() . '&r=' . $user->getRandomId() . '&c=' . $user->getClient()->getId());
                    return $this->sendResponse([__('Un compte existe déjà avec cette adresse email mais n\'est pas encore validé.') . '<br><a href="' . Tools::makeLink('site', 'user', 'confirmation/' . $userRandomId) . '">' . __('Valider mon compte') . '</a>'], false, ['step' => 'customer']);
                }

                $validatePostSignIn = $this->usersLoginService->validatePostSignIn(['email' => $email, 'password' => $password]);
                if (!$validatePostSignIn['valid']) {
                    return $this->sendResponse([__('Un compte existe déjà avec cette adresse email. Veuillez indiquer le mot de passe correspondant ou connectez-vous.') . '<br><a href="' . Tools::makeLink('site', 'signin', '', 'return=cart') . '">' . __('Connexion') . '</a>'], false, ['step' => 'customer']);
                }

                $signIn = $this->usersLoginService->insertSignIn(['email' => $email, 'password' => $password]);
                if (!$signIn['valid']) {
                    return $this->sendResponse([__('Un compte existe déjà avec cette adresse email. Veuillez indiquer le mot de passe correspondant ou connectez-vous.') . '<br><a href="' . Tools::makeLink('site', 'signin', '', 'return=cart') . '">' . __('Connexion') . '</a>'], false, ['step' => 'customer']);
                }
            } else {
                $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
                $password2 = filter_var($submittedData['password2'], FILTER_UNSAFE_RAW);
                $checkPassword = PasswordService::checkPassword($password);
                if (!$checkPassword['valid']) {
                    return $this->sendResponse([$checkPassword['message']], false, ['step' => 'customer']);
                }
                if ($password != $password2) {
                    return $this->sendResponse([__('Les 2 mots de passe ne sont pas identiques.')], false, ['step' => 'customer']);
                }
            }
        } else {
            $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);
            $user = $this->usersService->getUser();
            if ($user->getEmail() != $email) {
                $checkUser = $this->usersService->adminGetUserByEmail($email);
                if ($checkUser) {
                    return $this->sendResponse([__('Un compte existe déjà avec cette adresse email.')], false, ['step' => 'customer']);
                }
            }
        }*/

        $rules = ['id_payment' => [new NotBlank(['message' => __('Veuillez sélectionner un moyen de paiement.')])]];
        $errors = $this->validateForm($rules, $submittedData);
        if ($errors) {
            return $this->sendResponse($errors, false, ['step' => 'payment']);
        }

        $errors = [];
        /*if (isset($submittedData['password']) and $submittedData['password']) {
            $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
            $password2 = filter_var($submittedData['password2'], FILTER_UNSAFE_RAW);
            if (!$password or !$password2) {
                $errors[] = __('Veuillez indiquer un mot de passe.');
            } elseif ($password != $password2) {
                $errors[] = __('Les 2 mots de passe ne sont pas identiques.');
            } else {
                $checkPassword = PasswordService::checkPassword($password);
                if (!$checkPassword['valid']) {
                    $errors[] = $checkPassword['message'];
                }
            }
        }*/

        $idPayment = $params['id_payment'];
        $payment = $this->shopPaymentsService->getPaymentById($idPayment);
        if (!$payment) {
            $errors[] = __('Ce moyen de paiement n\'est pas disponible');
            return $this->sendResponse($errors, false, ['step' => 'customer']);
        }

        if (isset($submittedData['tva_intracom']) and $submittedData['tva_intracom']) {
            $tvaIntracom = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);
            $validTva = $this->shopVatRulesService->checkVatNumber($tvaIntracom);
            if (!$validTva) {
                $errors[] = __('Numéro de TVA intracommunautaire invalide');
                return $this->sendResponse($errors, false, ['step' => 'customer']);
            }
        }

        if (isset($submittedData['cgv'])) {
            if (!isset($submittedData['cgv-accept']) or !$submittedData['cgv-accept']) {
                $errors[] = __('Veuillez accepter les conditions générales de vente');
                return $this->sendResponse($errors, false, ['step' => 'customer']);
            }
        }

        return $this->sendResponse($errors);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function processProductOrder(array $submittedData): array
    {
        $params = $this->getParamsFromPost($submittedData);

        $cart = $this->shopEcommerceCartService->getCart();
        if (!$cart->getProducts()) {
            return $this->sendResponse([__('Aucun produit dans votre panier.')]);
        }

        //1. create customer
        $idCustomer = null;
        if (isset($_SESSION['customer'])) {
            $idCustomer = $_SESSION['customer']['id'];
        }
        $processCustomer = $this->shopCustomersService->processCustomer($submittedData, $idCustomer);
        if (!$processCustomer['valid']) {
            return $this->sendResponse([$processCustomer['message']], false, ['step' => 'customer']);
        }
        $idCustomer = $processCustomer['id_customer'];

        $customer = $this->shopCustomersService->getCustomerById($idCustomer);

        $log = "Analyse des produits : recherche d'un produit de type subscription\n";
        $hasSubscription = false;
        foreach ($cart->getProducts() as $cartProduct) {
            $shopProduct = $this->shopProductsService->getRepository()->findOneBy(['id' => $cartProduct->getId(), 'client' => $cartProduct->getClientId()]);
            if (!$shopProduct) {
                continue;
            }
            if ($shopProduct->getType() == ProductsEnum::TYPE_SUBSCRIPTION) {
                $hasSubscription = true;
                break;
            }
        }
        if ($hasSubscription) {
            //type subscription : a pro client will be created
            $log .= "Produit de type subscription trouvé\n";
        } else {
            /*if (!RightsService::isIndividual() and !RightsService::isEditor()) {
                //create customer client
                $container = ContainerBuilderService::getInstance();

                $log .= "Aucun produit de type subscription trouvé\n";
                $log .= "Search user with email = " . $customer->getEmail() . "\n";
                $user = $this->usersService->adminGetUserByEmail($customer->getEmail());

                if ($user) {
                    //try to log in with password
                    $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
                    $hash = PasswordService::hashPassword($password);
                    $user->setPassword($hash);
                    try {
                        $this->usersService->persistAndFlush($user);
                    } catch (\Exception $e) {
                        return $this->sendResponse([__('Une erreur est survenue lors de la mise à jour de l\'utilisateur')], false, ['step' => 'customer']);
                    }

                    $signIn = $container->get(UsersLoginService::class)->insertSignIn(['email' => $user->getEmail()], true);
                    if (!$signIn['valid']) {
                        LoggerService::logError('error insertSignIn : ' . $signIn['message']);
                        return $this->sendResponse([__('Un compte existe déjà avec cette adresse email. Veuillez indiquer le mot de passe correspondant ou connectez-vous.') . '<br><a href="' . Tools::makeLink('site', 'signin', '', 'return=cart') . '">' . __('Connexion') . '</a>'], false, ['step' => 'customer']);
                    }

                    //send welcome email
                    $container->get(UsersService::class)->sendConfirmationLink($user->getId(), $user->getClient()->getId(), 'freemium');

                    if ($user->getClient()->getId() != $_SESSION['client']['id']) {
                        $log .= "user client " . $user->getClient()->getId() . " != session client " . $_SESSION['client']['id'] . "\n";
                        LoggerService::logError($log);
                        return $this->sendResponse([__('Une erreur est survenue lors de la création du compte client.')], false, ['step' => 'customer']);
                    }
                    $log .= "user " . $user->getId() . " found\n";
                    $client = $user->getClient();
                } else {
                    $log .= "no user found --> call makeClientCustomer\n";
                    $makeClientCustomer = $this->shopCustomersService->makeClientCustomer($customer, $submittedData);
                    if (!$makeClientCustomer['valid']) {
                        $log .= 'makeClientCustomer failed : ' . $makeClientCustomer['message'];
                        LoggerService::logError($log);
                        return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du compte client.')];
                    }

                    $idClient = $makeClientCustomer['idClient'];
                    $log .= "client $idClient successfully created\n";
                    $log .= json_encode($makeClientCustomer) . "\n";

                    $client = $container->get(ClientsService::class)->getClientById($idClient);
                    $user = $client->getMainAdmin();
                }

                $cartSession = $_SESSION['cart'];
                if (isset($_SESSION['cart_customer_' . $idCustomer])) {
                    $cartCustomerSession = $_SESSION['cart_customer_' . $idCustomer];
                }

                //autologin
                $autologin = $container->get(UsersLoginService::class)->insertSignIn(['email' => $user->getEmail()], true);
                if (!$autologin['valid']) {
                    $log .= 'autologin failed : ' . $autologin['message'];
                    LoggerService::logError($log);
                    return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du compte client.')];
                }

                $_SESSION['cart'] = $cartSession;
                if (isset($cartCustomerSession)) {
                    $_SESSION['cart_customer_' . $idCustomer] = $cartCustomerSession;
                }
            } else {
                $user = $this->usersService->getUser();
                if (!$user) {
                    $log .= "Unable to find logged in user for client " . $_SESSION['client']['id'] . "\n";
                    LoggerService::logError($log);
                    return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du compte client.')];
                }

                if ($user->getClient()->getId() != $_SESSION['client']['id']) {
                    $log .= "user client " . $user->getClient()->getId() . " != session client " . $_SESSION['client']['id'] . "\n";
                    LoggerService::logError($log);
                    return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du compte client.')];
                }

                $log .= "user " . $user->getId() . " found\n";
                if ($customer->getEmail() != $user->getEmail()) {
                    //user has updated his email address
                    $user->setEmail($customer->getEmail());
                    try {
                        $this->usersService->persistAndFlush($user);
                    } catch (\Exception $e) {
                        $log .= "Unable to update user " . $user->getId() . " with email address " . $customer->getEmail() . " : " . $e->getMessage() . "\n";
                        LoggerService::logError($log);
                        return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du compte client.')];
                    }
                }
            }*/
        }

        //2. create cart
        $idCart = null;
        if ($cart->getId()) {
            $idCart = $cart->getId();
            $updateCart = $this->shopCartService->updateCart($idCart, $params);
            if (!$updateCart['valid']) {
                return $this->sendResponse([$updateCart['message']], false, ['step' => 'customer']);
            }
        }
        if ($idCart === null) {
            $createCart = $this->shopCartService->insertCart($idCustomer, $params);
            if (!$createCart['valid']) {
                return $this->sendResponse([$createCart['message']], false, ['step' => 'customer']);
            }
            $idCart = $createCart['id_cart'];
            $cart->setId($idCart);
        }

        $shopCart = $this->shopCartService->getCartById($idCart);

        //3. add products to cart
        $products = [];
        foreach ($cart->getProducts() as $product) {
            $products[] = [
                'id' => $product->getId(),
                'client_id' => $product->getClientId(),
                'qty' => $product->getQuantity(),
                'attributes' => $product->getAttributes(),
            ];
        }

        $updateProductInCart = $this->shopCartProductsService->addProductInCart($shopCart, $products);
        if (!$updateProductInCart['valid']) {
            return $this->sendResponse([$updateProductInCart['message']], false, ['step' => 'customer']);
        }

        $idPayment = $params['id_payment'];
        $payment = $this->shopPaymentsService->getPaymentById($idPayment);
        if (!$payment) {
            return $this->sendResponse([__('Ce moyen de paiement n\'est pas disponible')], false, ['step' => 'payment']);
        }

        //inscriptions autorépondeur (client)
        foreach ($cart->getProductsIds() as $productId) {
            $autoresponders = $this->autorespondersService->getRepository()->findBy(['product' => $productId], null, null, null, false);
            if ($autoresponders) {
                foreach ($autoresponders as $autoresponder) {
                    $this->autorespondersService->subscribe($autoresponder, $productId, $params);
                }
            }
        }

        //get amounts
        $amounts = $this->shopCartService->getCartAmounts($shopCart, $idPayment);

        $paymentSubType = '';
        if (isset($submittedData['payment-subtype-' . $payment->getType()])) {
            $paymentSubType = filter_var($submittedData['payment-subtype-' . $payment->getType()], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['paypal_token_nonce']) and $submittedData['paypal_token_nonce']) {
            $paymentSubType = 'paypal';
        }
        if (isset($submittedData['mollie_paypal_token_nonce']) and $submittedData['mollie_paypal_token_nonce']) {
            $paymentSubType = 'paypal';
        }

        $custom = Transaction::generateCustom($idCart, $idCustomer, 0, $idPayment, [], IpAddress::getRealIp(), true);

        $return = [
            'firstPaymentAmount' => $amounts['amount_to_pay'],
            'paymentType' => $payment->getType(),
            'paymentSubType' => $paymentSubType,
            'braintreeBillingAddress' => ShopProductOrderForm::getBraintreeBillingAddress(Entities::asArray($customer)),
            'stripeBillingAddress' => ShopProductOrderForm::getStripeBillingAddress(Entities::asArray($customer)),
            'mollieBillingAddress' => ShopProductOrderForm::getMollieBillingAddress(Entities::asArray($customer)),
            'custom' => $custom,
        ];
        if (!$amounts['amount_tax_excl']) {
            $return['paymentType'] = 'free';
            $return['paymentSubType'] = '';
        }
        return $this->sendSuccessResponse(null, false, $return);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function getParamsFromPost(array $submittedData) :array
    {
        $params = [
            'id_payment' => filter_var($submittedData['id_payment'], FILTER_VALIDATE_INT),
            'first_name' => filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW),
            'last_name' => filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW),
            'email' => filter_var($submittedData['email'], FILTER_UNSAFE_RAW),
            'country' => (isset($submittedData['country']) ? filter_var($submittedData['country'], FILTER_UNSAFE_RAW) : DEFAULT_COUNTRY),
            'address' => (isset($submittedData['address']) ? filter_var($submittedData['address'], FILTER_UNSAFE_RAW) : ''),
            'address2' => (isset($submittedData['address2']) ? filter_var($submittedData['address2'], FILTER_UNSAFE_RAW) : ''),
            'city' => (isset($submittedData['city']) ? filter_var($submittedData['city'], FILTER_UNSAFE_RAW) : ''),
            'zip' => (isset($submittedData['zip']) ? filter_var($submittedData['zip'], FILTER_UNSAFE_RAW) : ''),
            'state' => (isset($submittedData['state']) ? filter_var($submittedData['state'], FILTER_UNSAFE_RAW) : ''),
            'telephone' => (isset($submittedData['telephone']) ? filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW) : ''),
            'discount' => '',
            'url' => '',
        ];

        if (isset($submittedData['discount'])) {
            $params['discount'] = filter_var($submittedData['discount'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['url'])) {
            $params['url'] = filter_var($submittedData['url'], FILTER_VALIDATE_URL);
        }

        return $params;
    }
}
