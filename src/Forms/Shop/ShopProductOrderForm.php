<?php

namespace MatG<PERSON>ver\Forms\Shop;

use MatGyver\Enums\ProductsEnum;
use MatGyver\Forms\AbstractForm;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\IpAddress;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use MatGyver\Services\Autoresponders\AutorespondersProductService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\PasswordService;
use MatGyver\Services\Shop\ShopCartProductsService;
use MatGyver\Services\Shop\ShopCartService;
use MatGyver\Services\Shop\ShopCustomersFieldsService;
use MatGyver\Services\Shop\ShopCustomersService;
use MatGyver\Services\Shop\ShopPaymentsService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Class ShopProductOrderForm
 * @package MatGyver\Forms\Shop
 */
class ShopProductOrderForm extends AbstractForm
{
    /**
     * @var ShopProductsService
     */
    private $shopProductsService;

    /**
     * @var ShopCartService
     */
    private $shopCartService;

    /**
     * @var ShopCartProductsService
     */
    private $shopCartProductsService;

    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * @var ShopPaymentsService
     */
    private $shopPaymentsService;

    /**
     * @var ShopVatRulesService
     */
    private $shopVatRulesService;

    /**
     * @var AutorespondersProductService
     */
    private $autorespondersService;

    /**
     * @var ShopCustomersFieldsService
     */
    private $shopCustomersFieldsService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * ShopProductOrderForm constructor.
     * @param ShopProductsService $shopProductsService
     * @param ShopCartService $shopCartService
     * @param ShopCartProductsService $shopCartProductsService
     * @param ShopCustomersService $shopCustomersService
     * @param ShopPaymentsService $shopPaymentsService
     * @param ShopVatRulesService $shopVatRulesService
     * @param AutorespondersProductService $autorespondersService
     * @param ShopCustomersFieldsService $shopCustomersFieldsService
     * @param ClientsService $clientsService
     * @param UsersService $usersService
     */
    public function __construct(
        ShopProductsService $shopProductsService,
        ShopCartService $shopCartService,
        ShopCartProductsService $shopCartProductsService,
        ShopCustomersService $shopCustomersService,
        ShopPaymentsService $shopPaymentsService,
        ShopVatRulesService $shopVatRulesService,
        AutorespondersProductService $autorespondersService,
        ShopCustomersFieldsService $shopCustomersFieldsService,
        ClientsService $clientsService,
        UsersService $usersService
    ) {
        $this->shopProductsService = $shopProductsService;
        $this->shopCartService = $shopCartService;
        $this->shopCartProductsService = $shopCartProductsService;
        $this->shopCustomersService = $shopCustomersService;
        $this->shopPaymentsService = $shopPaymentsService;
        $this->shopVatRulesService = $shopVatRulesService;
        $this->autorespondersService = $autorespondersService;
        $this->shopCustomersFieldsService = $shopCustomersFieldsService;
        $this->clientsService = $clientsService;
        $this->usersService = $usersService;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostOrder(array $submittedData): array
    {
        $rules = [
            'id_product' => [new NotBlank(['message' => __('Ce produit n\'existe pas.')])],
            'id_payment' => [new NotBlank(['message' => __('Veuillez sélectionner un moyen de paiement.')])],
            'first_name' => [new NotBlank(['message' => __('Veuillez indiquer votre prénom.')])],
            'last_name' => [new NotBlank(['message' => __('Veuillez indiquer votre nom.')])],
            'email' => [new NotBlank(['message' => __('Veuillez indiquer votre adresse email.')])],
        ];

        $errors = $this->validateForm($rules, $submittedData);
        if ($errors) {
            return $this->sendResponse($errors, false, ['step' => 1]);
        }

        if (!SUBDOMAIN_ENABLED) {
            $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);
            if (!$email) {
                return $this->sendErrorResponse([__('Adresse email incorrecte')], false, ['step' => 1]);
            }
            $user = $this->usersService->adminGetUserByEmail($email);
            if ($user) {
                if (!$user->getValidated()) {
                    $userRandomId = base64_encode('u=' . $user->getId() . '&r=' . $user->getRandomId() . '&c=' . $user->getClient()->getId());
                    return $this->sendResponse([__('Un compte existe déjà avec cette adresse email mais n\'est pas encore validé.') . '<br><a href="' . Tools::makeLink('site', 'user', 'confirmation/' . $userRandomId) . '">' . __('Valider mon compte') . '</a>'], false, ['step' => 1]);
                }

                return $this->sendErrorResponse([__('Cette adresse email est déjà enregistrée')], false, ['step' => 1]);
            }
        }

        $rules = [
            'country' => [new NotBlank(['message' => __('Veuillez indiquer votre pays.')])],
        ];
        $errors = $this->validateForm($rules, $submittedData);
        if ($errors) {
            return $this->sendResponse($errors, false, ['step' => 2]);
        }

        $errors = [];
        $params = $this->getParamsFromPost($submittedData);
        $product = $this->shopProductsService->getProductById($params['id_product']);
        if (!$product) {
            return $this->sendResponse([__('Ce produit n\'existe pas')]);
        }

        if ($product->getType() == ProductsEnum::TYPE_SUBSCRIPTION or $product->getType() == ProductsEnum::TYPE_PAUSE or $product->getType() == ProductsEnum::TYPE_FREEMIUM) {
            $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
            $password2 = filter_var($submittedData['password2'], FILTER_UNSAFE_RAW);
            if (!$password or !$password2) {
                $errors[] = __('Veuillez indiquer un mot de passe.');
            } elseif ($password != $password2) {
                $errors[] = __('Les 2 mots de passe ne sont pas identiques.');
            } else {
                $checkPassword = PasswordService::checkPassword($password);
                if (!$checkPassword['valid']) {
                    $errors[] = $checkPassword['message'];
                }
            }

            if ($errors) {
                return $this->sendResponse($errors, false, ['step' => 1]);
            }

            //check uniqid
            if (SUBDOMAIN_ENABLED) {
                $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
                if (!$uniqId) {
                    $errors[] = __('Veuillez indiquer l\'adresse de votre compte.');
                } else {
                    $uniqId = permalink($uniqId);

                    $client = $this->clientsService->getClientByUniqId($uniqId);
                    if ($client) {
                        $errors[] = __('Cette adresse de compte est déjà utilisée');
                    } elseif (!$this->clientsService->isValidUniqId($uniqId)) {
                        $errors[] = __('Cette adresse de compte est déjà utilisée');
                    }
                }
            }

            if ($errors) {
                return $this->sendResponse($errors, false, ['step' => 2]);
            }
        }

        $fields = json_decode($product->getFields(), true);
        if ($fields) {
            foreach ($fields as $field => $config) {
                if (isset($config['required']) and $config['required'] and (!isset($params[$field]) or !$params[$field])) {
                    $errors[] = __('Veuillez remplir le champ1 %s', $config['name']);
                }
                if ($errors) {
                    return $this->sendResponse($errors, false, ['step' => 2]);
                }
            }
        }

        $checkCustomFields = $this->shopCustomersFieldsService->checkCustomerFields($params['id_product'], $submittedData);
        if (!$checkCustomFields['valid']) {
            $errors = array_merge($errors, $checkCustomFields['errors']);
            return $this->sendResponse($errors, false, ['step' => 3]);
        }

        $idPayment = $params['id_payment'];
        if (!$idPayment) {
            $payments = $this->shopPaymentsService->getPaymentsByProduct($product->getId());
            if (!$payments) {
                $errors[] = __('Ce moyen de paiement n\'est pas disponible');
                return $this->sendResponse($errors);
            }
            $idPayment = $payments[0]->getId();
        }

        $payment = $this->shopPaymentsService->getPaymentById($idPayment);
        if (!$payment) {
            $errors[] = __('Ce moyen de paiement n\'est pas disponible');
        }

        if (isset($submittedData['tva_intracom']) and $submittedData['tva_intracom']) {
            $tvaIntracom = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);
            $validTva = $this->shopVatRulesService->checkVatNumber($tvaIntracom);
            if (!$validTva) {
                $errors[] = __('Numéro de TVA intracommunautaire invalide');
                return $this->sendResponse($errors, false, ['step' => 2]);
            }
        }

        if (isset($submittedData['cgv'])) {
            if (!isset($submittedData['cgv-accept']) or !$submittedData['cgv-accept']) {
                $errors[] = __('Veuillez accepter les conditions générales de vente');
                return $this->sendResponse($errors, false, ['step' => 1]);
            }
        }

        return $this->sendResponse($errors);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function processProductOrder(array $submittedData): array
    {
        $params = $this->getParamsFromPost($submittedData);
        $idProduct = $params['id_product'];

        $product = $this->shopProductsService->getProductById($idProduct);
        if (!$product) {
            return $this->sendResponse([__('Ce produit n\'existe pas')]);
        }

        //1. create customer
        $idCustomer = null;
        if (isset($_SESSION['customer'])) {
            $idCustomer = $_SESSION['customer']['id'];
        }

        //add uniqid if user already exists
        $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);
        $user = $this->usersService->adminGetUserByEmail($email);
        if ($user) {
            $submittedData['uniqid'] = $user->getClient()->getUniqid();
        }

        $processCustomer = $this->shopCustomersService->processCustomer($submittedData, $idCustomer);
        if (!$processCustomer['valid']) {
            return $this->sendResponse([$processCustomer['message']], false, ['step' => 1]);
        }
        $idCustomer = $processCustomer['id_customer'];

        $customer = $this->shopCustomersService->getCustomerById($idCustomer);

        //2. create cart
        $idCart = null;
        if (isset($_SESSION['cart_customer_' . $idCustomer]) and $_SESSION['cart_customer_' . $idCustomer]) {
            $idCart = $_SESSION['cart_customer_' . $idCustomer]['id'];
            $updateCart = $this->shopCartService->updateCart($idCart, $params);
            if (!$updateCart['valid']) {
                return $this->sendResponse([$updateCart['message']]);
            }
        }
        if ($idCart === null) {
            $createCart = $this->shopCartService->insertCart($idCustomer, $params);
            if (!$createCart['valid']) {
                return $this->sendResponse([$createCart['message']]);
            }
            $idCart = $createCart['id_cart'];
        }

        $cart = $this->shopCartService->getCartById($idCart);

        //3. add product to cart
        $products = [];
        $products[] = [
            'id' => $idProduct,
            'qty' => ($params['quantities'][$idProduct] ?? 1),
            'attributes' => $params['product_attributes'] ?? [],
        ];

        $updateProductInCart = $this->shopCartProductsService->addProductInCart($cart, $products);
        if (!$updateProductInCart['valid']) {
            return $this->sendResponse([$updateProductInCart['message']]);
        }

        $idPayment = $params['id_payment'];
        if (!$idPayment) {
            $payments = $this->shopPaymentsService->getPaymentsByProduct($idProduct);
            if (!$payments) {
                return $this->sendResponse([__('Ce moyen de paiement n\'est pas disponible')]);
            }
            $idPayment = $payments[0]->getId();
        }

        $payment = $this->shopPaymentsService->getPaymentById($idPayment);
        if (!$payment) {
            return $this->sendResponse([__('Ce moyen de paiement n\'est pas disponible')]);
        }

        //inscriptions autorépondeur
        $autoresponders = $this->autorespondersService->getAutorespondersByProduct($idProduct);
        if ($autoresponders) {
            foreach ($autoresponders as $autoresponder) {
                $this->autorespondersService->subscribe($autoresponder, $idProduct, $params);
            }
        }

        $amounts = $this->shopCartService->getCartAmounts($cart, $idPayment);

        $paymentSubType = '';
        if (isset($submittedData['payment-subtype-' . $payment->getType()])) {
            $paymentSubType = filter_var($submittedData['payment-subtype-' . $payment->getType()], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['paypal_token_nonce']) and $submittedData['paypal_token_nonce']) {
            $paymentSubType = 'paypal';
        }
        if (isset($submittedData['mollie_paypal_token_nonce']) and $submittedData['mollie_paypal_token_nonce']) {
            $paymentSubType = 'paypal';
        }

        $custom = Transaction::generateCustom($idCart, $idCustomer, $idProduct, $idPayment, [$product], IpAddress::getRealIp());

        $return = [
            'firstPaymentAmount' => $amounts['amount_to_pay'],
            'paymentType' => $payment->getType(),
            'paymentSubType' => $paymentSubType,
            'braintreeBillingAddress' => self::getBraintreeBillingAddress(Entities::asArray($customer)),
            'stripeBillingAddress' => self::getStripeBillingAddress(Entities::asArray($customer)),
            'mollieBillingAddress' => self::getMollieBillingAddress(Entities::asArray($customer)),
            'custom' => $custom,
        ];

        return $this->sendSuccessResponse(null, false, $return);
    }

    /**
     * @param array $customer
     * @return array
     */
    public static function getBraintreeBillingAddress(array $customer): array
    {
        //billing details for 3d Secure
        $billingAddress = [
            'givenName' => $customer['first_name'],
            'surname' => $customer['last_name'],
            'phoneNumber' => $customer['telephone'],
            'streetAddress' => $customer['address'],
            'locality' => $customer['city'],
            'postalCode' => $customer['zip'],
            'countryCodeAlpha2' => $customer['country'],
        ];

        $convertToAscii = function ($value) {
            $value = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $value);
            return preg_replace("/[^a-zA-Z0-9 ]/", '', $value);
        };
        return array_map($convertToAscii, $billingAddress);
    }

    /**
     * @param array $customer
     * @return array
     */
    public static function getStripeBillingAddress(array $customer): array
    {
        //billing details for 3d Secure
        return [
            'email' => $customer['email'],
            'name' => $customer['first_name'] . ' ' . $customer['last_name'],
            'phone' => ($customer['telephone'] ?: null),
            'address' => [
                'city' => ($customer['city'] ?: null),
                'country' => ($customer['country'] ?: null),
                'line1' => ($customer['address'] ?: null),
                'line2' => ($customer['address2'] ?: null),
                'postal_code' => ($customer['zip'] ?: null),
                'state' => ($customer['state'] ?: null),
            ],
        ];
    }

    /**
     * @param array $customer
     * @return array
     */
    public static function getMollieBillingAddress(array $customer): array
    {
        //billing details for 3d Secure
        return [
            'email' => $customer['email'],
            'name' => $customer['first_name'] . ' ' . $customer['last_name'],
            'phone' => ($customer['telephone'] ?: null),
            'address' => [
                'city' => ($customer['city'] ?: null),
                'country' => ($customer['country'] ?: null),
                'streetAndNumber' => ($customer['address'] ?: null),
                'postalCode' => ($customer['zip'] ?: null),
                'region' => ($customer['state'] ?: null),
            ],
        ];
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function getParamsFromPost(array $submittedData) :array
    {
        $params = [
            'id_product' => filter_var($submittedData['id_product'], FILTER_VALIDATE_INT),
            'id_payment' => filter_var($submittedData['id_payment'], FILTER_VALIDATE_INT),
            'first_name' => filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW),
            'last_name' => filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW),
            'email' => filter_var($submittedData['email'], FILTER_UNSAFE_RAW),
            'country' => filter_var($submittedData['country'], FILTER_UNSAFE_RAW),
            'address' => '',
            'address2' => '',
            'city' => '',
            'zip' => '',
            'state' => '',
            'telephone' => '',
            'company' => '',
            'tva_intracom' => '',
            'discount' => '',
            'attributes' => [],
            'quantities' => [],
        ];

        if (isset($submittedData['address'])) {
            $params['address'] = filter_var($submittedData['address'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['address2'])) {
            $params['address2'] = filter_var($submittedData['address2'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['city'])) {
            $params['city'] = filter_var($submittedData['city'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['zip'])) {
            $params['zip'] = filter_var($submittedData['zip'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['state'])) {
            $params['state'] = filter_var($submittedData['state'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['telephone'])) {
            $params['telephone'] = filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['company'])) {
            $params['company'] = filter_var($submittedData['company'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['tva_intracom'])) {
            $params['tva_intracom'] = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['quantities'])) {
            $params['quantities'] = filter_var($submittedData['quantities'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
        }
        if (isset($submittedData['discount'])) {
            $params['discount'] = filter_var($submittedData['discount'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['product_attributes'])) {
            $params['product_attributes'] = filter_var($submittedData['product_attributes'], FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        }

        return $params;
    }
}
