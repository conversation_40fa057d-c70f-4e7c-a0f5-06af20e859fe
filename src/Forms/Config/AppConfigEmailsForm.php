<?php
namespace MatG<PERSON>ver\Forms\Config;

use MatG<PERSON>ver\Components\Mailer\Mailers\SmtpMailer;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\MailersEnum;
use MatG<PERSON>ver\Forms\AbstractForm;
use Mat<PERSON><PERSON>ver\FormsFactory\Config\AppConfigEmailsFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;

/**
 * Class AppConfigEmailsForm
 * @package MatGyver\Forms\Config
 */
class AppConfigEmailsForm extends AbstractForm
{
    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * AppConfigEmailsForm constructor.
     * @param ConfigService $configService
     * @param AppConfigEmailsFormFactory $formFactory
     */
    public function __construct(
        ConfigService $configService,
        AppConfigEmailsFormFactory $formFactory
    ) {
        $this->configService = $configService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $smtp = filter_var($submittedData[ConfigEnum::SITE_EMAILSMTP], FILTER_UNSAFE_RAW);
        if ($smtp) {
            $password = filter_var($submittedData[ConfigEnum::SITE_EMAILPASSWORD], FILTER_UNSAFE_RAW);
            if (!$password) {
                return $this->sendErrorResponse([__('Veuillez renseigner un mot de passe pour le serveur SMTP.')]);
            }
        }

        $currentConfig = $this->configService->getConfig();
        $currentData = [
            ConfigEnum::SITE_EMAILSMTP => $currentConfig[ConfigEnum::SITE_EMAILSMTP] ?? '',
            ConfigEnum::SITE_EMAILUSERNAME => $currentConfig[ConfigEnum::SITE_EMAILUSERNAME] ?? '',
            ConfigEnum::SITE_EMAILPASSWORD => $currentConfig[ConfigEnum::SITE_EMAILPASSWORD] ?? '',
            ConfigEnum::SITE_EMAILSSL => $currentConfig[ConfigEnum::SITE_EMAILSSL] ?? '',
            ConfigEnum::SITE_EMAILPORT => $currentConfig[ConfigEnum::SITE_EMAILPORT] ?? '',
        ];

        $data = $this->getData($submittedData);
        $insert = $this->configService->saveConfig($data);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        //test SMTP
        if ($data[ConfigEnum::MAILER] == MailersEnum::MAILER_SMTP and $data[ConfigEnum::SITE_EMAILSMTP] and $submittedData[ConfigEnum::SITE_EMAILPASSWORD]) {
            $testSmtp = $this->testSmtp($data);
            if (!$testSmtp['valid']) {
                //if error, rollback config
                $this->configService->saveConfig($currentData);
                return $this->sendErrorResponse([$testSmtp['message']]);
            }

            $redirection = Tools::makeLink('app', 'settings', 'emails/test/sent');
            return $this->sendSuccessResponse(__('Les paramètres ont bien été enregistrés.'), false, ['redirection' => $redirection]);
        }

        return $this->sendSuccessResponse(__('Les paramètres ont bien été enregistrés.'));
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function getData(array $submittedData): array
    {
        $data = array();
        $data[ConfigEnum::SITE_EMAIL] = filter_var($submittedData[ConfigEnum::SITE_EMAIL], FILTER_VALIDATE_EMAIL);
        $data[ConfigEnum::SITE_EMAILNAME] = filter_var($submittedData[ConfigEnum::SITE_EMAILNAME], FILTER_UNSAFE_RAW);
        $data[ConfigEnum::MAILER] = filter_var($submittedData[ConfigEnum::MAILER], FILTER_UNSAFE_RAW);

        //smtp
        $data[ConfigEnum::SITE_EMAILSMTP] = filter_var($submittedData[ConfigEnum::SITE_EMAILSMTP], FILTER_UNSAFE_RAW);
        $data[ConfigEnum::SITE_EMAILUSERNAME] = filter_var($submittedData[ConfigEnum::SITE_EMAILUSERNAME], FILTER_UNSAFE_RAW);
        $data[ConfigEnum::SITE_EMAILPORT] = filter_var($submittedData[ConfigEnum::SITE_EMAILPORT], FILTER_VALIDATE_INT);
        if ($submittedData[ConfigEnum::SITE_EMAILPASSWORD]) {
            $data[ConfigEnum::SITE_EMAILPASSWORD] = filter_var($submittedData[ConfigEnum::SITE_EMAILPASSWORD], FILTER_UNSAFE_RAW);
        }

        $data[ConfigEnum::SITE_EMAIL_SIGNATURE] = filter_input(INPUT_POST, ConfigEnum::SITE_EMAIL_SIGNATURE, FILTER_UNSAFE_RAW);

        // OAuth fields
        if (isset($submittedData[ConfigEnum::OUTLOOK_OAUTH_CLIENT_ID])) {
            $data[ConfigEnum::OUTLOOK_OAUTH_CLIENT_ID] = filter_var($submittedData[ConfigEnum::OUTLOOK_OAUTH_CLIENT_ID], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::OUTLOOK_OAUTH_CLIENT_SECRET])) {
            $data[ConfigEnum::OUTLOOK_OAUTH_CLIENT_SECRET] = filter_var($submittedData[ConfigEnum::OUTLOOK_OAUTH_CLIENT_SECRET], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::OUTLOOK_OAUTH_TENANT_ID])) {
            $data[ConfigEnum::OUTLOOK_OAUTH_TENANT_ID] = filter_var($submittedData[ConfigEnum::OUTLOOK_OAUTH_TENANT_ID], FILTER_UNSAFE_RAW);
        }

        $smtpProvider = filter_var($submittedData['smtp_provider'], FILTER_UNSAFE_RAW);
        if ($smtpProvider == 'gmail') {
            $data[ConfigEnum::SITE_EMAILSMTP] = 'smtp.gmail.com';
            $data[ConfigEnum::SITE_EMAILPORT] = 465;
            $data[ConfigEnum::SITE_EMAILSSL] = 'ssl';
        } elseif ($smtpProvider == 'ovh') {
            $data[ConfigEnum::SITE_EMAILSMTP] = 'ssl0.ovh.net';
            $data[ConfigEnum::SITE_EMAILPORT] = 465;
            $data[ConfigEnum::SITE_EMAILSSL] = 'ssl';
        } elseif ($smtpProvider == 'orange') {
            $data[ConfigEnum::SITE_EMAILSMTP] = 'smtp.orange.fr';
            $data[ConfigEnum::SITE_EMAILPORT] = 465;
            $data[ConfigEnum::SITE_EMAILSSL] = 'ssl';
        } elseif ($smtpProvider == 'outlook') {
            $data[ConfigEnum::SITE_EMAILSMTP] = 'smtp-mail.outlook.com';
            $data[ConfigEnum::SITE_EMAILPORT] = 587;
            $data[ConfigEnum::SITE_EMAILSSL] = 'tls';
        } elseif ($smtpProvider == 'free') {
            $data[ConfigEnum::SITE_EMAILSMTP] = 'smtp.free.fr';
            $data[ConfigEnum::SITE_EMAILPORT] = 465;
            $data[ConfigEnum::SITE_EMAILSSL] = 'ssl';
        }

        if ($data[ConfigEnum::MAILER] == MailersEnum::MAILER_SMTP and $data[ConfigEnum::SITE_EMAILSMTP] and $submittedData[ConfigEnum::SITE_EMAILPASSWORD]) {
            if (!$data[ConfigEnum::SITE_EMAILPORT]) {
                if (isset($submittedData[ConfigEnum::SITE_EMAILSSL])) {
                    $data[ConfigEnum::SITE_EMAILSSL] = $submittedData[ConfigEnum::SITE_EMAILSSL];
                    $data[ConfigEnum::SITE_EMAILPORT] = 465;
                } else {
                    $data[ConfigEnum::SITE_EMAILSSL] = false;
                    $data[ConfigEnum::SITE_EMAILPORT] = 25;
                }
            } else {
                if (isset($submittedData[ConfigEnum::SITE_EMAILSSL])) {
                    $data[ConfigEnum::SITE_EMAILSSL] = $submittedData[ConfigEnum::SITE_EMAILSSL];
                } else {
                    $data[ConfigEnum::SITE_EMAILSSL] = false;
                }
            }
        }

        return $data;
    }

    /**
     * @param array $data
     * @return array
     */
    public function testSmtp(array $data): array
    {
        $mailer = new SmtpMailer();
        $startConnection = $mailer->startConnection($_SESSION['client']['id']);
        if (!$startConnection['valid']) {
            return $startConnection;
        }

        $message = '<p>' . __('Ceci est un e-mail test envoyé depuis %s.', APP_NAME) . '</p>';
        $mailer->setIdClient($_SESSION['client']['id']);
        $mailer->setFromName(APP_NAME);
        $mailer->setFromEmail($data[ConfigEnum::SITE_EMAIL]);
        $mailer->setSubject(__('Message de test'));
        $mailer->setMessageHtml($message);
        $mailer->setMessageText(strip_tags($message));
        $mailer->setRecipientFirstName($_SESSION['user']['first_name']);
        $mailer->setRecipientLastName($_SESSION['user']['last_name']);
        $mailer->setRecipientEmail($_SESSION['user']['email']);

        return $mailer->sendTransactional();
    }
}
