<?php
namespace MatGyver\Forms\User;

use Mat<PERSON>yver\Forms\AbstractForm;
use MatGyver\FormsFactory\User\UserPasswordFormFactory;
use MatGyver\Services\PasswordService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersService;

/**
 * Class UserPasswordForm
 * @package MatGyver\Forms\User
 */
class UserPasswordForm extends AbstractForm
{
    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * UserPasswordForm constructor.
     * @param UsersService $usersService
     * @param UserPasswordFormFactory $formFactory
     */
    public function __construct(
        UsersService $usersService,
        UserPasswordFormFactory $formFactory
    ) {
        $this->usersService = $usersService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        $userId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $user = $this->usersService->getUser($userId);
        if (!$user) {
            return $this->sendErrorResponse([__('Cet utilisateur n\'existe pas')]);
        }

        if ($userId != $_SESSION['user']['id'] and $_SESSION['controller'] == 'app' and  !RightsService::hasAccess(UNIVERSE_APP_USERS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas l\'autorisation de modifier cet utilisateur')]);
        }

        if ($userId == $_SESSION['user']['id']) {
            $actualPassword = filter_var($submittedData['actual_password'], FILTER_UNSAFE_RAW);
            $verifyPassword = PasswordService::verifyPassword($actualPassword, $user->getPassword());
            if (!$verifyPassword) {
                return $this->sendErrorResponse([__('Mot de passe incorrect.')]);
            }
        }

        $password = filter_var($submittedData['new_password'], FILTER_UNSAFE_RAW);
        $password2 = filter_var($submittedData['new_password2'], FILTER_UNSAFE_RAW);

        $checkPassword = PasswordService::checkPassword($password);
        if (!$checkPassword['valid']) {
            return $this->sendErrorResponse([$checkPassword['message']]);
        }

        if ($password != $password2) {
            return $this->sendErrorResponse([__('Les 2 mots de passe ne sont pas identiques')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     * @throws \Exception
     */
    public function update(array $submittedData): array
    {
        $userId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $user = $this->usersService->getUser($userId);
        if (!$user) {
            return $this->sendErrorResponse([__('Cet utilisateur n\'existe pas')]);
        }

        $password = filter_var($submittedData['new_password'], FILTER_UNSAFE_RAW);

        $hash = PasswordService::hashPassword($password);
        if (!$hash) {
            return $this->sendErrorResponse([__('Une erreur est survenue.')]);
        }

        $user->setPassword($hash);
        $this->usersService->persistAndFlush($user);

        return $this->sendSuccessResponse(__('Le mot de passe a bien été modifié.'));
    }
}
