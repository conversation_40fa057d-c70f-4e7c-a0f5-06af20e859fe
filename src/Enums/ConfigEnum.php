<?php

namespace MatGyver\Enums;

/**
 * Class ConfigEnum
 * @package MatGyver\Enums
 */
abstract class ConfigEnum
{
    const APP_LANGUAGE = 'app_language';
    const AVATAR = 'avatar';
    const DEFAULT_TIMEZONE = 'default_timezone';
    const DEFAULT_CURRENCY = 'default_currency';
    const FISCAL_YEAR_START_DATE = 'fiscal_year_start_date';
    const FISCAL_YEAR_GOAL = 'fiscal_year_goal';
    const FOOTER = 'footer';
    const LOGO = 'logo';
    const MAINTENANCE_IP = 'maintenance_ip';
    const SITE_ACTIVE = 'site_active';
    const SITE_NAME = 'site_name';
    const LOGIN_TOKEN = 'loginToken';
    const INVOICE = 'facturation';
    const EXPERTISE_REPORT = 'expertise_report';
    const EXPERT_NUMBER = 'expert_number';
    const REPORT_RIGHT_CONTENT = 'report_right_content';
    const REPORT_FONT_FAMILY = 'report_font_family';
    const REPORT_FONT_SIZE = 'report_font_size';

    //email
    const MAILER = 'mailer';
    const SITE_EMAIL = 'site_email';
    const SITE_EMAILNAME = 'site_emailname';
    const SITE_EMAILSMTP = 'site_emailsmtp';
    const SITE_EMAILUSERNAME = 'site_emailusername';
    const SITE_EMAILPASSWORD = 'site_emailpassword';
    const SITE_EMAILPORT = 'site_emailport';
    const SITE_EMAILSSL = 'site_emailssl';
    const SITE_EMAIL_SIGNATURE = 'site_email_signature';

    //oauth email
    const OUTLOOK_OAUTH_ACCESS_TOKEN = 'outlook_oauth_access_token';
    const OUTLOOK_OAUTH_REFRESH_TOKEN = 'outlook_oauth_refresh_token';
    const OUTLOOK_OAUTH_TOKEN_EXPIRES = 'outlook_oauth_token_expires';
    const OUTLOOK_OAUTH_CONNECTED = 'outlook_oauth_connected';

    const FACEBOOK_PIXEL_ID = 'facebook_pixel_id';
    const GOOGLE_ANALYTICS_ID = 'google_analytics_id';
    const GOOGLE_TAG_MANAGER_ID = 'google_tag_manager_id';

    //affiliation
    const AFF_AUTO_APPROVE = 'aff_auto_approve';
    const AFF_DISPLAY_EMAILS = 'aff_display_emails';
    const AFF_DISPLAY_CLIENTS = 'aff_display_clients';
    const AFF_DISPLAY_SUBPARTNERS = 'aff_display_subpartners';
    const AFF_COMMISSIONS_AVAILABLE_DELAY = 'commissions_available_delay';
    const AFF_COMMISSIONS_EXPIRATION_DELAY = 'commissions_expiration_delay';
    const AFF_INVOICE_MINIMUM_AMOUNT_HT = 'invoice_minimum_amount_ht';
    const AFF_INVOICE_MAXIMUM_AMOUNT_HT = 'invoice_maximum_amount_ht';
    const AFF_BANK_HOLDER = 'aff_bank_holder';
    const AFF_BANK_IBAN = 'aff_bank_iban';
    const AFF_BANK_BIC = 'aff_bank_bic';
    const AFF_NO_IBAN = 'aff_noiban';

    //company
    const COMPANY = 'company';
    const ADDRESS = 'address';
    const ADDRESS2 = 'address2';
    const CITY = 'city';
    const COMMENT = 'comment';
    const ZIP = 'zip';
    const COUNTRY = 'country';
    const STATE = 'state';
    const TELEPHONE = 'telephone';
    const TVA_INTRACOM = 'tva_intracom';
    const RGPD_TEXT = 'rgpd_text';
    const RGPD_AFF_TEXT = 'rgpd_aff_text';

    const SIGNATURE = 'signature';

    //2FA
    const TWOFACTOR = 'twofactor';
    const TWOFACTOR_NONE = 'twofactor_none';
    const TWOFACTOR_CODE = 'twofactor_code';
    const TWOFACTOR_MAIL = 'twofactor_mail';
    const TWOFACTOR_SECRET = 'twofactor_secret';
}
