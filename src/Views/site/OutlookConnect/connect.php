<?php
/**
 * @var bool $isConfigured
 * @var bool $isConnected
 * @var string $redirectUrl
 * @var array|null $userInfo
 */
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-envelope mr-2"></i>
                        <?php echo __('Connexion avec Outlook'); ?>
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if (!$isConfigured): ?>
                        <!-- Configuration non disponible -->
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong><?php echo __('Configuration requise'); ?></strong>
                            <p class="mb-0 mt-2">
                                <?php echo __('La connexion Outlook n\'est pas encore configurée sur ce site. Veuillez contacter l\'administrateur pour activer cette fonctionnalité.'); ?>
                            </p>
                        </div>
                        
                        <div class="text-center">
                            <a href="<?php echo Tools::makeLink('site', 'index'); ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>
                                <?php echo __('Retour à l\'accueil'); ?>
                            </a>
                        </div>
                        
                    <?php elseif ($isConnected): ?>
                        <!-- Déjà connecté -->
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle mr-2"></i>
                            <strong><?php echo __('Connexion active'); ?></strong>
                            <p class="mb-0 mt-2">
                                <?php echo __('Votre compte Outlook est connecté et prêt à envoyer des emails.'); ?>
                            </p>
                            
                            <?php if (isset($userInfo) && !empty($userInfo)): ?>
                                <div class="mt-3 p-3 bg-light rounded">
                                    <h6><?php echo __('Informations du compte :'); ?></h6>
                                    <ul class="list-unstyled mb-0">
                                        <?php if (!empty($userInfo['displayName'])): ?>
                                            <li><strong><?php echo __('Nom :'); ?></strong> <?php echo htmlspecialchars($userInfo['displayName']); ?></li>
                                        <?php endif; ?>
                                        <?php if (!empty($userInfo['mail'])): ?>
                                            <li><strong><?php echo __('Email :'); ?></strong> <?php echo htmlspecialchars($userInfo['mail']); ?></li>
                                        <?php elseif (!empty($userInfo['userPrincipalName'])): ?>
                                            <li><strong><?php echo __('Email :'); ?></strong> <?php echo htmlspecialchars($userInfo['userPrincipalName']); ?></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo Tools::makeLink('site', 'outlook', 'test'); ?>" class="btn btn-outline-primary btn-block">
                                    <i class="fas fa-vial mr-2"></i>
                                    <?php echo __('Tester la connexion'); ?>
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="<?php echo Tools::makeLink('site', 'outlook', 'disconnect'); ?>" class="btn btn-outline-danger btn-block">
                                    <i class="fas fa-unlink mr-2"></i>
                                    <?php echo __('Se déconnecter'); ?>
                                </a>
                            </div>
                        </div>
                        
                    <?php else: ?>
                        <!-- Prêt à se connecter -->
                        <div class="text-center mb-4">
                            <i class="fab fa-microsoft fa-4x text-primary mb-3"></i>
                            <h4><?php echo __('Connectez votre compte Outlook'); ?></h4>
                            <p class="text-muted">
                                <?php echo __('Autorisez cette application à envoyer des emails via votre compte Microsoft Outlook.'); ?>
                            </p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-2"></i><?php echo __('Pourquoi se connecter ?'); ?></h6>
                            <ul class="mb-0">
                                <li><?php echo __('Envoi sécurisé d\'emails via votre compte Outlook'); ?></li>
                                <li><?php echo __('Pas besoin de partager votre mot de passe'); ?></li>
                                <li><?php echo __('Compatible avec l\'authentification à deux facteurs'); ?></li>
                                <li><?php echo __('Vous gardez le contrôle total de vos autorisations'); ?></li>
                            </ul>
                        </div>
                        
                        <div class="text-center mb-4">
                            <a href="<?php echo Tools::makeLink('site', 'outlook', 'authorize'); ?>" class="btn btn-primary btn-lg">
                                <i class="fab fa-microsoft mr-2"></i>
                                <?php echo __('Se connecter avec Outlook'); ?>
                            </a>
                        </div>
                        
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title"><?php echo __('Informations techniques'); ?></h6>
                                <small class="text-muted">
                                    <strong><?php echo __('URL de redirection :'); ?></strong> <?php echo htmlspecialchars($redirectUrl); ?><br>
                                    <strong><?php echo __('Permissions demandées :'); ?></strong> Mail.Send, User.Read<br>
                                    <strong><?php echo __('Fournisseur :'); ?></strong> Microsoft Graph API
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                </div>
            </div>
            
            <!-- Section d'aide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle mr-2"></i>
                        <?php echo __('Questions fréquentes'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        
                        <div class="card">
                            <div class="card-header" id="faq1">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                        <?php echo __('Mes données sont-elles sécurisées ?'); ?>
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    <?php echo __('Oui, nous utilisons le protocole OAuth 2.0 de Microsoft qui est un standard de sécurité. Nous ne stockons jamais votre mot de passe et vous pouvez révoquer l\'accès à tout moment depuis votre compte Microsoft.'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header" id="faq2">
                                <h6 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapse2">
                                        <?php echo __('Puis-je utiliser un compte personnel Microsoft ?'); ?>
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    <?php echo __('Oui, vous pouvez utiliser aussi bien un compte personnel Microsoft (Outlook.com, Hotmail.com) qu\'un compte professionnel (Office 365, Azure AD).'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header" id="faq3">
                                <h6 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapse3">
                                        <?php echo __('Comment révoquer l\'accès ?'); ?>
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse3" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    <?php echo __('Vous pouvez révoquer l\'accès de deux façons : en cliquant sur "Se déconnecter" sur cette page, ou en allant dans les paramètres de sécurité de votre compte Microsoft et en supprimant l\'autorisation pour cette application.'); ?>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-refresh status every 30 seconds if not connected
    <?php if ($isConfigured && !$isConnected): ?>
    setInterval(function() {
        $.get('<?php echo Tools::makeLink('site', 'outlook', 'status'); ?>')
            .done(function(data) {
                if (data.connected) {
                    location.reload();
                }
            });
    }, 30000);
    <?php endif; ?>
    
    // Handle connection button click
    $('a[href*="authorize"]').on('click', function() {
        $(this).html('<i class="fas fa-spinner fa-spin mr-2"></i><?php echo __('Redirection vers Microsoft...'); ?>');
    });
});
</script>
