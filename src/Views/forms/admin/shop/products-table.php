<?php
if (!isset($products) or !$products) {
    $products[] = array(
        'quantity' => 1,
        'name' => '',
        'price_tax_excl' => 0,
        'vat' => 20,
        'price_tax_incl' => 0
    );
}
?>

<table class="table table-separate table-head-custom table-products">
    <thead>
        <tr>
            <th class="w-80px"><?php echo __('Qté'); ?></th>
            <th><?php echo __('Service'); ?></th>
            <th class="w-120px"><?php echo __('Unitaire HT'); ?></th>
            <th class="w-60px"><?php echo __('Taux de TVA (%)'); ?></th>
            <th class="w-120px"><?php echo __('Prix TTC'); ?></th>
            <th class="w-30px"></th>
        </tr>
    </thead>
    <tbody>
        <?php $amount_tax_excl = $amount_tax_incl = $amount_tax = 0; ?>
        <?php foreach ($products as $i => $product) : ?>
            <?php $amount_tax_excl += $product['price_tax_excl']; ?>
            <?php $amount_tax_incl += $product['price_tax_incl']; ?>
            <?php $amount_tax += $product['price_tax_incl'] - $product['price_tax_excl']; ?>
            <tr data-id="<?php echo $i; ?>">
                <td class="w-80px">
                    <input name="products[<?php echo $i; ?>][quantity]" type="number" min="0" step="any" class="form-control input-small quantity w-80px mb-1" value="<?php echo $product['quantity']; ?>">
                </td>
                <td><textarea name="products[<?php echo $i; ?>][name]" class="form-control autosize" id="product_name_<?php echo $i; ?>" rows="1"><?php echo $product['name']; ?></textarea></td>
                <td class="w-120px"><input name="products[<?php echo $i; ?>][price_tax_excl]" type="number" min="0" step="any" class="form-control input-small price_tax_excl w-80px" value="<?php echo number_format(($product['quantity'] ? ($product['price_tax_excl'] / $product['quantity']) : $product['price_tax_excl']), 2, '.', ''); ?>"></td>
                <td class="w-60px"><input name="products[<?php echo $i; ?>][vat]" type="number" min="0" step="any" class="form-control input-small tva w-60px" value="<?php echo $product['vat']; ?>"></td>
                <td class="w-120px"><input name="products[<?php echo $i; ?>][price_tax_incl]" type="number" min="0" step="any" class="form-control input-small price_tax_incl w-80px" value="<?php echo number_format(($product['quantity'] ? ($product['price_tax_incl'] / $product['quantity']) : $product['price_tax_incl']), 2, '.', ''); ?>"></td>
                <td class="w-30px"><a onclick="RemoveProduct(<?php echo $i; ?>);" class="btn btn-clean btn-icon btn-sm btn-hover-light-danger cursor-pointer mt-1"><i class="fas fa-minus-circle icon-nm"></i></a></td>
            </tr>
        <?php endforeach; ?>
    </tbody>
    <tfoot>
        <tr>
            <th colspan="4" style="text-align: right"><?php echo __('Montant HT'); ?></th>
            <th id="amount_tax_excl"><?php echo $amount_tax_excl; ?></th>
            <th></th>
        </tr>
        <tr>
            <th colspan="4" style="text-align: right"><?php echo __('Montant TVA'); ?></th>
            <th id="amount_tax"><?php echo $amount_tax; ?></th>
            <th></th>
        </tr>
        <tr>
            <th colspan="4" style="text-align: right"><?php echo __('Montant TTC'); ?></th>
            <th id="amount_tax_incl"><?php echo $amount_tax_incl; ?></th>
            <th></th>
        </tr>
    </tfoot>
</table>

<div class="d-flex flex-row">
    <a class="btn btn-sm font-weight-bold btn-light-primary" onclick="AddProduct();"><i class="fa fa-plus"></i> <?php echo __('Ajouter un service'); ?></a>
    <?php if (isset($surcharges) and $surcharges) : ?>
        <?php /** @var MatGyver\Entity\Surcharge\Surcharge[] $surcharges */ ?>
        <div class="btn-group ml-4" role="group">
            <button type="button" class="btn btn-sm font-weight-bold btn-light-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <?php echo __('Ajouter un frais'); ?>
            </button>
            <div class="dropdown-menu">
                <?php foreach ($surcharges as $surcharge) : ?>
                <a class="dropdown-item cursor-pointer" onclick="AddSurcharge('<?php echo addslashes($surcharge->getName()); ?>', '<?php echo number_format($surcharge->getAmount(), 2, '.', ''); ?>', '<?php echo str_replace(array("\r\n", "\r", "\n"), array('<br/>', '<br/>', '<br/>'), $surcharge->getComment()); ?>'); return false;">
                    <?php echo $surcharge->getName() . ' (' . $surcharge->displayAmount() . ')'; ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    <?php if (isset($dossier) and $dossier->isJudiciaire()) : ?>
        <div class="btn-group ml-4" role="group">
            <button type="button" class="btn btn-sm font-weight-bold btn-light-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <?php echo __('Ajouter un frais pré-défini'); ?>
            </button>
            <div class="dropdown-menu">
                <?php
                $defaultSurchages = [
                    'Rédaction Compte Rendu',
                    'Rédaction du pré-rapport d\'expertise judiciaire',
                    'Rédaction du rapport d\'expertise judiciaire',
                    'Rédaction Compte Rendu Pré-rapport et Rapport',
                    'Réponses aux Dires',
                    'Vacation secrétariat',
                    'Honoraires de déplacement',
                    'Indemnité kilométrique',
                    'Courriers recommandés convocations',
                    'Forfaits photos',
                    'Photocopies',
                    'Gestion administrative, papeterie, envoi des rapports et divers',
                ];
                ?>
                <?php foreach ($defaultSurchages as $defaultSurchage) : ?>
                    <a class="dropdown-item cursor-pointer" onclick="AddSurcharge('<?php echo addslashes($defaultSurchage); ?>', '0', ''); return false;">
                        <?php echo $defaultSurchage; ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
