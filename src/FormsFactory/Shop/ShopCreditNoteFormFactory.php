<?php

namespace MatGyver\FormsFactory\Shop;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Shop\CreditNote\ShopCreditNote;
use MatGyver\Entity\Surcharge\Surcharge;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopCreditNotesForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use MatGyver\Services\Shop\Invoices\ShopInvoicesService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Surcharge\SurchargeService;
use MatGyver\Services\TwigService;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopCreditNoteFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(ShopCreditNotesForm::class);

        $this->addCreditNoteInfos();
        $this->addProducts();
        $this->addAddressGroup();

        $field = new BuilderFormField('credit_note_email');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Envoyer l\'avoir par email au client'));
        //$field->setHelp(__('L\'email à envoyer est modifiable dans le menu "Emails".'));
        $this->builderFields->addField($field);

        $this->setTitle(__('Création d\'un avoir'));
        $this->setCancelUrl(Tools::makeLink($_SESSION['controller'], 'shop', 'credit_notes'));

        $products = [];
        if (isset($_GET['invoiceId'])) {
            $invoiceId = filter_input(INPUT_GET, 'invoiceId', FILTER_VALIDATE_INT);
            $container = ContainerBuilderService::getInstance();
            $invoice = $container->get(ShopInvoicesService::class)->getInvoiceById($invoiceId);
            if ($invoice) {
                $invoiceProducts = $invoice->getInvoiceProducts();
                foreach ($invoiceProducts as $invoiceProduct) {
                    $products[] = [
                        'quantity' => $invoiceProduct->getQuantity(),
                        'name' => $invoiceProduct->getName(),
                        'price_tax_excl' => $invoiceProduct->getPriceTaxExcl(),
                        'vat' => $invoiceProduct->getVat(),
                        'price_tax_incl' => $invoiceProduct->getPriceTaxIncl()
                    ];
                }

                $this->builderFields->updateField('reference', 'value', $invoice->getTransactionReference());

                $group = $this->builderFields->getField('infos');
                if ($group instanceof BuilderFormGroup) {
                    $customer = json_decode($invoice->getCustomer(), true);
                    if (isset($customer['first_name']) and $customer['first_name']) {
                        $group->updateField('first_name', 'value', $customer['first_name']);
                    }
                    if (isset($customer['last_name']) and $customer['last_name']) {
                        $group->updateField('last_name', 'value', $customer['last_name']);
                    }
                    if (isset($customer['email']) and $customer['email']) {
                        $group->updateField('email', 'value', $customer['email']);
                    }
                    if (isset($customer['address']) and $customer['address']) {
                        $group->updateField('address', 'value', $customer['address']);
                    }
                    if (isset($customer['address2']) and $customer['address2']) {
                        $group->updateField('address2', 'value', $customer['address2']);
                    }
                    if (isset($customer['zip']) and $customer['zip']) {
                        $group->updateField('zip', 'value', $customer['zip']);
                    }
                    if (isset($customer['city']) and $customer['city']) {
                        $group->updateField('city', 'value', $customer['city']);
                    }
                    if (isset($customer['state']) and $customer['state']) {
                        $group->updateField('state', 'value', $customer['state']);
                    }
                    if (isset($customer['telephone']) and $customer['telephone']) {
                        $group->updateField('telephone', 'value', $customer['telephone']);
                    }
                    if (isset($customer['company']) and $customer['company']) {
                        $group->updateField('company', 'value', $customer['company']);
                    }
                    if (isset($customer['tva_intracom']) and $customer['tva_intracom']) {
                        $group->updateField('tva_intracom', 'value', $customer['tva_intracom']);
                    }
                    if (isset($customer['country']) and $customer['country']) {
                        $select = $group->getField('country')->getSelect();
                        $select->setParam($customer['country']);
                    }
                }
            }
        }

        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());

            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'credit_notes/' . $dossier->getId()));
            if (!$products) {
                $productName = __('Règlement du dossier %s (%s)', ($dossier->getContact() ? $dossier->getContact()->getCompanyOrName() : ''), ($dossier->getVehicle() ? $dossier->getVehicle()->getRegistration() : ''));
                if ($dossier->getExpertise() and $dossier->getExpertise()->getDate()) {
                    $productName = __('Expertise du %s', dateTimeFr($dossier->getExpertise()->getDate()->format('Y-m-d H:i:s')));
                    $productName .= "\n" . $dossier->getExpertise()->displayPlace(false, ', ');
                }
                if ($dossier->getPriceType() == Dossier::PRICE_PER_HOUR) {
                    $productName .= "\n(Prix par heure)";
                }
                $products[] = [
                    'quantity' => 1,
                    'name' => $productName,
                    'price_tax_excl' => round($dossier->getPrice() / 1.2, 2),
                    'vat' => 20,
                    'price_tax_incl' => $dossier->getPrice()
                ];
                $surcharges = $dossier->getSurcharges();
                if (count($surcharges)) {
                    foreach ($surcharges as $surcharge) {
                        $surchargeName = $surcharge->getName();
                        if ($surcharge->getAmountType() == Surcharge::PRICE_PER_HOUR) {
                            $surchargeName .= ' (prix par heure)';
                        } elseif ($surcharge->getAmountType() == Surcharge::PRICE_PER_KM) {
                            $surchargeName .= ' (prix par km)';
                        }
                        $products[] = [
                            'quantity' => 1,
                            'name' => $surchargeName,
                            'price_tax_excl' => round($surcharge->getAmount() / 1.2, 2),
                            'vat' => 20,
                            'price_tax_incl' => $surcharge->getAmount()
                        ];
                    }
                }
            }

            $mandate = $dossier->getMandate();
            $group = $this->builderFields->getField('infos');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('first_name', 'value', $mandate->getFirstName());
                $group->updateField('last_name', 'value', $mandate->getLastName());
                $group->updateField('email', 'value', $mandate->getEmail());
                $group->updateField('address', 'value', $mandate->getAddress());
                $group->updateField('address2', 'value', $mandate->getAddress2());
                $group->updateField('zip', 'value', $mandate->getZip());
                $group->updateField('city', 'value', $mandate->getCity());
                $group->updateField('telephone', 'value', $mandate->getTelephone());
                $group->updateField('company', 'value', $mandate->getCompany());
            }
        }

        if ($products) {
            $content = $this->getProductsContent($products);
            $group = $this->builderFields->getField('products_group');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('products', 'content', $content);
            }
        }
    }

    /**
     * @param ShopCreditNote|null $creditNote
     */
    public function preRenderFields(?ShopCreditNote $creditNote)
    {
        Assets::addJs('admin/shop/transaction_add.js');

        if ($creditNote) {
            $this->setTitle(__('Modification d\'un avoir'));

            $this->builderFields->updateField('number', 'value', $creditNote->getNumber());
            $this->builderFields->updateField('reference', 'value', $creditNote->getTransactionReference());
            $select = $this->builderFields->getField('currency')->getSelect();
            $select->setParam($creditNote->getCurrency());

            $products = [];
            foreach ($creditNote->getCreditNoteProducts() as $creditNoteProduct) {
                $products[] = [
                    'quantity' => $creditNoteProduct->getQuantity(),
                    'name' => $creditNoteProduct->getName(),
                    'price_tax_excl' => $creditNoteProduct->getPriceTaxExcl(),
                    'vat' => $creditNoteProduct->getVat(),
                    'price_tax_incl' => $creditNoteProduct->getPriceTaxIncl()
                ];
            }
            $content = $this->getProductsContent($products);
            $group = $this->builderFields->getField('products_group');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('products', 'content', $content);
            }

            $customerData = json_decode($creditNote->getCustomer(), true);
            $group = $this->builderFields->getField('infos');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('first_name', 'value', $customerData['first_name']);
                $group->updateField('last_name', 'value', $customerData['last_name']);
                $group->updateField('email', 'value', $customerData['email']);
                $group->updateField('address', 'value', $customerData['address']);
                if (isset($customerData['address2'])) {
                    $group->updateField('address2', 'value', $customerData['address2']);
                }
                $group->updateField('zip', 'value', $customerData['zip']);
                $group->updateField('city', 'value', $customerData['city']);
                if (isset($customerData['state'])) {
                    $group->updateField('state', 'value', $customerData['state']);
                }
                $group->updateField('telephone', 'value', $customerData['telephone']);
                $group->updateField('company', 'value', $customerData['company']);
                if (isset($customerData['tva_intracom'])) {
                    $group->updateField('tva_intracom', 'value', $customerData['tva_intracom']);
                }
            }

            return;
        }

        $container = ContainerBuilderService::getInstance();
        $settings = [];
        $settingsCreditNote = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
        if ($settingsCreditNote) {
            $settings = json_decode($settingsCreditNote->getValue(), true);
        }

        $prefix = $this->builderFields->getField('prefix')->getValue();
        if (!$prefix and $settings and isset($settings['credit_note_prefix'])) {
            $prefix = $settings['credit_note_prefix'];
            $this->builderFields->updateField('prefix', 'value', $settings['credit_note_prefix']);
        }

        $number = $this->builderFields->getField('number')->getValue();
        if (!$number) {
            if ($settings and isset($settings['number'])) {
                $number = $settings['number'];
            }

            if (!$number) {
                if ($prefix === null) {
                    $prefix = '';
                }
                $lastCreditNote = $container->get(ShopCreditNotesService::class)->getLastNumCreditNote($prefix);
                if ($lastCreditNote) {
                    $number = $lastCreditNote->getNumber();
                    ++$number;
                }
            }

            if (!$number) {
                $number = 1;
            }
            $this->builderFields->updateField('number', 'value', $number);
        }

        $products = array();
        if (isset($_POST['products'])) {
            $products = filter_input(INPUT_POST, 'products', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        }

        $reference = $this->builderFields->getField('reference')->getValue();
        if (!$reference and isset($_GET['reference'])) {
            $reference = filter_input(INPUT_GET, 'reference', FILTER_UNSAFE_RAW);
            if ($reference) {
                $this->builderFields->updateField('reference', 'value', $reference);
                if (!$products) {
                    $transaction = $container->get(ShopTransactionService::class)->getTransactionByReference($reference);
                    if ($transaction) {
                        $transactionProducts = $transaction->getTransactionProducts();
                        if ($transactionProducts) {
                            $products = array();
                            foreach ($transactionProducts as $transactionProduct) {
                                $products[] = Entities::asArray($transactionProduct);
                            }
                        }

                        $userGroup = $this->builderFields->getField('infos');
                        if ($userGroup instanceof BuilderFormGroup) {
                            $userGroup->updateField('first_name', 'value', $transaction->getFirstName());
                            $userGroup->updateField('last_name', 'value', $transaction->getLastName());
                            $userGroup->updateField('email', 'value', $transaction->getEmail());

                            if ($transaction->getShopCustomer()) {
                                $customer = $transaction->getShopCustomer();
                                $userGroup->updateField('address', 'value', $customer->getAddress());
                                $userGroup->updateField('address2', 'value', $customer->getAddress2());
                                $userGroup->updateField('city', 'value', $customer->getCity());
                                $userGroup->updateField('zip', 'value', $customer->getZip());
                                $userGroup->updateField('state', 'value', $customer->getState());
                                $userGroup->updateField('telephone', 'value', $customer->getTelephone());

                                if ($customer->getDatas()) {
                                    $customerDatas = json_decode($customer->getDatas(), true);
                                    if (isset($customerDatas['company']) and $customerDatas['company']) {
                                        $userGroup->updateField('company', 'value', $customerDatas['company']);
                                    }
                                    if (isset($customerDatas['tva_intracom']) and $customerDatas['tva_intracom']) {
                                        $userGroup->updateField('tva_intracom', 'value', $customerDatas['tva_intracom']);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($products) {
            $content = $this->getProductsContent($products);
            $group = $this->builderFields->getField('products_group');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('products', 'content', $content);
            }
        }
    }

    private function addCreditNoteInfos()
    {
        $field = new BuilderFormField('content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="row"><div class="col-6">');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('prefix');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Préfixe'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="col-6">');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('number');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Numéro de l\'avoir'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('reference');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de transaction'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('payment_method');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Mode de règlement'));
        $field->setHelp(__('Paypal, Stripe, Chèque, Virement bancaire, etc.'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('currency');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Devise'));
        $select = new BuilderFormSelect();
        $select->setClass(Currency::class);
        $select->setFunction('generateSelectCurrency');
        $select->setParam(DEFAULT_CURRENCY);
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner une devise')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('observations');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Observations'));
        $field->setHelp(__('Affichées en bas de l\'avoir'));
        $this->builderFields->addField($field);
    }

    private function addProducts()
    {
        $group = new BuilderFormGroup('products_group');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Produits'));
        $group->setOpened(true);

        $field = new BuilderFormField('products');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez ajouter un produit')]));
        $field->setContent($this->getProductsContent());
        $group->addField($field);
        $this->builderFields->addField($group);
    }

    /**
     * @param array $products
     * @return string
     */
    private function getProductsContent(array $products = []): string
    {
        $parser = TwigService::getInstance()->set('products', $products);
        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            $container = ContainerBuilderService::getInstance();
            $surcharges = $container->get(SurchargeService::class)->getRepository()->findBy([], ['name' => 'ASC']);
            $parser->set('surcharges', $surcharges);
        }
        return $parser->render('forms/admin/shop/products-table.php');
    }

    private function addAddressGroup()
    {
        $group = new BuilderFormGroup('infos');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Informations du client'));
        $group->setOpened(true);

        $field = new BuilderFormField('first_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom'));
        $group->addField($field);

        $field = new BuilderFormField('last_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $group->addField($field);

        $field = new BuilderFormField('email');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer une adresse email')]));
        $field->addValidation(new Email(['message' => __('Veuillez entrer une adresse email valide')]));
        $field->setRequired(true);
        $group->addField($field);

        $addressFields = $this->getAddressFields();
        foreach ($addressFields as $field) {
            $group->addField($field);
        }

        $field = new BuilderFormField('company');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Société'));
        $group->addField($field);

        $field = new BuilderFormField('tva_intracom');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de TVA intracommunautaire'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
