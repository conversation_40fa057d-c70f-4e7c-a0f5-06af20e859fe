<?php

namespace Mat<PERSON><PERSON>ver\FormsFactory\Config;

use MatG<PERSON>ver\Components\Widgets\OutlookConnectWidget;
use MatGyver\Entity\Help\Article\HelpArticle;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Enums\MailersEnum;
use MatGyver\Forms\Config\AppConfigEmailsForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Help\HelpArticlesService;
use Symfony\Component\Validator\Constraints\NotBlank;

class AppConfigEmailsFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(AppConfigEmailsForm::class);

        $this->addInfos();
        $this->addEmailFields();

        //add help for gmail
        $container = ContainerBuilderService::getInstance();
        $helpArticle = $container->get(HelpArticlesService::class)->getRepository()->findOneBy(['permalink' => 'comment-configurer-mon-adresse-email-d-envoi']);
        if ($helpArticle) {
            $this->addHelpArticle($helpArticle);
        }

        $this->addSmtpGroup();

        $this->setTitle(__('Envoi d\'emails'));
    }

    public function preRenderFields()
    {
        Assets::addJs('admin/emails_settings.js');

        $container = ContainerBuilderService::getInstance();
        $settings = $container->get(ConfigService::class)->getConfig();
        $this->builderFields->updateField(ConfigEnum::SITE_EMAILNAME, 'value', ($settings[ConfigEnum::SITE_EMAILNAME] ?? ''));
        $this->builderFields->updateField(ConfigEnum::SITE_EMAIL, 'value', ($settings[ConfigEnum::SITE_EMAIL] ?? ''));
        $this->builderFields->updateField(ConfigEnum::MAILER, 'value', ($settings[ConfigEnum::MAILER] ?? ''));
        $this->builderFields->updateField(ConfigEnum::SITE_EMAIL_SIGNATURE, 'value', ($settings[ConfigEnum::SITE_EMAIL_SIGNATURE] ?? ''));

        if (!isset($settings[ConfigEnum::SITE_EMAILSMTP]) or !$settings[ConfigEnum::SITE_EMAILSMTP]) {
            $this->builderFields->removeField('content1');
        }

        $group = $this->builderFields->getField('smtp');
        $smtpServer = '';
        if ($group instanceof BuilderFormGroup) {
            $smtpServer = $settings[ConfigEnum::SITE_EMAILSMTP] ?? '';
            $group->updateField(ConfigEnum::SITE_EMAILSMTP, 'value', ($settings[ConfigEnum::SITE_EMAILSMTP] ?? ''));
            $group->updateField(ConfigEnum::SITE_EMAILUSERNAME, 'value', ($settings[ConfigEnum::SITE_EMAILUSERNAME] ?? ''));
            $group->updateField(ConfigEnum::SITE_EMAILPORT, 'value', ($settings[ConfigEnum::SITE_EMAILPORT] ?? ''));
            $group->updateField(ConfigEnum::SITE_EMAILSSL, 'value', ($settings[ConfigEnum::SITE_EMAILSSL] ?? ''));
            $group->updateField(ConfigEnum::SITE_EMAILPASSWORD, 'value', ($settings[ConfigEnum::SITE_EMAILPASSWORD] ?? ''));

            if (isset($settings[ConfigEnum::SITE_EMAILSMTP]) and $settings[ConfigEnum::SITE_EMAILSMTP]) {
                if (str_contains($settings[ConfigEnum::SITE_EMAILSMTP], 'gmail')) {
                    $group->updateField('smtp_provider', 'value', 'gmail');
                } elseif (str_contains($settings[ConfigEnum::SITE_EMAILSMTP], 'orange')) {
                    $group->updateField('smtp_provider', 'value', 'orange');
                } elseif (str_contains($settings[ConfigEnum::SITE_EMAILSMTP], 'ovh')) {
                    $group->updateField('smtp_provider', 'value', 'ovh');
                } elseif (str_contains($settings[ConfigEnum::SITE_EMAILSMTP], 'outlook')) {
                    $group->updateField('smtp_provider', 'value', 'outlook');
                } elseif (str_contains($settings[ConfigEnum::SITE_EMAILSMTP], 'free')) {
                    $group->updateField('smtp_provider', 'value', 'free');
                }
            }

            $widget = new OutlookConnectWidget();
            $isConnected = $widget->isConnected();
            if ($isConnected) {
                $group->updateField('smtp_provider', 'value', 'outlook');
            }
        }

        /*if ($this->builderFields->getField('site_email')->getValue()) {
            $freeDomains = Tools::getFreeDomains();
            $explode = explode('@', $this->builderFields->getField('site_email')->getValue());
            if (in_array($explode[1], $freeDomains)) {
                $newField = new BuilderFormField('alert');
                $newField->setType(FieldsEnum::TYPE_CONTENT);
                $newField->setContent('
                    <div class="alert alert-custom alert-light-danger fade show mb-5" role="alert">
                        <div class="alert-icon"><i class="flaticon-warning"></i></div>
                        <div class="alert-text">' . __('Attention : il n\'est pas conseillé d\'utiliser une adresse email gratuite pour vos envois') . ' (' . $explode[1] . '), ' . __('merci d\'utiliser une autre adresse email.') . '</div>
                        <div class="alert-close">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true"><i class="ki ki-close"></i></span>
                            </button>
                        </div>
                    </div>');
                $this->builderFields->addField($newField, true);
            }
        }*/

        if (!$smtpServer) {
            $this->addInfos();
        }
    }

    private function addInfos()
    {
        $container = ContainerBuilderService::getInstance();
        $settings = $container->get(ConfigService::class)->getConfig(CLIENT_MASTER);
        $smtpEmail = ($settings[ConfigEnum::SITE_EMAIL] ?? '');

        $content = '
        <div class="alert alert-custom alert-white alert-shadow fade show gutter-b align-items-start" role="alert">
            <div class="alert-icon">
                <span class="svg-icon svg-icon-primary svg-icon-2x">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"/>
                            <circle fill="#000000" opacity="0.3" cx="12" cy="12" r="10"/>
                            <rect fill="#000000" x="11" y="10" width="2" height="7" rx="1"/>
                            <rect fill="#000000" x="11" y="7" width="2" height="2" rx="1"/>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="alert-text">
                <h3>' . __('Envois d\'emails') . '</h3>
                <p class="mb-0">' . __('Par défaut, les emails seront envoyés depuis l\'adresse %s.', $smtpEmail) . '</p>
                <p class="mb-0">' . __('Si vous souhaitez utiliser votre propre adresse email d\'envoi, vous pouvez indiquer les paramètres SMTP ci-dessous.') . '</p>
            </div>
        </div>';

        $field = new BuilderFormField('infos');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $this->builderFields->addField($field);
    }

    private function addEmailFields()
    {
        $field = new BuilderFormField(ConfigEnum::SITE_EMAILNAME);
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom pour les emails'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer un nom pour les emails')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAIL);
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email de réponse aux emails'));
        $field->setHelp(__('Cette adresse email sera aussi utilisée pour les envois d\'emails si les paramètres SMTP sont configurés ci-dessous.'));
        $this->builderFields->addField($field);

        $this->addHiddenFields(ConfigEnum::MAILER, MailersEnum::MAILER_SMTP);

        $content = '
        <div class="form-group">
            <label class="control-label">' . __('Envoyer un e-mail test') . '</label>
            <div class="controls">
                <a class="btn btn-default" href="' . Tools::makeLink('app', 'settings', 'emails/test') . '">' . __('Envoyer un e-mail test à votre adresse') . ' ' . $_SESSION['email'] . '</a>
            </div>
        </div>';
        $field = new BuilderFormField('content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $this->builderFields->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAIL_SIGNATURE);
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Signature par défaut à utiliser dans les emails'));
        $field->setHelp(__('Cette signature sera ajoutée à tous les emails envoyés par le site, mais vous pourrez la modifier pour chaque email créé.'));
        $this->builderFields->addField($field);
    }

    private function addSmtpGroup()
    {
        $group = new BuilderFormGroup('smtp');
        $group->setLabel(__('Paramètres SMTP'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened(true);

        $options = [
            ['value' => 'custom', 'label' => __('Personnalisé'), 'img' => Assets::getImageUrl('integrations/custom_mail_provider.png'), 'label_class' => 'flex-1-1-0 mr-2'],
            ['value' => 'gmail', 'label' => __('Gmail'), 'img' => Assets::getImageUrl('integrations/gmail.png'), 'label_class' => 'flex-1-1-0 mr-2'],
            ['value' => 'ovh', 'label' => __('OVH'), 'img' => Assets::getImageUrl('integrations/ovh.png'), 'label_class' => 'flex-1-1-0 mr-2'],
            ['value' => 'orange', 'label' => __('Orange'), 'img' => Assets::getImageUrl('integrations/orange.png'), 'label_class' => 'flex-1-1-0 mr-2'],
            ['value' => 'outlook', 'label' => __('Outlook'), 'img' => Assets::getImageUrl('integrations/outlook.png'), 'label_class' => 'flex-1-1-0 mr-2'],
            ['value' => 'free', 'label' => __('Free.fr'), 'img' => Assets::getImageUrl('integrations/free.png'), 'label_class' => 'flex-1-1-0'],
        ];
        $field = new BuilderFormField('smtp_provider');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Configuration SMTP'));
        $field->setOptions($options);
        $field->setData(['display' => 'mega', 'class' => 'radio-inline flex-nowrap overflow-auto']);
        $field->setValue('custom');
        $group->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAILSMTP);
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Serveur SMTP'));
        $group->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAILUSERNAME);
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom d\'utilisateur'));
        $field->setHelp(__('Si aucun nom d\'utilisateur spécifié, l\'adresse email indiquée en haut de cette page sera utilisée'));
        $group->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAILPASSWORD);
        $field->setType(FieldsEnum::TYPE_PASSWORD);
        $field->setLabel(__('Mot de passe'));
        $field->setData(['toggle-password' => true]);
        $group->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAILPORT);
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Port'));
        $group->addField($field);

        $field = new BuilderFormField(ConfigEnum::SITE_EMAILSSL);
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Connexion'));
        $field->setOptions([
            ['value' => '', 'label' => __('Aucun')],
            ['value' => 'ssl', 'label' => __('SSL')],
            ['value' => 'tls', 'label' => __('TLS')],
        ]);
        $group->addField($field);

        //add help for gmail
        $container = ContainerBuilderService::getInstance();
        $helpArticle = $container->get(HelpArticlesService::class)->getRepository()->findOneBy(['permalink' => 'comment-configurer-mon-adresse-email-d-envoi']);
        if ($helpArticle) {
            $content = '<div class="help-content help-custom d-none border p-4 rounded mb-8">';
            $content .= '<h4>' . __('Aide pour configurer son serveur d\'envoi') . '</h4>';
            $content .= str_replace('<img src', '<img class="img-fluid" src', $helpArticle->getContent());
            $content .= '</div>';

            $field = new BuilderFormField('help_custom');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);
        }

        $helpArticle = $container->get(HelpArticlesService::class)->getRepository()->findOneBy(['permalink' => 'comment-configurer-mon-adresse-email-gmail']);
        if ($helpArticle) {
            $content = '<div class="help-content help-gmail d-none border p-4 rounded mb-8">';
            $content .= '<h4>' . __('Aide pour configurer Gmail') . '</h4>';
            $content .= str_replace('<img src', '<img class="img-fluid" src', $helpArticle->getContent());
            $content .= '</div>';

            $field = new BuilderFormField('help_gmail');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);
        }

        $widget = new OutlookConnectWidget();
        $isConnected = $widget->isConnected();
        $content = '<div class="help-content help-outlook d-none rounded mb-8">';
        if ($isConnected) {
            $content .= '
            <div class="alert alert-custom alert-light-success">
                <div class="alert-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="alert-text">
                    <h5>' . __('Connexion Outlook active') . '</h5>
                    <div class="mt-2">
                        <a href="' . Tools::makeLink('app', 'oauth', 'microsoft/disconnect') . '" class="btn btn-sm btn-outline-danger">' . __('Déconnecter') . '</a>
                    </div>
                </div>
            </div>';
        } else {
            $content .= '
            <div class="alert alert-custom alert-light-info">
                <div class="alert-text">
                    <h5>' . __('Connexion Outlook non configurée') . '</h5>
                    <div class="mt-2">
                        <a href="' . Tools::makeLink('app', 'oauth', 'microsoft/authorize') . '" class="btn btn-sm btn-primary">' . __('Se connecter avec Microsoft') . '</a>
                    </div>
                </div>
            </div>';
        }
        $content .= '</div>';

        $field = new BuilderFormField('help_outlook');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addHelpArticle(HelpArticle $helpArticle)
    {
        $group = new BuilderFormGroup('help');
        $group->setLabel(__('Aide pour configurer son adresse email'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened(true);

        $content = '<div class="help-content2 help-custom show-more" data-nb-lines="5">';
        $content .= '<div class="show-more-content lines-5">';
        $content .= str_replace('<img src', '<img class="img-fluid" src', $helpArticle->getContent());
        $content .= '</div>';
        $content .= '</div>';

        $field = new BuilderFormField('help_article_custom');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
