<?php
namespace MatG<PERSON>ver\Components\Mailer;

use <PERSON>G<PERSON>ver\Components\Mailer\Mailers\AbstractMailer;
use Mat<PERSON><PERSON>ver\Components\Mailer\Mailers\OutlookOAuthMailer;
use MatG<PERSON>ver\Components\Mailer\Mailers\SesMailer;
use MatGyver\Components\Mailer\Mailers\SmtpMailer;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\MailersEnum;
use MatGyver\Services\ConfigService;

class MailerFactory
{
    private ConfigService $configService;
    private string $mailerType;

    /**
     * @param ConfigService $configService
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * @return string
     */
    public function getMailerType(): string
    {
        return $this->mailerType;
    }

    /**
     * @param string $mailerType
     */
    public function setMailerType(string $mailerType): void
    {
        $this->mailerType = $mailerType;
    }

    /**
     * @return AbstractMailer
     * @throws \Exception
     */
    public function getMailer(?int $clientId = null): AbstractMailer
    {
        if ($clientId === null) {
            $clientId = CLIENT_MASTER;
        }

        $mailerConfig = $this->configService->findByName(ConfigEnum::MAILER, CLIENT_MASTER);
        if (!$mailerConfig or !$mailerConfig->getValue()) {
            throw new \Exception('No mailer defined');
        }

        $this->setMailerType($mailerConfig->getValue());

        return $this->instantiateMailer($clientId);
    }

    /**
     * @param int $clientId
     * @return AbstractMailer
     * @throws \Exception
     */
    public function instantiateMailer(int $clientId): AbstractMailer
    {
        $instance = null;
        switch ($this->mailerType) {
            case MailersEnum::MAILER_SES:
                $instance = new SesMailer();
                break;
            case MailersEnum::MAILER_SMTP:
                $instance = new SmtpMailer();
                break;
            case MailersEnum::MAILER_OUTLOOK_OAUTH:
                $instance = new OutlookOAuthMailer();
                break;
        }

        if ($instance === null) {
            throw new \Exception(sprintf('Mailer %s does not exists', $this->mailerType));
        }

        $startConnection = $instance->startConnection($clientId);
        if (!$startConnection['valid']) {
            throw new \Exception($startConnection['message']);
        }

        return $instance;
    }
}
