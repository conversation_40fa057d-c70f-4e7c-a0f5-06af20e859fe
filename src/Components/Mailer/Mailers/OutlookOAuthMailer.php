<?php

namespace MatGyver\Components\Mailer\Mailers;

use <PERSON><PERSON><PERSON><PERSON>\Enums\ConfigEnum;
use MatG<PERSON>ver\Enums\MailersEnum;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\OAuth\MicrosoftOAuthService;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\File;

/**
 * Class OutlookOAuthMailer
 * @package MatGyver\Components\Mailer\Mailers
 */
class OutlookOAuthMailer extends AbstractMailer
{
    /**
     * @var MicrosoftOAuthService
     */
    private $oauthService;

    /**
     * @var string|null
     */
    private $accessToken;

    /**
     * OutlookOAuthMailer constructor.
     */
    public function __construct()
    {
        $this->setMailerName(MailersEnum::MAILER_OUTLOOK_OAUTH);
    }

    /**
     * @param int|null $clientId
     * @return array
     */
    public function startConnection(?int $clientId = null): array
    {
        if ($clientId === null) {
            $clientId = CLIENT_MASTER;
        }

        try {
            $this->oauthService = new MicrosoftOAuthService($clientId);
            
            if (!$this->oauthService->isConnected()) {
                return ['valid' => false, 'message' => 'Outlook OAuth not connected. Please authenticate first.'];
            }

            $this->accessToken = $this->oauthService->getValidAccessToken();
            
            if (!$this->accessToken) {
                return ['valid' => false, 'message' => 'Unable to get valid access token. Please re-authenticate.'];
            }

            return ['valid' => true];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Send email using Microsoft Graph API
     * @return array
     */
    public function send(): array
    {
        if (!$this->accessToken) {
            return ['valid' => false, 'message' => 'No valid access token available'];
        }

        try {
            $emailData = $this->buildEmailData();
            $response = $this->sendViaGraphAPI($emailData);
            
            return ['valid' => true, 'message' => 'Email sent successfully', 'response' => $response];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => 'Failed to send email: ' . $e->getMessage()];
        }
    }

    /**
     * Build email data for Microsoft Graph API
     * @return array
     */
    private function buildEmailData(): array
    {
        $container = ContainerBuilderService::getInstance();
        $config = $container->get(ConfigService::class)->getConfig($this->getIdClient());
        
        $fromEmail = $config[ConfigEnum::SITE_EMAIL] ?? '';
        $fromName = $config[ConfigEnum::SITE_EMAILNAME] ?? '';

        $emailData = [
            'message' => [
                'subject' => $this->getSubject(),
                'body' => [
                    'contentType' => 'HTML',
                    'content' => $this->getMessageHtml()
                ],
                'from' => [
                    'emailAddress' => [
                        'address' => $fromEmail,
                        'name' => $fromName
                    ]
                ],
                'toRecipients' => [
                    [
                        'emailAddress' => [
                            'address' => $this->getRecipientEmail(),
                            'name' => $this->getRecipientFirstName() . ' ' . $this->getRecipientLastName()
                        ]
                    ]
                ]
            ]
        ];

        // Add CC recipients if any
        if (!empty($this->getCcEmails())) {
            $ccRecipients = [];
            foreach ($this->getCcEmails() as $ccEmail) {
                $ccRecipients[] = [
                    'emailAddress' => [
                        'address' => $ccEmail
                    ]
                ];
            }
            $emailData['message']['ccRecipients'] = $ccRecipients;
        }

        // Add BCC recipients if any
        if (!empty($this->getBccEmails())) {
            $bccRecipients = [];
            foreach ($this->getBccEmails() as $bccEmail) {
                $bccRecipients[] = [
                    'emailAddress' => [
                        'address' => $bccEmail
                    ]
                ];
            }
            $emailData['message']['bccRecipients'] = $bccRecipients;
        }

        // Add attachments if any
        if (!empty($this->getAttachments())) {
            $attachments = [];
            foreach ($this->getAttachments() as $attachment) {
                if ($attachment instanceof DataPart) {
                    $attachments[] = [
                        '@odata.type' => '#microsoft.graph.fileAttachment',
                        'name' => $attachment->getFilename(),
                        'contentType' => $attachment->getContentType(),
                        'contentBytes' => base64_encode($attachment->getBody())
                    ];
                }
            }
            if (!empty($attachments)) {
                $emailData['message']['attachments'] = $attachments;
            }
        }

        return $emailData;
    }

    /**
     * Send email via Microsoft Graph API
     * @param array $emailData
     * @return array
     */
    private function sendViaGraphAPI(array $emailData): array
    {
        $url = 'https://graph.microsoft.com/v1.0/me/sendMail';
        
        $headers = [
            'Authorization: Bearer ' . $this->accessToken,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($emailData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('cURL error: ' . $error);
        }

        if ($httpCode >= 400) {
            $errorData = json_decode($response, true);
            $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
            throw new \Exception('Microsoft Graph API error (HTTP ' . $httpCode . '): ' . $errorMessage);
        }

        return json_decode($response, true) ?: [];
    }

    /**
     * Test the OAuth connection
     * @return array
     */
    public function testConnection(): array
    {
        if (!$this->oauthService) {
            return ['valid' => false, 'message' => 'OAuth service not initialized'];
        }

        return $this->oauthService->testConnection();
    }

    /**
     * Get OAuth service instance
     * @return MicrosoftOAuthService|null
     */
    public function getOAuthService(): ?MicrosoftOAuthService
    {
        return $this->oauthService;
    }
}
