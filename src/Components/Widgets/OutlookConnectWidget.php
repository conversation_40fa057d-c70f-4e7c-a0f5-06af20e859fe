<?php

namespace MatGyver\Components\Widgets;

use Mat<PERSON><PERSON>ver\Enums\ConfigEnum;
use Mat<PERSON><PERSON>ver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\OAuth\MicrosoftOAuthService;

/**
 * Class OutlookConnectWidget
 * @package MatGyver\Components\Widgets
 */
class OutlookConnectWidget
{
    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * @var array
     */
    private $config;

    /**
     * OutlookConnectWidget constructor.
     */
    public function __construct()
    {
        $container = ContainerBuilderService::getInstance();
        $this->configService = $container->get(ConfigService::class);
        $this->config = $this->configService->getConfig();
    }

    /**
     * Check if OAuth is configured
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->config[ConfigEnum::OUTLOOK_OAUTH_ACCESS_TOKEN] ?? '') &&
               !empty($this->config[ConfigEnum::OUTLOOK_OAUTH_REFRESH_TOKEN] ?? '');
    }

    /**
     * Check if OAuth is connected
     * @return bool
     */
    public function isConnected(): bool
    {
        return ($this->config[ConfigEnum::OUTLOOK_OAUTH_CONNECTED] ?? 'non') === 'oui';
    }

    /**
     * Get user information if connected
     * @return array|null
     */
    public function getUserInfo(): ?array
    {
        if (!$this->isConnected()) {
            return null;
        }

        try {
            $oauthService = new MicrosoftOAuthService();
            $result = $oauthService->testConnection();
            return $result['valid'] ? ($result['user'] ?? []) : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Render a simple connect button
     * @param array $options
     * @return string
     */
    public function renderButton(array $options = []): string
    {
        $defaults = [
            'size' => 'md',           // sm, md, lg
            'style' => 'primary',     // primary, outline-primary, secondary
            'text' => null,           // Custom text
            'return_url' => null,     // URL to return to after connection
            'show_status' => true,    // Show connection status
            'class' => ''             // Additional CSS classes
        ];

        $options = array_merge($defaults, $options);

        if (!$this->isConfigured()) {
            return $this->renderNotConfigured();
        }

        if ($this->isConnected()) {
            return $this->renderConnected($options);
        }

        return $this->renderNotConnected($options);
    }

    /**
     * Render a detailed widget with status and actions
     * @param array $options
     * @return string
     */
    public function renderWidget(array $options = []): string
    {
        $defaults = [
            'title' => __('Connexion Outlook'),
            'show_user_info' => true,
            'show_actions' => true,
            'return_url' => null
        ];

        $options = array_merge($defaults, $options);

        if (!$this->isConfigured()) {
            return $this->renderNotConfiguredWidget($options);
        }

        if ($this->isConnected()) {
            return $this->renderConnectedWidget($options);
        }

        return $this->renderNotConnectedWidget($options);
    }

    /**
     * Render not configured state
     * @return string
     */
    private function renderNotConfigured(): string
    {
        return '
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            ' . __('Connexion Outlook non configurée') . '
        </div>';
    }

    /**
     * Render connected state
     * @param array $options
     * @return string
     */
    private function renderConnected(array $options): string
    {
        $userInfo = $this->getUserInfo();
        $email = '';

        if ($userInfo) {
            $email = $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? '';
        }

        $sizeClass = 'btn-' . $options['size'];
        $styleClass = 'btn-' . $options['style'];

        $html = '<div class="outlook-connect-widget connected">';

        if ($options['show_status']) {
            $html .= '
            <div class="alert alert-success alert-sm mb-2">
                <i class="fas fa-check-circle mr-1"></i>
                ' . __('Connecté') . ($email ? ' : ' . htmlspecialchars($email) : '') . '
            </div>';
        }

        $disconnectUrl = Tools::makeLink('site', 'outlook', 'disconnect');
        if ($options['return_url']) {
            $disconnectUrl .= '?return_url=' . urlencode($options['return_url']);
        }

        $html .= '
        <a href="' . $disconnectUrl . '" class="btn btn-outline-danger ' . $sizeClass . ' ' . $options['class'] . '">
            <i class="fas fa-unlink mr-1"></i>
            ' . ($options['text'] ?: __('Se déconnecter')) . '
        </a>
        </div>';

        return $html;
    }

    /**
     * Render not connected state
     * @param array $options
     * @return string
     */
    private function renderNotConnected(array $options): string
    {
        $sizeClass = 'btn-' . $options['size'];
        $styleClass = 'btn-' . $options['style'];

        $connectUrl = Tools::makeLink('site', 'outlook', 'authorize');
        if ($options['return_url']) {
            $connectUrl .= '?return_url=' . urlencode($options['return_url']);
        }

        return '
        <a href="' . $connectUrl . '" class="btn ' . $styleClass . ' ' . $sizeClass . ' ' . $options['class'] . '">
            <i class="fab fa-microsoft mr-2"></i>
            ' . ($options['text'] ?: __('Se connecter avec Outlook')) . '
        </a>';
    }

    /**
     * Render not configured widget
     * @param array $options
     * @return string
     */
    private function renderNotConfiguredWidget(array $options): string
    {
        return '
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">' . htmlspecialchars($options['title']) . '</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    ' . __('La connexion Outlook n\'est pas configurée sur ce site.') . '
                </div>
            </div>
        </div>';
    }

    /**
     * Render connected widget
     * @param array $options
     * @return string
     */
    private function renderConnectedWidget(array $options): string
    {
        $userInfo = $this->getUserInfo();

        $html = '
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-check-circle mr-2"></i>
                    ' . htmlspecialchars($options['title']) . '
                </h6>
            </div>
            <div class="card-body">';

        if ($options['show_user_info'] && $userInfo) {
            $html .= '
                <div class="mb-3">
                    <small class="text-muted">' . __('Connecté en tant que :') . '</small><br>
                    <strong>' . htmlspecialchars($userInfo['displayName'] ?? $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? 'Utilisateur') . '</strong>';

            if (!empty($userInfo['mail']) || !empty($userInfo['userPrincipalName'])) {
                $email = $userInfo['mail'] ?? $userInfo['userPrincipalName'];
                $html .= '<br><small class="text-muted">' . htmlspecialchars($email) . '</small>';
            }

            $html .= '</div>';
        }

        if ($options['show_actions']) {
            $testUrl = Tools::makeLink('site', 'outlook', 'test');
            $disconnectUrl = Tools::makeLink('site', 'outlook', 'disconnect');

            if ($options['return_url']) {
                $testUrl .= '?return_url=' . urlencode($options['return_url']);
                $disconnectUrl .= '?return_url=' . urlencode($options['return_url']);
            }

            $html .= '
                <div class="btn-group btn-group-sm w-100">
                    <a href="' . $testUrl . '" class="btn btn-outline-primary">
                        <i class="fas fa-vial mr-1"></i>
                        ' . __('Tester') . '
                    </a>
                    <a href="' . $disconnectUrl . '" class="btn btn-outline-danger">
                        <i class="fas fa-unlink mr-1"></i>
                        ' . __('Déconnecter') . '
                    </a>
                </div>';
        }

        $html .= '
            </div>
        </div>';

        return $html;
    }

    /**
     * Render not connected widget
     * @param array $options
     * @return string
     */
    private function renderNotConnectedWidget(array $options): string
    {
        $connectUrl = Tools::makeLink('site', 'outlook', 'authorize');
        if ($options['return_url']) {
            $connectUrl .= '?return_url=' . urlencode($options['return_url']);
        }

        return '
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">' . htmlspecialchars($options['title']) . '</h6>
            </div>
            <div class="card-body text-center">
                <i class="fab fa-microsoft fa-2x text-primary mb-3"></i>
                <p class="text-muted mb-3">' . __('Connectez votre compte Outlook pour envoyer des emails.') . '</p>
                <a href="' . $connectUrl . '" class="btn btn-primary">
                    <i class="fab fa-microsoft mr-2"></i>
                    ' . __('Se connecter avec Outlook') . '
                </a>
            </div>
        </div>';
    }
}
