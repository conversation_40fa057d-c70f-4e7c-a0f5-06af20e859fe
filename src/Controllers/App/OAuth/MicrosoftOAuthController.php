<?php

namespace MatGyver\Controllers\App\OAuth;

use MatGyver\Controllers\App\AbstractAppController;
use MatGyver\Helpers\Tools;
use MatGyver\Services\OAuth\MicrosoftOAuthService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class MicrosoftOAuthController
 * @package MatGyver\Controllers\App\OAuth
 */
class MicrosoftOAuthController extends AbstractAppController
{
    #[Route('/app/oauth/microsoft/authorize/', name: 'app_oauth_microsoft_authorize')]
    public function authorize(): void
    {
        try {
            $oauthService = new MicrosoftOAuthService();
            $authUrl = $oauthService->getAuthorizationUrl();

            header('Location: ' . $authUrl);
            exit();
        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'OAuth configuration error: ' . $e->getMessage();
            header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
            exit();
        }
    }

    #[Route('/app/oauth/microsoft/callback/', name: 'app_oauth_microsoft_callback')]
    public function callback(): void
    {
        $code = filter_input(INPUT_GET, 'code', FILTER_UNSAFE_RAW);
        $state = filter_input(INPUT_GET, 'state', FILTER_UNSAFE_RAW);
        $error = filter_input(INPUT_GET, 'error', FILTER_UNSAFE_RAW);

        if ($error) {
            $errorDescription = filter_input(INPUT_GET, 'error_description', FILTER_UNSAFE_RAW);
            $_SESSION['error-message'] = 'OAuth authorization failed: ' . ($errorDescription ?: $error);
            header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
            exit();
        }

        if (!$code || !$state) {
            $_SESSION['error-message'] = 'Invalid OAuth callback parameters';
            header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
            exit();
        }

        try {
            $oauthService = new MicrosoftOAuthService();
            $result = $oauthService->handleCallback($code, $state);

            if ($result['valid']) {
                $_SESSION['success-message'] = 'Microsoft Outlook OAuth connection successful!';
            } else {
                $_SESSION['error-message'] = $result['message'];
            }
        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'OAuth callback error: ' . $e->getMessage();
        }

        header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
        exit();
    }

    #[Route('/app/oauth/microsoft/disconnect/', name: 'app_oauth_microsoft_disconnect')]
    public function disconnect(): void
    {
        try {
            $oauthService = new MicrosoftOAuthService();
            $oauthService->clearTokens();

            $_SESSION['success-message'] = 'Microsoft Outlook déconnecté avec succès';
        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'Error disconnecting OAuth: ' . $e->getMessage();
        }

        header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
        exit();
    }

    #[Route('/app/oauth/microsoft/test/', name: 'app_oauth_microsoft_test')]
    public function test(): void
    {
        try {
            $oauthService = new MicrosoftOAuthService();
            $result = $oauthService->testConnection();

            if ($result['valid']) {
                $userInfo = $result['user'] ?? [];
                $email = $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? 'Unknown';
                $_SESSION['success-message'] = 'OAuth connection test successful! Connected as: ' . $email;
            } else {
                $_SESSION['error-message'] = 'OAuth connection test failed: ' . $result['message'];
            }
        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'OAuth test error: ' . $e->getMessage();
        }

        header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
        exit();
    }
}
