<?php

namespace MatGyver\Controllers\App\Test;

use MatGyver\Components\Mailer\Mailers\OutlookOAuthMailer;
use MatGyver\Controllers\AbstractController;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\MailersEnum;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\OAuth\MicrosoftOAuthService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class EmailTestController
 * @package MatGyver\Controllers\App\Test
 */
class EmailTestController extends AbstractController
{
    /**
     * Test OAuth email sending
     */
    #[Route('/app/test/oauth-email', name: 'app_test_oauth_email')]
    public function testOAuthEmail()
    {
        try {
            $container = ContainerBuilderService::getInstance();
            $configService = $container->get(ConfigService::class);
            $config = $configService->getConfig();

            // Check if OAuth is configured
            if (($config[ConfigEnum::OUTLOOK_OAUTH_CONNECTED] ?? 'non') !== 'oui') {
                $_SESSION['error-message'] = 'OAuth not configured. Please configure OAuth first.';
                header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
                exit();
            }

            // Create OAuth mailer
            $mailer = new OutlookOAuthMailer();
            $startConnection = $mailer->startConnection();

            if (!$startConnection['valid']) {
                $_SESSION['error-message'] = 'OAuth connection failed: ' . $startConnection['message'];
                header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
                exit();
            }

            // Prepare test email
            $mailer->setIdClient($_SESSION['client']['id'] ?? CLIENT_MASTER);
            $mailer->setFromName($config[ConfigEnum::SITE_EMAILNAME] ?? APP_NAME);
            $mailer->setFromEmail($config[ConfigEnum::SITE_EMAIL] ?? APP_EMAIL);
            $mailer->setSubject('Test OAuth Email - ' . APP_NAME);
            
            $message = '<h2>Test Email via OAuth</h2>';
            $message .= '<p>This is a test email sent using Microsoft OAuth authentication.</p>';
            $message .= '<p>Sent at: ' . date('Y-m-d H:i:s') . '</p>';
            $message .= '<p>From: ' . APP_NAME . '</p>';
            
            $mailer->setMessageHtml($message);
            $mailer->setMessageText(strip_tags($message));
            $mailer->setRecipientFirstName($_SESSION['user']['first_name'] ?? 'Test');
            $mailer->setRecipientLastName($_SESSION['user']['last_name'] ?? 'User');
            $mailer->setRecipientEmail($_SESSION['user']['email'] ?? $config[ConfigEnum::SITE_EMAIL]);

            // Send email
            $result = $mailer->send();

            if ($result['valid']) {
                $_SESSION['success-message'] = 'OAuth test email sent successfully!';
            } else {
                $_SESSION['error-message'] = 'Failed to send OAuth test email: ' . $result['message'];
            }

        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'OAuth test error: ' . $e->getMessage();
        }

        header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
        exit();
    }

    /**
     * Test OAuth connection without sending email
     */
    #[Route('/app/test/oauth-connection', name: 'app_test_oauth_connection')]
    public function testOAuthConnection()
    {
        try {
            $oauthService = new MicrosoftOAuthService();
            $result = $oauthService->testConnection();

            if ($result['valid']) {
                $userInfo = $result['user'] ?? [];
                $email = $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? 'Unknown';
                $displayName = $userInfo['displayName'] ?? $email;
                
                $_SESSION['success-message'] = "OAuth connection successful! Connected as: {$displayName} ({$email})";
            } else {
                $_SESSION['error-message'] = 'OAuth connection test failed: ' . $result['message'];
            }
        } catch (\Exception $e) {
            $_SESSION['error-message'] = 'OAuth connection test error: ' . $e->getMessage();
        }

        header('Location: ' . Tools::makeLink('app', 'settings', 'emails'));
        exit();
    }
}
