<?php

namespace MatGyver\Services\Clients;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Components\Medias\MediasComponent;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\Client\History\ClientHistory;
use MatGyver\Entity\Client\Subscription\ClientSubscription;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Client\ClientRepository;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use MatGyver\Services\Aws\S3\MediasService;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Company\CompanyService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Expertise\ExpertiseSettingsService;
use MatGyver\Services\I18nService;
use MatGyver\Services\Limit\LimitClientsService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Logs\LogActionService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Product\ShopProductsAttributesService;
use MatGyver\Services\Shop\Product\ShopProductsLimitsService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use MatGyver\Services\SiteService;
use MatGyver\Services\Tos\TosClientsService;
use MatGyver\Services\Tos\TosService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersPasswordService;
use MatGyver\Services\Users\UsersRolesService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ClientsService
 * @package MatGyver\Services\Clients
 * @property ClientRepository $repository
 * @method ClientRepository getRepository()
 */
class ClientsService extends BaseEntityService
{
    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * ClientService constructor.
     * @param EntityManager $em
     * @param MailSender $mailSender
     * @param UsersService $usersService
     */
    public function __construct(
        EntityManager $em,
        MailSender $mailSender,
        UsersService $usersService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(Client::class);
        $this->mailSender = $mailSender;
        $this->usersService = $usersService;
    }

    /**
     * @return Client|null
     */
    public function getClient(): ?Client
    {
        return $this->getClientById($_SESSION['client']['id']);
    }

    /**
     * @param int|null $idClient
     * @return Client|null
     */
    public function getClientById(int $idClient = null): ?Client
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $qb = $this->repository->createQueryBuilder('c')
            ->where('c.id = :clientId')
            ->setParameter('clientId', $idClient);

        $query = $qb->getQuery();
        $query->useResultCache(true, 3600, 'client_' . $idClient);

        return $query->getOneOrNullResult();
    }

    /**
     * @return Client[]
     */
    public function getAllClients(): array
    {
        return $this->repository->findAll();
    }

    /**
     * @param string $clientUniqId
     * @return array|null
     */
    public function getClientByUniqId(string $clientUniqId): ?Client
    {
        return $this->repository->findOneBy(['uniqid' => $clientUniqId]);
    }

    /**
     * @param int $idPartner
     * @return Client|null
     */
    public function getClientByPartnerId(int $idPartner): ?Client
    {
        return $this->repository->findOneBy(['affiliationPartner' => $idPartner]);
    }

    public function checkClient()
    {
        //first set master config in session
        $this->setMasterConfig();

        //cron jobs
        if ('cli' == php_sapi_name() or empty($_SERVER['REMOTE_ADDR'])) {
            return;
        }

        if (ENV === ENV_PROD or ENV === ENV_PRE_PROD) {
            if ((!isset($_SERVER['HTTPS']) or '' == $_SERVER['HTTPS']) and (!isset($_SERVER['HTTP_X_FORWARDED_PROTO']) or 'https' != $_SERVER['HTTP_X_FORWARDED_PROTO'])) {
                header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);
                exit();
            }
        }

        if (!isset($_SERVER['HTTP_HOST'])) {
            LoggerService::logError('HTTP_HOST not set 3');
            if (isset($_SERVER['REQUEST_URI'])) {
                LoggerService::logError('REQUEST_URI : ' . $_SERVER['REQUEST_URI']);
            } else {
                LoggerService::logError('REQUEST_URI not set');
            }
            LoggerService::logError(json_encode($_SERVER));
            header($_SERVER['SERVER_PROTOCOL'] . ' 403 Forbidden', true, 403);
            die('Forbidden');
        }

        $explodeDomain = explode('.', $_SERVER['HTTP_HOST']);
        $uniqId = $explodeDomain[0];
        $appDomainNbDots = count(explode('.', APP_DOMAIN));

        if (count($explodeDomain) > 3) {
            //a.matgyver.com = 3 parts : a / matgyver / com
            $client = $this->getClientById(CLIENT_MASTER);
            $this->setClientInSession($client);
            SiteService::displayClientDeactivated();
            exit();
        }

        //redirect if subdomain is www or master
        if (($uniqId == 'www' or $uniqId == 'master') and !$_POST) {
            $url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            $url = str_replace('www.', '', $url);
            $url = str_replace('master.', '', $url);

            header('Status: 301 Moved Permanently', false, 301);
            header('Location: ' . $url);
            exit();
        }

        if (!SUBDOMAIN_ENABLED) {
            if (count($explodeDomain) > $appDomainNbDots) {
                //no subdomain allowed
                $url = APP_URL . $_SERVER['REQUEST_URI'];
                header('Status: 301 Moved Permanently', false, 301);
                header('Location: ' . $url);
                exit();
            }

            if (isset($_SESSION['client']) and $_SESSION['client']) {
                $uniqId = $_SESSION['client']['uniqid'];
            } else {
                $user = $this->usersService->getUser();
                if ($user) {
                    $uniqId = $user->getClient()->getUniqid();
                } else {
                    $uniqId = 'master';
                }
            }
        }

        if (SUBDOMAIN_ENABLED and count($explodeDomain) <= $appDomainNbDots) {
            //no subdomain so client is master
            $uniqId = 'master';
        }

        $client = $this->getClientByUniqId($uniqId);
        if (!$client) {
            LoggerService::logError('client ' . $uniqId . ' does not exist');
            $client = $this->getClientById(CLIENT_MASTER);
            $this->setClientInSession($client);
            SiteService::displayClientDeactivated();
            exit();
        }

        $this->setClientInSession($client);
    }

    /**
     * @param Client $client
     */
    public function setClientInSession(Client $client): void
    {
        $_SESSION['client'] = [
            'id' => $client->getId(),
            'name' => $client->getName(),
            'uniqid' => $client->getUniqid(),
            'subscription' => $client->getSubscription(),
            'active' => $client->getActive(),
            'freemium' => $client->getFreemium(),
            'on_pause' => $client->getOnPause(),
            'date' => $client->getDate()->format('Y-m-d H:i:s'),
            'date_desactivation' => ($client->getDateDesactivation() ? $client->getDateDesactivation()->format('Y-m-d H:i:s') : null),
            'date_end_subscription' => $client->getDateEndSubscription()->format('Y-m-d H:i:s'),
            'partner_id' => null,
        ];

        $_SESSION['client_uniqid'] = $client->getUniqid();

        $_SESSION['partner_id'] = null;
        if ($client->getAffiliationPartner()) {
            $_SESSION['partner_id'] = $client->getAffiliationPartner()->getId();
            $_SESSION['client']['partner_id'] = $client->getAffiliationPartner()->getId();
        }

        if (!defined('APP_CLIENT_URL')) {
            if ($client->getId() == CLIENT_MASTER or !SUBDOMAIN_ENABLED) {
                define('APP_CLIENT_URL', APP_URL);
                define('APP_CLIENT_DOMAIN', APP_DOMAIN);
            } else {
                define('APP_CLIENT_URL', 'https://' . $client->getUniqid() . '.' . APP_DOMAIN);
                define('APP_CLIENT_DOMAIN', $client->getUniqid() . '.' . APP_DOMAIN);
            }
        }

        //client settings
        $container = ContainerBuilderService::getInstance();
        $settings = $container->get(ConfigService::class)->getConfig();
        $_SESSION['settings'] = $settings;

        //start i18n
        $i18n = $container->get(I18nService::class);
        $i18n->setLanguage();

        //set limits
        $limits = [];
        $clientLimits = $container->get(LimitClientsService::class)->getRepository()->findBy(['client' => $client->getId()]);
        if ($clientLimits) {
            foreach ($clientLimits as $clientLimit) {
                $limits[$clientLimit->getLimit()->getReference()] = $clientLimit->getValue();
            }
        }
        $_SESSION['client']['limits'] = $limits;
    }

    public function setMasterConfig()
    {
        $container = ContainerBuilderService::getInstance();
        $params = $container->get(ConfigService::class)->getConfig(CLIENT_MASTER);
        if (!$params) {
            TwigService::getInstance()
                ->set('error', 'Impossible de charger les paramètres, veuillez recommencer l\'installation')
                ->render('site/index-error.php');
            exit();
        }

        define('ASSETS_VERSION', ($params['assets_version'] ?? (ENV === ENV_PROD ? '0' : uniqid())));
        define('SITE_ACTIVE', ($params[ConfigEnum::SITE_ACTIVE] ?? 'oui'));
        define('MAINTENANCE_IP', ($params[ConfigEnum::MAINTENANCE_IP] ?? ''));
        define('DEFAULT_COUNTRY', ($params[ConfigEnum::COUNTRY] ?? 'FR'));
        define('DEFAULT_CURRENCY', ($params[ConfigEnum::DEFAULT_CURRENCY] ?? 'EUR'));
        define('DEFAULT_VAT', ($params[ConfigEnum::DEFAULT_CURRENCY] ?? 20));
        define('APP_LOGO', ($params[ConfigEnum::LOGO] ?? Assets::getImageUrl('logo.png')));
        define('COMMISSIONS_AVAILABLE_DELAY', ($params[ConfigEnum::AFF_COMMISSIONS_AVAILABLE_DELAY] ?? 60)); // in days
        define('COMMISSIONS_EXPIRE_DELAY', (isset($params[ConfigEnum::AFF_COMMISSIONS_EXPIRATION_DELAY]) ? COMMISSIONS_AVAILABLE_DELAY + $params[ConfigEnum::AFF_COMMISSIONS_EXPIRATION_DELAY] : COMMISSIONS_AVAILABLE_DELAY + 90)); // in days
        define('INVOICE_MINIMUM_AMOUNT_HT', ($params[ConfigEnum::AFF_INVOICE_MINIMUM_AMOUNT_HT] ?? 100));
        define('INVOICE_MAXIMUM_AMOUNT_HT', ($params[ConfigEnum::AFF_INVOICE_MAXIMUM_AMOUNT_HT] ?? 10000));
    }

    /**
     * @param string $uniqId
     * @return bool
     */
    public function isValidUniqId(string $uniqId): bool
    {
        if (in_array($uniqId, PROTECTED_PERMALINKS)) {
            return false;
        }
        if (str_contains($uniqId, 'spam')) {
            return false;
        }
        if (str_contains($uniqId, 'paypal')) {
            return false;
        }
        return true;
    }

    /**
     * @param int $idClient
     * @return string
     */
    public function generateSelectClients(int $idClient = 0): string
    {
        $clients = $this->getAllClients();
        if (!$clients) {
            return '';
        }

        $options = [];
        $options[] = '-';
        foreach ($clients as $client) {
            if ($client->getId() == CLIENT_MASTER) {
                continue;
            }
            $options[$client->getId()] = $client->getName();
        }

        return SelectService::render($options, $idClient);
    }

    /**
     * @param int|null $productId
     * @param int|null $idClient
     * @return string
     */
    public function generateSelectSubscription(?int $productId = null, ?int $idClient = null): string
    {
        $container = ContainerBuilderService::getInstance();
        $products = $container->get(ShopProductsService::class)->getProductsByType(ProductsEnum::TYPE_SUBSCRIPTION);
        if (!$products) {
            return '';
        }

        if ($idClient and $productId === null) {
            $clientSubscriptions = $container->get(ClientsSubscriptionsService::class)->getRepository()->findBy(['client' => $idClient], ['id' => 'DESC']);
            if ($clientSubscriptions) {
                foreach ($clientSubscriptions as $clientSubscription) {
                    if ($clientSubscription->getStatus() == ClientSubscription::STATUS_ACTIVE and $clientSubscription->getProduct() and $clientSubscription->getProduct()->getType() == ProductsEnum::TYPE_SUBSCRIPTION) {
                        $productId = $clientSubscription->getProduct()->getId();
                        break;
                    }
                }
            }
        }

        $options = [];
        $options[] = __('Aucun');
        foreach ($products as $product) {
            $options[$product->getId()] = $product->getName();
        }

        return SelectService::render($options, $productId);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insertClient(array $submittedData): array
    {
        $name = filter_var($submittedData['name'], FILTER_UNSAFE_RAW);
        $name = str_replace('&#39;', '\'', $name);

        $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
        $uniqId = permalink($uniqId);
        if (!$this->isValidUniqId($uniqId)) {
            return array('valid' => false, 'message' => __('Cet id unique est protégé, merci d\'en utiliser un autre.'));
        }

        $subscription = '';
        if (isset($submittedData['subscription'])) {
            $subscription = filter_var($submittedData['subscription'], FILTER_UNSAFE_RAW);
        }

        $dateEnd = date('Y-m-d', strtotime('+1 month'));
        if (isset($submittedData['date_end_subscription']) and $submittedData['date_end_subscription']) {
            $dateEnd = filter_var($submittedData['date_end_subscription'], FILTER_UNSAFE_RAW);
        } elseif (isset($submittedData['duree']) and $submittedData['duree']) {
            $dateEnd = date('Y-m-d', strtotime($submittedData['duree']));
        }

        $recurring = true;
        if (!$subscription) {
            $recurring = false;
        }

        $client = new Client();
        $client->setName($name);
        $client->setUniqid($uniqId);
        $client->setSubscription($subscription);
        $client->setRecurring($recurring);
        $client->setActive(true);
        $client->setMaxusers(0);
        $client->setDateEndSubscription(new \DateTime($dateEnd));
        $client->setDate(new \DateTime());

        if (isset($submittedData['active'])) {
            $client->setActive($submittedData['active']);
        }
        if (isset($submittedData['recurring'])) {
            $client->setRecurring($submittedData['recurring']);
        }
        if (isset($submittedData['freemium']) and $submittedData['freemium']) {
            $client->setFreemium(true);
        }
        if (!empty($submittedData['client_id'])) {
            $client->setId($submittedData['client_id']);
        }
        if (isset($submittedData['customerData']) and isset($submittedData['customerData']['source']) and $submittedData['customerData']['source']) {
            $client->setSource($submittedData['customerData']['source']);
            if (isset($submittedData['customerData']['source_referer']) and $submittedData['customerData']['source_referer']) {
                $client->setReferer($submittedData['customerData']['source_referer']);
            }
        }

        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement du client."));
        }

        $idClient = $client->getId();

        if (FILE_SYSTEM === FILE_SYSTEM_S3) {
            $s3Service = new MediasService();
            $createDirectory = $s3Service->createDirectory($uniqId);
            if (!$createDirectory['valid']) {
                LoggerService::logError('Impossible de créer le dossier /' . $uniqId . ' : ' . $createDirectory['message']);
            }
        } else {
            @mkdir(WEB_PATH . '/medias/' . $uniqId, 0777);
        }


        $container = ContainerBuilderService::getInstance();

        //limits
        $limits = [];
        if (isset($submittedData['limits']) and $submittedData['limits']) {
            $limits = $submittedData['limits'];
        } elseif (isset($submittedData['product']) and $submittedData['product']) {
            $productLimits = $container->get(ShopProductsLimitsService::class)->getByProduct($submittedData['product']['id']);
            if ($productLimits) {
                foreach ($productLimits as $productLimit) {
                    if (!$productLimit->getLimit()) {
                        continue;
                    }
                    $limits[$productLimit->getLimit()->getId()] = [
                        'action' => $productLimit->getAction(),
                        'value' => $productLimit->getValue(),
                    ];
                }
            }

            //check attributes
            if (isset($submittedData['transactionProduct']) and $submittedData['transactionProduct']['attributes']) {
                $productAttributes = json_decode($submittedData['transactionProduct']['attributes'], true);
                foreach ($productAttributes as $group) {
                    if (!$group['current']) {
                        continue;
                    }
                    $productAttribute = $container->get(ShopProductsAttributesService::class)->getRepository()->find($group['current']);
                    if (!$productAttribute) {
                        continue;
                    }
                    if ($productAttribute->getLimits()) {
                        foreach ($productAttribute->getLimits() as $productAttributeLimit) {
                            $limits[$productAttributeLimit->getLimit()->getId()] = [
                                'action' => $productAttributeLimit->getAction(),
                                'value' => $productAttributeLimit->getValue(),
                            ];
                        }
                    }
                }
            }
        }
        if ($limits) {
            foreach ($limits as $idLimit => $limit) {
                if ($limit['action'] == 'none') {
                    continue;
                }

                $insert = $container->get(LimitClientsService::class)->insert($idLimit, $limit['value'], $idClient);
                if (!$insert['valid']) {
                    return $insert;
                }
            }
        }

        //save config
        $params = array(
            ConfigEnum::SITE_NAME => $name,
            ConfigEnum::SITE_EMAIL => $submittedData['email'],
            ConfigEnum::SITE_EMAILNAME => $name,
            ConfigEnum::ADDRESS => '',
            ConfigEnum::ADDRESS2 => '',
            ConfigEnum::CITY => '',
            ConfigEnum::ZIP => '',
            ConfigEnum::COUNTRY => '',
            ConfigEnum::STATE => '',
            ConfigEnum::TELEPHONE => '',
            ConfigEnum::TVA_INTRACOM => '',
            ConfigEnum::COMPANY => '',
        );

        $customer = [];
        if (isset($submittedData['customer']) and $submittedData['customer']) {
            $customer = $submittedData['customer'];
        }
        if ($customer) {
            $params = array_merge($params, array_intersect_key($customer, $params));
        }
        if (isset($submittedData['customerData']) and isset($submittedData['customerData'][ConfigEnum::COMPANY])) {
            $params[ConfigEnum::COMPANY] = $submittedData['customerData'][ConfigEnum::COMPANY];
        }
        if (isset($submittedData['customerData']) and isset($submittedData['customerData'][ConfigEnum::TVA_INTRACOM])) {
            $params[ConfigEnum::TVA_INTRACOM] = $submittedData['customerData'][ConfigEnum::TVA_INTRACOM];
        }

        $country = DEFAULT_COUNTRY;
        if ($customer and $customer[ConfigEnum::COUNTRY]) {
            $country = $customer[ConfigEnum::COUNTRY];
        }

        $europeTvaRates = $container->get(ShopVatRulesService::class)->getVatRates();
        if (isset($europeTvaRates[$country])) {
            //country in Europe
            $params[ConfigEnum::DEFAULT_CURRENCY] = 'EUR';
        }

        $saveConfig = $container->get(ConfigService::class)->saveConfig($params, $idClient);
        if (!$saveConfig['valid']) {
            return array('valid' => false, 'message' => $saveConfig['message']);
        }

        if ($params[ConfigEnum::COMPANY]) {
            $container->get(CompanyService::class)->createFromSettings($idClient);
        }


        // Create default roles
        $usersRolesService = $container->get(UsersRolesService::class);
        $usersRolesService->createClientDefaultRoles($idClient);


        // Create User
        $user = [
            'client_id' => $idClient,
            'first_name' => $submittedData['first_name'],
            'last_name' => $submittedData['last_name'],
            'email' => $submittedData['email'],
            'validated' => 1,
            'newsletter' => 1,
            'password' => ($submittedData['password'] ?? ''),
            'address' => ($customer ? $customer['address'] : ''),
            'address2' => ($customer ? $customer['address2'] : ''),
            'city' => ($customer ? $customer['city'] : ''),
            'zip' => ($customer ? $customer['zip'] : ''),
            'country' => ($customer ? $customer['country'] : ''),
            'state' => ($customer ? $customer['state'] : ''),
            'telephone' => ($customer ? $customer['telephone'] : ''),
            'ip' => ($customer ? $customer['ip'] : ''),
            'admin' => true,
            'dont_send_email' => true,
            'add_default_notifications' => true,
        ];

        if (isset($submittedData['no_validated'])) {
            unset($user['validated']);
        }

        if (isset($submittedData['customerData']) and isset($submittedData['customerData']['password']) and $submittedData['customerData']['password']) {
            $user['password'] = $submittedData['customerData']['password'];
            $user['no_hash'] = true; //password is already hashed
        }

        if (isset($submittedData['customerData']) and isset($submittedData['customerData']['rgpd']) and $submittedData['customerData']['rgpd']) {
            $user['rgpd'] = true;
            $user['rgpd_date'] = $submittedData['customerData']['rgpd_date'];
            $user['rgpd_notice'] = $submittedData['customerData']['rgpd_notice'];
        }
        if (isset($submittedData['customerData']) and isset($submittedData['customerData']['rgpd_aff']) and $submittedData['customerData']['rgpd_aff']) {
            $user['rgpd_aff'] = true;
            $user['rgpd_aff_date'] = $submittedData['customerData']['rgpd_aff_date'];
            $user['rgpd_aff_notice'] = $submittedData['customerData']['rgpd_aff_notice'];
        }

        // Force user role as admin
        $usersRolesService = $container->get(UsersRolesService::class);
        if ($subscription == 'affiliation') {
            $userRole = $usersRolesService->getUserRoleByRole(ROLE_AFFILIATE, $idClient);
        } else {
            $userRole = $usersRolesService->getUserRoleByRole(ROLE_ADMIN, $idClient);
        }
        $user['user_role_id'] = $userRole->getUserRoleId();

        $insertUser = $this->usersService->insertUser($user);
        if (!$insertUser['valid']) {
            return array('valid' => false, 'message' => $insertUser['message']);
        }

        $user_id = $insertUser['user_id'];
        $random_id = $insertUser['random_id'];


        // create partner
        $arrayPartner = array(
            'client_id' => $idClient,
            'user_id' => $user_id,
            'first_name' => $submittedData['first_name'],
            'last_name' => $submittedData['last_name'],
            'email' => $submittedData['email'],
            'no_mail' => true,
        );
        if ($subscription == 'affiliation') {
            unset($arrayPartner['no_mail']);
        }
        if (isset($submittedData['parent'])) {
            $arrayPartner['parent'] = filter_var($submittedData['parent'], FILTER_UNSAFE_RAW);
        }

        $insertPartner = $container->get(AffiliationPartnersService::class)->insertPartner($arrayPartner);
        if (!$insertPartner['valid']) {
            return array('valid' => false, 'message' => __("Erreur lors de la création de l'affilié.") . $insertPartner['message']);
        }

        //send mail
        if (!isset($submittedData['dont_send_email']) or !$submittedData['dont_send_email']) {
            $recipient = [
                [
                    'first_name' => $submittedData['first_name'],
                    'last_name' => $submittedData['last_name'],
                    'email' => $submittedData['email']
                ]
            ];

            if ($user['password']) {
                $password = __('Défini lors de votre inscription');
            } else {
                //create reset link so user can defined his own password
                $reset = $container->get(UsersPasswordService::class)->resetPassword(['email' => $submittedData['email']], false, $idClient);
                if (!$reset['valid']) {
                    return $reset;
                }

                $password = __('Avant de pouvoir vous connecter, vous devez définir un mot de passe en cliquant sur le lien ci-dessous :');
                $password .= '</p><p style="text-align: center"><a class="btn" href="' . $reset['reset_link'] . '">' . __('Cliquez ici pour définir votre mot de passe') . '</a></p><p><br>';
            }
            $vars = [
                'PASSWORD_INFO' => $password,
                'APP_URL_APP' => Tools::makeClientLink($uniqId, 'app', 'index'),
            ];
            $send_mail = $this->mailSender->sendTemplateToClient('new_client', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
            if (!$send_mail['valid']) {
                return $send_mail;
            }
        }

        if (isset($submittedData['customerData']) and isset($submittedData['customerData']['cgv']) and $submittedData['customerData']['cgv']) {
            if (isset($submittedData['customerData']['cgv_id']) and $submittedData['customerData']['cgv_id']) {
                $lastTos = $container->get(TosService::class)->getRepository()->find($submittedData['customerData']['cgv_id']);
            } else {
                $lastTos = $container->get(TosService::class)->getRepository()->getLast();
            }
            if ($lastTos) {
                $container->get(TosClientsService::class)->acceptTos($lastTos->getId(), $idClient, $user_id);
            }
        }

        // Log
        if (isset($_SESSION['universe_id']) and $_SESSION['universe_id'] === UNIVERSE_ADMIN_DEFAULT) {
            LogActionService::log('create_client');
        }

        $container->get(ExpertiseSettingsService::class)->createDefaultSettings($idClient);

        ClientsHistoriesService::add(ClientHistory::ACTION_SUBSCRIBE, null, $idClient);

        return array('valid' => true, 'client_id' => $idClient, 'uniqid' => $uniqId, 'user_id' => $user_id, 'random_id' => $random_id);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function updateClient(array $submittedData): array
    {
        $idClient = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        if ($idClient == CLIENT_MASTER) {
            return array('valid' => false, 'message' => __("Ce client ne peut être modifié."));
        }

        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas."));
        }

        if (isset($submittedData['uniqid'])) {
            $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
            $uniqId = permalink($uniqId);
            if (!$this->isValidUniqId($uniqId)) {
                return array('valid' => false, 'message' => __('Ce lien est protégé, merci d\'en utiliser un autre.'));
            }
        } else {
            $uniqId = $client->getUniqid();
        }

        $name = filter_var($submittedData['name'], FILTER_UNSAFE_RAW);
        $client->setName($name);

        if (isset($submittedData['subscription'])) {
            $client->setSubscription(filter_var($submittedData['subscription'], FILTER_UNSAFE_RAW));
        }

        $dateEnd = ''; //vide au cas où on modifie le client dans l'administration
        if (isset($submittedData['date_end_subscription']) and $submittedData['date_end_subscription']) {
            $dateEnd = filter_var($submittedData['date_end_subscription'], FILTER_UNSAFE_RAW);
        } elseif (isset($submittedData['duree']) and $submittedData['duree']) {
            $dateEnd = date('Y-m-d', strtotime($submittedData['duree']));
        }
        if ($dateEnd) {
            $client->setDateEndSubscription(new \DateTime($dateEnd));
        }

        if (isset($submittedData['price_tax_incl']) and $client->getFreemium()) {
            //call updateClient from transaction
            $client->setFreemium(false);
            $client->setRecurring(true);

            $container = ContainerBuilderService::getInstance();
            $container->get(ClientsFreemiumService::class)->completeFreemiumPeriod($client->getId());
        }

        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la mise à jour du client.') . $e->getMessage());
        }

        if (SUBDOMAIN_ENABLED and $client->getUniqid() != $uniqId) {
            $update = $this->updateClientUniqId($idClient, $uniqId);
            if (!$update['valid']) {
                return array('valid' => false, 'message' => $update['message']);
            }
        }

        $container = ContainerBuilderService::getInstance();

        //limits
        if (isset($submittedData['limits']) and $submittedData['limits']) {
            $limits = $submittedData['limits'];
            if ($limits) {
                foreach ($limits as $idLimit => $limit) {
                    if (isset($limit['action']) and $limit['action'] == 'none') {
                        $delete = $container->get(LimitClientsService::class)->delete($idLimit, $idClient);
                        if (!$delete['valid']) {
                            return $delete;
                        }
                        continue;
                    }
                    if ($limit['action'] == 'set') {
                        $insert = $container->get(LimitClientsService::class)->insert($idLimit, $limit['value'], $idClient);
                    } else {
                        $insert = $container->get(LimitClientsService::class)->increase($idLimit, $limit['value'], $idClient);
                    }
                    if (!$insert['valid']) {
                        return $insert;
                    }
                }
            }
        } elseif (isset($submittedData['product']) and !isset($submittedData['dont_update_limits'])) {
            $productLimits = $container->get(ShopProductsLimitsService::class)->getByProduct($submittedData['product']['id']);
            if ($productLimits) {
                foreach ($productLimits as $productLimit) {
                    if ($productLimit->getAction() == 'none' or !$productLimit->getLimit()) {
                        continue;
                    }

                    $qty = 1;
                    if (isset($submittedData['transactionProduct']) and isset($submittedData['transactionProduct']['quantity'])) {
                        $qty = $submittedData['transactionProduct']['quantity'];
                    }
                    if (!$qty) {
                        $qty = 1;
                    }

                    if ($productLimit->getAction() == 'set') {
                        $insert = $container->get(LimitClientsService::class)->insert($productLimit->getLimit()->getId(), ($productLimit->getValue() * $qty), $idClient);
                    } else {
                        $insert = $container->get(LimitClientsService::class)->increase($productLimit->getLimit()->getId(), ($productLimit->getValue() * $qty), $idClient);
                    }
                    if (!$insert['valid']) {
                        return $insert;
                    }
                }
            }
        }

        if (isset($_SESSION['universe_id']) and $_SESSION['universe_id'] === UNIVERSE_ADMIN_DEFAULT) {
            LogActionService::log('update_client');
        }

        return array('valid' => true);
    }

    /**
     * @param int $idClient
     * @param string $uniqId
     * @return array
     */
    public function updateClientUniqId(int $idClient, string $uniqId): array
    {
        $uniqId = permalink($uniqId);

        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas."));
        }

        if ($client->getUniqid() == $uniqId) {
            return array('valid' => false, 'message' => __("Ce client posséde déjà l'adresse") . ' : ' . $uniqId);
        }

        if (FILE_SYSTEM == FILE_SYSTEM_S3) {
            @set_time_limit(0);
            $s3Service = new MediasService();
            $duplicateDirectory = $s3Service->duplicateDirectory($client->getUniqid(), $uniqId);
            if (!$duplicateDirectory['valid']) {
                return array('valid' => false, 'message' => $duplicateDirectory['valid']);
            }
        }

        //mise à jour
        $client->setUniqid($uniqId);
        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la mise à jour du client.'));
        }

        //search customer
        $container = ContainerBuilderService::getInstance();
        $subscriptions = $container->get(ClientsSubscriptionsService::class)->getSubscriptionsByClient($idClient);
        if (!$subscriptions) {
            return array('valid' => true);
        }

        foreach ($subscriptions as $subscription) {
            if (!$subscription->getCustomer()) {
                continue;
            }

            $customer = $subscription->getCustomer();
            if (!$customer) {
                continue;
            }

            $data = json_decode($customer->getDatas(), true);
            if (isset($data['uniqid'])) {
                $data['uniqid'] = $uniqId;
                $customer->setDatas(json_encode($data));
                try {
                    $this->persistAndFlush($customer);
                } catch (\Exception $e) {
                    return array('valid' => false, 'message' => __('Erreur lors de la mise à jour du client.'));
                }
            }
        }

        return array('valid' => true);
    }

    /**
     * @param int $idClient
     * @return array
     */
    public function active_client(int $idClient): array
    {
        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas."));
        }

        $client->setActive(true);
        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'activation du client."));
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_ACTIVATE, null, $idClient);

        return array('valid' => true);
    }

    /**
     * @param int $idClient
     * @return array
     */
    public function desactive_client(int $idClient): array
    {
        $client = $this->getClientById($idClient);
        if (!$client) {
            return ['valid' => false, 'message' => __("Ce client n'existe pas.")];
        }

        $client->setActive(false);
        $client->setDateDesactivation(new \DateTime());
        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la désactivation du client.')];
        }

        //disable subscriptions if needed
        $container = ContainerBuilderService::getInstance();
        $cancelAllSubscriptions = $container->get(ClientsSubscriptionsService::class)->cancelAllSubscriptions($idClient);
        if (!$cancelAllSubscriptions['valid']) {
            return ['valid' => false, 'message' => $cancelAllSubscriptions['message']];
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_DISABLE, null, $idClient);

        return ['valid' => true];
    }

    /**
     * @param int $idClient
     * @param string $subscription
     * @return array
     */
    public function setClientOnPause(int $idClient, string $subscription = ''): array
    {
        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas."));
        }

        $client->setOnPause(true);
        if ($subscription) {
            $client->setSubscription($subscription);
        }
        $client->setDateDesactivation(new \DateTime());
        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la mise en pause du client.'));
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_PAUSE_ON, null, $idClient);

        return array('valid' => true);
    }

    /**
     * @param int $idClient
     * @return array
     */
    public function setClientOffPause(int $idClient): array
    {
        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas."));
        }

        $client->setOnPause(false);
        $client->setDateDesactivation(null);
        try {
            $this->persistAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la suppression de la pause du client.'));
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_PAUSE_OFF, null, $idClient);

        return array('valid' => true);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function delete_some_clients(array $submittedData): array
    {
        if (!$submittedData or !isset($submittedData['clients'])) {
            return array('valid' => false, 'message' => __('Erreur : aucun client sélectionné.'));
        }

        $clients = filter_var($submittedData['clients'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
        if (!$clients) {
            return array('valid' => false, 'message' => __('Erreur : aucun client sélectionné.'));
        }

        $error = '';
        foreach ($clients as $idClient) {
            if ($idClient != $_SESSION['client']['id'] and $idClient != CLIENT_MASTER) {
                $deleteClient = $this->delete_client($idClient);
                if (!$deleteClient['valid']) {
                    $error .= $deleteClient['message'] . '<br>';
                }
            }
        }

        if ($error) {
            return array('valid' => false, 'message' => __('Erreur lors de la suppression de certains clients.') . '<br>' . $error);
        }

        return array('valid' => true);
    }

    /**
     * @param int $idClient
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function delete_client(int $idClient): array
    {
        if (!RightsService::isSuperAdmin() or !RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SAV)) {
            return array('valid' => false, 'message' => __('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.'));
        }
        if (!$idClient or $idClient == CLIENT_MASTER) {
            return array('valid' => false, 'message' => __('Ce client ne peut être supprimé'));
        }

        $client = $this->getClientById($idClient);
        if (!$client) {
            return array('valid' => false, 'message' => __("Ce client n'existe pas"));
        }

        $clientUniqid = $client->getUniqid();
        try {
            $this->deleteAndFlush($client);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la suppression du client.'));
        }

        LogActionService::log('delete_client');

        //suppression du dossier medias
        $deleteDirectory = MediasComponent::deleteDirectory($clientUniqid);
        if (!$deleteDirectory['valid']) {
            return array('valid' => false, 'message' => __('Impossible de supprimer le dossier /medias/%s', $clientUniqid));
        }

        //check if client has errors
        $container = ContainerBuilderService::getInstance();
        $clientErrors = $container->get(ClientsErrorService::class)->getClientErrorsByClientId($idClient);
        if ($clientErrors) {
            foreach ($clientErrors as $clientError) {
                $container->get(ClientsErrorService::class)->deleteClientError($clientError->getId());
            }
        }

        return array('valid' => true);
    }

    /**
     * @param string $uniqId
     * @return array
     */
    public function checkAccount(string $uniqId): array
    {
        if (!isset($_SESSION['check_accounts'])) {
            $_SESSION['check_accounts'] = 1;
        }
        $_SESSION['check_accounts']++;

        if ($_SESSION['check_accounts'] > 5) {
            return ['valid' => false, 'message' => __('Fonctionnalité indisponible temporairement.')];
        }

        $uniqId = filter_var($uniqId, FILTER_UNSAFE_RAW);
        $uniqId = permalink($uniqId);
        if (!$uniqId) {
            return ['valid' => false, 'message' => __('Veuillez indiquer l\'adresse de votre compte %s', APP_NAME)];
        }

        if (!$this->isValidUniqId($uniqId)) {
            return ['valid' => false, 'message' => __('Cette adresse n\'existe pas.')];
        }

        $client = $this->getClientByUniqId($uniqId);
        if (!$client or !$client->getActive()) {
            return ['valid' => false, 'message' => __('Cette adresse n\'existe pas.')];
        }

        return ['valid' => true, 'redirect' => Tools::makeClientLink($client->getUniqid(), 'app', 'index')];
    }
}
