<?php
namespace MatGyver\Services\Clients;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Client\History\ClientHistory;
use MatGyver\Entity\Client\Subscription\ClientSubscription;
use MatGyver\Entity\PaymentMethod\PaymentMethod;
use MatGyver\Entity\PaymentMethod\PaymentMethodCharge;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Helpers\Entities;
use MatGyver\Repository\Client\Subscription\ClientSubscriptionRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\Payments\ClientsPaymentsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Limit\LimitClientsService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Logger\TransactionLoggerService;
use MatGyver\Services\PasswordService;
use MatGyver\Services\PaymentsMethodsService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Product\ShopProductsLimitsService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopCustomersService;
use MatGyver\Services\Shop\ShopPaymentsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Subscription\SubscriptionService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ClientsSubscriptionsService
 * @package MatGyver\Services\Clients
 * @property ClientSubscriptionRepository $repository
 * @method ClientSubscriptionRepository getRepository()
 */
class ClientsSubscriptionsService extends BaseEntityService
{
    /**
     * @var SubscriptionService
     */
    private $subscriptionService;

    /**
     * @var ShopTransactionService
     */
    private $shopTransactionService;

    /**
     * @var ShopProductsService
     */
    private $shopProductsService;

    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * @var ShopVatRulesService
     */
    private $shopVatRulesService;

    /**
     * @var ShopPaymentsService
     */
    private $shopPaymentsService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var ClientsPaymentsService
     */
    private $clientsPaymentsService;

    /**
     * @var ClientsTransactionsService
     */
    private $clientsTransactionsService;

    /**
     * @var LimitClientsService
     */
    private $limitClientsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * ClientsSubscriptionsService constructor.
     * @param EntityManager $em
     * @param SubscriptionService $subscriptionService
     * @param ShopTransactionService $shopTransactionService
     * @param ShopProductsService $shopProductsService
     * @param ShopCustomersService $shopCustomersService
     * @param ShopVatRulesService $shopVatRulesService
     * @param ShopPaymentsService $shopPaymentsService
     * @param ClientsService $clientsService
     * @param ClientsPaymentsService $clientsPaymentsService
     * @param ClientsTransactionsService $clientsTransactionsService
     * @param LimitClientsService $limitClientsService
     * @param UsersService $usersService
     */
    public function __construct(
        EntityManager $em,
        SubscriptionService $subscriptionService,
        ShopTransactionService $shopTransactionService,
        ShopProductsService $shopProductsService,
        ShopCustomersService $shopCustomersService,
        ShopVatRulesService $shopVatRulesService,
        ShopPaymentsService $shopPaymentsService,
        ClientsService $clientsService,
        ClientsPaymentsService $clientsPaymentsService,
        ClientsTransactionsService $clientsTransactionsService,
        LimitClientsService $limitClientsService,
        UsersService $usersService
    ) {
        $this->em = $em;
        $this->repository = $this->em->getRepository(ClientSubscription::class);
        $this->subscriptionService = $subscriptionService;
        $this->shopTransactionService = $shopTransactionService;
        $this->shopProductsService = $shopProductsService;
        $this->shopCustomersService = $shopCustomersService;
        $this->shopVatRulesService = $shopVatRulesService;
        $this->shopPaymentsService = $shopPaymentsService;
        $this->clientsService = $clientsService;
        $this->clientsPaymentsService = $clientsPaymentsService;
        $this->clientsTransactionsService = $clientsTransactionsService;
        $this->limitClientsService = $limitClientsService;
        $this->usersService = $usersService;
    }

    /**
     * @param int $idSubscription
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionById(int $idSubscription, ?int $idClient = null): ?ClientSubscription
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['id' => $idSubscription, 'client' => $idClient]);
    }

    /**
     * @param string $transactionReference
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionByTransaction(string $transactionReference, int $idClient = null): ?ClientSubscription
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['transactionReference' => $transactionReference, 'client' => $idClient]);
    }

    /**
     * @param int      $productId
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionByProduct(int $productId, int $idClient = null): ?ClientSubscription
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['product' => $productId, 'client' => $idClient]);
    }

    /**
     * @param int      $productId
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionActiveByProduct(int $productId, int $idClient = null): ?ClientSubscription
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['product' => $productId, 'status' => 'active', 'client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return ClientSubscription[]
     */
    public function getSubscriptionsByClient(int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return ClientSubscription[]
     */
    public function getAllSubscriptionsActive(int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['status' => 'active', 'client' => $idClient]);
    }

    /**
     * @param  array $submittedData
     * @return array
     */
    public function insertSubscription(array $submittedData): array
    {
        $clientSubscription = new ClientSubscription();

        $clientSubscription->setClient($this->clientsService->getClientById($submittedData['client_id']));
        $clientSubscription->setProduct($this->shopProductsService->getRepository()->findOneBy(['id' => $submittedData['product_id'], 'client' => CLIENT_MASTER]));
        $clientSubscription->setProductName($submittedData['product_name']);
        $clientSubscription->setSubscriptionId($submittedData['subscription_id']);
        $clientSubscription->setTypeSubscription($submittedData['type_subscription']);
        $clientSubscription->setTransactionReference($submittedData['transaction_reference']);
        $clientSubscription->setCustomer($this->shopCustomersService->getRepository()->findOneBy(['id' => $submittedData['customer_id'], 'client' => CLIENT_MASTER]));
        $clientSubscription->setAmountTaxExcl($submittedData['amount_tax_excl']);
        $clientSubscription->setAmountTaxIncl($submittedData['amount_tax_incl']);
        $clientSubscription->setStatus('active');
        $clientSubscription->setDate(new \DateTime());
        try {
            $this->persistAndFlush($clientSubscription);
        } catch (\Exception $e) {
            LoggerService::logError('Erreur lors de l\'enregistrement de l\'abonnement : ' . $e->getMessage());
            return array('valid' => false, 'message' => __('Erreur lors de l\'enregistrement de l\'abonnement'));
        }

        $idSubscription = $clientSubscription->getId();
        ClientsHistoriesService::add(ClientHistory::ACTION_SUBSCRIPTION_ADD, $idSubscription, $submittedData['client_id']);

        return array('valid' => true, 'id_subscription' => $idSubscription);
    }

    /**
     * @param int $idSubscription
     * @return float
     */
    public function getAmountLeft(int $idSubscription): float
    {
        $amountLeft = 0;

        $subscription = $this->getSubscriptionById($idSubscription);
        if (!$subscription) {
            return $amountLeft;
        }

        $product = $subscription->getProduct();
        if (!$product) {
            return $amountLeft;
        }

        $lastTransaction = $this->clientsTransactionsService->getLastTransactionBySubscription($idSubscription);
        if (!$lastTransaction or !$lastTransaction->getTransaction() or !$lastTransaction->getTransaction()->getValid()) {
            return $amountLeft;
        }

        $transaction = $lastTransaction->getTransaction();
        $subscriptionAmount = $transaction->getAmountTaxExcl();

        $transactionProducts = $transaction->getTransactionProducts();
        if ($transactionProducts) {
            foreach ($transactionProducts as $transactionProduct) {
                if ($transactionProduct->getProduct() === $product) {
                    $subscriptionAmount = $subscriptionAmount / $transactionProduct->getQuantity();
                    break;
                }
            }
        }

        $dateNow = date('Y-m-d');
        $dateEndSubscription = date('Y-m-d', strtotime($transaction->getDate()->format('Y-m-d H:i:s') . ' +' . $product->getDuration()));

        //subscription in progress -> get amount left
        if ($dateNow < $dateEndSubscription) {
            $dateStartSubscription = new \DateTime(date('Y-m-d', $transaction->getDate()->getTimestamp()));
            $dateEndSubscription = new \DateTime($dateEndSubscription);

            $interval = $dateStartSubscription->diff($dateEndSubscription);
            $nbDaysSubscription = (int) $interval->format('%a');

            $dateNow = new \DateTime($dateNow);
            $interval = $dateEndSubscription->diff($dateNow);
            $nbDaysLeft = (int) $interval->format('%a');

            $amountLeft = (float) ($nbDaysLeft / $nbDaysSubscription) * $subscriptionAmount;
            if ($amountLeft < 0) {
                $amountLeft = 0;
            }
        }

        return $amountLeft;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function changeSubscription(array $submittedData): array
    {
        $logContext = ['log_key' => 'client_change_subscription.log'];
        $log = "changeSubscription\n";
        $log .= date('Y-m-d H:i:s') . "\n";
        $log .= "User : " . $_SESSION['user']['id'] . "\n";
        $log .= json_encode($submittedData) . "\n\n";

        $idClientSubscription = 0;
        $permalink = '';
        $qty = 1;

        if (isset($submittedData['id_subscription'])) {
            $idClientSubscription = filter_var($submittedData['id_subscription'], FILTER_VALIDATE_INT);
        }
        if (isset($submittedData['permalink'])) {
            $permalink = filter_var($submittedData['permalink'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['qty'])) {
            $qty = filter_var($submittedData['qty'], FILTER_VALIDATE_INT);
        }
        if (!$qty) {
            $qty = 1;
        }

        $log .= "Change subscription $idClientSubscription to product $permalink (qty = $qty) for client " . $_SESSION['client']['id'] . "\n";

        if (!$idClientSubscription or !$permalink) {
            $log .= "Pas de idClientSubscription ou de permalink\n";
            TransactionLoggerService::logError($log, $logContext);
            return array('valid' => false, 'message' => __('Une erreur est survenue.'));
        }

        $subscription = $this->getSubscriptionById($idClientSubscription);
        if (!$subscription) {
            $log .= "L'abonnement $idClientSubscription n'existe pas\n";
            TransactionLoggerService::logError($log, $logContext);
            return array('valid' => false, 'message' => __('Cet abonnement n\'existe pas.'));
        }

        $product = $this->shopProductsService->getProductByPermalink($permalink, CLIENT_MASTER);
        if (!$product) {
            $log .= "Le produit $permalink n'existe pas\n";
            TransactionLoggerService::logError($log, $logContext);
            return array('valid' => false, 'message' => __('Ce produit n\'existe pas.'));
        }
        $productInfos = Entities::asArray($product);

        $upgradeSubscription = false;
        $productSubscription = $this->getSubscriptionActiveByProduct($product->getId());
        if ($productSubscription) {
            $upgradeSubscription = true;
        }

        $container = ContainerBuilderService::getInstance();
        if ($upgradeSubscription) {
            $canUpgrade = false;
            $productLimits = $container->get(ShopProductsLimitsService::class)->getByProduct($product->getId());
            if ($productLimits) {
                foreach ($productLimits as $productLimit) {
                    if ($productLimit->getAction() == 'increment') {
                        $canUpgrade = true;
                        break;
                    }
                }
            }
            if (!$canUpgrade) {
                //renouvellement d'abonnement
                $upgradeSubscription = false;
            }
        }

        $idProduct = $product->getId();
        $idSubscription = $subscription->getSubscriptionId();

        //get prorata amount
        $prorataAmountTaxExcl = $this->getAmountLeft($idClientSubscription);
        if (isset($submittedData['no_prorata'])) {
            $prorataAmountTaxExcl = 0;
        }

        $vatRate = $this->shopVatRulesService->getVatRateByClient($idProduct);
        if ($vatRate) {
            $productInfos['price_tax_incl'] = $product->getPriceTaxExcl() * (1 + $vatRate / 100);
        }

        $toPayAmountTaxExcl = $productInfos['price_tax_excl'] - $prorataAmountTaxExcl;
        if ($upgradeSubscription and $prorataAmountTaxExcl) {
            //client need to pay the amount left for the same product
            $toPayAmountTaxExcl = $prorataAmountTaxExcl;
        }
        if ($toPayAmountTaxExcl <= 0) {
            $toPayAmountTaxExcl = 0;
        }
        if ($qty < 0) {
            $toPayAmountTaxExcl = 0;
        }

        if ($qty > 1) {
            $toPayAmountTaxExcl += ($product->getPriceTaxExcl() * ($qty - 1));
            if ($upgradeSubscription and $prorataAmountTaxExcl) {
                //client need to pay the amount left for the same product
                $toPayAmountTaxExcl = $prorataAmountTaxExcl * $qty;
            }
        }

        if ($upgradeSubscription and $prorataAmountTaxExcl) {
            //product price = 1€ / prorata = 0.20 so amount to pay = 0.20
            //this means that prorata must be equal to 0.80 so that discount amount will be 0.80
            $prorataAmountTaxExcl = ($product->getPriceTaxExcl() - $prorataAmountTaxExcl) * $qty;
        }

        $toPayAmount = $toPayAmountTaxExcl;
        if ($vatRate) {
            $toPayAmount = $toPayAmountTaxExcl * (1 + $vatRate / 100);
            $toPayAmount = number_format($toPayAmount, 2, '.', '');
        }

        if ($subscription->isInTrial()) {
            $toPayAmount = 0;
            $log .= "subscription is in trial period --> amount = $toPayAmount\n";
        }

        $log .= "toPayAmount : $toPayAmount\n";

        if ($upgradeSubscription and $qty < 0) {
            //client reduced his subscription
            $log .= "Reduce client subscription\n";

            $reduceSubscription = $this->reduceSubscription($productSubscription, $qty, $vatRate);
            if (isset($reduceSubscription['log'])) {
                $log .= $reduceSubscription['log'];
            }
            $log .= "FIN\n";
            TransactionLoggerService::logError($log, $logContext);

            if (!$reduceSubscription['valid']) {
                return $reduceSubscription;
            }

            return array('valid' => true, 'message' => __('Votre abonnement a été mis à jour.'), 'id_order' => '');
        }

        if ($toPayAmount > 0 and $toPayAmount < 0.5) {
            $paymentMethod = $container->get(PaymentsMethodsService::class)->getDefault();
            if ($paymentMethod and $paymentMethod->getType() == PaymentMethod::TYPE_STRIPE) {
                $toPayAmount = 0.5;
                $log .= "Stripe : montant_prelevement set to : $toPayAmount\n";
            }
        }

        $makePayment = $this->clientsPaymentsService->makePayment($product, $productInfos, $qty, $toPayAmount, $vatRate, $prorataAmountTaxExcl, $upgradeSubscription, $idClientSubscription);
        if (!$makePayment['valid']) {
            $log .= "Erreur makePayment : " . $makePayment['message'] . "\n";
            if (isset($makePayment['log']) and $makePayment['log']) {
                $log .= $makePayment['log'];
            }
            if (isset($makePayment['redirect_url']) and $makePayment['redirect_url']) {
                $log .= "redirect to : " . $makePayment['redirect_url'];
                TransactionLoggerService::logError($log, $logContext);
                return array('valid' => false, 'message' => $makePayment['message'], 'redirect_url' => $makePayment['redirect_url']);
            }

            TransactionLoggerService::logError($log, $logContext);

            return array('valid' => false, 'message' => $makePayment['message']);
        }

        $idOrder = $makePayment['id_order'];
        $log .= "MakePayment done for order $idOrder\n";
        TransactionLoggerService::logError($log, $logContext);

        return ['valid' => true, 'message' => __('Votre abonnement a été mis à jour.'), 'id_order' => $idOrder];
    }

    public function validateCharge(PaymentMethodCharge $charge): array
    {
        $logContext = ['log_key' => 'client_validate_charge.log'];
        $log = "validateCharge\n";
        $log .= date('Y-m-d H:i:s') . "\n";

        //step 1
        $postProcessMakePayment = $this->clientsPaymentsService->postProcessMakePayment($charge);
        $log .= $postProcessMakePayment['log'] . "\n";
        if (!$postProcessMakePayment['valid']) {
            TransactionLoggerService::logError($log, $logContext);
            return $postProcessMakePayment;
        }

        $idSubscriptionNew = $postProcessMakePayment['idSubscriptionNew'];
        $log .= "idSubscriptionNew : $idSubscriptionNew\n";

        $checkout = json_decode($charge->getCheckout(), true);
        $idClientSubscription = $checkout['idClientSubscription'];

        //step 2
        if ($idClientSubscription) {
            $log .= "postProcessChangeSubscription\n";
            $postProcessSubscription = $this->postProcessChangeSubscription($charge, $idSubscriptionNew);
        } else {
            $log .= "postProcessAddSubscription\n";
            $postProcessSubscription = $this->postProcessAddSubscription($charge, $idSubscriptionNew);
        }
        $log .= $postProcessSubscription['log'] . "\n";
        if (!$postProcessSubscription['valid']) {
            $log .= $postProcessSubscription['message'] . "\n";
            TransactionLoggerService::logError($log, $logContext);
            return ['valid' => false, 'message' => $postProcessSubscription['message']];
        }

        $log .= "validateCharge done for charge " . $charge->getTransactionReference() . "\n";
        TransactionLoggerService::logError($log, $logContext);

        return ['valid' => true, 'log' => $log];
    }

    /**
     * @param PaymentMethodCharge $charge
     * @param int $idSubscriptionNew
     * @return array
     */
    public function postProcessAddSubscription(PaymentMethodCharge $charge, int $idSubscriptionNew): array
    {
        $checkout = json_decode($charge->getCheckout(), true);
        $clientId = $checkout['clientId'];
        $productId = $checkout['productId'];
        $productInfos = $checkout['productInfos'];
        $transactionReference = $charge->getTransactionReference();

        $log = '';

        //NOTE
        //on vient de prélever le client
        //on a ensuite envoyé un webhook (/stripe/validation/, /braintree/validation/ etc.)
        // -> ce webhook a créé la transaction dans clients_transactions
        // -> ce webhook a créé un abonnement si nécessaire dans clients_subscriptions
        //PAR CONTRE l'abonnement créé dans clients_subscriptions n'a pas d'id_subscription relié à stripe_subscriptions, etc.

        $container = ContainerBuilderService::getInstance();
        $product = $container->get(ShopProductsService::class)->getProductById($productId, CLIENT_MASTER);
        if (!$product) {
            return ['valid' => false, 'message' => __('Ce produit n\'existe plus.'), 'log' => $log];
        }

        $idClientSubscription = 0;

        if ($product->getRecurring()) {
            //l'abonnement a déjà été créé par le webhook
            //mise à jour de l'abonnement concerné
            $getClientSubscription = $this->getSubscriptionByTransaction($transactionReference, $clientId);
            if (!$getClientSubscription) {
                //erreur
                LoggerService::logError('Erreur lors de la récupération de l\'abonnement lié à la transaction ' . $transactionReference);
            } else {
                //update subscription
                $getClientSubscription->setSubscriptionId($idSubscriptionNew);
                if ($getClientSubscription->getAmountTaxExcl() != $productInfos['price_tax_excl']) {
                    $getClientSubscription->setAmountTaxExcl($productInfos['price_tax_excl']);
                }
                if ($getClientSubscription->getAmountTaxIncl() != $productInfos['price_tax_incl']) {
                    $getClientSubscription->setAmountTaxIncl($productInfos['price_tax_incl']);
                }
                try {
                    $this->persistAndFlush($getClientSubscription);
                } catch (\Exception $e) {
                    LoggerService::logError('Erreur lors de la mise à jour de l\'abonnement lié à la transaction ' . $transactionReference . ' : ' . $e->getMessage());
                }

                $idClientSubscription = $getClientSubscription->getId();
            }
        }

        //normalement la transaction a déjà été créée par le webhook
        //mise à jour de l'id_subscription si besoin
        if ($idClientSubscription) {
            $transaction = $this->shopTransactionService->getTransactionByReference($transactionReference, CLIENT_MASTER);
            if ($transaction) {
                $update = $this->clientsTransactionsService->updateTransactionSubscription($transaction->getId(), $idClientSubscription, $clientId);
                if (!$update['valid']) {
                    LoggerService::logError($update['message']);
                }
            }
        }

        $log .= "FIN\n";

        return ['valid' => true, 'log' => $log];
    }

    /**
     * @param PaymentMethodCharge $charge
     * @param int $idSubscriptionNew
     * @return array
     */
    public function postProcessChangeSubscription(PaymentMethodCharge $charge, int $idSubscriptionNew): array
    {
        $checkout = json_decode($charge->getCheckout(), true);
        $clientId = $checkout['clientId'];
        $upgradeSubscription = $checkout['upgradeSubscription'];
        $idClientSubscription = $checkout['idClientSubscription'];

        $transactionReference = $charge->getTransactionReference();

        $log = '';

        //cancel old subscription
        if (!$upgradeSubscription) {
            $log .= "Désactivation de l'abonnement client $idClientSubscription\n";
            $cancelSubscription = $this->cancelSubscription($idClientSubscription, $clientId);
            if (!$cancelSubscription['valid']) {
                return ['valid' => false, 'message' => $cancelSubscription['message'], 'log' => $log];
            }

            //NOTE
            //on vient de prélever le client
            //on a ensuite envoyé un webhook (/stripe/validation/, /braintree/validation/ etc.)
            // -> ce webhook a créé la transaction dans clients_transactions
            // -> ce webhook a créé un abonnement si nécessaire dans clients_subscriptions
            //PAR CONTRE l'abonnement créé dans clients_subscriptions n'a pas d'id_subscription relié à stripe_subscriptions, etc.

            $idClientSubscription = 0;
            $log .= "Recherche de l'abonnement lié à la transaction $transactionReference\n";
            $getSubscription = $this->getSubscriptionByTransaction($transactionReference, $clientId);
            if (!$getSubscription) {
                //erreur
                $log .= "Impossible de trouver l'abonnement lié à la transaction $transactionReference\n";
                LoggerService::logError('Erreur lors de la récupération de l\'abonnement lié à la transaction ' . $transactionReference);
            } else {
                //update subscription
                $log .= "Abonnement trouvé\n";
                $getSubscription->setSubscriptionId($idSubscriptionNew);
                try {
                    $this->persistAndFlush($getSubscription);
                } catch (\Exception $e) {
                    LoggerService::logError('Erreur lors de la mise à jour de l\'abonnement lié à la transaction ' . $transactionReference . ' : ' . $e->getMessage());
                }

                $idClientSubscription = $getSubscription->getId();
            }

            //normalement la transaction $idOrder a déjà été créée par le webhook
            //mise à jour de l'id_subscription si besoin
            if ($idClientSubscription) {
                $log .= "On a un nouvel abonnement client $idClientSubscription\n";
                $transaction = $this->shopTransactionService->getTransactionByReference($transactionReference, CLIENT_MASTER);
                if (!$transaction) {
                    $log .= "Impossible de trouver la transaction $transactionReference\n";
                } else {
                    $log .= "Mise à jour de la transaction client $transactionReference\n";
                    $update = $this->clientsTransactionsService->updateTransactionSubscription($transaction->getId(), $idClientSubscription, $clientId);
                    if (!$update['valid']) {
                        $log .= "Erreur lors de la mise à jour de la transaction client $transactionReference : " . $update['message'] . "\n";
                        LoggerService::logError($update['message']);
                    }
                }
            }
        }

        $log .= "FIN\n";

        return ['valid' => true, 'log' => $log];
    }

    /**
     * @param ClientSubscription $clientSubscription
     * @param int $qty
     * @param float $vatRate
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function reduceSubscription(ClientSubscription $clientSubscription, int $qty = 0, float $vatRate = 0): array
    {
        $log = "Reduce subscription " . $clientSubscription->getId() . " for client " . $clientSubscription->getClient()->getId() . "\n";
        $log .= "Qty = $qty\n";

        $product = $clientSubscription->getProduct();
        if (!$product) {
            return ['valid' => false, 'message' => __('Ce produit n\'existe plus.'), 'log' => $log];
        }

        //check limits before reducing subscription
        $productLimits = $product->getLimits();
        if ($productLimits) {
            foreach ($productLimits as $productLimit) {
                $limit = $productLimit->getLimit();
                if (!$limit) {
                    continue;
                }

                $clientLimit = $this->limitClientsService->getRepository()->findOneBy(['client' => $_SESSION['client']['id'], 'limit' => $limit]);
                if (!$clientLimit) {
                    continue;
                }

                $newValue = $clientLimit->getValue() + ($productLimit->getValue() * $qty);
                if (!$newValue) {
                    return ['valid' => false, 'message' => __('La valeur de la limite %s ne peut être égale à 0.', $limit->getName())];
                }

                /*if ($limit->getReference() == 'xxx') {
                    //check objects here
                }*/
            }
        }

        $newAmountTaxExcl = $clientSubscription->getAmountTaxExcl() + ($product->getPriceTaxExcl() * $qty);
        if (!$newAmountTaxExcl) {
            LoggerService::logError('Change subscription --> new amount = 0');
            return ['valid' => false, 'message' => __('Le montant du nouvel abonnement ne peut être nul.'), 'log' => $log];
        }

        $newAmountTaxIncl = $newAmountTaxExcl;
        if ($vatRate) {
            $newAmountTaxIncl = $newAmountTaxExcl * (1 + $vatRate / 100);
            $newAmountTaxIncl = number_format($newAmountTaxIncl, 2, '.', '');
        }

        $log .= "actual amount : " . $clientSubscription->getAmountTaxExcl() . " (" . $clientSubscription->getAmountTaxIncl() . " TTC)\n";
        $log .= "new amount : $newAmountTaxExcl ($newAmountTaxIncl TTC)\n";
        $clientSubscription->setAmountTaxExcl($newAmountTaxExcl);
        $clientSubscription->setAmountTaxIncl($newAmountTaxIncl);
        try {
            $this->persistAndFlush($clientSubscription);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue'), 'log' => $log];
        }

        $subscription = null;
        if ($clientSubscription->getSubscriptionId()) {
            $subscription = $this->subscriptionService->getSubscriptionByIdAndType($clientSubscription->getSubscriptionId(), $clientSubscription->getTypeSubscription(), CLIENT_MASTER);
        }
        if ($subscription) {
            $subscription->setAmount($newAmountTaxIncl);
            if ($clientSubscription->getTypeSubscription() == ShopTransaction::METHOD_STRIPE) {
                $intAmount = (int) (round($newAmountTaxIncl * 100));
                $subscription->setAmount($intAmount);
            }
            $this->em->persist($subscription);
            $this->em->flush();
        }

        //update limits
        $productLimits = $product->getLimits();
        if ($productLimits) {
            foreach ($productLimits as $productLimit) {
                $limit = $productLimit->getLimit();
                if (!$limit) {
                    continue;
                }

                $decrease = $this->limitClientsService->increase($limit->getId(), ($productLimit->getValue() * $qty));
                if (!$decrease['valid']) {
                    return $decrease;
                }
            }
        }

        return ['valid' => true, 'log' => $log];
    }

    /**
     * @param array $submittedData
     * @return array
     * @throws \Exception
     */
    public function addSubscription(array $submittedData): array
    {
        $permalink = '';
        if (isset($submittedData['permalink'])) {
            $permalink = filter_var($submittedData['permalink'], FILTER_UNSAFE_RAW);
        }

        $qty = 1;
        if (isset($submittedData['qty'])) {
            $qty = filter_var($submittedData['qty'], FILTER_VALIDATE_INT);
        }
        if (!$qty) {
            $qty = 1;
        }

        if (!$permalink) {
            return array('valid' => false, 'message' => __('Une erreur est survenue.'));
        }

        $product = $this->shopProductsService->getProductByPermalink($permalink, CLIENT_MASTER);
        if (!$product) {
            return array('valid' => false, 'message' => __('Ce produit n\'existe pas.'));
        }
        $productInfos = Entities::asArray($product);

        //calcul du montant à prélever
        $vatRate = $this->shopVatRulesService->getVatRateByClient($product->getId());

        $productInfos['price_tax_incl'] = $productInfos['price_tax_excl'];
        if ($vatRate) {
            $productInfos['price_tax_incl'] = $productInfos['price_tax_excl'] * (1 + $vatRate / 100);
        }

        if ($qty > 1) {
            $productInfos['price_tax_excl'] = $productInfos['price_tax_excl'] * $qty;
            $productInfos['price_tax_incl'] = $productInfos['price_tax_incl'] * $qty;
        }

        $toPayAmount = $productInfos['price_tax_incl'];
        $toPayAmount = number_format($toPayAmount, 2, '.', '');

        $nextDate = '';
        $payment = $this->shopPaymentsService->getRepository()->findOneBy(['product' => $product, 'active' => true, 'client' => CLIENT_MASTER]);
        if ($payment) {
            $checkout = json_decode($payment->getCheckout(), true);
            if (isset($checkout['trial_period']) and $checkout['trial_period']) {
                //can client get free trial
                $hadTrial = false;
                $subscriptions = $this->getSubscriptionsByClient($_SESSION['client']['id']);
                foreach ($subscriptions as $subscription) {
                    if ($subscription->hadTrial()) {
                        $hadTrial = true;
                        break;
                    }
                }

                if ($_SESSION['client']['id'] === 56) {
                    $hadTrial = true;
                }

                if (!$hadTrial) {
                    $toPayAmount = $checkout['trial_amount'];
                    $nextDate = date('Y-m-d', strtotime('+' . $checkout['trial_time'] . ' ' . $checkout['trial_timetype']));
                }
            }
        }

        $makePayment = $this->clientsPaymentsService->makePayment($product, $productInfos, $qty, $toPayAmount, $vatRate, 0, false, 0, $nextDate);
        if (!$makePayment['valid']) {
            $log = "Erreur addSubscription -> makePayment : " . $makePayment['message'] . "\n";
            if (isset($makePayment['log']) and $makePayment['log']) {
                $log .= $makePayment['log'];
            }
            $logContext = ['log_key' => 'client_add_subscription.log'];
            TransactionLoggerService::logError($log, $logContext);

            return array('valid' => false, 'message' => $makePayment['message']);
        }

        $idOrder = $makePayment['id_order'];

        return array('valid' => true, 'message' => __('Votre abonnement a bien été enregistré.'), 'id_order' => $idOrder);
    }

    /**
     * @param int|null $idClient
     * @return array
     */
    public function cancelAllSubscriptions(?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $subscriptions = $this->getAllSubscriptionsActive($idClient);
        if (!$subscriptions) {
            return ['valid' => true];
        }

        foreach ($subscriptions as $subscription) {
            $cancelSubscription = $this->cancelSubscription($subscription->getId(), $idClient);
            if (!$cancelSubscription['valid']) {
                return ['valid' => false, 'message' => $cancelSubscription['message']];
            }
        }

        return ['valid' => true];
    }

    /**
     * @param int $idClientSubscription
     * @param int|null $idClient
     * @return array
     */
    public function cancelSubscription(int $idClientSubscription, ?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $subscription = $this->getSubscriptionById($idClientSubscription, $idClient);
        if (!$subscription) {
            return array('valid' => false, 'message' => __('Abonnement inconnu'));
        }

        if ($subscription->getSubscriptionId()) {
            $getSubscription = $this->subscriptionService->getSubscriptionByIdAndType($subscription->getSubscriptionId(), $subscription->getTypeSubscription(), CLIENT_MASTER);
            if (!$getSubscription) {
                return array('valid' => false, 'message' => __('Abonnement inconnu'));
            }

            $desactive = $this->subscriptionService->desactiveSubscription($getSubscription->getId(), $subscription->getTypeSubscription(), 'appel de cancelSubscription', CLIENT_MASTER);
            if (!$desactive['valid']) {
                return array('valid' => false, 'message' => $desactive['message']);
            }
        }

        $subscription->setStatus('cancelled');
        try {
            $this->persistAndFlush($subscription);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de l\'annulation de l\'abonnement'));
        }

        if ($subscription->getProduct()) {
            $this->postProcessCancelSubscription($subscription->getProduct(), $idClient, $subscription->getProductName());
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_SUBSCRIPTION_CANCEL, $subscription->getId(), $idClient);

        return array('valid' => true, 'message' => __('L\'abonnement a été mis à jour'));
    }

    /**
     * @param ShopProduct $product
     * @param int|null $idClient
     * @param string $productName
     * @return array
     */
    public function postProcessCancelSubscription(ShopProduct $product, ?int $idClient = null, string $productName = ''): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $client = $this->clientsService->getClientById($idClient);
        if (!$client) {
            return ['valid' => false, 'message' => __('Ce client n\'existe pas.')];
        }

        $error = '';
        //if ($product->getPermalink() == 'xxxx') {
            //do some stuff
        //}

        if ($error) {
            LoggerService::logError("Error postProcessCancelSubscription (client $idClient / product " . $product->getId() . ") : " . $error);
        }

        //update client subscription
        $subscriptions = $this->getAllSubscriptionsActive($idClient);
        if ($subscriptions) {
            krsort($subscriptions);
            foreach ($subscriptions as $subscription) {
                if ($subscription->getProduct() and $subscription->getProduct()->getType() == ProductsEnum::TYPE_SUBSCRIPTION) {
                    $client->setSubscription($subscription->getProduct()->getName());
                    $this->persistAndFlush($client);
                    break;
                }
            }
        }

        return ['valid' => true];
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function subscriptionPause(array $submittedData): array
    {
        if (!isset($submittedData['subscription_cancel'])) {
            return array('valid' => false, 'message' => __('Veuillez confirmer la mise en pause de votre abonnement'));
        }

        $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
        if (!$password) {
            return array('valid' => false, 'message' => __('Veuillez entrer le mot de passe de l\'adresse email') . ' ' . $_SESSION['email']);
        }

        $user = $this->usersService->getUser();
        if (!$user) {
            return array('valid' => false, 'message' => __('Une erreur est survenue'));
        }
        if (!RightsService::isAdmin()) {
            return array('valid' => false, 'message' => __('Vous n\'avez pas les permissions nécessaires pour réaliser cette opération.'));
        }

        //test password
        $verifyPassword = PasswordService::verifyPassword($password, $user->getPassword());
        if (!$verifyPassword) {
            return array('valid' => false, 'message' => __('Mot de passe incorrect.'));
        }

        $subscription = $this->repository->getSubscriptionActiveByType(ProductsEnum::TYPE_SUBSCRIPTION);
        if (!$subscription or !$subscription->getSubscriptionId()) {
            return array('valid' => false, 'message' => __('Aucun abonnement en cours.'));
        }

        $idSubscription = $subscription->getId();
        $getSubscription = $this->subscriptionService->getSubscriptionByIdAndType($subscription->getSubscriptionId(), $subscription->getTypeSubscription(), CLIENT_MASTER);
        if (!$getSubscription) {
            return array('valid' => false, 'message' => __('Erreur : abonnement inconnu'));
        }

        $product = $this->shopProductsService->getActiveProductByType(ProductsEnum::TYPE_PAUSE, CLIENT_MASTER);
        if (!$product) {
            return array('valid' => false, 'message' => __('Cette page n\'est pas disponible pour l\'instant.'));
        }

        //new subscription
        $dataSubscription = array(
            'id_subscription' => $idSubscription,
            'permalink' => $product->getPermalink(),
            'qty' => 1,
            'no_prorata' => true,
        );
        $newSubscription = $this->changeSubscription($dataSubscription);
        if (!$newSubscription['valid']) {
            return array('valid' => false, 'message' => $newSubscription['message']);
        }

        //set client on pause
        $client = $subscription->getClient();
        $this->clientsService->setClientOnPause($client->getId(), $product->getName());

        //cancel other subscriptions
        $subscriptions = $this->getAllSubscriptionsActive();
        if ($subscriptions) {
            foreach ($subscriptions as $subscription) {
                if ($subscription->getProduct() and $subscription->getProduct()->getId() == $product->getId()) {
                    continue;
                }

                $cancelSubscription = $this->cancelSubscription($subscription->getId(), $_SESSION['client']['id']);
                if (!$cancelSubscription['valid']) {
                    return array('valid' => false, 'message' => $cancelSubscription['message']);
                }
            }
        }

        $_SESSION['client']['on_pause'] = true;

        return array('valid' => true, 'message' => __('Votre compte a bien été mis en pause.'));
    }

    /**
     * @param ClientSubscription $clientSubscription
     * @return array
     */
    public function isEditable(ClientSubscription $clientSubscription): array
    {
        $product = $clientSubscription->getProduct();
        if (!$product) {
            return ['valid' => false, 'message' => __('Ce produit n\'existe plus.')];
        }
        if (!$product->getEditable()) {
            return ['valid' => false, 'message' => __('Cet abonnement ne peut être désactivé.')];
        }
        if ($clientSubscription->getStatus() == ClientSubscription::STATUS_CANCELLED) {
            return ['valid' => false, 'message' => __('Cet abonnement est désactivé.')];
        }

        return ['valid' => true];
    }

    /**
     * @param ClientSubscription $clientSubscription
     * @return array
     */
    public function isCancellable(ClientSubscription $clientSubscription): array
    {
        $product = $clientSubscription->getProduct();
        if (!$product) {
            return ['valid' => false, 'message' => __('Ce produit n\'existe plus.')];
        }
        if (!$product->getCancellable()) {
            return ['valid' => false, 'message' => __('Cet abonnement ne peut être désactivé.')];
        }
        if ($clientSubscription->getStatus() == ClientSubscription::STATUS_CANCELLED) {
            return ['valid' => false, 'message' => __('Cet abonnement est déjà désactivé.')];
        }

        return ['valid' => true];
    }
}
