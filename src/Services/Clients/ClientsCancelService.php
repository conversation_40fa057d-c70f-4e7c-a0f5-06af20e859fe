<?php

namespace MatG<PERSON>ver\Services\Clients;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Entity\Client\Cancel\ClientCancel;
use MatG<PERSON>ver\Entity\Client\History\ClientHistory;
use MatGyver\Enums\StatusesEnum;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Client\Cancel\ClientCancelRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\PasswordService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ClientsCancelService
 * @package MatGyver\Services\Clients
 * @property ClientCancelRepository $repository
 * @method ClientCancelRepository getRepository()
 */
class ClientsCancelService extends BaseEntityService
{
    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var ClientsSubscriptionsService
     */
    private $clientsSubscriptionsService;

    /**
     * @var ClientsTransactionsService
     */
    private $clientsTransactionsService;

    /**
     * @var ClientsErrorService
     */
    private $clientsErrorService;

    /**
     * ClientService constructor.
     * @param EntityManager $em
     * @param MailSender $mailSender
     * @param UsersService $usersService
     * @param ClientsService $clientsService
     * @param ClientsSubscriptionsService $clientsSubscriptionsService
     * @param ClientsTransactionsService $clientsTransactionsService
     * @param ClientsErrorService $clientsErrorService
     */
    public function __construct(
        EntityManager $em,
        MailSender $mailSender,
        UsersService $usersService,
        ClientsService $clientsService,
        ClientsSubscriptionsService $clientsSubscriptionsService,
        ClientsTransactionsService $clientsTransactionsService,
        ClientsErrorService $clientsErrorService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(ClientCancel::class);
        $this->mailSender = $mailSender;
        $this->usersService = $usersService;
        $this->clientsService = $clientsService;
        $this->clientsSubscriptionsService = $clientsSubscriptionsService;
        $this->clientsTransactionsService = $clientsTransactionsService;
        $this->clientsErrorService = $clientsErrorService;
    }

    /**
     * @return array
     */
    public static function getReasons(): array
    {
        return [
            'none' => __('Sélectionnez une raison'),
            'price' => __('L\'abonnement est trop cher'),
            'too_hard' => __('C\'est trop difficile à utiliser'),
            'bugs' => __('J\'ai rencontré trop de bugs à l\'utilisation'),
            'competitor' => __('J\'ai choisi un concurrent'),
            'temporary' => __('Je reviendrai plus tard'),
            'helpdesk' => __('Je n\'ai pas eu de réponse du support et/ou je n\'ai pas eu la bonne réponse'),
            'functions' => __('Il manque des fonctionnalités dont j\'ai besoin'),
            'other' => __('Autre raison, je l\'indique ci-dessous'),
        ];
    }

    /**
     * @param string $reason
     * @return string|null
     */
    public static function getReason(string $reason): ?string
    {
        $reasons = self::getReasons();
        return $reasons[$reason] ?? null;
    }

    /**
     * @param string $reason
     * @return string
     */
    public function generateSelectReasons(string $reason = ''): string
    {
        return SelectService::render(self::getReasons(), $reason);
    }

    /**
     * @param int $idSubscriptionCancel
     * @return ClientCancel|null
     */
    public function getSubscriptionCancelById(int $idSubscriptionCancel): ?ClientCancel
    {
        return $this->repository->findWoClient($idSubscriptionCancel);
    }

    /**
     * @param int $idClient
     * @return ClientCancel|null
     */
    public function getSubscriptionCancelByIdClient(int $idClient): ?ClientCancel
    {
        return $this->repository->findOneBy(['client' => $idClient]);
    }

    /**
     * @param int $idClient
     * @return array|null
     */
    public function getAllSubscriptionsCancelByIdClient(int $idClient): ?array
    {
        return $this->repository->findBy(['client' => $idClient]);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function subscriptionCancel(array $submittedData): array
    {
        if (!isset($submittedData['subscription_cancel'])) {
            return array('valid' => false, 'message' => __('Veuillez confirmer l\'annulation de votre abonnement'));
        }

        if (!$_SESSION['client']['id'] or $_SESSION['client']['id'] == CLIENT_MASTER) {
            return array('valid' => false, 'message' => __('Opération non autorisée'));
        }

        $cancelReason = filter_var($submittedData['cancel_reason'], FILTER_UNSAFE_RAW);
        $reason = filter_var($submittedData['reason'], FILTER_UNSAFE_RAW);
        $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);

        if (!$cancelReason) {
            return array('valid' => false, 'message' => __('Veuillez sélectionner une raison'));
        }
        if (!$password) {
            return array('valid' => false, 'message' => __('Veuillez entrer le mot de passe de l\'adresse email') . ' ' . $_SESSION['email']);
        }

        $client = $this->clientsService->getClient();
        if (!$client) {
            return array('valid' => false, 'message' => __('Une erreur est survenue'));
        }

        $user = $this->usersService->getUser();
        if (!$user) {
            return array('valid' => false, 'message' => __('Une erreur est survenue'));
        }
        if (!RightsService::isAdmin()) {
            return array('valid' => false, 'message' => __('Vous n\'avez pas les permissions nécessaires pour réaliser cette opération.'));
        }
        if (!$user->getAdmin()) {
            return array('valid' => false, 'message' => __('Seul le propriétaire de ce compte peut réaliser cette opération.'));
        }

        //test password
        $verifyPassword = PasswordService::verifyPassword($password, $user->getPassword());
        if (!$verifyPassword) {
            return array('valid' => false, 'message' => __('Mot de passe incorrect.'));
        }

        $clientCancel = new ClientCancel();
        $clientCancel->setClient($client);
        $clientCancel->setName($client->getName());
        $clientCancel->setUniqid($client->getUniqid());
        $clientCancel->setLastName($user->getLastName());
        $clientCancel->setFirstName($user->getFirstName());
        $clientCancel->setEmail($user->getEmail());
        $clientCancel->setCancelReason($cancelReason);
        $clientCancel->setReason($reason);
        $clientCancel->setStatus(StatusesEnum::STATUS_PENDING);
        $clientCancel->setDateDemande(new \DateTime());

        $subscriptions = $this->clientsSubscriptionsService->getSubscriptionsByClient();
        if ($subscriptions) {
            $clientCancel->setSubscription(true);
        }

        try {
            $this->persistAndFlush($clientCancel);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement de votre demande d'annulation d'abonnement."));
        }

        ClientsHistoriesService::add(ClientHistory::ACTION_CLIENT_CANCEL, $clientCancel->getId(), $client->getId());

        if ($subscriptions) {
            foreach ($subscriptions as $subscription) {
                $desactive = $this->clientsSubscriptionsService->cancelSubscription($subscription->getId(), $client->getId());
                if (!$desactive['valid']) {
                    return array('valid' => false, 'message' => $desactive['message']);
                }
            }
        }

        //suppression du client en erreur si nécessaire
        $clientErrors = $this->clientsErrorService->getClientErrorsByClientId($client->getId());
        if ($clientErrors) {
            foreach ($clientErrors as $clientError) {
                $deleteClientError = $this->clientsErrorService->deleteClientError($clientError->getId());
                if (!$deleteClientError['valid']) {
                    return array('valid' => false, 'message' => __('Une erreur est survenue.'));
                }
            }
        }

        if ($client->getDateEndSubscription()->format('Y-m-d H:i:s') > date('Y-m-d') and !$client->getFreemium()) {
            //on désactive le client à la date d'échéance
            $customer = null;
            $order = null;

            $clientTransaction = $this->clientsTransactionsService->getLastTransactionByClient($client->getId());
            if ($clientTransaction and $clientTransaction->getTransaction()) {
                $order = $clientTransaction->getTransaction();
                $customer = $order->getShopCustomer();
            }

            $insert = $this->clientsErrorService->insertClientError($client, $customer, $order, true);
            if (!$insert['valid']) {
                return $insert;
            }

            if ($client->getRecurring()) {
                //pour afficher un message "votre abonnement va expirer" avant la date d'expiration
                $client->setRecurring(false);
                try {
                    $this->persistAndFlush($client);
                } catch (\Exception $e) {
                    LoggerService::logError('Erreur : ' . $e->getMessage());
                    return array('valid' => false, 'message' => __('Une erreur est survenue.'));
                }
            }

            //send mail
            $vars = [
                'DATE' => dateFrench('d F Y', $client->getDateEndSubscription()->getTimestamp()),
            ];
            $recipient = [
                ['first_name' => $user->getFirstName(), 'last_name' => $user->getLastName(), 'email' => $user->getEmail()]
            ];
            $send_mail = $this->mailSender->sendTemplateToClient('new_client_cancel_planned', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
            if (!$send_mail['valid']) {
                return array('valid' => false, 'message' => __('Une erreur est survenue.'));
            }
        } else {
            $disableClient = $this->clientsService->desactive_client($client->getId());
            if (!$disableClient['valid']) {
                return array('valid' => false, 'message' => __('Une erreur est survenue.'));
            }

            $recipient = [
                ['first_name' => $user->getFirstName(), 'last_name' => $user->getLastName(), 'email' => $user->getEmail()]
            ];
            $send_mail = $this->mailSender->sendTemplateToClient('new_client_cancel_processed', [], UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
            if (!$send_mail['valid']) {
                return array('valid' => false, 'message' => __('Une erreur est survenue.'));
            }
        }

        $clientCancel->setStatus(StatusesEnum::STATUS_PROCESSED);
        $clientCancel->setDateValidation(new \DateTime());
        try {
            $this->persistAndFlush($clientCancel);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'annulation de l'abonnement."));
        }

        //send mail
        $vars = [
            'CLIENT_NAME' => $client->getName(),
            'REASON' => $cancelReason,
            'LINK' => Tools::makeLink('admin', 'client', 'cancel/' . $clientCancel->getId()),
        ];
        $send_mail = $this->mailSender->sendTemplateToClient('new_client_cancel', $vars, UserEmailNotificationTypeEnum::IMPORTANT, [], CLIENT_MASTER);
        if (!$send_mail['valid']) {
            return array('valid' => false, 'message' => __('Une erreur est survenue.'));
        }

        return array('valid' => true);
    }

    /**
     * @param $submittedData
     * @return array
     */
    public function processCancelSubscription($submittedData): array
    {
        $idSubscriptionCancel = filter_var($submittedData['id_subscription_cancel'], FILTER_VALIDATE_INT);
        if (!$idSubscriptionCancel) {
            return array('valid' => false, 'message' => __('Cette demande d\'abonnement n\'existe pas'));
        }

        $clientCancel = $this->getSubscriptionCancelById($idSubscriptionCancel);
        if (!$clientCancel) {
            return array('valid' => false, 'message' => __('Cette demande d\'abonnement n\'existe pas'));
        }
        if (!$clientCancel->getClient()) {
            return array('valid' => false, 'message' => __('Cette demande d\'abonnement n\'existe pas'));
        }

        $client = $clientCancel->getClient();
        if (!$client) {
            return array('valid' => false, 'message' => __('Ce client n\'existe pas'));
        }

        $subscriptions = array();
        if (isset($submittedData['subscriptions'])) {
            $subscriptions = filter_var($submittedData['subscriptions'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
        }

        if ($subscriptions) {
            foreach ($subscriptions as $idSubscription) {
                $cancelSubscription = $this->clientsSubscriptionsService->cancelSubscription($idSubscription, $client->getId());
                if (!$cancelSubscription['valid']) {
                    return $cancelSubscription;
                }
            }
        }

        $clientCancel->setStatus(StatusesEnum::STATUS_PROCESSED);
        $clientCancel->setDateValidation(new \DateTime());
        try {
            $this->persistAndFlush($clientCancel);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'annulation de l'abonnement."));
        }

        if (isset($submittedData['submit-desactive'])) {
            //schedule disable client
            $customer = null;
            $order = null;

            $clientTransaction = $this->clientsTransactionsService->getLastTransactionByClient($client->getId());
            if ($clientTransaction and $clientTransaction->getTransaction()) {
                $order = $clientTransaction->getTransaction();
                $customer = $order->getShopCustomer();
            }

            $insert = $this->clientsErrorService->insertClientError($client, $customer, $order, true);
            if (!$insert['valid']) {
                return $insert;
            }

            if ($client->getRecurring()) {
                //pour afficher un message "votre abonnement va expirer" avant la date d'expiration
                $client->setRecurring(false);
                try {
                    $this->persistAndFlush($client);
                } catch (\Exception $e) {
                    LoggerService::logError('Erreur : ' . $e->getMessage());
                    return array('valid' => false, 'message' => __('Une erreur est survenue.'));
                }
            }

            //send mail
            if ($client->getMainAdmin()) {
                $vars = [
                    'DATE' => dateFrench('d F Y', $client->getDateEndSubscription()->getTimestamp()),
                ];
                $recipient = [
                    ['first_name' => $client->getMainAdmin()->getFirstName(), 'last_name' => $client->getMainAdmin()->getLastName(), 'email' => $client->getMainAdmin()->getEmail()]
                ];
                $send_mail = $this->mailSender->sendTemplateToClient('new_client_cancel_planned', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
                if (!$send_mail['valid']) {
                    return array('valid' => false, 'message' => __('Une erreur est survenue.'));
                }
            }
        } else {
            $disableClient = $this->clientsService->desactive_client($client->getId());
            if (!$disableClient['valid']) {
                return array('valid' => false, 'message' => __('Une erreur est survenue.'));
            }

            $recipient = [
                ['first_name' => $client->getMainAdmin()->getFirstName(), 'last_name' => $client->getMainAdmin()->getLastName(), 'email' => $client->getMainAdmin()->getEmail()]
            ];
            $send_mail = $this->mailSender->sendTemplateToClient('new_client_cancel_processed', [], UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
            if (!$send_mail['valid']) {
                return array('valid' => false, 'message' => __('Une erreur est survenue.'));
            }
        }

        return array('valid' => true);
    }

    /**
     * @param int $idSubscriptionCancel
     * @return array
     */
    public function deleteCancelSubscription(int $idSubscriptionCancel): array
    {
        $subscriptionCancel = $this->getSubscriptionCancelById($idSubscriptionCancel);
        if (!$subscriptionCancel) {
            return array('valid' => false, 'message' => __('Cette demande d\'abonnement n\'existe pas'));
        }
        if (!$subscriptionCancel->getClient()) {
            return array('valid' => false, 'message' => __('Cette demande d\'abonnement n\'existe pas'));
        }

        $client = $subscriptionCancel->getClient();
        if (!$client) {
            return array('valid' => false, 'message' => __('Ce client n\'existe pas'));
        }

        try {
            $this->deleteAndFlush($subscriptionCancel);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la suppression de la demande d\'annulation.'));
        }

        return array('valid' => true);
    }
}
