<?php

namespace MatGyver\Services\OpenAi;

use MatGyver\Services\Logger\LoggerService;

class OpenAiDocumentService extends OpenAiService
{
    /**
     * @param string $file
     * @return array
     */
    public function extractFromDocument(string $file): array
    {
        $instructions = "Tu es un expert de justice dont la mission est d'extraire des informations à partir de fichiers PDF.\n";
        $instructions .= "Tu vas recevoir des ordonnances de tribunal, ta mission sera d'extraire les informations demandées.\n";
        $instructions .= "Important : utilise toujours l'outil de réponse pour répondre à l'utilisateur. Renvoie la réponse au format JSON. L'utilisateur peut spécifier la structure.\n";
        $instructions .= "N'ajoute jamais de texte supplémentaire à la réponse.\n";
        $instructions .= "Réponds uniquement en renvoyant un tableau au format JSON. Le résultat doit toujours commencer par { et terminer par }. Ne rajoute aucun contenu à part le résultat au format JSON.\n";

        //step 1 : create a chat assistant
        $client = $this->getOpenAiClient();
        try {
            $response = $client->assistants()->create([
                'model' => 'gpt-4o',
                'name' => 'Court Assistant',
                'instructions' => $instructions,
                'tools' => [['type' => 'file_search']],
                //'response_format' => ['type' => 'json_object'],
            ]);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        $assistantId = $response->id;


        //step 2 : upload the file
        try {
            $response = $client->files()->upload([
                'file' => fopen($file, 'r'),
                'purpose' => 'assistants',
            ]);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $fileId = $response->id;
        $status = $response->status;

        LoggerService::logError('upload file : ' . json_encode((array) $response));

        if ($status != 'processed') {
            $i = 0;
            $maxLoops = 30;
            sleep(1);
            while ($i <= $maxLoops) {
                try {
                    $response = $client->files()->retrieve($fileId);
                } catch (\Exception $e) {
                    LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
                    return ['valid' => false, 'message' => __('Une erreur est survenue.')];
                }
                LoggerService::logError('get file : ' . json_encode((array)$response));
                $status = $response->status;
                if ($status == 'processed') {
                    break;
                }

                $i++;
                sleep(1);
            }
        }

        if ($status != 'processed') {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }



        //step 3 : create thread
        $prompt = "Voici un document reçu d'un tribunal.\n";
        $prompt .= "Tu dois extraire toutes les informations du véhicule indiqué. Renvoie le résultat dans une clé `vehicle` au format JSON, contenant la marque dans `brand`, le modèle dans `model` et le numéro d'immatriculation dans `registration`.\n";
        $prompt .= "Extrais aussi le numéro RG, qui commence souvent par \"N° RG\", et renvoie le résultat dans une clé `rg_number`.\n";
        $prompt .= "Extrais aussi le numéro Portalis si il est présent, qui commence souvent par \"N° Portalis\", et renvoie le résultat dans une clé `portalis_number`.\n";
        $prompt .= "Extrais aussi le numéro de minute, qui commence souvent par \"Ordonnance du\", et renvoie le résultat dans une clé `order_date` au format Y-m-d.\n";
        $prompt .= "Extrais aussi la date de l'ordonnance, qui commence souvent par \"Minute N°\", et renvoie le résultat dans une clé `minute_number`.\n";
        $prompt .= "Pour chacune des personnes indiquées ci-dessous, tu dois ajouter une clé `address`, qui contient plusieurs clés : l'adresse postale dans `line1`, le code postal dans `zip`, la ville dans `city`, le numéro de téléphone dans `phone` et le nom de la société dans `company`.\n";
        $prompt .= "Extrais toutes les informations concernant le tribunal concerné. Renvoie le résultat dans une clé `court` au format JSON, contenant le nom du tribunal dans `name`, et l'adresse complète dans un tableau `address`.\n";
        $prompt .= "Extrais aussi le nom et le prénom du président du tribunal dans `court_president` avec le prénom dans `first_name`, le nom dans `last_name`, ainsi que le prénom et le nom du greffier dans `court_clerk`.\n";
        $prompt .= "Extrais aussi toutes les informations concernant les demandeurs. On les trouve souvent dans une section 'Demandeurs' ou 'A la requête de' ou 'Partie demanderesses'. Renvoie le résultat dans une clé `demandeurs` au format JSON, contenant un tableau avec les différents demandeurs, avec les mêmes informations que pour un tribunal. Ajoute en plus le prénom dans `first_name`, le nom dans `last_name`, et le nom de la société si présent, dans `company`.\n";
        $prompt .= "Extrais aussi toutes les informations concernant les défendeurs. On les trouve souvent dans une section 'Défendeurs' ou 'Contre' ou 'Parties défenderesses'. Renvoie le résultat dans une clé `defendeurs` au format JSON, contenant un tableau avec les différents défendeurs, avec les mêmes informations que pour le demandeur.\n";
        $prompt .= "Pour les demandeurs et les défendeurs, si il y a une information concernant leur avocat, ajoute le dans une clé `lawyer` au format JSON, avec le nom du cabinet dans `company`, les noms et prénoms de l'avocat, ainsi que l'adresse postale du cabinet toujours dans un tableau intitulé `address`.\n";
        $prompt .= "Enfin, extrais toutes les questions du tribunal. Dans ce document, il y a une section qui commence en général par \"Avec mission de :\", contenant une liste de questions auxquelles doit répondre l'expert de justice. Renvoie cette liste de questions dans une clé `questions`.";
        $prompt .= "Si tu ne trouves pas une information, laisse simplement le champ vide.\n";
        $data = [
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt,
                    'attachments' => [
                        [
                            'file_id' => $fileId,
                            'tools' => [['type' => 'file_search']]
                        ],
                    ],
                ],
            ],
        ];
        try {
            $response = $client->threads()->create($data);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $threadId = $response->id;


        // Step 4 : run the thread
        $data = ['assistant_id' => $assistantId];
        try {
            $response = $client->threads()->runs()->create($threadId, $data);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $runId = $response->id;


        //get run and check status
        $i = 0;
        $maxLoops = 30;
        while ($i <= $maxLoops) {
            try {
                $response = $client->threads()->runs()->retrieve($threadId, $runId);
            } catch (\Exception $e) {
                LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
                return ['valid' => false, 'message' => __('Une erreur est survenue.')];
            }
            $status = $response->status;
            if ($status == 'completed') {
                break;
            }

            if (in_array($status, ['cancelling', 'cancelled', 'failed', 'incomplete', 'expired'])) {
                LoggerService::logError('Error calling OpenAiApi : run.status = ' . $status);
                LoggerService::logError(json_encode($response));
                return ['valid' => false, 'message' => __('Une erreur est survenue lors de la lecture de votre document.')];
            }

            $i++;
            sleep(1);
        }

        try {
            $response = $client->threads()->messages()->list($threadId);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        //LoggerService::logError(json_encode((array)$response));
        $lastMessage = $response->data[0] ?? [];
        //LoggerService::logError(json_encode($lastMessage));
        $content = $lastMessage->content[0]->text->value;
        $content = str_replace('```json', '', $content);
        $content = str_replace('```', '', $content);
        $content = str_replace('\n', '', $content);
        $content = str_replace("\n", '', $content);
        $content = str_replace('\"', '"', $content);
        //LoggerService::logError($content);

        $result = [];
        if (str_starts_with($content, '{')) {
            $content = preg_replace('/\{\s+"/', '{"', $content);
            $content = str_replace(': {', ':{', $content);
            $result = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                LoggerService::logError('JSON decode error: ' . json_last_error_msg());
                return ['valid' => false, 'message' => __('Une erreur est survenue lors de la lecture de votre document.')];
            }

            $pattern = "/【([a-z0-9-].*)】/";
            foreach ($result as $id => $content) {
                if (!is_array($content)) {
                    $content = preg_replace($pattern, '', $content);
                    $result[$id] = $content;
                } else {
                    foreach ($content as $key => $value) {
                        if (is_array($value)) {
                            foreach ($value as $k => $v) {
                                if (!is_array($v)) {
                                    $v = preg_replace($pattern, '', $v);
                                    $value[$k] = $v;
                                }
                            }
                        } else {
                            $value = preg_replace($pattern, '', $value);
                        }
                        $content[$key] = $value;
                    }
                    $result[$id] = $content;
                }
            }
        }

        //delete assistant
        try {
            $client->assistants()->delete($assistantId);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
        }

        return ['valid' => true, 'result' => $result];
    }
}
