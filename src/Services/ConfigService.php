<?php

namespace MatGyver\Services;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Entity\Config\Config;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Helpers\IpAddress;
use MatGyver\Repository\Config\ConfigRepository;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;

/**
 * Class ConfigService
 * @package MatGyver\Services
 * @property ConfigRepository $repository
 * @method ConfigRepository getRepository()
 */
class ConfigService extends BaseEntityService
{
    /**
     * ConfigService constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManager $entityManager)
    {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(Config::class);
    }

    /**
     * @param string $name
     * @param int|null $idClient
     * @return Config|null
     */
    public function findByName(string $name, int $idClient = null): ?Config
    {
        if (null === $idClient) {
            if (!isset($_SESSION['client']['id'])) {
                return null;
            }
            $idClient = $_SESSION['client']['id'];
        }

        if (isset($_SESSION['client']['id']) and $idClient == $_SESSION['client']['id']) {
            if (isset($_SESSION['settings']) and isset($_SESSION['settings'][$name])) {
                $config = new Config();
                $config->setName($name);
                $config->setValue($_SESSION['settings'][$name]);
                return $config;
            }
        }

        return $this->repository->findOneBy(['name' => $name, 'client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return array|null
     */
    public function getConfig(int $idClient = null): ?array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $params = $this->repository->findBy(['client' => $idClient]);
        if (!$params) {
            return null;
        }

        $results = [];
        foreach ($params as $param) {
            $results[$param->getName()] = $param->getValue();
        }

        return $results;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insertSettingsPages(array $submittedData): array
    {
        $settings = array();

        if (isset($submittedData['pages'])) {
            $pages = filter_var($submittedData['pages'], FILTER_VALIDATE_URL, FILTER_REQUIRE_ARRAY);
            if ($pages) {
                foreach ($pages as $page => $url) {
                    $settings[$page] = $url;
                }
            }
        }

        //footer
        $footer = array();
        $footer['column1'] = filter_input(INPUT_POST, 'footer_column1', FILTER_UNSAFE_RAW);
        $footer['column2'] = filter_input(INPUT_POST, 'footer_column2', FILTER_UNSAFE_RAW);
        $footer['column3'] = filter_input(INPUT_POST, 'footer_column3', FILTER_UNSAFE_RAW);
        $settings[ConfigEnum::FOOTER] = json_encode($footer);

        $settings[ConfigEnum::FACEBOOK_PIXEL_ID] = 0;
        $settings[ConfigEnum::GOOGLE_ANALYTICS_ID] = 0;
        $settings[ConfigEnum::GOOGLE_TAG_MANAGER_ID] = 0;
        if (isset($submittedData[ConfigEnum::FACEBOOK_PIXEL_ID])) {
            $settings[ConfigEnum::FACEBOOK_PIXEL_ID] = filter_var($submittedData[ConfigEnum::FACEBOOK_PIXEL_ID], FILTER_VALIDATE_INT);
        }
        if (isset($submittedData[ConfigEnum::GOOGLE_ANALYTICS_ID])) {
            $settings[ConfigEnum::GOOGLE_ANALYTICS_ID] = filter_var($submittedData[ConfigEnum::GOOGLE_ANALYTICS_ID], FILTER_VALIDATE_INT);
        }
        if (isset($submittedData[ConfigEnum::GOOGLE_TAG_MANAGER_ID])) {
            $settings[ConfigEnum::GOOGLE_TAG_MANAGER_ID] = filter_var($submittedData[ConfigEnum::GOOGLE_TAG_MANAGER_ID], FILTER_VALIDATE_INT);
        }

        return $this->saveConfig($settings);
    }

    /**
     * @param array $data
     * @param int|null $idClient
     * @return array
     */
    public function saveConfig(array $data, ?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        foreach ($data as $name => $value) {
            $update = $this->updateConfigByName($name, $value, $idClient);
            if (!$update['valid']) {
                return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $update['message']);
            }
        }

        //session
        if ($idClient == $_SESSION['client']['id']) {
            $settings = $this->getConfig();
            $_SESSION['settings'] = $settings;
        }

        return array('valid' => true);
    }

    /**
     * @param string $paramName
     * @param string $newValue
     * @param null $idClient
     * @return array
     */
    public function updateConfigByName(string $paramName, string $newValue, $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $config = $this->repository->findOneBy(['name' => $paramName, 'client' => $idClient]);
        if (!$config) {
            $config = new Config();
            $container = ContainerBuilderService::getInstance();
            $client = $container->get(ClientsService::class)->getRepository()->find($idClient);
            $config->setClient($client);
            $config->setName($paramName);
        }

        $config->setValue($newValue);
        try {
            $this->persistAndFlush($config);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement du paramètre."));
        }

        if ($idClient == $_SESSION['client']['id'] and isset($_SESSION['settings'])) {
            $_SESSION['settings'][$paramName] = $newValue;
        }

        return array('valid' => true);
    }

    /**
     * @param string $paramName
     * @return array
     */
    public function deleteConfig(string $paramName): array
    {
        $param = $this->repository->findOneBy(['name' => $paramName]);
        if ($param) {
            $this->deleteAndFlush($param);
        }

        if (isset($_SESSION['settings'][$paramName])) {
            unset($_SESSION['settings'][$paramName]);
        }

        return array('valid' => true);
    }

    /**
     * @param bool $siteActive
     * @return array
     */
    public function setMaintenance(bool $siteActive = false): array
    {
        $active = 'non';
        if ($siteActive) {
            $active = 'oui';
        }

        $setSiteInactive = $this->updateConfigByName(ConfigEnum::SITE_ACTIVE, $active, CLIENT_MASTER);
        if (!$setSiteInactive['valid']) {
            return $setSiteInactive;
        }

        $ip = IpAddress::getRealIp();
        $maintenanceIps = $this->findByName(ConfigEnum::MAINTENANCE_IP, CLIENT_MASTER);
        if (!$maintenanceIps) {
            $setIps = $this->updateConfigByName(ConfigEnum::MAINTENANCE_IP, $ip, CLIENT_MASTER);
            if (!$setIps['valid']) {
                return $setIps;
            }
        } else {
            $ips = explode(',', $maintenanceIps->getValue());
            if (!in_array($ip, $ips)) {
                $ip[] = $ips;
                $setIps = $this->updateConfigByName(ConfigEnum::MAINTENANCE_IP, json_encode($ips), CLIENT_MASTER);
                if (!$setIps['valid']) {
                    return $setIps;
                }
            }
        }

        return array('valid' => true);
    }

    /**
     * @return array
     */
    public function sendTestEmail(): array
    {
        $subject = '[' . APP_NAME . '] ' . __('Message de test');
        $message = '<p>' . __('Ceci est un e-mail test envoyé depuis %s.', APP_NAME) . '</p>';

        $recipient = [['user_id' => $_SESSION['user']['id']]];
        $container = ContainerBuilderService::getInstance();
        $send = $container->get(MailSender::class)->sendToClient($subject, $message, 0, $recipient);
        if (!$send['valid']) {
            return array('valid' => false, 'message' => $send['message']);
        }

        return array('valid' => true);
    }
}
