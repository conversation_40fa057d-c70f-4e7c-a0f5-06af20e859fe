<?php

namespace MatGyver\Services\Shop\Product;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Repository\Shop\Product\ShopProductRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Partials\ApexChartService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Transaction\ShopTransactionsProductsService;

/**
 * Class ShopProductsService
 * @package MatGyver\Services\Shop\Product
 * @property ShopProductRepository $repository
 * @method ShopProductRepository getRepository()
 */
class ShopProductsService extends BaseEntityService
{
    /**
     * @var array
     */
    private $productsTypes;

    /**
     * ShopProductsService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(ShopProduct::class);

        $this->productsTypes = array(
            ProductsEnum::TYPE_SUBSCRIPTION => __('Abonnement'),
            ProductsEnum::TYPE_FREEMIUM => __('Freemium'),
            ProductsEnum::TYPE_PAUSE => __('Pause de l\'abonnement du client'),
            ProductsEnum::TYPE_OTHERS => __('Autre'),
        );
    }

    public function getProductTypes(): array
    {
        return $this->productsTypes;
    }

    public function getProductType(string $productType)
    {
        if (isset($this->productsTypes[$productType])) {
            return $this->productsTypes[$productType];
        }
        return '';
    }

    /**
     * @param int $idProduct
     * @param int|null $idClient
     * @return ShopProduct|null
     */
    public function getProductById(int $idProduct, ?int $idClient = null): ?ShopProduct
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['id' => $idProduct, 'client' => $idClient]);
    }

    /**
     * @param string $permalink
     * @param int|null $idClient
     * @return ShopProduct|null
     */
    public function getProductByPermalink(string $permalink, ?int $idClient = null): ?ShopProduct
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['permalink' => $permalink, 'client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return ShopProduct[]|null
     */
    public function getAllProducts(?int $idClient = null): ?array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['client' => $idClient]);
    }

    /**
     * @param array $selectedProducts
     * @param bool $firstOption
     * @return string|null
     */
    public function generateSelectProducts(array $selectedProducts = [], bool $firstOption = false): ?string
    {
        $products = $this->getAllProducts();
        $products = RightsService::filterObjects(UNIVERSE_ADMIN_PRODUCTS, $products);
        if (!$products) {
            return null;
        }

        $options = [];
        if ($firstOption) {
            $options[] = '-';
        }
        foreach ($products as $product) {
            $options[$product->getId()] = $product->getName();
        }

        return SelectService::render($options, $selectedProducts);
    }

    /**
     * @param string $type
     * @param int|null $idClient
     * @return ShopProduct[]|null
     */
    public function getProductsByType(string $type, ?int $idClient = null): ?array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['type' => $type, 'client' => $idClient]);
    }

    /**
     * @param string $type
     * @param int|null $idClient
     * @return ShopProduct|null
     */
    public function getActiveProductByType(string $type, ?int $idClient = null): ?ShopProduct
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['type' => $type, 'active' => true, 'client' => $idClient]);
    }

    /**
     * @return ShopProduct[]|null
     */
    public function getVisibleProducts(): ?array
    {
        return $this->repository->findBy(['type' => ProductsEnum::TYPE_SUBSCRIPTION, 'visible' => true, 'active' => true, 'client' => CLIENT_MASTER]);
    }

    /**
     * @param int $idVatRule
     * @param int|null $idClient
     * @return ShopProduct[]|null
     */
    public function getProductsByVatRule(int $idVatRule, ?int $idClient = null): ?array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['vatRule' => $idVatRule, 'client' => $idClient]);
    }

    /**
     * @param string $selectedType
     * @return string
     */
    public function generateSelectProductsTypes(string $selectedType = ''): string
    {
        $options = [];
        $options[] = '-';
        foreach ($this->productsTypes as $type => $productType) {
            $options[$type] = $productType;
        }

        return SelectService::render($options, $selectedType);
    }

    /**
     * @param int $idProduct
     * @param float $product_quantity
     * @return array
     */
    public function updateStock(int $idProduct, float $product_quantity = 1): array
    {
        if (!$idProduct) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : aucun numéro de produit reçu'));
        }

        $product = $this->getProductById($idProduct);
        if (!$product) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : ce produit n\'existe pas'));
        }

        if (!$product->getQuantities()) {
            return array('valid' => true);
        }

        $quantities = json_decode($product->getQuantities(), true);
        if (!isset($quantities['active']) or !$quantities['active']) {
            return array('valid' => true);
        }
        if (!isset($quantities['in_stock']) or !is_numeric($quantities['in_stock'])) {
            return array('valid' => true);
        }

        $quantities['in_stock'] -= $product_quantity;

        $product->setQuantities(json_encode($quantities));
        try {
            $this->persistAndFlush($product);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la mise à jour du produit.'));
        }

        return array('valid' => true);
    }

    /**
     * @param int $idProduct
     * @return string
     */
    public function getSalesChart(int $idProduct): string
    {
        $date30days = date('Y-m-d', strtotime('-30 days')) . ' 00:00:00';

        $container = ContainerBuilderService::getInstance();
        $orders = $container->get(ShopTransactionsProductsService::class)->getRepository()->getOrdersByProductAndDate($idProduct, $date30days);

        $statsOrders = array();
        for ($i = 29; $i >= 0; --$i) {
            $new_date = date('m-d', strtotime('-' . $i . ' days'));
            $statsOrders[$new_date] = 0;
        }

        if ($orders) {
            foreach ($orders as $order) {
                $statsOrders[$order['day']] = $order['nbSales'];
            }
        }

        $series = [
            ['data' => $statsOrders, 'title' => __('Ventes'), 'type' => 'bar'],
        ];

        return ApexChartService::render(__('Statistiques'), 250, $series, true, '', ['type' => 'bar']);
    }
}
