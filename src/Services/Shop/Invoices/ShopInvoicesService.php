<?php
namespace MatGyver\Services\Shop\Invoices;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Helpers\Country;
use MatGyver\Helpers\Number;
use MatGyver\Repository\Shop\Invoice\ShopInvoiceRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Shop\ShopAccountingService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Users\UserConfigService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ShopInvoicesService
 * @package MatGyver\Services\Shop\Invoices
 * @property ShopInvoiceRepository $repository
 * @method ShopInvoiceRepository getRepository()
 */
class ShopInvoicesService extends BaseEntityService
{
    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * @var ShopTransactionService
     */
    private $shopTransactionService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var UserConfigService
     */
    private $userConfigService;

    /**
     * @var ShopInvoicesProductsService
     */
    private $shopInvoicesProductsService;

    /**
     * ShopInvoicesService constructor.
     * @param EntityManager $em
     * @param MailSender $mailSender
     * @param ClientsService $clientsService
     * @param ConfigService $configService
     * @param ShopTransactionService $shopTransactionService
     * @param UsersService $usersService
     * @param UserConfigService $userConfigService
     * @param ShopInvoicesProductsService $shopInvoicesProductsService
     */
    public function __construct(
        EntityManager $em,
        MailSender $mailSender,
        ClientsService $clientsService,
        ConfigService $configService,
        ShopTransactionService $shopTransactionService,
        UsersService $usersService,
        UserConfigService $userConfigService,
        ShopInvoicesProductsService $shopInvoicesProductsService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(ShopInvoice::class);
        $this->mailSender = $mailSender;
        $this->clientsService = $clientsService;
        $this->configService = $configService;
        $this->shopTransactionService = $shopTransactionService;
        $this->usersService = $usersService;
        $this->userConfigService = $userConfigService;
        $this->shopInvoicesProductsService = $shopInvoicesProductsService;
    }

    /**
     * @param int $invoiceId
     * @return ShopInvoice|null
     */
    public function getInvoiceById(int $invoiceId): ?ShopInvoice
    {
        return $this->repository->find($invoiceId);
    }

    /**
     * @param int $number
     * @param string $prefix
     * @return ShopInvoice|null
     */
    public function getInvoiceByNumberAndPrefix(int $number, string $prefix): ?ShopInvoice
    {
        return $this->repository->findOneBy(['number' => $number, 'prefix' => $prefix]);
    }

    /**
     * @param int    $number
     * @param string $prefix
     * @param int    $idClient
     * @return ShopInvoice[]
     */
    public function getAllInvoicesByNumberAndPrefix(int $number, string $prefix, int $idClient): array
    {
        return $this->repository->findBy(['number' => $number, 'prefix' => $prefix, 'client' => $idClient]);
    }

    /**
     * @param string $reference
     * @param int|null $clientId
     * @return ShopInvoice|null
     */
    public function getInvoiceByTransaction(string $reference, ?int $clientId = null): ?ShopInvoice
    {
        if ($clientId === null) {
            $clientId = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['transactionReference' => $reference, 'client' => $clientId]);
    }

    /**
     * @return ShopInvoice[]
     */
    public function getAllInvoices(): array
    {
        return $this->repository->findAll();
    }

    /**
     * @return int
     */
    public function getCountInvoices(): int
    {
        return $this->repository->count([]);
    }

    /**
     * @param array $submittedData
     * @param ShopInvoice|null $updateInvoice
     * @return array
     */
    public function insertInvoice(array $submittedData, ?ShopInvoice $updateInvoice = null): array
    {
        $log = "Function insertInvoice : ".date('Y-m-d H:i:s')."\n";

        if ($updateInvoice and $updateInvoice->getLocked()) {
            return array('valid' => false, 'message' => __("Cette facture est verrouillée et ne peut donc être modifiée."));
        }

        $idClient = $_SESSION['client']['id'];
        if (isset($submittedData['id_client'])) {
            $idClient = filter_var($submittedData['id_client'], FILTER_VALIDATE_INT);
        }

        $settings = array();
        $getSettings = $this->configService->findByName(ConfigEnum::INVOICE, $idClient);
        if ($getSettings) {
            $settings = json_decode($getSettings->getValue(), true);
        }

        $config = $this->configService->getConfig($idClient);
        $settings['company'] = ($config['company'] ?? APP_NAME);
        $settings['address'] = ($config['address'] ?? '');
        $settings['address2'] = ($config['address2'] ?? '');
        $settings['city'] = ($config['city'] ?? '');
        $settings['zip'] = ($config['zip'] ?? '');
        $settings['country'] = ($config['country'] ?? '');
        $settings['state'] = ($config['state'] ?? '');
        $settings['telephone'] = ($config['telephone'] ?? '');


        $customer = [];
        $customer['last_name'] = filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW);
        $customer['first_name'] = filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW);
        $customer['email'] = filter_var($submittedData['email'], FILTER_SANITIZE_EMAIL);

        $customer['address'] = filter_var($submittedData['address'], FILTER_UNSAFE_RAW);
        if (isset($submittedData['address2'])) {
            $customer['address2'] = filter_var($submittedData['address2'], FILTER_UNSAFE_RAW);
        }
        $customer['zip'] = filter_var($submittedData['zip'], FILTER_UNSAFE_RAW);
        $customer['city'] = filter_var($submittedData['city'], FILTER_UNSAFE_RAW);
        if (isset($submittedData['state'])) {
            $customer['state'] = filter_var($submittedData['state'], FILTER_UNSAFE_RAW);
        }
        $customer['country'] = filter_var($submittedData['country'], FILTER_UNSAFE_RAW);
        $customer['telephone'] = filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW);
        $customer['company'] = filter_var($submittedData['company'], FILTER_UNSAFE_RAW);
        $customer['tva_intracom'] = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);

        $paymentMethod = filter_var($submittedData['payment_method'], FILTER_UNSAFE_RAW);
        $reference = filter_var($submittedData['reference'], FILTER_UNSAFE_RAW);
        $currency = filter_var($submittedData['currency'], FILTER_UNSAFE_RAW);

        $products = filter_var($submittedData['products'], FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);

        $amountTaxExcl = $amountTaxIncl = 0;
        $productsNames = array();
        foreach ($products as $id => $product) {
            if (!$product['name']) {
                unset($products[$id]);
                continue;
            }

            if (!$product['price_tax_excl']) {
                $products[$id]['price_tax_excl'] = 0;
            }
            if (!$product['price_tax_incl']) {
                $products[$id]['price_tax_incl'] = 0;
            }
            if (!$product['quantity']) {
                $products[$id]['quantity'] = 1;
            }

            if (str_contains($product['price_tax_excl'], ',')) {
                $products[$id]['price_tax_excl'] = (float) str_replace(',', '.', $product['price_tax_excl']);
            }
            if (str_contains($product['price_tax_incl'], ',')) {
                $products[$id]['price_tax_incl'] = (float) str_replace(',', '.', $product['price_tax_incl']);
            }

            //prices have been multiplied by qty
            $products[$id]['price_tax_incl'] /= $products[$id]['quantity'];

            if ($product['name'] == ShopInvoice::NAME_DISCOUNT) {
                $amountTaxExcl -= $products[$id]['quantity'] * $products[$id]['price_tax_excl'];
                $amountTaxIncl -= $products[$id]['quantity'] * $products[$id]['price_tax_incl'];
            } else {
                $amountTaxExcl += $products[$id]['quantity'] * $products[$id]['price_tax_excl'];
                $amountTaxIncl += $products[$id]['quantity'] * $products[$id]['price_tax_incl'];
            }

            if ($product['name'] != ShopInvoice::NAME_TRIAL_PERIOD and $product['name'] != ShopInvoice::NAME_PAYMENT_FEES and $product['name'] != ShopInvoice::NAME_DISCOUNT) {
                $productsNames[] = $product['name'];
            }
        }

        $productName = implode(' / ', $productsNames);
        $amountTaxExcl = number_format($amountTaxExcl, 2, '.', '');
        $amountTaxIncl = number_format($amountTaxIncl, 2, '.', '');

        $userId = 0;
        if (isset($submittedData['user_id'])) {
            $userId = filter_var($submittedData['user_id'], FILTER_VALIDATE_INT);
        }
        if (!$userId and $customer['email']) {
            $user = $this->usersService->adminGetUserByEmail($customer['email']);
            if ($user) {
                $userId = $user->getId();
            }
        }

        $invoiceEmail = false;
        $invoiceEmailSubscription = false;
        if (isset($submittedData['invoice_email'])) {
            $invoiceEmail = true;
        }

        if (isset($submittedData['invoice_email_subscription']) and $submittedData['invoice_email_subscription']) {
            $transaction = $this->shopTransactionService->getTransactionByReference($reference);
            if ($transaction and $transaction->getParent()) {
                $parentTransaction = $transaction->getParent();
                if ($parentTransaction->getValid()) {
                    $invoiceEmail = false;
                    $invoiceEmailSubscription = true;
                }
            }
        }

        $totalVat = $amountTaxIncl - $amountTaxExcl;
        if ($totalVat < 0) {
            $totalVat = 0;
        }
        $totalVat = number_format($totalVat, 2, '.', '');


        $number = 0;
        if (isset($submittedData['number'])) {
            $number = filter_var($submittedData['number'], FILTER_UNSAFE_RAW);
        }

        $prefix = '';
        if (isset($submittedData['prefix'])) {
            $prefix = filter_var($submittedData['prefix'], FILTER_UNSAFE_RAW);
        }

        if (!$prefix and $settings and isset($settings['prefix'])) {
            $prefix = $settings['prefix'];
        }

        if (str_contains($prefix, '[[YEAR]]')) {
            $prefix = str_replace('[[YEAR]]', date('Y'), $prefix);
        }
        if (str_contains($prefix, '[[MONTH]]')) {
            $prefix = str_replace('[[MONTH]]', date('m'), $prefix);
        }

        if (!$number and isset($settings['number']) and $settings['number']) {
            $number = $settings['number'];

            $settings['number'] = 0;
            $this->configService->updateConfigByName(ConfigEnum::INVOICE, json_encode($settings), $idClient);
        }

        $log .= "Num_invoice = $number : ".date('Y-m-d H:i:s')."\n";

        if (!$number) {
            //invoice auto created so no number specified
            $number = 1;

            $lastInvoiceNumber = $this->repository->getLastInvoiceNumber($prefix);
            if ($lastInvoiceNumber) {
                $number = $lastInvoiceNumber;
                $number++;
            }
        }

        $checkInvoiceNumber = $this->getInvoiceByNumberAndPrefix($number, $prefix);
        if ($checkInvoiceNumber) {
            if ($updateInvoice and $checkInvoiceNumber === $updateInvoice) {
                //ok
            } else {
                $lastInvoiceNumber = $this->repository->getLastInvoiceNumber($prefix);
                if ($lastInvoiceNumber) {
                    $number = $lastInvoiceNumber;
                    $number++;
                }
            }
        }

        $log .= "Num_invoice = $number\n";

        if ($updateInvoice) {
            $invoice = $updateInvoice;

            //remove invoice products
            $invoiceProducts = $updateInvoice->getInvoiceProducts();
            foreach ($invoiceProducts as $invoiceProduct) {
                $this->shopInvoicesProductsService->deleteAndFlush($invoiceProduct);
            }
        } else {
            $invoice = new ShopInvoice();
            $invoice->setClient($this->clientsService->getClientById($idClient));
        }

        $invoice->setUser(null);
        if ($userId) {
            $invoice->setUser($this->usersService->getUser($userId));
        }
        $invoice->setPrefix($prefix);
        $invoice->setNumber($number);
        $invoice->setTransactionReference($reference);
        $invoice->setProduct($productName);
        $invoice->setAmountTaxExcl($amountTaxExcl);
        $invoice->setAmountTaxIncl($amountTaxIncl);
        $invoice->setCurrency($currency);
        $invoice->setPaymentMethod($paymentMethod);
        $invoice->setCustomer(json_encode($customer));
        $invoice->setSettings(json_encode($settings));
        $invoice->setDateCreation(new \DateTime());
        if (isset($submittedData['locked'])) {
            $invoice->setLocked($submittedData['locked']);
        }
        if (isset($submittedData['payment'])) {
            $invoice->setPayment($submittedData['payment']);
        }
        try {
            $this->persistAndFlush($invoice);
        } catch (\Exception $e) {
            //try once again in case of deadlock
            sleep(rand(3, 10));
            try {
                $this->persistAndFlush($invoice);
            } catch (\Exception $e) {
                LoggerService::logError('Erreur lors de l\'enregistrement de la facture : ' . $e->getMessage());
                return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement de la facture."));
            }
        }

        $log .= "Invoice has been created : " . date('Y-m-d H:i:s') . "\n";
        $idInvoice = $invoice->getId();

        //avoid duplicate invoice number
        $checkInvoiceNumber = $this->getAllInvoicesByNumberAndPrefix($number, $prefix, $idClient);
        if ($checkInvoiceNumber and count($checkInvoiceNumber) >= 2) {
            LoggerService::logError('Numéros de factures en double (facture ' . $number . ')');

            $lastNumber = $this->getRepository()->getLastInvoiceNumber($prefix, $idClient);
            $number = $lastNumber ?? 0;
            ++$number;

            $invoice->setNumber($number);
            try {
                $this->persistAndFlush($invoice);
            } catch (\Exception $e) {
                return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement de la facture."));
            }
        }

        $log .= "After check duplicate invoices : " . date('Y-m-d H:i:s') . "\n";

        $insertProducts = $this->shopInvoicesProductsService->insertProducts($idInvoice, $products);
        if (!$insertProducts['valid']) {
            return array('valid' => false, 'message' => $insertProducts['message']);
        }

        $log .= "After insert products : ".date('Y-m-d H:i:s')."\n";

        //envoi par email
        if (($invoiceEmail or $invoiceEmailSubscription) and $amountTaxIncl > 0) {
            $container = ContainerBuilderService::getInstance();
            $generatePDF = $container->get(ShopInvoicesExportService::class)->exportInvoicePdf($idInvoice, 'file');
            if (!$generatePDF['valid']) {
                return array('valid' => false, 'message' => $generatePDF['message']);
            }
            $file = $generatePDF['file'];

            $template = 'new_invoice';
            if ($invoiceEmailSubscription) {
                $template = 'new_recurring_invoice';
            }

            $recipient = [];
            if ($userId) {
                $recipient[] = ['user_id' => $userId];
            } else {
                $recipient[] = ['first_name' => $customer['first_name'], 'last_name' => $customer['last_name'], 'email' => $customer['email']];
            }

            $details = $container->get(ShopAccountingService::class)->getDetailsForInvoice($idInvoice);

            $vatAmount = $totalVat;
            if (!empty($details['tvas'])) {
                $vatAmount = 0;
                foreach ($details['tvas'] as $tva => $amount) {
                    $vatAmount += $amount;
                }
            }

            foreach ($products as $product) {
                if ($product['name'] == ShopInvoice::NAME_TRIAL_PERIOD) {
                    $productName .= ' (Période d\'essai)';
                    break;
                }
            }

            if (isset($details['subscription_progression']['checkout'])) {
                $checkout = $details['subscription_progression']['checkout'];
                if (isset($checkout['payment']) and $checkout['payment'] == 'subscription') {
                    $dateEnd = clone $details['date'];
                    $dateEnd->add(new \DateInterval('P' . $checkout['subscription_time'] . ($checkout['subscription_timetype'] == 'days' ? 'D' : 'M')));
                    if (isset($details['trial_period_ttc']) && $details['trial_period_ttc'] > 0 and isset($checkout['trial_time']) and isset($checkout['trial_timetype'])) {
                        $dateEnd = clone $details['date'];
                        $dateEnd->add(new \DateInterval('P' . $checkout['trial_time'] . ($checkout['trial_timetype'] == 'days' ? 'D' : 'M')));
                    }
                    $productName .= '<br>' . __('Période du %s au %s', dateFr($details['date']->format('Y-m-d')), dateFr($dateEnd->format('Y-m-d')));
                }
            }

            $vars = [
                'REFERENCE' => $reference,
                'DATE' => dateTimeFr(date('Y-m-d H:i:s')),
                'PRODUCTS' => $productName,
                'AMOUNT_VAT_EXCL' => Number::formatAmount($details['total_ht'], $currency),
                'VAT_AMOUNT' => Number::formatAmount($vatAmount, $currency),
                'AMOUNT_VAT_INCL' => Number::formatAmount($details['total_ttc'], $currency),
            ];
            $send = $this->mailSender->sendTemplateToClient($template, $vars, 0, $recipient, null, array($file));
            if (!$send['valid']) {
                return array('valid' => true, 'message' => $send['message']);
            }
        }

        return array('valid' => true, 'invoice_id' => $idInvoice, 'log' => $log);
    }

    /**
     * @param int $idInvoice
     * @return array
     */
    public function sendInvoice(int $idInvoice): array
    {
        $invoice = $this->getInvoiceById($idInvoice);
        if (!$invoice) {
            return array('valid' => false, 'message' => __('Cette facture n\'existe pas'));
        }

        $container = ContainerBuilderService::getInstance();
        $generatePDF = $container->get(ShopInvoicesExportService::class)->exportInvoicePdf($idInvoice, 'file');
        if (!$generatePDF['valid']) {
            return array('valid' => false, 'message' => $generatePDF['message']);
        }

        $file = $generatePDF['file'];

        $userId = 0;
        if ($invoice->getUser()) {
            $userId = $invoice->getUser()->getId();
        }

        $totalVat = $invoice->getAmountTaxIncl() - $invoice->getAmountTaxExcl();
        $totalVat = number_format($totalVat, 2, '.', '');

        $recipient = [];
        if ($userId) {
            $recipient[] = ['user_id' => $userId];
        } else {
            $customer = json_decode($invoice->getCustomer(), true);
            $recipient[] = ['first_name' => $customer['first_name'], 'last_name' => $customer['last_name'], 'email' => $customer['email']];
        }
        $vars = [
            'REFERENCE' => $invoice->getTransactionReference(),
            'DATE' => dateTimeFr($invoice->getDateCreation()->format('Y-m-d H:i:s')),
            'PRODUCTS' => $invoice->getProduct(),
            'AMOUNT_VAT_EXCL' => Number::formatAmount($invoice->getAmountTaxExcl(), $invoice->getCurrency()),
            'VAT_AMOUNT' => Number::formatAmount($totalVat, $invoice->getCurrency()),
            'AMOUNT_VAT_INCL' => Number::formatAmount($invoice->getAmountTaxIncl(), $invoice->getCurrency()),
        ];
        $send = $this->mailSender->sendTemplateToClient('new_invoice', $vars, 0, $recipient, null, array($file));
        if (!$send['valid']) {
            return array('valid' => true, 'message' => $send['message']);
        }

        return array('valid' => true);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function generateInvoiceFromTransaction(array $submittedData): array
    {
        if (!isset($submittedData['transaction_reference'])) {
            return array('valid' => false, 'message' => __('Aucun numéro de transaction reçu'));
        }

        $log = "Insert invoice automatiquement " . date('Y-m-d H:i:s') . "\n";

        $transactionReference = filter_var($submittedData['transaction_reference'], FILTER_UNSAFE_RAW);
        $transaction = $this->shopTransactionService->getTransactionByReference($transactionReference);
        if (!$transaction) {
            return array('valid' => false, 'message' => __('Erreur : cette transaction n\'existe pas'));
        }
        if ($transaction->getParent() and $transaction->getNbPayments() != 'x') {
            return array('valid' => false, 'message' => __('Erreur : cette transaction a un parent'));
        }
        if (ShopTransaction::STATUS_REFUNDED == $transaction->getStatus()) {
            return array('valid' => false, 'message' => __('Erreur : cette transaction est remboursée'));
        }

        $data = array(
            'id_client' => $transaction->getClient()->getId(),
            'reference' => $transactionReference,
            'last_name' => $transaction->getLastName(),
            'first_name' => $transaction->getFirstName(),
            'email' => $transaction->getEmail(),
            'address' => '',
            'zip' => '',
            'city' => '',
            'country' => '',
            'telephone' => '',
            'company' => '',
            'tva_intracom' => '',
            'amount_tax_excl' => $transaction->getAmountTaxExcl(),
            'amount_tax_incl' => $transaction->getAmountTaxIncl(),
            'payment_method' => $transaction->getPaymentMethod(),
            'currency' => $transaction->getCurrency(),
        );

        if (isset($submittedData['date_invoice'])) {
            $data['date_invoice'] = $submittedData['date_invoice'];
        } else {
            $data['date_invoice'] = $transaction->getDate()->format('Y-m-d H:i:s');
            if ($transaction->getPaymentMethod() == ShopTransaction::METHOD_VIREMENT or $transaction->getPaymentMethod() == ShopTransaction::METHOD_CHEQUE) {
                $data['date_invoice'] = date('Y-m-d H:i:s');
            }
        }

        if ($transaction->getCustom() == 'transaction_add') {
            $paymentData = json_decode($transaction->getDatas(), true);
            if (isset($paymentData['address'])) {
                $data['address'] = $paymentData['address'];
            }
            if (isset($paymentData['zip'])) {
                $data['zip'] = $paymentData['zip'];
            }
            if (isset($paymentData['city'])) {
                $data['city'] = $paymentData['city'];
            }
            if (isset($paymentData['country'])) {
                $country = Country::getCountryByCode($paymentData['country']);
                if ($country) {
                    $data['country'] = $country->getName();
                }
            }
            if (isset($paymentData['company'])) {
                $data['company'] = $paymentData['company'];
            }
            if (isset($paymentData['tva_intracom'])) {
                $data['tva_intracom'] = $paymentData['tva_intracom'];
            }
        }

        if ($transaction->getShopCustomer()) {
            $customer = $transaction->getShopCustomer();
            $data['address'] = $customer->getAddress();
            $data['address2'] = $customer->getAddress2();
            $data['city'] = $customer->getCity();
            $data['zip'] = $customer->getZip();
            $data['country'] = $customer->getCountry();
            $data['telephone'] = $customer->getTelephone();
            $data['state'] = $customer->getState();

            $customerData = json_decode($customer->getDatas(), true);
            if ($customerData) {
                if (isset($customerData['company']) and $customerData['company']) {
                    $data['company'] = $customerData['company'];
                }
                if (isset($customerData['tva_intracom']) and $customerData['tva_intracom']) {
                    $data['tva_intracom'] = $customerData['tva_intracom'];
                }
            }
        }

        if ($transaction->getUser()) {
            $user = $transaction->getUser();
            $userConfig = $this->userConfigService->getUserConfig($user);
            if ($userConfig) {
                if (!$data['address'] and isset($userConfig['address']) and $userConfig['address']) {
                    $data['address'] = $userConfig['address'];
                }
                if (!$data['zip'] and isset($userConfig['zip']) and $userConfig['zip']) {
                    $data['zip'] = $userConfig['zip'];
                }
                if (!$data['zip'] and isset($userConfig['zip']) and $userConfig['zip']) {
                    $data['zip'] = $userConfig['zip'];
                }
                if (!$data['city'] and isset($userConfig['city']) and $userConfig['city']) {
                    $data['city'] = $userConfig['city'];
                }
                if (!$data['telephone'] and isset($userConfig['telephone']) and $userConfig['telephone']) {
                    $data['telephone'] = $userConfig['telephone'];
                }
                if (!$data['company'] and isset($userConfig['company']) and $userConfig['company']) {
                    $data['company'] = $userConfig['company'];
                }
                if (!$data['tva_intracom'] and isset($userConfig['tva_intracom']) and $userConfig['tva_intracom']) {
                    $data['tva_intracom'] = $userConfig['tva_intracom'];
                }
                if (!$data['country'] and isset($userConfig['country']) and $userConfig['country']) {
                    $data['country'] = $userConfig['country'];
                }
            }
        }

        $product = $transaction->getProductName();
        $data['product'] = $product;

        if (!$data['currency']) {
            $data['currency'] = DEFAULT_CURRENCY;
        }

        //user_id
        if ($transaction->getUser()) {
            $data['user_id'] = $transaction->getUser()->getId();
        }

        if ($transaction->getValid() and !isset($submittedData['dont_send_email'])) {
            $invoicesSettings = $this->configService->findByName(ConfigEnum::INVOICE, $transaction->getClient()->getId());
            if ($invoicesSettings) {
                $invoicesSettings = json_decode($invoicesSettings->getValue(), true);
                if (isset($invoicesSettings['invoice_email']) and $invoicesSettings['invoice_email']) {
                    $data['invoice_email'] = true;
                }
                if (isset($invoicesSettings['invoice_email_subscription']) and $invoicesSettings['invoice_email_subscription']) {
                    $data['invoice_email_subscription'] = true;
                }
            }
        }

        $products = $transaction->getTransactionProducts();
        if ($products) {
            $arrayProducts = [];
            foreach ($products as $product) {
                $arrayProducts[] = [
                    'name' => $product->getName(),
                    'quantity' => $product->getQuantity(),
                    'price_tax_excl' => $product->getPriceTaxExcl() / $product->getQuantity(), //insertInvoicesProducts multiply price by qty
                    'price_tax_incl' => $product->getPriceTaxIncl(),
                    'vat' => $product->getVat(),
                    'currency' => $product->getCurrency(),
                    'attributes' => ($product->getAttributes() ? json_decode($product->getAttributes(), true) : []),
                ];
            }
            $data['products'] = $arrayProducts;
        }

        $invoice_id = 0;
        $invoice = $this->getInvoiceByTransaction($transactionReference);
        if ($invoice) {
            LoggerService::logError('An invoice already exists for transaction ' . $transactionReference);
            return array('valid' => false, 'message' => 'An invoice already exists for transaction ' . $transactionReference);
        }

        $insert = $this->insertInvoice($data);
        if (!$insert['valid']) {
            $log .= "Erreur insertInvoice : " . $insert['message'] . "\n";
        }
        if (isset($insert['log'])) {
            $log .= $insert['log'] . "\n";
        }

        if (!$insert['valid']) {
            return array('valid' => false, 'message' => $insert['message']);
        }
        if (isset($insert['invoice_id'])) {
            $invoice_id = $insert['invoice_id'];
        }

        return array('valid' => true, 'message' => $log, 'invoice_id' => $invoice_id);
    }

    /**
     * @return void
     */
    public function checkDuplicateInvoiceNumbers(): void
    {
        $dateYesterday = date('Y-m-d', strtotime('-1 day')).' 00:00:00';
        $result = $this->repository->getDuplicateInvoiceNumbers($dateYesterday);
        if ($result) {
            foreach ($result as $invoice) {
                LoggerService::logError($invoice['nbInvoices'] . ' factures avec le numéro ' . $invoice['number']);
            }
        }
    }
}
