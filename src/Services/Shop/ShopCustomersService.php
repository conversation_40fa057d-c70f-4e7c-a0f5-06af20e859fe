<?php
namespace MatGyver\Services\Shop;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Helpers\Chars;
use MatGyver\Helpers\Csv;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\IpAddress;
use MatGyver\Repository\Shop\Customer\ShopCustomerRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Clients\ClientsSourcesService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Filters\ShopCustomersFiltersService;
use MatGyver\Services\Logs\LogActionService;
use MatGyver\Services\PasswordService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Users\UsersRgpdService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ShopCustomersService
 * @package MatGyver\Services\Shop
 * @property ShopCustomerRepository $repository
 * @method ShopCustomerRepository getRepository()
 */
class ShopCustomersService extends BaseEntityService
{
    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var ShopVatRulesService
     */
    private $shopVatRulesService;

    /**
     * @var UsersRgpdService
     */
    private $usersRgpdService;

    /**
     * @var ShopCustomersFieldsService
     */
    private $shopCustomersFieldsService;

    /**
     * @var ShopCustomersFiltersService
     */
    private $customersFiltersService;

    /**
     * ShopCustomersService constructor.
     * @param EntityManager $em
     * @param ClientsService $clientsService
     * @param UsersService $usersService
     * @param ShopVatRulesService $shopVatRulesService
     * @param UsersRgpdService $usersRgpdService
     * @param ShopCustomersFieldsService $shopCustomersFieldsService
     * @param ShopCustomersFiltersService $customersFiltersService
     */
    public function __construct(
        EntityManager $em,
        ClientsService $clientsService,
        UsersService $usersService,
        ShopVatRulesService $shopVatRulesService,
        UsersRgpdService $usersRgpdService,
        ShopCustomersFieldsService $shopCustomersFieldsService,
        ShopCustomersFiltersService $customersFiltersService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(ShopCustomer::class);
        $this->clientsService = $clientsService;
        $this->usersService = $usersService;
        $this->shopVatRulesService = $shopVatRulesService;
        $this->usersRgpdService = $usersRgpdService;
        $this->shopCustomersFieldsService = $shopCustomersFieldsService;
        $this->customersFiltersService = $customersFiltersService;
    }

    /**
     * @param int $idCustomer
     * @param int|null $idClient
     * @return ShopCustomer|null
     */
    public function getCustomerById(int $idCustomer, ?int $idClient = null): ?ShopCustomer
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['id' => $idCustomer, 'client' => $idClient]);
    }

    /**
     * @param string $email
     * @param int|null $idClient
     * @return ShopCustomer|null
     */
    public function getCustomerByEmail(string $email, ?int $idClient = null): ?ShopCustomer
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['email' => $email, 'client' => $idClient]);
    }

    /**
     * @param string $randomId
     * @param int|null $idClient
     * @return ShopCustomer|null
     */
    public function getCustomerByRandomId(string $randomId, ?int $idClient = null): ?ShopCustomer
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['randomId' => $randomId, 'client' => $idClient]);
    }

    /**
     * @param int $userId
     * @param int|null $idClient
     * @return ShopCustomer|null
     */
    public function getCustomerByUserId(int $userId, ?int $idClient = null): ?ShopCustomer
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['user' => $userId, 'client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return ShopCustomer[]
     */
    public function getCustomers(?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return int
     */
    public function getCountCustomers(?int $idClient = null): int
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->count(['client' => $idClient]);
    }

    /**
     * @param array $submittedData
     * @param int|null $idCustomer
     * @return array
     */
    public function processCustomer(array $submittedData, ?int $idCustomer = null): array
    {
        $idProduct = 0;
        if (isset($submittedData['id_product'])) {
            $idProduct = filter_var($submittedData['id_product'], FILTER_VALIDATE_INT);
        }
        $lastName = filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW);
        $firstName = filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW);
        $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);

        $address = (isset($submittedData['address']) ? filter_var($submittedData['address'], FILTER_UNSAFE_RAW) : '');
        $address2 = (isset($submittedData['address2']) ? filter_var($submittedData['address2'], FILTER_UNSAFE_RAW) : '');
        $city = (isset($submittedData['city']) ? filter_var($submittedData['city'], FILTER_UNSAFE_RAW) : '');
        $zip = (isset($submittedData['zip']) ? filter_var($submittedData['zip'], FILTER_UNSAFE_RAW) : '');
        $state = (isset($submittedData['state']) ? filter_var($submittedData['state'], FILTER_UNSAFE_RAW) : '');
        $country = (isset($submittedData['country']) ? filter_var($submittedData['country'], FILTER_UNSAFE_RAW) : DEFAULT_COUNTRY);
        $telephone = (isset($submittedData['telephone']) ? filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW) : '');

        $email = str_replace('\'', '', $email);
        $email = strtolower($email);

        $customerData = [];
        if ($idCustomer) {
            //if !customer, it has been deleted, let's create a new one
            $customer = $this->getCustomerById($idCustomer);
        } else {
            $customer = $this->getCustomerByEmail($email);
        }
        if ($customer) {
            $customerData = json_decode($customer->getDatas(), true);

            if ($email != $customer->getEmail()) {
                //customer changed email so let's create a new one
                $idCustomer = null;
                $customer = null;
            }

            //fix for client master : if uniqid is different -> create a new customer
            if ($_SESSION['client']['id'] == CLIENT_MASTER) {
                $uniqId = ($submittedData['uniqid'] ?? '');
                $previousUniqId = '';
                if ($customer and $customer->getDecodedDatas() and isset($customer->getDecodedDatas()['uniqid'])) {
                    $previousUniqId = $customer->getDecodedDatas()['uniqid'];
                }
                if ($uniqId and $previousUniqId and $uniqId != $previousUniqId) {
                    $idCustomer = null;
                    $customer = null;
                }
            }
        }

        if (isset($submittedData['company'])) {
            $customerData['company'] = filter_var($submittedData['company'], FILTER_UNSAFE_RAW);
        }

        if (isset($submittedData['tva_intracom'])) {
            $vatNumber = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);
            $vatNumber = $this->shopVatRulesService->filterVatNumber($vatNumber);
            if ($vatNumber) {
                $validVat = $this->shopVatRulesService->checkVatNumber($vatNumber);
                if ($validVat) {
                    $customerData['valid_tvaintracom'] = true;
                    $customerData['tva_intracom'] = $vatNumber;
                } else {
                    if (isset($customerData['tva_intracom'])) {
                        unset($customerData['tva_intracom']);
                    }
                    if (isset($customerData['valid_tvaintracom'])) {
                        unset($customerData['valid_tvaintracom']);
                    }
                }
            }
        }

        if (isset($submittedData['password'])) {
            $password = filter_var($submittedData['password'], FILTER_UNSAFE_RAW);
            $customerData['password'] = PasswordService::hashPassword($password);
        }

        //store cookies
        if (isset($_COOKIE['_ga'])) {
            $customerData['_gid'] = $_COOKIE['_ga'];
        }
        if (isset($_COOKIE['gclid'])) {
            $customerData['gclid'] = $_COOKIE['gclid'];
        }
        if (isset($_COOKIE['_fbp'])) {
            $customerData['fbp'] = $_COOKIE['_fbp'];
        }
        if (isset($_COOKIE['_fbc'])) {
            $customerData['fbc'] = $_COOKIE['_fbc'];
        }

        //source
        $source = ClientsSourcesService::getCookieSource();
        $sourceReferer = ClientsSourcesService::getCookieReferer();
        if ($source) {
            $customerData['source'] = $source;
            if ($sourceReferer) {
                $customerData['source_referer'] = $sourceReferer;
            }
        }

        if (isset($_COOKIE['partner'])) {
            $customerData['partner'] = $_COOKIE['partner'];
        }

        if (isset($submittedData['cgv-accept'])) {
            $customerData['cgv'] = 1;
            $customerData['cgv_txt'] = htmlspecialchars_decode(filter_var($submittedData['cgv_txt'], FILTER_UNSAFE_RAW));
            $customerData['cgv_id'] = filter_var($submittedData['cgv_id'], FILTER_VALIDATE_INT);
        }

        if (isset($submittedData['cgu-accept'])) {
            $customerData['cgu'] = 1;
            $customerData['cgu_txt'] = htmlspecialchars_decode(filter_var($submittedData['cgu_txt'], FILTER_UNSAFE_RAW));
        }

        if (isset($submittedData['rgpd'])) {
            $customerData['rgpd'] = 1;
            $customerData['rgpd_date'] = date('Y-m-d H:i:s');
            $customerData['rgpd_notice'] = filter_var($submittedData['rgpd_notice'], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData['rgpd_aff'])) {
            $customerData['rgpd_aff'] = 1;
            $customerData['rgpd_aff_date'] = date('Y-m-d H:i:s');
            $customerData['rgpd_aff_notice'] = filter_var($submittedData['rgpd_aff_notice'], FILTER_UNSAFE_RAW);
        }

        $ip = IpAddress::getRealIp();

        if ($customer) {
            $randomId = $customer->getRandomId();
        } else {
            $validRandomId = false;
            $randomId = '';
            while (!$validRandomId) {
                $randomId = createRandomID();
                $check = $this->getCustomerByRandomId($randomId);
                if (!$check) {
                    $validRandomId = true;
                }
            }
            $randomId = str_replace('e', '', $randomId);
        }

        if (isset($submittedData['uniqid'])) {
            $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
            $uniqId = str_replace(array('http://', 'https://', APP_DOMAIN), '', $uniqId);
            $customerData['uniqid'] = permalink($uniqId);
        }

        if (!$customer) {
            $customer = new ShopCustomer();
            $customer->setDate(new \DateTime());
        }

        $customer->setClient($this->clientsService->getClient());
        $customer->setLastName($lastName);
        $customer->setFirstName($firstName);
        $customer->setEmail($email);
        $customer->setAddress($address);
        $customer->setAddress2($address2);
        $customer->setCity($city);
        $customer->setZip($zip);
        $customer->setState(substr($state, 0, 50));
        $customer->setCountry($country);
        $customer->setTelephone($telephone);
        $customer->setDatas(json_encode($customerData));
        $customer->setIp($ip);
        $customer->setRandomId($randomId);
        try {
            $this->persistAndFlush($customer);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de la mise à jour du compte client."));
        }

        $idCustomer = $customer->getId();

        //set customer in session
        $customerArray = Entities::asArray($customer);
        if ($idProduct) {
            $customerArray['product_id'] = $idProduct;
        }
        $customerArray['client_id'] = $_SESSION['client']['id'];
        $_SESSION['customer'] = $customerArray;

        //RGPD
        if (isset($customerData['rgpd']) && $customerData['rgpd']) {
            $user = $this->usersService->adminGetUserByEmail($email);
            if ($user) {
                if (!$user->getRgpd()) {
                    $user->setRgpd(true);
                    try {
                        $this->usersService->persistAndFlush($user);
                    } catch (\Exception $e) {
                        return array('valid' => false, 'message' => __('Une erreur est survenue, veuillez nous en excuser.'));
                    }
                }

                $arrayRgpd = array(
                    'rgpd' => $customerData['rgpd'],
                    'rgpd_date' => $customerData['rgpd_date'],
                    'rgpd_notice' => $customerData['rgpd_notice'],
                );
                $this->usersRgpdService->insertHistory($user->getId(), $arrayRgpd);
            }
        }

        if (isset($customerData['rgpd_aff']) && $customerData['rgpd_aff']) {
            $user = $this->usersService->adminGetUserByEmail($email);
            if ($user) {
                if (!$user->getRgpdAff()) {
                    $user->setRgpdAff(true);
                    try {
                        $this->usersService->persistAndFlush($user);
                    } catch (\Exception $e) {
                        return array('valid' => false, 'message' => __('Une erreur est survenue, veuillez nous en excuser.'));
                    }
                }

                $arrayRgpd = array(
                    'rgpd_aff' => $customerData['rgpd_aff'],
                    'rgpd_aff_date' => $customerData['rgpd_aff_date'],
                    'rgpd_aff_notice' => $customerData['rgpd_aff_notice'],
                );
                $this->usersRgpdService->insertHistory($user->getId(), $arrayRgpd);
            }
        }

        if ($idProduct) {
            $processCustomFields = $this->shopCustomersFieldsService->processCustomerFields($idCustomer, $idProduct, $submittedData);
            if (!$processCustomFields['valid']) {
                return array('valid' => false, 'message' => $processCustomFields['message']);
            }
        }

        return array('valid' => true, 'id_customer' => $idCustomer);
    }

    /**
     * @param ShopCustomer $shopCustomer
     * @param array $submittedData
     * @return array
     */
    public function makeClientCustomer(ShopCustomer $shopCustomer, array $submittedData): array
    {
        $customerData = $shopCustomer->getDecodedDatas();
        if (!isset($customerData['password']) or !$customerData['password']) {
            return ['valid' => false, 'message' => __('Aucun mot de passe défini')];
        }

        $data = [
            'name' => $shopCustomer->getFirstName() . ' ' . $shopCustomer->getLastName(),
            'uniqid' => Chars::generateUniqid(),
            'subscription' => ProductsEnum::TYPE_FREEMIUM,
            'active' => false,
            'date_end_subscription' => '2100-12-31',
            'dont_send_email' => true,
            'freemium' => true,
            'no_validated' => true, //user must not be validated
            'recurring' => false,
            'first_name' => $shopCustomer->getFirstName(),
            'last_name' => $shopCustomer->getLastName(),
            'email' => $shopCustomer->getEmail(),
            'telephone' => $shopCustomer->getTelephone(),
            'customer' => [
                'address' => $shopCustomer->getAddress(),
                'address2' => $shopCustomer->getAddress2(),
                'city' => $shopCustomer->getCity(),
                'zip' => $shopCustomer->getZip(),
                'state' => $shopCustomer->getState(),
                'country' => $shopCustomer->getCountry(),
                'telephone' => $shopCustomer->getTelephone(),
                'company' => ($customerData['company'] ?? ''),
                'tva_intracom' => ($customerData['tva_intracom'] ?? ''),
                'ip' => $shopCustomer->getIp(),
            ],
            'customerData' => [
                'password' => $customerData['password'],
            ],
        ];

        if (isset($submittedData['cgv-accept']) and $submittedData['cgv-accept']) {
            $data['cgv'] = true;
            $data['cgv-accept'] = true;
        }
        if (isset($submittedData['rgpd']) and $submittedData['rgpd']) {
            $data['customerData']['rgpd'] = true;
            $data['customerData']['rgpd_date'] = date('Y-m-d H:i:s');
            $data['customerData']['rgpd_notice'] = (isset($submittedData['rgpd_notice']) ? filter_var($submittedData['rgpd_notice'], FILTER_UNSAFE_RAW) : '');
        }
        if (isset($submittedData['rgpd_aff']) and $submittedData['rgpd_aff']) {
            $data['customerData']['rgpd_aff'] = true;
            $data['customerData']['rgpd_aff_date'] = date('Y-m-d H:i:s');
            $data['customerData']['rgpd_aff_notice'] = (isset($submittedData['rgpd_aff_notice']) ? filter_var($submittedData['rgpd_aff_notice'], FILTER_UNSAFE_RAW) : '');
        }

        $container = ContainerBuilderService::getInstance();

        $insertClient = $container->get(ClientsService::class)->insertClient($data);
        if (!$insertClient['valid']) {
            return $insertClient;
        }

        $userId = $insertClient['user_id'];
        $idClient = $insertClient['client_id'];

        //send welcome email
        $sendMail = $container->get(UsersService::class)->sendConfirmationLink($userId, $idClient, 'freemium');
        if (!$sendMail['valid']) {
            return $sendMail;
        }

        unset($customerData['password']);
        $shopCustomer->setDatas(json_encode($customerData));
        try {
            $this->persistAndFlush($shopCustomer);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        return ['valid' => true, 'idClient' => $idClient, 'userId' => $userId];
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function appInsertCustomer(array $submittedData): array
    {
        $lastName = filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW);
        $firstName = filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW);
        $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);
        $comment = filter_var($submittedData['comment'], FILTER_UNSAFE_RAW);
        $address = filter_var($submittedData['address'], FILTER_UNSAFE_RAW);
        $address2 = filter_var($submittedData['address2'], FILTER_UNSAFE_RAW);
        $city = filter_var($submittedData['city'], FILTER_UNSAFE_RAW);
        $zip = filter_var($submittedData['zip'], FILTER_UNSAFE_RAW);
        $state = filter_var($submittedData['state'], FILTER_UNSAFE_RAW);
        $country = filter_var($submittedData['country'], FILTER_UNSAFE_RAW);
        $company = filter_var($submittedData['company'], FILTER_UNSAFE_RAW);
        $telephone = filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW);

        $idClient = $_SESSION['client']['id'];
        if (isset($submittedData['id_client'])) {
            $idClient = filter_var($submittedData['id_client'], FILTER_VALIDATE_INT);
        }

        $data = [
            'company' => $company,
        ];
        if (isset($submittedData['uniqid'])) {
            $data['uniqid'] = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
        }

        //user_random_id
        $randomId = '';
        $validRandomId = false;
        while (!$validRandomId) {
            $randomId = createRandomID();
            $check = $this->getCustomerByRandomId($randomId);
            if (!$check) {
                $validRandomId = true;
            }
        }

        $customer = new ShopCustomer();
        $customer->setClient($this->clientsService->getClientById($idClient));
        $customer->setLastName($lastName);
        $customer->setFirstName($firstName);
        $customer->setEmail($email);
        $customer->setAddress($address);
        $customer->setAddress2($address2);
        $customer->setCity($city);
        $customer->setZip($zip);
        $customer->setState($state);
        $customer->setCountry($country);
        $customer->setTelephone($telephone);
        $customer->setDatas(json_encode($data));
        $customer->setIp('');
        $customer->setComment($comment);
        $customer->setRandomId($randomId);
        $customer->setDate(new \DateTime());
        try {
            $this->persistAndFlush($customer);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de la mise à jour du compte client."));
        }

        return array('valid' => true, 'id_customer' => $customer->getId());
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function appUpdateCustomer(array $submittedData): array
    {
        $idCustomer = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $lastName = filter_var($submittedData['last_name'], FILTER_UNSAFE_RAW);
        $firstName = filter_var($submittedData['first_name'], FILTER_UNSAFE_RAW);
        $email = filter_var($submittedData['email'], FILTER_VALIDATE_EMAIL);
        $comment = filter_var($submittedData['comment'], FILTER_UNSAFE_RAW);
        $address = filter_var($submittedData['address'], FILTER_UNSAFE_RAW);
        $address2 = filter_var($submittedData['address2'], FILTER_UNSAFE_RAW);
        $city = filter_var($submittedData['city'], FILTER_UNSAFE_RAW);
        $zip = filter_var($submittedData['zip'], FILTER_UNSAFE_RAW);
        $state = filter_var($submittedData['state'], FILTER_UNSAFE_RAW);
        $country = filter_var($submittedData['country'], FILTER_UNSAFE_RAW);
        $company = filter_var($submittedData['company'], FILTER_UNSAFE_RAW);
        $telephone = filter_var($submittedData['telephone'], FILTER_UNSAFE_RAW);
        $tvaIntracom = filter_var($submittedData['tva_intracom'], FILTER_UNSAFE_RAW);

        $customer = $this->getCustomerById($idCustomer);
        if (!$customer) {
            return array('valid' => false, 'message' => __('Ce client n\'existe pas.'));
        }

        $data = json_decode($customer->getDatas(), true);
        $data['company'] = $company;
        if ($tvaIntracom) {
            $data['tva_intracom'] = $tvaIntracom;
        }
        if (isset($submittedData['uniqid'])) {
            $data['uniqid'] = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
        }

        $customer->setLastName($lastName);
        $customer->setFirstName($firstName);
        $customer->setEmail($email);
        $customer->setAddress($address);
        $customer->setAddress2($address2);
        $customer->setCity($city);
        $customer->setZip($zip);
        $customer->setState($state);
        $customer->setCountry($country);
        $customer->setTelephone($telephone);
        $customer->setDatas(json_encode($data));
        $customer->setComment($comment);
        try {
            $this->persistAndFlush($customer);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de la mise à jour du compte client."));
        }

        return array('valid' => true);
    }

    /**
     * @param int $idCustomer
     * @return array
     */
    public function delete_customer(int $idCustomer): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SHOP)) {
            return array('valid' => false, 'message' => __('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.'));
        }

        $customer = $this->getCustomerById($idCustomer);
        if (!$customer) {
            return array('valid' => false, 'message' => __('Ce client n\'existe pas.'));
        }

        try {
            $this->deleteAndFlush($customer);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __('Erreur lors de la suppression du client.'));
        }

        return array('valid' => true);
    }

    /**
     * @param int|null $idProduct
     * @return array|void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function exportCustomers(?int $idProduct = null): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EXPORT, UNIVERSE_ADMIN_SHOP, $idProduct)) {
            return array('valid' => false, 'message' => __('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.'));
        }

        $filters = $this->customersFiltersService->getFilters();
        $dateStart = $filters['date_start'];
        $dateEnd = $filters['date_end'];
        $search = filter_var($filters['search'], FILTER_UNSAFE_RAW);
        if ($idProduct === null) {
            $idProduct = $filters['id_product'];
        }

        $customers = $this->repository->searchCustomers($idProduct, $search, $dateStart, $dateEnd);
        if (!$customers) {
            return array('valid' => false, 'message' => __('Aucun client'));
        }

        set_time_limit(0);

        $container = ContainerBuilderService::getInstance();
        $orders = $container->get(ShopTransactionService::class)->getRepository()->getCountOrdersGroupByCustomer();
        if ($orders) {
            $orders = array_change_key($orders, 'customerId', 'nbOrders');
        }

        $totalAmount = $container->get(ShopTransactionService::class)->getRepository()->getTotalAmountGroupByCustomer();
        if ($totalAmount) {
            $totalAmount = array_change_key($totalAmount, 'customerId', 'totalTaxIncl');
        }

        $csv = array();
        $i = 0;
        foreach ($customers as $customer) {
            $csv[$i] = Entities::asArray($customer);
            $csv[$i]['company'] = '';
            $csv[$i]['tva_intracom'] = '';
            $csv[$i]['amount'] = 0;
            $csv[$i]['nb_orders'] = 0;
            $csv[$i]['cgv'] = '';
            $csv[$i]['rgpd'] = '';
            $csv[$i]['rgpd_aff'] = '';

            foreach (['datas', 'decoded_datas', 'comment', 'random_id'] as $fieldName) {
                if (isset($csv[$i][$fieldName])) {
                    unset($csv[$i][$fieldName]);
                }
            }

            //orders
            $nbOrders = 0;
            if (isset($orders[$customer->getId()])) {
                $nbOrders = $orders[$customer->getId()];
            }

            $csv[$i]['nb_orders'] = $nbOrders;
            if ($nbOrders) {
                $amount = 0;
                if (isset($totalAmount[$customer->getId()])) {
                    $amount = $totalAmount[$customer->getId()];
                }

                $amount = round($amount, 2);
                $amount = number_format($amount, 2, ',', '');
                $csv[$i]['amount'] = $amount;
            }

            if ($customer->getDatas()) {
                $datas = json_decode($customer->getDatas(), true);
                if ($datas) {
                    foreach ($datas as $name => $field) {
                        if (isset($csv[$i][$name])) {
                            $csv[$i][$name] = $field;
                        }
                    }

                    $csv[$i]['cgv'] = '';
                    $csv[$i]['rgpd'] = '';
                    $csv[$i]['rgpd_aff'] = '';

                    if (isset($datas['cgv']) and $datas['cgv'] and isset($datas['cgv_txt']) and $datas['cgv_txt']) {
                        $csv[$i]['cgv'] = str_replace('&#39;', '\'', $datas['cgv_txt']);
                    }
                    if (isset($datas['rgpd']) and $datas['rgpd'] and isset($datas['rgpd_notice']) and $datas['rgpd_notice']) {
                        $csv[$i]['rgpd'] = str_replace('&#39;', '\'', $datas['rgpd_notice']);
                    }
                    if (isset($datas['rgpd_aff']) and $datas['rgpd_aff'] and isset($datas['rgpd_aff_notice']) and $datas['rgpd_aff_notice']) {
                        $csv[$i]['rgpd_aff'] = str_replace('&#39;', '\'', $datas['rgpd_aff_notice']);
                    }
                }
            }

            ++$i;
        }

        if (!$csv) {
            return array('valid' => false, 'message' => __('Aucun client enregistré'));
        }

        // Log
        LogActionService::log('export_customers');

        $headers = array(__('Client'), __('Nom'), __('Prénom'), __('Adresse email'), __('Adresse'), __('Adresse 2'), __('Ville'), __('Code Postal'), __('Etat/Région'), __('Pays'), __('Téléphone'), __('IP'), __('Date'), __('Société'), __('TVA Intracommunautaire'), __('Montant total'), __('Nombre de commandes'), __('Conditions Générales de Vente'), __('Consentement RGPD'), __('Consentement RGPD Partenaires'));
        Csv::export('customers', $csv, $headers);
    }
}
