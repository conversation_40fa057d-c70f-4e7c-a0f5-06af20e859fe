<?php

namespace MatGyver\Services\OAuth;

use MatGyver\Enums\ConfigEnum;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use Stevenma<PERSON><PERSON>\OAuth2\Client\Provider\Microsoft;
use League\OAuth2\Client\Token\AccessToken;

/**
 * Class MicrosoftOAuthService
 * @package MatGyver\Services\OAuth
 */
class MicrosoftOAuthService
{
    /**
     * @var Microsoft
     */
    private $provider;

    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * @var int|null
     */
    private $clientId;

    /**
     * MicrosoftOAuthService constructor.
     * @param int|null $clientId
     */
    public function __construct(?int $clientId = null)
    {
        $this->clientId = $clientId ?? CLIENT_MASTER;
        $container = ContainerBuilderService::getInstance();
        $this->configService = $container->get(ConfigService::class);
        $this->initializeProvider();
    }

    /**
     * Initialize the OAuth provider
     */
    private function initializeProvider(): void
    {
        $config = $this->configService->getConfig($this->clientId);
        
        $clientId = $config[ConfigEnum::OUTLOOK_OAUTH_CLIENT_ID] ?? '';
        $clientSecret = $config[ConfigEnum::OUTLOOK_OAUTH_CLIENT_SECRET] ?? '';
        $tenantId = $config[ConfigEnum::OUTLOOK_OAUTH_TENANT_ID] ?? 'common';

        if (!$clientId || !$clientSecret) {
            throw new \Exception('OAuth configuration is missing. Please configure Client ID and Client Secret.');
        }

        $this->provider = new Microsoft([
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
            'redirectUri' => $this->getRedirectUri(),
            'urlAuthorize' => "https://login.microsoftonline.com/{$tenantId}/oauth2/v2.0/authorize",
            'urlAccessToken' => "https://login.microsoftonline.com/{$tenantId}/oauth2/v2.0/token",
            'urlResourceOwnerDetails' => 'https://graph.microsoft.com/v1.0/me',
            'scopes' => ['https://graph.microsoft.com/Mail.Send', 'https://graph.microsoft.com/User.Read']
        ]);
    }

    /**
     * Get the redirect URI for OAuth callback
     * @return string
     */
    private function getRedirectUri(): string
    {
        return APP_URL . '/app/oauth/microsoft/callback';
    }

    /**
     * Get the authorization URL
     * @return string
     */
    public function getAuthorizationUrl(): string
    {
        $authUrl = $this->provider->getAuthorizationUrl([
            'scope' => ['https://graph.microsoft.com/Mail.Send', 'https://graph.microsoft.com/User.Read']
        ]);

        // Store the state in session for security
        $_SESSION['oauth2state'] = $this->provider->getState();

        return $authUrl;
    }

    /**
     * Handle the OAuth callback and exchange code for tokens
     * @param string $code
     * @param string $state
     * @return array
     */
    public function handleCallback(string $code, string $state): array
    {
        // Verify state to prevent CSRF attacks
        if (empty($_SESSION['oauth2state']) || ($_SESSION['oauth2state'] !== $state)) {
            unset($_SESSION['oauth2state']);
            return ['valid' => false, 'message' => 'Invalid state parameter'];
        }

        unset($_SESSION['oauth2state']);

        try {
            // Exchange authorization code for access token
            $token = $this->provider->getAccessToken('authorization_code', [
                'code' => $code
            ]);

            // Save tokens to configuration
            $this->saveTokens($token);

            return ['valid' => true, 'message' => 'OAuth connection successful'];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => 'OAuth error: ' . $e->getMessage()];
        }
    }

    /**
     * Save OAuth tokens to configuration
     * @param AccessToken $token
     */
    private function saveTokens(AccessToken $token): void
    {
        $config = [
            ConfigEnum::OUTLOOK_OAUTH_ACCESS_TOKEN => $token->getToken(),
            ConfigEnum::OUTLOOK_OAUTH_REFRESH_TOKEN => $token->getRefreshToken(),
            ConfigEnum::OUTLOOK_OAUTH_TOKEN_EXPIRES => $token->getExpires(),
            ConfigEnum::OUTLOOK_OAUTH_CONNECTED => 'oui'
        ];

        $this->configService->saveConfig($config, $this->clientId);
    }

    /**
     * Get a valid access token (refresh if necessary)
     * @return string|null
     */
    public function getValidAccessToken(): ?string
    {
        $config = $this->configService->getConfig($this->clientId);
        
        $accessToken = $config[ConfigEnum::OUTLOOK_OAUTH_ACCESS_TOKEN] ?? '';
        $refreshToken = $config[ConfigEnum::OUTLOOK_OAUTH_REFRESH_TOKEN] ?? '';
        $expires = $config[ConfigEnum::OUTLOOK_OAUTH_TOKEN_EXPIRES] ?? 0;

        if (!$accessToken || !$refreshToken) {
            return null;
        }

        // Check if token is expired (with 5 minute buffer)
        if ($expires && $expires < (time() + 300)) {
            return $this->refreshAccessToken($refreshToken);
        }

        return $accessToken;
    }

    /**
     * Refresh the access token using refresh token
     * @param string $refreshToken
     * @return string|null
     */
    private function refreshAccessToken(string $refreshToken): ?string
    {
        try {
            $newToken = $this->provider->getAccessToken('refresh_token', [
                'refresh_token' => $refreshToken
            ]);

            $this->saveTokens($newToken);
            return $newToken->getToken();
        } catch (\Exception $e) {
            // If refresh fails, clear the tokens
            $this->clearTokens();
            return null;
        }
    }

    /**
     * Clear OAuth tokens from configuration
     */
    public function clearTokens(): void
    {
        $config = [
            ConfigEnum::OUTLOOK_OAUTH_ACCESS_TOKEN => '',
            ConfigEnum::OUTLOOK_OAUTH_REFRESH_TOKEN => '',
            ConfigEnum::OUTLOOK_OAUTH_TOKEN_EXPIRES => 0,
            ConfigEnum::OUTLOOK_OAUTH_CONNECTED => 'non'
        ];

        $this->configService->saveConfig($config, $this->clientId);
    }

    /**
     * Check if OAuth is connected and valid
     * @return bool
     */
    public function isConnected(): bool
    {
        $config = $this->configService->getConfig($this->clientId);
        $connected = $config[ConfigEnum::OUTLOOK_OAUTH_CONNECTED] ?? 'non';
        
        return $connected === 'oui' && !empty($config[ConfigEnum::OUTLOOK_OAUTH_REFRESH_TOKEN] ?? '');
    }

    /**
     * Test the OAuth connection by making a simple API call
     * @return array
     */
    public function testConnection(): array
    {
        $accessToken = $this->getValidAccessToken();
        
        if (!$accessToken) {
            return ['valid' => false, 'message' => 'No valid access token available'];
        }

        try {
            // Make a simple API call to test the connection
            $request = $this->provider->getAuthenticatedRequest(
                'GET',
                'https://graph.microsoft.com/v1.0/me',
                $accessToken
            );

            $response = $this->provider->getParsedResponse($request);
            
            if (isset($response['mail']) || isset($response['userPrincipalName'])) {
                return ['valid' => true, 'message' => 'Connection successful', 'user' => $response];
            }

            return ['valid' => false, 'message' => 'Invalid response from Microsoft Graph API'];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => 'Connection test failed: ' . $e->getMessage()];
        }
    }
}
