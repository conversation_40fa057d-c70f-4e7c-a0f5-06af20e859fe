<?php

namespace MatGyver\Commands;

use MatGyver\Services\ConfigService;
use MatGyver\Services\MailQueue\MailQueueService;

/**
 * Class MailQueueCommand
 * @package MatGyver\Commands
 */
class MailQueueCommand extends AbstractCommand
{
    private ConfigService $configService;
    private MailQueueService $mailQueueService;

    /**
     * MailQueueCommand constructor.
     * @param ConfigService $configService
     * @param MailQueueService $mailQueueService
     */
    public function __construct(
        ConfigService $configService,
        MailQueueService $mailQueueService
    ) {
        $this->configService = $configService;
        $this->mailQueueService = $mailQueueService;
    }

    public function execute()
    {
        if (ENV === ENV_DEV) {
            $configName = 'mail_queue_in_process';
            $config = $this->configService->findByName($configName);
            if ($config) {
                $this->configService->deleteConfig($configName);
            }
        }

        return $this->mailQueueService->sendMailQueue();
    }
}
