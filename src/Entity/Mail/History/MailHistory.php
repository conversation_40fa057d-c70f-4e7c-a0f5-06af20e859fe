<?php

namespace MatGyver\Entity\Mail\History;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON>ver\Entity\Client\Client;
use <PERSON>ed<PERSON>\Mapping\Annotation as Gedmo;
use MatG<PERSON><PERSON>\Entity\Dossier\Dossier;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Helpers\Assets;

#[ORM\Table(name: 'mg_mail_history')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mail\History\MailHistoryRepository::class)]
class MailHistory
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: true, name: 'dossier_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Dossier\Dossier::class)]
    private $dossier = null;

    #[ORM\Column(type: 'integer')]
    private $typeId;

    #[ORM\Column(type: 'string', length: 250)]
    private $subject;

    #[ORM\Column(type: 'string')]
    private $message;

    #[ORM\Column(type: 'string', nullable: true)]
    private $attachments;

    #[ORM\Column(type: 'string', length: 250, nullable: true)]
    private $fromName;

    #[ORM\Column(type: 'string', length: 250)]
    private $fromEmail;

    #[ORM\Column(type: 'boolean')]
    private $sent = false;

    #[ORM\Column(type: 'boolean')]
    private $error = false;

    #[ORM\Column(type: 'string')]
    private $errorMessage = '';

    #[ORM\Column(type: 'string')]
    private $preview = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Mail\History\MailHistoryUser::class, mappedBy: 'mail')]
    private $users;

    public function __construct()
    {
        $this->users = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getTypeId(): int
    {
        return $this->typeId;
    }

    public function setTypeId(int $typeId): self
    {
        $this->typeId = $typeId;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getAttachments(): ?string
    {
        return $this->attachments;
    }

    public function setAttachments(?string $attachments): self
    {
        $this->attachments = $attachments;

        return $this;
    }

    public function getFromName(): ?string
    {
        return $this->fromName;
    }

    public function setFromName(?string $fromName): self
    {
        $this->fromName = $fromName;

        return $this;
    }

    public function getFromEmail(): ?string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(string $fromEmail): self
    {
        $this->fromEmail = $fromEmail;

        return $this;
    }

    public function getSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getError(): ?bool
    {
        return $this->error;
    }

    public function setError(bool $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    public function getPreview(): ?string
    {
        return $this->preview;
    }

    public function setPreview(string $preview): self
    {
        $this->preview = $preview;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection|MailHistoryUser[]
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(MailHistoryUser $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->setMail($this);
        }

        return $this;
    }

    public function removeUser(MailHistoryUser $user): self
    {
        if ($this->users->contains($user)) {
            $this->users->removeElement($user);
            // set the owning side to null (unless already changed)
            if ($user->getMail() === $this) {
                $user->setMail(null);
            }
        }

        return $this;
    }

    public function getPreviewFullPath(): string
    {
        return WEB_PATH . '/medias/' . $this->getDossier()->getFolder() . $this->getPreview();
    }

    /**
     * @param bool $addDossierReference
     * @param bool $download
     * @return string
     */
    public function getPreviewUrl(bool $addDossierReference = false, bool $download = false): string
    {
        $query = ($addDossierReference ? '?dossier_ref=' . $this->getDossier()->getReference() : '');
        $query .= ($download ? ($query ? '&' : '?') . 'download' : '');

        return Assets::getMediaUrl($this->getDossier()->getFolder() . $this->getPreview() . $query);
    }
}
