<?php

namespace MatGyver\Entity\Notification;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON>yver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_notifications')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Notification\NotificationRepository::class)]
class Notification
{
    const TYPE_NOTIFICATION = 'notification';
    const TYPE_ERROR = 'error';
    const TYPE_IMPORTANT = 'important';

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $type;

    #[ORM\Column(type: 'text')]
    private $subject;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'boolean')]
    private $seen;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }
    
    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }
    
    public function getSeen(): ?bool
    {
        return $this->seen;
    }

    public function setSeen(bool $seen): self
    {
        $this->seen = $seen;

        return $this;
    }
    
    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getLabel(): array
    {
        switch ($this->type) {
            case self::TYPE_IMPORTANT:
                $class = 'warning';
                $info = __('Important');
                $icon = 'fas fa-exclamation-circle';
                break;
            case self::TYPE_ERROR:
                $class = 'danger';
                $info = __('Urgent');
                $icon = 'fas fa-exclamation-triangle';
                break;
            default:
                $class = 'primary';
                $info = __('Notification');
                $icon = 'fas fa-envelope-open-text';
                break;
        }
        return ['class' => $class, 'info' => $info, 'icon' => $icon];
    }
}
