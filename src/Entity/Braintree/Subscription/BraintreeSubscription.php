<?php

namespace Mat<PERSON><PERSON>ver\Entity\Braintree\Subscription;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\PaymentMethod\PaymentMethodSubscription;

#[ORM\Table(name: 'mg_braintree_subscriptions')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Braintree\Subscription\BraintreeSubscriptionRepository::class)]
#[Gedmo\SoftDeleteable(fieldName: 'deletedAt', timeAware: false)]
class BraintreeSubscription extends PaymentMethodSubscription
{
    #[ORM\Column(type: 'string', length: 100)]
    private $paymentToken;

    #[ORM\Column(type: 'string', length: 50)]
    private $customerId;

    #[ORM\Column(type: 'float')]
    private $amount;

    public function getPaymentToken(): ?string
    {
        return $this->paymentToken;
    }

    public function setPaymentToken(string $paymentToken): self
    {
        $this->paymentToken = $paymentToken;

        return $this;
    }

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }
}
