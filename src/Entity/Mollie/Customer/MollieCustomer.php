<?php

namespace <PERSON>Gyver\Entity\Mollie\Customer;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_mollie_customers')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mollie\Customer\MollieCustomerRepository::class)]
class MollieCustomer
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\Column(type: 'string', length: 100)]
    private $customerId;

    #[ORM\Column(type: 'string', length: 100)]
    private $paymentMethod = '';

    #[ORM\Column(type: 'string', length: 200)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 200)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 200)]
    private $email;

    #[ORM\Column(type: 'string', length: 10)]
    private $last4 = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getLast4(): ?string
    {
        return $this->last4;
    }

    public function setLast4(string $last4): self
    {
        $this->last4 = $last4;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
