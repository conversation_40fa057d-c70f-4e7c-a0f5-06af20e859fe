<?php

namespace MatGyver\Entity\Mollie\Account;

use Doctrine\ORM\Mapping as ORM;
use G<PERSON>mo\Mapping\Annotation as Ged<PERSON>;
use MatG<PERSON>ver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_mollie_accounts')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mollie\Account\MollieAccountRepository::class)]
class MollieAccount
{
    use ClientEntity;

    const STATUS_STARTED = 'started';
    const STATUS_VALIDATED = 'validated';
    const STATUS_ERROR = 'error';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 100)]
    private $mollieAccountId;

    #[ORM\JoinColumn(nullable: true, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\Column(type: 'string', length: 255)]
    private $email;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'text')]
    private $error = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getMollieAccountId(): ?string
    {
        return $this->mollieAccountId;
    }

    public function setMollieAccountId(string $mollieAccountId): self
    {
        $this->mollieAccountId = $mollieAccountId;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(?string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
