<?php

namespace MatGyver\Entity\Stripe\Payout;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_stripe_payouts')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Stripe\Payout\StripePayoutRepository::class)]
class StripePayout
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\Column(type: 'string', length: 100)]
    private $stripePayoutId;

    #[ORM\Column(type: 'integer')]
    private $amount;

    #[ORM\Column(type: 'string', length: 3)]
    private $currency;

    #[ORM\Column(type: 'string', length: 50)]
    private $status;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getStripePayoutId(): ?string
    {
        return $this->stripePayoutId;
    }

    public function setStripePayoutId(string $stripePayoutId): self
    {
        $this->stripePayoutId = $stripePayoutId;

        return $this;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
