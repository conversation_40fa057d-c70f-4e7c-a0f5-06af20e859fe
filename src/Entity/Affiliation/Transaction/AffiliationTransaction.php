<?php

namespace MatGyver\Entity\Affiliation\Transaction;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Affiliation\SubPartner\AffiliationSubPartner;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;

#[ORM\Table(name: 'aff_transactions')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Transaction\AffiliationTransactionRepository::class)]
class AffiliationTransaction
{
    const STATE_VALID = 'valid';
    const STATE_INVALID = 'invalid';
    const STATE_EXPIRED = 'expired';
    const STATE_REFUND = 'refund';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 200)]
    private $productName;

    #[ORM\Column(type: 'float')]
    private $amount;

    #[ORM\Column(type: 'float')]
    private $commission;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'boolean')]
    private $paid;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $datePayment;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\JoinColumn(nullable: false, name: 'subpartner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\SubPartner\AffiliationSubPartner::class)]
    private $subPartner;

    #[ORM\JoinColumn(nullable: false, name: 'transaction_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class)]
    private $transaction;

    #[ORM\JoinColumn(nullable: false, name: 'commission_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Commission\AffiliationCommission::class)]
    private $affiliationCommission;

    #[ORM\JoinColumn(nullable: false, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getCommission(): ?float
    {
        return $this->commission;
    }

    public function setCommission(float $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getPaid(): ?bool
    {
        return $this->paid;
    }

    public function setPaid(bool $paid): self
    {
        $this->paid = $paid;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDatePayment(): ?\DateTimeInterface
    {
        return $this->datePayment;
    }

    public function setDatePayment(\DateTimeInterface $datePayment): self
    {
        $this->datePayment = $datePayment;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getSubPartner(): ?AffiliationSubPartner
    {
        return $this->subPartner;
    }

    public function setSubPartner(?AffiliationSubPartner $subPartner): self
    {
        $this->subPartner = $subPartner;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getAffiliationCommission(): ?AffiliationCommission
    {
        return $this->affiliationCommission;
    }

    public function setAffiliationCommission(?AffiliationCommission $affiliationCommission): self
    {
        $this->affiliationCommission = $affiliationCommission;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }
}
