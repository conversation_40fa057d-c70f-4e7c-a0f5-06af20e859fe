<?php

namespace MatGyver\Entity\Affiliation\Commission;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'aff_commissions')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Commission\AffiliationCommissionRepository::class)]
class AffiliationCommission
{
    const STATE_VALID = 'valid';
    const STATE_INVALID = 'invalid';
    const STATE_EXPIRED = 'expired';
    const STATE_REFUND = 'refund';

    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $transactionReference;

    #[ORM\Column(type: 'string', length: 10)]
    private $type = 'normal';

    #[ORM\Column(type: 'string', length: 200)]
    private $product;

    #[ORM\Column(type: 'float')]
    private $amountTaxExcl;

    #[ORM\Column(type: 'float')]
    private $commission;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'boolean')]
    private $paid;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $datePayment;

    #[ORM\Column(type: 'datetime')]
    private $dateModification;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class, inversedBy: 'commissions')]
    private $partner;

    #[ORM\JoinTable(name: 'aff_commissions_invoices')]
    #[ORM\JoinColumn(name: 'aff_commission_id', referencedColumnName: 'id')]
    #[ORM\InverseJoinColumn(name: 'aff_invoice_id', referencedColumnName: 'id')]
    #[ORM\ManyToMany(targetEntity: \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::class, inversedBy: 'commissions')]
    private $invoices;

    public function __construct()
    {
        $this->invoices = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getProduct(): ?string
    {
        return $this->product;
    }

    public function setProduct(string $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getAmountTaxExcl(): ?float
    {
        return $this->amountTaxExcl;
    }

    public function setAmountTaxExcl(float $amountTaxExcl): self
    {
        $this->amountTaxExcl = $amountTaxExcl;

        return $this;
    }

    public function getCommission(): ?float
    {
        return $this->commission;
    }

    public function setCommission(float $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getPaid(): ?bool
    {
        return $this->paid;
    }

    public function setPaid(bool $paid): self
    {
        $this->paid = $paid;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDatePayment(): ?\DateTimeInterface
    {
        return $this->datePayment;
    }

    public function setDatePayment(\DateTimeInterface $datePayment): self
    {
        $this->datePayment = $datePayment;

        return $this;
    }

    public function getDateModification(): ?\DateTimeInterface
    {
        return $this->dateModification;
    }

    public function setDateModification(\DateTimeInterface $dateModification): self
    {
        $this->dateModification = $dateModification;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    /**
     * @return Collection|AffiliationInvoice[]
     */
    public function getInvoices(): Collection
    {
        return $this->invoices;
    }

    public function addInvoice(AffiliationInvoice $invoice): self
    {
        if (!$this->invoices->contains($invoice)) {
            $this->invoices[] = $invoice;
        }

        return $this;
    }

    public function removeInvoice(AffiliationInvoice $invoice): self
    {
        if ($this->invoices->contains($invoice)) {
            $this->invoices->removeElement($invoice);
        }

        return $this;
    }

    /**
     * @return array
     */
    public function getCommissionStatus() :array
    {
        $guaranteeDuration = COMMISSIONS_AVAILABLE_DELAY;

        switch ($this->getStatus()) {
            case self::STATE_VALID:
                $name = __('Valide');
                $label = 'light-success';
                $icon = 'fa fa-check';

                if ($guaranteeDuration !== null and $this->getDate() !== null) {
                    $dateNow = new \DateTime();
                    $dateLimit = clone $this->getDate();
                    $dateLimit->modify('+' . $guaranteeDuration . ' days');
                    if ($dateNow <= $dateLimit) {
                        $name = __('En attente');
                        $label = 'info';
                        $icon = 'fa fa-hourglass-half';
                    }
                }
                if ($this->getPaid()) {
                    $name = __('Payée');
                    $label = 'light-success';
                    $icon = 'fa fa-check';
                }
                break;

            case self::STATE_INVALID:
                $name = __('Invalide');
                $label = 'light-danger';
                $icon = 'fa fa-times';
                break;

            case self::STATE_REFUND:
                $name = __('Remboursée');
                $label = 'light-warning';
                $icon = 'fa fa-times';
                break;

            case self::STATE_EXPIRED:
                $name = __('Expirée');
                $label = 'light-default';
                $icon = 'fa fa-clock-o';
                break;

            default:
                $name = $this->getStatus();
                $label = 'light-info';
                $icon = 'fa fa-times';
                break;
        }

        return [
            'name' => $name,
            'label' => $label,
            'icon' => $icon
        ];
    }

    /**
     * @return string
     */
    public function displayCommissionType(): string
    {
        switch ($this->getType()) {
            case 'normal':
                $type = '<span class="label label-success label-inline">' . __('Commission') . '</span>';
                break;
            case 'super':
                $type = '<span class="label label-primary label-inline">' . __('SuperCommission') . '</span>';
                break;
            case 'second':
                $type = '<span class="label label-default label-inline">' . __('Second niveau') . '</span>';
                break;
            case 'extra':
                $type = '<span class="label label-info label-inline">'. __('Extra commission').'</span>';
                break;
            default:
                $type = '<span class="label label-danger label-inline">' . __('Type inconnu') . '</span>';
                break;
        }

        return $type;
    }
}
