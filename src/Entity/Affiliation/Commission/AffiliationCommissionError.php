<?php

namespace MatGyver\Entity\Affiliation\Commission;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Shop\Product\ShopProduct;

#[ORM\Table(name: 'aff_commissions_error')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Commission\AffiliationCommissionErrorRepository::class)]
class AffiliationCommissionError
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $transactionReference;

    #[ORM\JoinColumn(nullable: true, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\JoinColumn(nullable: true, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    #[ORM\Column(type: 'float')]
    private $amountTaxExcl;

    #[ORM\Column(type: 'float')]
    private $commission;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'text')]
    private $error;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $datePayment;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getAmountTaxExcl(): ?float
    {
        return $this->amountTaxExcl;
    }

    public function setAmountTaxExcl(float $amountTaxExcl): self
    {
        $this->amountTaxExcl = $amountTaxExcl;

        return $this;
    }

    public function getCommission(): ?float
    {
        return $this->commission;
    }

    public function setCommission(float $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDatePayment(): ?\DateTimeInterface
    {
        return $this->datePayment;
    }

    public function setDatePayment(\DateTimeInterface $datePayment): self
    {
        $this->datePayment = $datePayment;

        return $this;
    }

}
