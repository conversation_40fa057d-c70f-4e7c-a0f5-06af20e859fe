<?php

namespace MatGyver\Entity\Affiliation\Partner;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice;
use MatGyver\Entity\Affiliation\Payment\AffiliationPayment;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'aff_partners')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Partner\AffiliationPartnerRepository::class)]
class AffiliationPartner
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 200)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 200)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 200)]
    private $email;

    #[ORM\Column(type: 'integer')]
    private $parent;

    #[ORM\Column(type: 'text')]
    private $comment;

    #[ORM\Column(type: 'boolean')]
    private $approved;

    #[ORM\Column(type: 'string', length: 50)]
    private $randomId;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'client_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false, name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $user;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::class, mappedBy: 'partner', orphanRemoval: true)]
    private $invoices;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Affiliation\Payment\AffiliationPayment::class, mappedBy: 'partner')]
    private $payments;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Affiliation\Commission\AffiliationCommission::class, mappedBy: 'partner')]
    private $commissions;

    public function __construct()
    {
        $this->payments = new ArrayCollection();
        $this->commissions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getParent(): ?int
    {
        return $this->parent;
    }

    public function setParent(int $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getApproved(): ?bool
    {
        return $this->approved;
    }

    public function setApproved(bool $approved): self
    {
        $this->approved = $approved;

        return $this;
    }

    public function getRandomId(): ?string
    {
        return $this->randomId;
    }

    public function setRandomId(string $randomId): self
    {
        $this->randomId = $randomId;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return Collection|AffiliationInvoice[]
     */
    public function getInvoices(): Collection
    {
        return $this->invoices;
    }

    /**
     * @param AffiliationInvoice $invoice
     * @return AffiliationPartner
     */
    public function addInvoice(AffiliationInvoice $invoice): self
    {
        if (!$this->invoices->contains($invoice)) {
            $this->invoices[] = $invoice;
            $invoice->setPartner($this);
        }

        return $this;
    }

    /**
     * @return Collection|AffiliationPayment[]
     */
    public function getPayments(): Collection
    {
        return $this->payments;
    }

    public function addPayment(AffiliationPayment $payment): self
    {
        if (!$this->payments->contains($payment)) {
            $this->payments[] = $payment;
            $payment->setPartner($this);
        }

        return $this;
    }

    public function removePayment(AffiliationPayment $payment): self
    {
        if ($this->payments->contains($payment)) {
            $this->payments->removeElement($payment);
            // set the owning side to null (unless already changed)
            if ($payment->getPartner() === $this) {
                $payment->setPartner(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|AffiliationCommission[]
     */
    public function getCommissions(): Collection
    {
        return $this->commissions;
    }

    public function addCommission(AffiliationCommission $commission): self
    {
        if (!$this->commissions->contains($commission)) {
            $this->commissions[] = $commission;
            $commission->setPartner($this);
        }

        return $this;
    }

    public function removeCommission(AffiliationCommission $commission): self
    {
        if ($this->commissions->contains($commission)) {
            $this->commissions->removeElement($commission);
            // set the owning side to null (unless already changed)
            if ($commission->getPartner() === $this) {
                $commission->setPartner(null);
            }
        }

        return $this;
    }
}
