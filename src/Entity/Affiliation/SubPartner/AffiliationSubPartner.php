<?php

namespace MatGyver\Entity\Affiliation\SubPartner;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON>ver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'aff_subpartners')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\SubPartner\AffiliationSubPartnerRepository::class)]
class AffiliationSubPartner
{

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 200)]
    private $name;

    #[ORM\Column(type: 'integer')]
    private $parent;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'client_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\JoinColumn(nullable: false, name: 'subpartner_client_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $subPartnerClient;

    #[ORM\JoinColumn(nullable: false, name: 'subpartner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $subPartner;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getParent(): ?int
    {
        return $this->parent;
    }

    public function setParent(int $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getSubPartnerClient(): ?Client
    {
        return $this->subPartnerClient;
    }

    public function setSubPartnerClient(?Client $subPartnerClient): self
    {
        $this->subPartnerClient = $subPartnerClient;

        return $this;
    }

    public function getSubPartner(): ?AffiliationPartner
    {
        return $this->subPartner;
    }

    public function setSubPartner(?AffiliationPartner $subPartner): self
    {
        $this->subPartner = $subPartner;

        return $this;
    }
}
