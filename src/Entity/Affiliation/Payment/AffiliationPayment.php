<?php

namespace MatGyver\Entity\Affiliation\Payment;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON>ver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice;

#[ORM\Table(name: 'aff_payments')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Payment\AffiliationPaymentRepository::class)]
class AffiliationPayment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'float')]
    private $amount;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::class, mappedBy: 'payment', cascade: ['persist', 'remove'])]
    private $invoice;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getInvoice(): ?AffiliationInvoice
    {
        return $this->invoice;
    }

    public function setInvoice(?AffiliationInvoice $invoice): self
    {
        $this->invoice = $invoice;

        // set (or unset) the owning side of the relation if necessary
        $newAffiliationPayment = null === $invoice ? null : $this;
        if ($invoice->getPayment() !== $newAffiliationPayment) {
            $invoice->setPayment($newAffiliationPayment);
        }

        return $this;
    }

}
