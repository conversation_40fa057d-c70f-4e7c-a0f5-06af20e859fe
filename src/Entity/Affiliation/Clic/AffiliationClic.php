<?php

namespace MatGyver\Entity\Affiliation\Clic;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Affiliation\Link\AffiliationLink;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;

#[ORM\Table(name: 'aff_clics')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Clic\AffiliationClicRepository::class)]
class AffiliationClic
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'string', length: 200)]
    private $referer;

    #[ORM\Column(type: 'boolean')]
    private $valid = 1;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\JoinColumn(nullable: false, name: 'link_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Link\AffiliationLink::class)]
    private $link;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getReferer(): ?string
    {
        return $this->referer;
    }

    public function setReferer(string $referer): self
    {
        $this->referer = $referer;

        return $this;
    }

    public function getValid(): ?bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): self
    {
        $this->valid = $valid;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getLink(): ?AffiliationLink
    {
        return $this->link;
    }

    public function setLink(?AffiliationLink $link): self
    {
        $this->link = $link;

        return $this;
    }
}
