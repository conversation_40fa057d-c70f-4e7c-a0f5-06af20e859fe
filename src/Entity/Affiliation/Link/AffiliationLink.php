<?php

namespace MatGyver\Entity\Affiliation\Link;

use Doctrine\ORM\Mapping as ORM;
use MatGy<PERSON>\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'aff_links')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Link\AffiliationLinkRepository::class)]
class AffiliationLink
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 200)]
    private $name;

    #[ORM\Column(type: 'string', length: 200)]
    private $permalink;

    #[ORM\Column(type: 'text')]
    private $url;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'integer')]
    private $nbClics;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getPermalink(): ?string
    {
        return $this->permalink;
    }

    public function setPermalink(string $permalink): self
    {
        $this->permalink = $permalink;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getNbClics(): ?int
    {
        return $this->nbClics;
    }

    public function setNbClics(int $nbClics): self
    {
        $this->nbClics = $nbClics;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
