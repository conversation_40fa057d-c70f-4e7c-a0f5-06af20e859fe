<?php

namespace MatGyver\Entity\Affiliation\Invoice;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Entity\Affiliation\Payment\AffiliationPayment;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'aff_invoices')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Invoice\AffiliationInvoiceRepository::class)]
class AffiliationInvoice
{
    const STATUS_SENT = 'sent';
    const STATUS_REFUSED = 'refused';
    const STATUS_VALIDATED = 'validated';
    const STATUS_PAID = 'paid';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private $preTaxAmount;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private $vatPercent;

    #[ORM\Column(type: 'string', length: 255)]
    private $status;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $uploadedAt;

    #[ORM\JoinColumn(nullable: true, referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $refusedBy;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $refusedAt;

    #[ORM\Column(type: 'text', nullable: true)]
    private $refusedReason;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class, inversedBy: 'invoices')]
    private $partner;

    #[ORM\Column(type: 'text')]
    private $filePath;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $lastModification;

    #[ORM\ManyToMany(targetEntity: \MatGyver\Entity\Affiliation\Commission\AffiliationCommission::class, mappedBy: 'invoices')]
    private $commissions;

    #[ORM\JoinColumn(nullable: true, name: 'payment_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Affiliation\Payment\AffiliationPayment::class, inversedBy: 'invoice', cascade: ['persist', 'remove'])]
    private $payment;

    public function __construct()
    {
        $this->commissions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPreTaxAmount()
    {
        return $this->preTaxAmount;
    }

    public function setPreTaxAmount($preTaxAmount): self
    {
        $this->preTaxAmount = $preTaxAmount;

        return $this;
    }

    public function getVatPercent()
    {
        return $this->vatPercent;
    }

    public function setVatPercent($vatPercent): self
    {
        $this->vatPercent = $vatPercent;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUploadedAt(): ?\DateTimeInterface
    {
        return $this->uploadedAt;
    }

    public function setUploadedAt(?\DateTimeInterface $uploadedAt): self
    {
        $this->uploadedAt = $uploadedAt;

        return $this;
    }

    public function getRefusedBy(): ?User
    {
        return $this->refusedBy;
    }

    public function setRefusedBy(?User $refusedBy): self
    {
        $this->refusedBy = $refusedBy;

        return $this;
    }

    public function getRefusedAt(): ?\DateTimeInterface
    {
        return $this->refusedAt;
    }

    public function setRefusedAt(?\DateTimeInterface $refusedAt): self
    {
        $this->refusedAt = $refusedAt;

        return $this;
    }

    public function getRefusedReason(): ?string
    {
        return $this->refusedReason;
    }

    public function setRefusedReason(?string $refusedReason): self
    {
        $this->refusedReason = $refusedReason;

        return $this;
    }

    /**
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_SENT => __('Envoyée'),
            self::STATUS_VALIDATED => __('Validée'),
            self::STATUS_REFUSED => __('Refusée'),
            self::STATUS_PAID => __('Payée'),
        ];
    }

    /**
     * @return bool
     */
    public function isSended(): bool
    {
        return $this->status === self::STATUS_SENT;
    }

    /**
     * @return bool
     */
    public function isValidated(): bool
    {
        return $this->status === self::STATUS_VALIDATED;
    }

    /**
     * @return bool
     */
    public function isRefused(): bool
    {
        return $this->status === self::STATUS_REFUSED;
    }

    /**
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * @return null|string
     */
    public function getDisplayStatus(): ?string
    {
        if (null === $this->getStatus()) {
            return null;
        }

        $statuses = self::getStatuses();
        return $statuses[$this->getStatus()];
    }

    /**
     * @return float
     */
    public function getPostTaxAmount(): float
    {
        $postTaxAmount = $this->preTaxAmount * (($this->vatPercent + 100) / 100);
        return round($postTaxAmount, 2);
    }

    /**
     * @return AffiliationPartner|null
     */
    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    /**
     * @param AffiliationPartner|null $partner
     * @return AffiliationInvoice
     */
    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    public function setFilePath(?string $filePath): self
    {
        $this->filePath = $filePath;

        return $this;
    }

    public function getLastModification(): ?\DateTimeInterface
    {
        return $this->lastModification;
    }

    public function setLastModification(?\DateTimeInterface $lastModification): self
    {
        $this->lastModification = $lastModification;

        return $this;
    }

    /**
     * @return Collection|AffiliationCommission[]
     */
    public function getCommissions(): Collection
    {
        return $this->commissions;
    }

    public function addCommissions(AffiliationCommission $commissions): self
    {
        if (!$this->commissions->contains($commissions)) {
            $this->commissions[] = $commissions;
            $commissions->addInvoice($this);
        }

        return $this;
    }

    public function removeCommissions(AffiliationCommission $commissions): self
    {
        if ($this->commissions->contains($commissions)) {
            $this->commissions->removeElement($commissions);
            $commissions->removeInvoice($this);
        }

        return $this;
    }

    public function getPayment(): ?AffiliationPayment
    {
        return $this->payment;
    }

    public function setPayment(?AffiliationPayment $payment): self
    {
        $this->payment = $payment;

        return $this;
    }

}
