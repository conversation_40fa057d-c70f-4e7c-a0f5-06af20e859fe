<?php

namespace MatGyver\Entity\Affiliation\Rule;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'aff_rules')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Rule\AffiliationRuleRepository::class)]
class AffiliationRule
{
    CONST ACTION_NO_COMMISSION = 'no_commission';
    CONST ACTION_NEW_RATE = 'new_rate';
    CONST ACTION_NEW_AMOUNT = 'new_amount';

    CONST CONDITION_NB_COMMISSIONS = 'nb_commissions';
    CONST CONDITION_PRODUCT_NAME = 'product_name';
    CONST CONDITION_AMOUNT_GREATER = 'amount_greater';
    CONST CONDITION_AMOUNT_LESS = 'amount_less';
    CONST CONDITION_AMOUNT_TOTAL = 'amount_total';
    CONST CONDITION_LEAD_EXIST = 'lead_exist';
    CONST CONDITION_CLIENT_EXIST = 'client_exist';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $conditionRule;

    #[ORM\Column(type: 'string', length: 250)]
    private $value;

    #[ORM\Column(type: 'float')]
    private $commission;

    #[ORM\Column(type: 'string', length: 50)]
    private $action;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getConditionRule(): ?string
    {
        return $this->conditionRule;
    }

    public function setConditionRule(string $conditionRule): self
    {
        $this->conditionRule = $conditionRule;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getCommission(): ?float
    {
        return $this->commission;
    }

    public function setCommission(float $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
