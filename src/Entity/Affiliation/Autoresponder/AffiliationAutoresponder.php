<?php

namespace MatGyver\Entity\Affiliation\Autoresponder;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON>ver\Entity\Integration\Account\IntegrationAccount;

#[ORM\Table(name: 'aff_autoresponders')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Affiliation\Autoresponder\AffiliationAutoresponderRepository::class)]
class AffiliationAutoresponder
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 20)]
    private $type;

    #[ORM\Column(type: 'text')]
    private $datas;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }
    
    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
    
    public function getDatas(): ?string
    {
        return $this->datas;
    }

    public function setDatas(string $datas): self
    {
        $this->datas = $datas;

        return $this;
    }
    
    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

}
