<?php

namespace MatGyver\Entity\Company;

use MatGyver\Entity\Client\Client;
use MatGyver\Repository\Company\CompanyRepository;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'mg_companies')]
#[ORM\Entity(repositoryClass: CompanyRepository::class)]
class Company
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'string', length: 255)]
    private $firstName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $lastName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $address;

    #[ORM\Column(type: 'string', length: 255)]
    private $address2 = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $zip;

    #[ORM\Column(type: 'string', length: 255)]
    private $city;

    #[ORM\Column(type: 'string', length: 2)]
    private $country;

    #[ORM\Column(type: 'string', length: 50)]
    private $telephone = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $email = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $siret = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $naf = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $tvaIntracom = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $expertRegistrationNumber = '';

    #[ORM\Column(type: 'integer', nullable: true)]
    private $mfAddressId = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private $introduction = null;

    #[ORM\Column(type: 'text')]
    private $generalConditions = '';

    #[ORM\Column(type: 'text')]
    private $signatureIntroduction = '';

    #[ORM\Column(type: 'text', nullable: true)]
    private $outro = null;

    #[ORM\Column(type: 'float')]
    private $amount = 0;

    #[ORM\Column(type: 'boolean')]
    private $judiciaireLogo = true;

    #[ORM\Column(type: 'float')]
    private $judiciaireAmount = 0;

    #[ORM\Column(type: 'text')]
    private $judiciaireIntroduction = '';

    #[ORM\Column(type: 'text')]
    private $judiciaireDeclarationIndependence = '';

    #[ORM\Column(type: 'text')]
    private $judiciaireObservationsPreliminaryReport = '';

    #[ORM\Column(type: 'text')]
    private $judiciaireObservationsFinalReport = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setAddress2(string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getZip(): ?string
    {
        return $this->zip;
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getSiret(): ?string
    {
        return $this->siret;
    }

    public function setSiret(string $siret): self
    {
        $this->siret = $siret;

        return $this;
    }

    public function getNaf(): ?string
    {
        return $this->naf;
    }

    public function setNaf(string $naf): self
    {
        $this->naf = $naf;

        return $this;
    }

    public function getTvaIntracom(): ?string
    {
        return $this->tvaIntracom;
    }

    public function setTvaIntracom(string $tvaIntracom): self
    {
        $this->tvaIntracom = $tvaIntracom;

        return $this;
    }

    public function getExpertRegistrationNumber(): ?string
    {
        return $this->expertRegistrationNumber;
    }

    public function setExpertRegistrationNumber(string $expertRegistrationNumber): self
    {
        $this->expertRegistrationNumber = $expertRegistrationNumber;

        return $this;
    }

    public function getMFAddressId(): ?int
    {
        return $this->mfAddressId;
    }

    public function setMFAddressId(?int $mfAddressId): self
    {
        $this->mfAddressId = $mfAddressId;

        return $this;
    }

    public function getIntroduction(): ?string
    {
        return $this->introduction;
    }

    public function setIntroduction(string $introduction): self
    {
        $this->introduction = $introduction;

        return $this;
    }

    public function getGeneralConditions(): ?string
    {
        return $this->generalConditions;
    }

    public function setGeneralConditions(string $generalConditions): self
    {
        $this->generalConditions = $generalConditions;

        return $this;
    }

    public function getSignatureIntroduction(): ?string
    {
        return $this->signatureIntroduction;
    }

    public function setSignatureIntroduction(string $signatureIntroduction): self
    {
        $this->signatureIntroduction = $signatureIntroduction;

        return $this;
    }

    public function getOutro(): ?string
    {
        return $this->outro;
    }

    public function setOutro(string $outro): self
    {
        $this->outro = $outro;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getJudiciaireLogo(): ?bool
    {
        return $this->judiciaireLogo;
    }

    public function setJudiciaireLogo(bool $judiciaireLogo): self
    {
        $this->judiciaireLogo = $judiciaireLogo;

        return $this;
    }

    public function getJudiciaireAmount(): ?float
    {
        return $this->judiciaireAmount;
    }

    public function setJudiciaireAmount(float $judiciaireAmount): self
    {
        $this->judiciaireAmount = $judiciaireAmount;

        return $this;
    }

    public function getJudiciaireIntroduction(): ?string
    {
        return $this->judiciaireIntroduction;
    }

    public function setJudiciaireIntroduction(string $judiciaireIntroduction): self
    {
        $this->judiciaireIntroduction = $judiciaireIntroduction;

        return $this;
    }

    public function getJudiciaireDeclarationIndependence(): ?string
    {
        return $this->judiciaireDeclarationIndependence;
    }

    public function setJudiciaireDeclarationIndependence(string $judiciaireDeclarationIndependence): self
    {
        $this->judiciaireDeclarationIndependence = $judiciaireDeclarationIndependence;

        return $this;
    }

    public function getJudiciaireObservationsPreliminaryReport(): ?string
    {
        return $this->judiciaireObservationsPreliminaryReport;
    }

    public function setJudiciaireObservationsPreliminaryReport(string $judiciaireObservationsPreliminaryReport): self
    {
        $this->judiciaireObservationsPreliminaryReport = $judiciaireObservationsPreliminaryReport;

        return $this;
    }

    public function getJudiciaireObservationsFinalReport(): ?string
    {
        return $this->judiciaireObservationsFinalReport;
    }

    public function setJudiciaireObservationsFinalReport(string $judiciaireObservationsFinalReport): self
    {
        $this->judiciaireObservationsFinalReport = $judiciaireObservationsFinalReport;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
