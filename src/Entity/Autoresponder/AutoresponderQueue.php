<?php

namespace MatGyver\Entity\Autoresponder;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Traits\ClientEntity;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'mg_autoresponders_queue')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Autoresponder\AutoresponderQueueRepository::class)]
class AutoresponderQueue
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 20)]
    private $type;

    #[ORM\Column(type: 'text')]
    private $autoresponderData;

    #[ORM\Column(type: 'text')]
    private $data;

    #[ORM\Column(type: 'boolean')]
    private $processed = false;

    #[ORM\Column(type: 'integer')]
    private $attempt = 0;

    #[ORM\Column(type: 'text')]
    private $error = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getAutoresponderData(): ?string
    {
        return $this->autoresponderData;
    }

    public function setAutoresponderData(string $autoresponderData): self
    {
        $this->autoresponderData = $autoresponderData;

        return $this;
    }

    public function getData(): ?string
    {
        return $this->data;
    }

    public function setData(string $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getProcessed(): ?bool
    {
        return $this->processed;
    }

    public function setProcessed(bool $processed): self
    {
        $this->processed = $processed;

        return $this;
    }

    public function getAttempt(): ?int
    {
        return $this->attempt;
    }

    public function setAttempt(int $attempt): self
    {
        $this->attempt = $attempt;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }
}
