<?php

namespace MatGyver\Entity\Event;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'mg_events')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Event\EventRepository::class)]
class Event
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'text')]
    private $image;

    #[ORM\Column(type: 'text')]
    private $bgImage;

    #[ORM\Column(type: 'text')]
    private $smallImage;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateStart;

    #[ORM\Column(type: 'datetime')]
    private $dateEnd;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Event\EventProduct::class, mappedBy: 'event', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private $eventProducts;

    public function __construct()
    {
        $this->eventProducts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getBgImage(): ?string
    {
        return $this->bgImage;
    }

    public function setBgImage(string $bgImage): self
    {
        $this->bgImage = $bgImage;

        return $this;
    }

    public function getSmallImage(): ?string
    {
        return $this->smallImage;
    }

    public function setSmallImage(string $smallImage): self
    {
        $this->smallImage = $smallImage;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateStart(): ?\DateTimeInterface
    {
        return $this->dateStart;
    }

    public function setDateStart(?\DateTimeInterface $dateStart): self
    {
        $this->dateStart = $dateStart;

        return $this;
    }

    public function getDateEnd(): ?\DateTimeInterface
    {
        return $this->dateEnd;
    }

    public function setDateEnd(?\DateTimeInterface $dateEnd): self
    {
        $this->dateEnd = $dateEnd;

        return $this;
    }

    /**
     * @return Collection|EventProduct[]
     */
    public function getEventProducts(): Collection
    {
        return $this->eventProducts;
    }

    public function addEventProduct(EventProduct $eventProduct): self
    {
        if (!$this->eventProducts->contains($eventProduct)) {
            $this->eventProducts[] = $eventProduct;
            $eventProduct->setEvent($this);
        }

        return $this;
    }

    public function removeEventProduct(EventProduct $eventProduct): self
    {
        if ($this->eventProducts->contains($eventProduct)) {
            $this->eventProducts->removeElement($eventProduct);
            // set the owning side to null (unless already changed)
            if ($eventProduct->getEvent() === $this) {
                $eventProduct->setEvent(null);
            }
        }

        return $this;
    }
}
