<?php

namespace MatGyver\Entity\Nationality;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mg_nationalities')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Nationality\NationalityRepository::class)]
class Nationality
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 2)]
    private $code;

    #[ORM\Column(type: 'string')]
    private $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
