<?php

namespace MatGyver\Entity\Support\Ticket;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatG<PERSON>ver\Entity\Support\Department\SupportDepartment;
use MatGyver\Entity\Support\Rate\SupportRate;
use MatGyver\Entity\Support\Service\SupportService;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'hdk_tickets')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Support\Ticket\SupportTicketRepository::class)]
class SupportTicket
{
    const FLAG_SPAM = 'spam';
    const FLAG_IMPORTANT = 'important';

    const STATUS_CLOSE = 'close';
    const STATUS_OPEN = 'open';

    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';

    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 10)]
    private $ticketid = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $categories = '';

    #[ORM\Column(type: 'string', length: 100)]
    private $lastName = '';

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 250)]
    private $email = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $telephone;

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $subject;

    #[ORM\Column(type: 'text')]
    private $message;

    #[ORM\Column(type: 'string', length: 20)]
    private $priority = 'Moyenne';

    #[ORM\Column(type: 'string', length: 10)]
    private $status;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private $flag;

    #[ORM\Column(type: 'boolean')]
    private $answered = 0;

    #[ORM\Column(type: 'string', length: 20)]
    private $ip = '';

    #[ORM\Column(type: 'string', length: 100)]
    private $city;

    #[ORM\Column(type: 'string', length: 100)]
    private $country;

    #[ORM\Column(type: 'text')]
    private $attachments;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateCreation;

    #[ORM\Column(type: 'datetime')]
    private $dateClose;

    #[ORM\JoinColumn(nullable: false, name: 'admin_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $admin;

    #[ORM\JoinColumn(nullable: false, name: 'service_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Support\Service\SupportService::class)]
    private $service;

    #[ORM\JoinColumn(nullable: false, name: 'department_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Support\Department\SupportDepartment::class)]
    private $department;

    #[ORM\JoinColumn(nullable: true, name: 'rate_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Support\Rate\SupportRate::class, cascade: ['persist', 'remove'])]
    private $rate;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTicketid(): ?string
    {
        return $this->ticketid;
    }

    public function setTicketid(string $ticketid): self
    {
        $this->ticketid = $ticketid;

        return $this;
    }

    public function getCategories(): ?string
    {
        return $this->categories;
    }

    public function setCategories(string $categories): self
    {
        $this->categories = $categories;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFlag(): ?string
    {
        return $this->flag;
    }

    public function setFlag(?string $flag): self
    {
        $this->flag = $flag;

        return $this;
    }

    public function getAnswered(): ?bool
    {
        return $this->answered;
    }

    public function setAnswered(bool $answered): self
    {
        $this->answered = $answered;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getAttachments(): ?string
    {
        return $this->attachments;
    }

    public function setAttachments(string $attachments): self
    {
        $this->attachments = $attachments;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): self
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getDateClose(): ?\DateTimeInterface
    {
        return $this->dateClose;
    }

    public function setDateClose(\DateTimeInterface $dateClose): self
    {
        $this->dateClose = $dateClose;

        return $this;
    }

    /**
     * @return User|null
     */
    public function getAdmin(): ?User
    {
        return $this->admin;
    }

    /**
     * @param User|null $admin
     * @return $this
     */
    public function setAdmin(?User $admin): SupportTicket
    {
        $this->admin = $admin;

        return $this;
    }

    public function getService(): ?SupportService
    {
        return $this->service;
    }

    public function setService(?SupportService $service): self
    {
        $this->service = $service;

        return $this;
    }

    public function getDepartment(): ?SupportDepartment
    {
        return $this->department;
    }

    public function setDepartment(?SupportDepartment $department): self
    {
        $this->department = $department;

        return $this;
    }

    public function getRate(): ?SupportRate
    {
        return $this->rate;
    }

    public function setRate(?SupportRate $rate): self
    {
        $this->rate = $rate;

        return $this;
    }

    /**
     * @return string
     */
    public function renderPriorityLabel(): string
    {
        switch ($this->getPriority()) {
            case self::PRIORITY_LOW:
                $priorityLabel = '<span class="label label-default label-inline mr-1">' . __('Basse') . '</span>';
                break;
            case self::PRIORITY_MEDIUM:
                $priorityLabel = '<span class="label label-success label-inline mr-1">' . __('Moyenne') . '</span>';
                break;
            case self::PRIORITY_HIGH:
                $priorityLabel = '<span class="label label-danger label-inline mr-1">' . __('Haute') . '</span>';
                break;
            default:
                $priorityLabel = '<span class="label label-primary label-inline mr-1">' . $this->getPriority() . '</span>';
                break;
        }

        return $priorityLabel;
    }
}
