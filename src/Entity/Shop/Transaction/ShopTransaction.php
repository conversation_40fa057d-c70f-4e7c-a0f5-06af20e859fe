<?php

namespace MatGyver\Entity\Shop\Transaction;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Dossier\DossierPaymentLink;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Shop\Cart\ShopCart;
use MatGyver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\SoftDeleteableEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'shop_transactions')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Transaction\ShopTransactionRepository::class)]
#[Gedmo\SoftDeleteable(fieldName: 'deletedAt', timeAware: false)]
class ShopTransaction
{
    use ClientEntity;
    use UserEntity;
    use SoftDeleteableEntity;

    const METHOD_AUTRE = 'autre';
    const METHOD_BRAINTREE = 'Braintree';
    const METHOD_CHEQUE = 'cheque';
    const METHOD_MOLLIE = 'Mollie';
    const METHOD_PAYPAL = 'Paypal';
    const METHOD_STRIPE = 'Stripe';
    const METHOD_VIREMENT = 'virement';

    const STATUS_CANCELED = 'CANCELED';
    const STATUS_COMPLETED = 'COMPLETED';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_DELIVERING = 'delivering';
    const STATUS_WAITING = 'waiting';
    const STATUS_ERROR = 'ERROR';
    const STATUS_FAILED = 'FAILED';
    const STATUS_REFUNDED = 'REFUNDED';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $reference;

    #[ORM\Column(type: 'boolean')]
    private $valid;

    #[ORM\Column(type: 'string', length: 20)]
    private $status = '';

    #[ORM\Column(type: 'string', length: 20)]
    private $paymentMethod;

    #[ORM\Column(type: 'float')]
    private $amountTaxExcl;

    #[ORM\Column(type: 'float')]
    private $amountTaxIncl = 0;

    #[ORM\Column(type: 'string')]
    private $currency = '';

    #[ORM\Column(type: 'string', length: 100)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 100)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 255)]
    private $email = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $productName = '';

    #[ORM\Column(type: 'text')]
    private $request;

    #[ORM\Column(type: 'text')]
    private $custom;

    #[ORM\Column(type: 'text')]
    private $datas;

    #[ORM\Column(type: 'text')]
    private $validUrl;

    #[ORM\Column(type: 'text')]
    private $cancelUrl;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'integer')]
    private $nbPayments = 0;

    #[ORM\JoinColumn(nullable: false, name: 'parent', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class)]
    private $parent;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateValid;

    #[ORM\Column(type: 'boolean')]
    private $fbLog = false;

    #[ORM\JoinColumn(nullable: true, name: 'customer_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Customer\ShopCustomer::class)]
    private $shopCustomer;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransactionProduct::class, cascade: ['persist', 'remove'], mappedBy: 'transaction')]
    private $transactionProducts;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransactionPayment::class, mappedBy: 'transaction')]
    private $transactionPayments;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransactionHistory::class, mappedBy: 'transaction')]
    private $transactionHistories;

    #[ORM\JoinColumn(name: 'cart_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Cart\ShopCart::class)]
    private $shopCart;

    #[ORM\JoinColumn(nullable: false, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $partner;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\JoinColumn(nullable: true, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    #[ORM\JoinColumn(nullable: true, name: 'facebook_pixel_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $facebookPixel;

    #[ORM\OneToOne(targetEntity: DossierPaymentLink::class, mappedBy: 'transaction', cascade: ['persist', 'remove'])]
    private $dossierPaymentLink;

    public function __construct()
    {
        $this->transactionProducts = new ArrayCollection();
        $this->transactionPayments = new ArrayCollection();
        $this->transactionHistories = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getValid(): ?bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): self
    {
        $this->valid = $valid;

        return $this;
    }

    public function isValid(): ?bool
    {
        return $this->valid;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getAmountTaxExcl(): ?float
    {
        return $this->amountTaxExcl;
    }

    public function setAmountTaxExcl(float $amountTaxExcl): self
    {
        $this->amountTaxExcl = $amountTaxExcl;

        return $this;
    }

    public function getAmountTaxIncl(): ?float
    {
        return $this->amountTaxIncl;
    }

    public function setAmountTaxIncl(float $amountTaxIncl): self
    {
        $this->amountTaxIncl = $amountTaxIncl;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    public function getRequest(): ?string
    {
        return $this->request;
    }

    public function setRequest(string $request): self
    {
        $this->request = $request;

        return $this;
    }

    public function getCustom(): ?string
    {
        return $this->custom;
    }

    public function setCustom(string $custom): self
    {
        $this->custom = $custom;

        return $this;
    }

    public function getDatas(): ?string
    {
        return $this->datas;
    }

    public function setDatas(string $datas): self
    {
        $this->datas = $datas;

        return $this;
    }

    public function getValidUrl(): ?string
    {
        return $this->validUrl;
    }

    public function setValidUrl(string $validUrl): self
    {
        $this->validUrl = $validUrl;

        return $this;
    }

    public function getCancelUrl(): ?string
    {
        return $this->cancelUrl;
    }

    public function setCancelUrl(string $cancelUrl): self
    {
        $this->cancelUrl = $cancelUrl;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getNbPayments(): ?int
    {
        return $this->nbPayments;
    }

    public function setNbPayments(int $nbPayments): self
    {
        $this->nbPayments = $nbPayments;

        return $this;
    }

    public function getParent(): ?ShopTransaction
    {
        return $this->parent;
    }

    public function setParent(?ShopTransaction $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateValid(): ?\DateTimeInterface
    {
        return $this->dateValid;
    }

    public function setDateValid(\DateTimeInterface $dateValid): self
    {
        $this->dateValid = $dateValid;

        return $this;
    }

    public function getShopCustomer(): ?ShopCustomer
    {
        return $this->shopCustomer;
    }

    public function setShopCustomer(?ShopCustomer $shopCustomer): self
    {
        $this->shopCustomer = $shopCustomer;

        return $this;
    }

    /**
     * @return Collection|ShopTransactionProduct[]
     */
    public function getTransactionProducts(): Collection
    {
        return $this->transactionProducts;
    }

    public function addTransactionProduct(ShopTransactionProduct $transactionProduct): self
    {
        if (!$this->transactionProducts->contains($transactionProduct)) {
            $this->transactionProducts[] = $transactionProduct;
            $transactionProduct->setTransaction($this);
        }

        return $this;
    }

    public function removeTransactionProduct(ShopTransactionProduct $transactionProduct): self
    {
        if ($this->transactionProducts->contains($transactionProduct)) {
            $this->transactionProducts->removeElement($transactionProduct);
            // set the owning side to null (unless already changed)
            if ($transactionProduct->getTransaction() === $this) {
                $transactionProduct->setTransaction(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ShopTransactionPayment[]
     */
    public function getTransactionPayments(): Collection
    {
        return $this->transactionPayments;
    }

    public function addTransactionPayment(ShopTransactionPayment $transactionPayment): self
    {
        if (!$this->transactionPayments->contains($transactionPayment)) {
            $this->transactionPayments[] = $transactionPayment;
            $transactionPayment->setTransaction($this);
        }

        return $this;
    }

    public function removeTransactionPayment(ShopTransactionPayment $transactionPayment): self
    {
        if ($this->transactionPayments->contains($transactionPayment)) {
            $this->transactionPayments->removeElement($transactionPayment);
            // set the owning side to null (unless already changed)
            if ($transactionPayment->getTransaction() === $this) {
                $transactionPayment->setTransaction(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ShopTransactionHistory[]
     */
    public function getTransactionHistories(): Collection
    {
        return $this->transactionHistories;
    }

    public function addTransactionHistory(ShopTransactionHistory $transactionHistory): self
    {
        if (!$this->transactionHistories->contains($transactionHistory)) {
            $this->transactionHistories[] = $transactionHistory;
            $transactionHistory->setTransaction($this);
        }

        return $this;
    }

    public function removeTransactionHistory(ShopTransactionHistory $transactionHistory): self
    {
        if ($this->transactionHistories->contains($transactionHistory)) {
            $this->transactionHistories->removeElement($transactionHistory);
            // set the owning side to null (unless already changed)
            if ($transactionHistory->getTransaction() === $this) {
                $transactionHistory->setTransaction(null);
            }
        }

        return $this;
    }

    /**
     * @return string
     */
    public function getDisplayAmountTaxExcl(): string
    {
        return \MatGyver\Helpers\Number::formatAmount($this->amountTaxExcl, $this->currency);
    }

    /**
     * @return string
     */
    public function getDisplayAmountTaxIncl(): string
    {
        return \MatGyver\Helpers\Number::formatAmount($this->amountTaxIncl, $this->currency);
    }

    public function getShopCart(): ?ShopCart
    {
        return $this->shopCart;
    }

    public function setShopCart(?ShopCart $shopCart): self
    {
        $this->shopCart = $shopCart;

        return $this;
    }

    public function getVatAmount(): float
    {
        if ($this->amountTaxIncl !== null && $this->amountTaxExcl !== null && $this->amountTaxExcl !== 0) {
            return $this->amountTaxIncl - $this->amountTaxExcl;
        }

        return 0;
    }

    /**
     * @return array
     */
    public static function getPaymentMethodsChoices(): array
    {
        return [
            self::METHOD_AUTRE => __('Autre'),
            self::METHOD_BRAINTREE => 'Braintree',
            self::METHOD_CHEQUE => __('Chèque'),
            self::METHOD_PAYPAL => 'Paypal',
            self::METHOD_STRIPE => 'Stripe',
            self::METHOD_VIREMENT => __('Virement'),
        ];
    }

    /**
     * @return null|string
     */
    public function getDisplayPaymentMethod(): ?string
    {
        if (null === $this->getPaymentMethod()) {
            return null;
        }

        $paymentMethods = self::getPaymentMethodsChoices();
        return $paymentMethods[$this->getPaymentMethod()] ?? __('Autre');
    }

    /**
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_COMPLETED => __('Valide'),
            self::STATUS_ERROR => __('Erreur'),
            self::STATUS_FAILED => __('Erreur'),
            self::STATUS_CANCELED => __('Annulée'),
            self::STATUS_REFUNDED => __('Remboursée'),
            self::STATUS_WAITING => __('En attente de règlement'),
            self::STATUS_DELIVERING => __('En cours de livraison'),
            self::STATUS_DELIVERED => __('Livrée')
        ];
    }

    /**
     * @return null|string
     */
    public function getDisplayStatus(): ?string
    {
        if (null === $this->getStatus()) {
            return null;
        }

        $statuses = self::getStatuses();
        return $statuses[$this->getStatus()] ?? __('Autre');
    }

    public function getFbLog(): ?bool
    {
        return $this->fbLog;
    }

    public function setFbLog(bool $fbLog): self
    {
        $this->fbLog = $fbLog;

        return $this;
    }

    public function getPartner(): ?AffiliationPartner
    {
        return $this->partner;
    }

    public function setPartner(?AffiliationPartner $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getFacebookPixel(): ?IntegrationAccount
    {
        return $this->facebookPixel;
    }

    public function setFacebookPixel(?IntegrationAccount $facebookPixel): self
    {
        $this->facebookPixel = $facebookPixel;

        return $this;
    }

    public function getDossierPaymentLink(): ?DossierPaymentLink
    {
        return $this->dossierPaymentLink;
    }

    public function setDossierPaymentLink(?DossierPaymentLink $dossierPaymentLink): self
    {
        // unset the owning side of the relation if necessary
        if ($dossierPaymentLink === null && $this->dossierPaymentLink !== null) {
            $this->dossierPaymentLink->setTransaction(null);
        }

        // set the owning side of the relation if necessary
        if ($dossierPaymentLink !== null && $dossierPaymentLink->getTransaction() !== $this) {
            $dossierPaymentLink->setTransaction($this);
        }

        $this->dossierPaymentLink = $dossierPaymentLink;

        return $this;
    }
}
