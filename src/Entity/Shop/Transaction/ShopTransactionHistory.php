<?php

namespace MatGyver\Entity\Shop\Transaction;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'shop_transactions_history')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Transaction\ShopTransactionHistoryRepository::class)]
class ShopTransactionHistory
{
    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'transaction_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class, inversedBy: 'transactionHistories')]
    private $transaction;

    #[ORM\Column(type: 'string', length: 250)]
    private $status;

    #[ORM\Column(type: 'float')]
    private $amount;

    #[ORM\Column(type: 'text')]
    private $comment;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return array
     */
    public function getStatusInfos(): array
    {
        $status = $this->getStatus();
        $icon = '';
        $title = '';
        $class = '';
        switch ($status) {
            case ShopTransaction::STATUS_WAITING:
                $icon = 'fa-cart-plus';
                $title = __('Commande en attente');
                $class = 'warning';
                break;

            case ShopTransaction::STATUS_COMPLETED:
                $icon = 'fa-check';
                $title = __('Commande validée');
                $class = 'primary';
                break;

            case ShopTransaction::STATUS_CANCELED:
                $icon = 'fa-times';
                $title = __('Commande annulée');
                $class = 'danger';
                break;

            case ShopTransaction::STATUS_ERROR:
                $icon = 'fa-times';
                $title = __('Commande en erreur');
                $class = 'danger';
                break;

            case ShopTransaction::STATUS_REFUNDED:
                $icon = 'fa-sync';
                $title = __('Commande remboursée');
                $class = 'danger';
                break;

            case ShopTransaction::STATUS_DELIVERING:
                $icon = 'fa-truck';
                $title = __('Commande en cours de livraison');
                $class = 'warning';
                break;

            case ShopTransaction::STATUS_DELIVERED:
                $icon = 'fa-archive';
                $title = __('Commande livrée');
                $class = 'info';
                break;
        }

        return [
            'status' => $status,
            'icon' => $icon,
            'title' => $title,
            'class' => $class,
        ];
    }
}
