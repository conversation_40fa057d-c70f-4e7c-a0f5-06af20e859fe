<?php

namespace MatGyver\Entity\Shop\Transaction\Error;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Mat<PERSON>yver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'shop_transactions_errors')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Transaction\Error\ShopTransactionErrorRepository::class)]
class ShopTransactionError
{
    use ClientEntity;

    const STATUS_OPEN = 1;
    const STATUS_TO_CALL = 2;
    const STATUS_WAITING_CLIENT = 3;
    const STATUS_CLOSED = 4;
    const STATUS_PAID = 5;
    const STATUS_PAYMENT_IN_PROGRESS = 6;
    const STATUS_NEEDS_DELAY = 7;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'transaction_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class)]
    private $transaction;

    #[ORM\Column(type: 'integer')]
    private $subscriptionId;

    #[ORM\Column(type: 'string', length: 255)]
    private $type;

    #[ORM\Column(type: 'string', length: 255)]
    private $product;

    #[ORM\Column(type: 'text')]
    private $custom;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: true, name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $user;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Transaction\Error\ShopTransactionErrorHistory::class, mappedBy: 'transactionError')]
    private $transactionErrorHistories;

    #[ORM\JoinColumn(nullable: false, name: 'status_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\Error\ShopTransactionErrorStatus::class)]
    private $transactionErrorStatus;

    public function __construct()
    {
        $this->transactionErrorHistories = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getSubscriptionId(): ?int
    {
        return $this->subscriptionId;
    }

    public function setSubscriptionId(int $subscriptionId): self
    {
        $this->subscriptionId = $subscriptionId;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getProduct(): ?string
    {
        return $this->product;
    }

    public function setProduct(string $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getCustom(): ?string
    {
        return $this->custom;
    }

    public function setCustom(string $custom): self
    {
        $this->custom = $custom;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return Collection|ShopTransactionErrorHistory[]
     */
    public function getTransactionErrorHistories(): Collection
    {
        return $this->transactionErrorHistories;
    }

    public function addTransactionErrorHistory(ShopTransactionErrorHistory $transactionErrorHistory): self
    {
        if (!$this->transactionErrorHistories->contains($transactionErrorHistory)) {
            $this->transactionErrorHistories[] = $transactionErrorHistory;
            $transactionErrorHistory->setTransactionError($this);
        }

        return $this;
    }

    public function removeTransactionErrorHistory(ShopTransactionErrorHistory $transactionErrorHistory): self
    {
        if ($this->transactionErrorHistories->contains($transactionErrorHistory)) {
            $this->transactionErrorHistories->removeElement($transactionErrorHistory);
            // set the owning side to null (unless already changed)
            if ($transactionErrorHistory->getTransactionError() === $this) {
                $transactionErrorHistory->setTransactionError(null);
            }
        }

        return $this;
    }

    public function getTransactionErrorStatus(): ?ShopTransactionErrorStatus
    {
        return $this->transactionErrorStatus;
    }

    public function setTransactionErrorStatus(?ShopTransactionErrorStatus $transactionErrorStatus): self
    {
        $this->transactionErrorStatus = $transactionErrorStatus;

        return $this;
    }
}
