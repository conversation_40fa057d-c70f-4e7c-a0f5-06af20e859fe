<?php

namespace MatGyver\Entity\Shop\Transaction\Error;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\User\User;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'shop_transactions_errors_history')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Transaction\Error\ShopTransactionErrorHistoryRepository::class)]
class ShopTransactionErrorHistory
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'text')]
    private $comment;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: true, name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $user;

    #[ORM\JoinColumn(nullable: true, name: 'notify_user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $notifyUser;

    #[ORM\JoinColumn(nullable: false, name: 'error_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\Error\ShopTransactionError::class, inversedBy: 'transactionErrorHistories')]
    private $transactionError;

    #[ORM\JoinColumn(nullable: false, name: 'status_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\Error\ShopTransactionErrorStatus::class)]
    private $transactionErrorStatus;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getTransactionError(): ?ShopTransactionError
    {
        return $this->transactionError;
    }

    public function setTransactionError(?ShopTransactionError $transactionError): self
    {
        $this->transactionError = $transactionError;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getNotifyUser(): ?User
    {
        return $this->notifyUser;
    }

    public function setNotifyUser(?User $notifyUser): self
    {
        $this->notifyUser = $notifyUser;

        return $this;
    }

    public function getTransactionErrorStatus(): ?ShopTransactionErrorStatus
    {
        return $this->transactionErrorStatus;
    }

    public function setTransactionErrorStatus(?ShopTransactionErrorStatus $transactionErrorStatus): self
    {
        $this->transactionErrorStatus = $transactionErrorStatus;

        return $this;
    }
}
