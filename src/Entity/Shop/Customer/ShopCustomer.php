<?php

namespace MatGyver\Entity\Shop\Customer;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Traits\ClientEntity;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'shop_customers')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Customer\ShopCustomerRepository::class)]
class ShopCustomer
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 250)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 250)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 250)]
    private $email;

    #[ORM\Column(type: 'string', length: 250)]
    private $address;

    #[ORM\Column(type: 'string', length: 250)]
    private $address2;

    #[ORM\Column(type: 'string', length: 250)]
    private $city;

    #[ORM\Column(type: 'string', length: 50)]
    private $zip;

    #[ORM\Column(type: 'string', length: 50)]
    private $state;

    #[ORM\Column(type: 'string', length: 50)]
    private $country;

    #[ORM\Column(type: 'string', length: 50)]
    private $telephone;

    #[ORM\Column(type: 'text')]
    private $datas;

    #[ORM\Column(type: 'text')]
    private $comment = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'string', length: 50)]
    private $randomId;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setAddress2(string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getZip(): ?string
    {
        return $this->zip;
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getDatas(): ?string
    {
        return $this->datas;
    }

    public function setDatas(string $datas): self
    {
        $this->datas = $datas;

        return $this;
    }

    public function getDecodedDatas(): ?array
    {
        if (!empty($this->datas)) {
            return json_decode($this->datas, true);
        }

        return $this->datas;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getRandomId(): ?string
    {
        return $this->randomId;
    }

    public function setRandomId(string $randomId): self
    {
        $this->randomId = $randomId;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
