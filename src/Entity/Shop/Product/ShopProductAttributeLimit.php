<?php

namespace MatGyver\Entity\Shop\Product;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Limit\Limit;
use Gedmo\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'shop_product_attributes_limits')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Product\ShopProductAttributeLimitRepository::class)]
class ShopProductAttributeLimit
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class, cascade: ['remove'])]
    private $product;

    #[ORM\JoinColumn(nullable: false, name: 'product_attribute_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProductAttribute::class, cascade: ['remove'])]
    private $productAttribute;

    #[ORM\JoinColumn(nullable: false, name: 'limit_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Limit\Limit::class)]
    private $limit;

    #[ORM\Column(type: 'string', length: 20)]
    private $action;

    #[ORM\Column(type: 'integer')]
    private $value;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getProductAttribute(): ?ShopProductAttribute
    {
        return $this->productAttribute;
    }

    public function setProductAttribute(?ShopProductAttribute $productAttribute): self
    {
        $this->productAttribute = $productAttribute;

        return $this;
    }

    public function getLimit(): ?Limit
    {
        return $this->limit;
    }

    public function setLimit(?Limit $limit): self
    {
        $this->limit = $limit;

        return $this;
    }

    public function getValue(): int
    {
        return $this->value;
    }

    public function setValue(int $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
