<?php

namespace MatGyver\Entity\Shop\Product;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Shop\Attribute\ShopAttribute;
use MatGyver\Entity\Shop\Attribute\ShopAttributeGroup;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'shop_product_attributes')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Product\ShopProductAttributeRepository::class)]
class ShopProductAttribute
{
    const IMPACT_NONE = 'none';
    const IMPACT_INCREASE = 'increase';
    const IMPACT_DECREASE = 'decrease';

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    #[ORM\JoinColumn(nullable: false, name: 'attribute_group_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Attribute\ShopAttributeGroup::class)]
    private $attributeGroup;

    #[ORM\JoinColumn(nullable: false, name: 'attribute_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Attribute\ShopAttribute::class)]
    private $attribute;

    #[ORM\Column(type: 'text')]
    private $image;

    #[ORM\Column(type: 'string', length: 50)]
    private $priceImpact;

    #[ORM\Column(type: 'float')]
    private $priceValue;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Product\ShopProductAttributeLimit::class, cascade: ['persist', 'remove'], mappedBy: 'productAttribute')]
    private $limits;

    public function __construct()
    {
        $this->limits = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getAttributeGroup(): ?ShopAttributeGroup
    {
        return $this->attributeGroup;
    }

    public function setAttributeGroup(?ShopAttributeGroup $attributeGroup): self
    {
        $this->attributeGroup = $attributeGroup;

        return $this;
    }

    public function getAttribute(): ?ShopAttribute
    {
        return $this->attribute;
    }

    public function setAttribute(?ShopAttribute $attribute): self
    {
        $this->attribute = $attribute;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getPriceImpact(): ?string
    {
        return $this->priceImpact;
    }

    public function setPriceImpact(string $priceImpact): self
    {
        $this->priceImpact = $priceImpact;

        return $this;
    }

    public function getPriceValue(): ?float
    {
        return $this->priceValue;
    }

    public function setPriceValue(float $priceValue): self
    {
        $this->priceValue = $priceValue;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection|ShopProductAttributeLimit[]
     */
    public function getLimits(): Collection
    {
        return $this->limits;
    }

    public function addLimit(ShopProductAttributeLimit $limit): self
    {
        if (!$this->limits->contains($limit)) {
            $this->limits[] = $limit;
            $limit->setProductAttribute($this);
        }

        return $this;
    }

    public function removeLimit(ShopProductAttributeLimit $limit): self
    {
        if ($this->limits->contains($limit)) {
            $this->limits->removeElement($limit);
            // set the owning side to null (unless already changed)
            if ($limit->getProductAttribute() === $this) {
                $limit->setProductAttribute(null);
            }
        }

        return $this;
    }

    public function removeLimits(): self
    {
        if ($this->limits) {
            foreach ($this->limits as $limit) {
                $this->limits->removeElement($limit);
            }
        }

        return $this;
    }
}
