<?php

namespace MatGyver\Entity\Shop\Product;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Dossier\DossierPaymentLink;
use MatGyver\Entity\Shop\VatRule\ShopVatRule;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'shop_products')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Product\ShopProductRepository::class)]
class ShopProduct
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 250)]
    private $name;

    #[ORM\Column(type: 'string', length: 250)]
    private $permalink;

    #[ORM\Column(type: 'string', length: 50)]
    private $type;

    #[ORM\Column(type: 'text')]
    private $image;

    #[ORM\Column(type: 'text')]
    private $description;

    #[ORM\Column(type: 'float')]
    private $priceTaxExcl;

    #[ORM\Column(type: 'float')]
    private $priceTaxIncl;

    #[ORM\JoinColumn(nullable: false, name: 'vat_rule_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\VatRule\ShopVatRule::class)]
    private $vatRule;

    #[ORM\Column(type: 'boolean')]
    private $displayHt;

    #[ORM\Column(type: 'string', length: 10)]
    private $currency = 'EUR';

    #[ORM\Column(type: 'text')]
    private $quantities;

    #[ORM\Column(type: 'text')]
    private $fields;

    #[ORM\Column(type: 'text')]
    private $customFields = '';

    #[ORM\Column(type: 'text')]
    private $gdpr;

    #[ORM\Column(type: 'boolean')]
    private $required;

    #[ORM\Column(type: 'boolean')]
    private $affiliation;

    #[ORM\Column(type: 'float')]
    private $affiliationCommission;

    #[ORM\Column(type: 'float')]
    private $affiliationCommission2;

    #[ORM\Column(type: 'boolean')]
    private $recurring = false;

    #[ORM\Column(type: 'string', length: 20)]
    private $duration;

    #[ORM\Column(type: 'boolean')]
    private $cancellable = false;

    #[ORM\Column(type: 'boolean')]
    private $editable = false;

    #[ORM\Column(type: 'string', length: 50)]
    private $universe = '';

    #[ORM\Column(type: 'text')]
    private $validateUrl;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\Column(type: 'boolean')]
    private $visible;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $dateUpdate;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Product\ShopProductLimit::class, cascade: ['persist', 'remove'], mappedBy: 'product')]
    private $limits;

    #[ORM\OneToOne(targetEntity: DossierPaymentLink::class, mappedBy: 'product', cascade: ['persist', 'remove'])]
    private $dossierPaymentLink;

    public function __construct()
    {
        $this->limits = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getPermalink(): ?string
    {
        return $this->permalink;
    }

    public function setPermalink(string $permalink): self
    {
        $this->permalink = $permalink;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getPriceTaxExcl(): ?float
    {
        return $this->priceTaxExcl;
    }

    public function setPriceTaxExcl(float $priceTaxExcl): self
    {
        $this->priceTaxExcl = $priceTaxExcl;

        return $this;
    }

    public function getPriceTaxIncl(): ?float
    {
        return $this->priceTaxIncl;
    }

    public function setPriceTaxIncl(float $priceTaxIncl): self
    {
        $this->priceTaxIncl = $priceTaxIncl;

        return $this;
    }

    public function getVatRule(): ?ShopVatRule
    {
        return $this->vatRule;
    }

    public function setVatRule(?ShopVatRule $vatRule): self
    {
        $this->vatRule = $vatRule;

        return $this;
    }

    public function getDisplayHt(): ?bool
    {
        return $this->displayHt;
    }

    public function setDisplayHt(bool $displayHt): self
    {
        $this->displayHt = $displayHt;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getQuantities(): ?string
    {
        return $this->quantities;
    }

    public function setQuantities(string $quantities): self
    {
        $this->quantities = $quantities;

        return $this;
    }

    public function getFields(): ?string
    {
        return $this->fields;
    }

    public function setFields(string $fields): self
    {
        $this->fields = $fields;

        return $this;
    }

    public function getCustomFields(): ?string
    {
        return $this->customFields;
    }

    public function setCustomFields(string $customFields): self
    {
        $this->customFields = $customFields;

        return $this;
    }

    public function getGdpr(): ?string
    {
        return $this->gdpr;
    }

    public function setGdpr(string $gdpr): self
    {
        $this->gdpr = $gdpr;

        return $this;
    }

    public function getRequired(): ?bool
    {
        return $this->required;
    }

    public function setRequired(bool $required): self
    {
        $this->required = $required;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getVisible(): ?bool
    {
        return $this->visible;
    }

    public function setVisible(bool $visible): self
    {
        $this->visible = $visible;

        return $this;
    }

    public function getAffiliation(): ?bool
    {
        return $this->affiliation;
    }

    public function setAffiliation(bool $affiliation): self
    {
        $this->affiliation = $affiliation;

        return $this;
    }

    public function getAffiliationCommission(): ?float
    {
        return $this->affiliationCommission;
    }

    public function setAffiliationCommission(float $affiliationCommission): self
    {
        $this->affiliationCommission = $affiliationCommission;

        return $this;
    }

    public function getAffiliationCommission2(): ?float
    {
        return $this->affiliationCommission2;
    }

    public function setAffiliationCommission2(float $affiliationCommission2): self
    {
        $this->affiliationCommission2 = $affiliationCommission2;

        return $this;
    }

    public function getRecurring(): ?string
    {
        return $this->recurring;
    }

    public function setRecurring(string $recurring): self
    {
        $this->recurring = $recurring;

        return $this;
    }

    public function getDuration(): ?string
    {
        return $this->duration;
    }

    public function setDuration(string $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getCancellable(): ?bool
    {
        return $this->cancellable;
    }

    public function setCancellable(bool $cancellable): self
    {
        $this->cancellable = $cancellable;

        return $this;
    }

    public function getEditable(): ?bool
    {
        return $this->editable;
    }

    public function setEditable(bool $editable): self
    {
        $this->editable = $editable;

        return $this;
    }

    public function getUniverse(): ?string
    {
        return $this->universe;
    }

    public function setUniverse(string $universe): self
    {
        $this->universe = $universe;

        return $this;
    }

    public function getValidateUrl(): ?string
    {
        return $this->validateUrl;
    }

    public function setValidateUrl(string $validateUrl): self
    {
        $this->validateUrl = $validateUrl;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateUpdate(): ?\DateTimeInterface
    {
        return $this->dateUpdate;
    }

    public function setDateUpdate(\DateTimeInterface $dateUpdate): self
    {
        $this->dateUpdate = $dateUpdate;

        return $this;
    }

    /**
     * @return Collection|ShopProductLimit[]
     */
    public function getLimits(): Collection
    {
        return $this->limits;
    }

    public function addLimit(ShopProductLimit $limit): self
    {
        if (!$this->limits->contains($limit)) {
            $this->limits[] = $limit;
            $limit->setProduct($this);
        }

        return $this;
    }

    public function removeLimit(ShopProductLimit $limit): self
    {
        if ($this->limits->contains($limit)) {
            $this->limits->removeElement($limit);
            // set the owning side to null (unless already changed)
            if ($limit->getProduct() === $this) {
                $limit->setProduct(null);
            }
        }

        return $this;
    }

    public function removeLimits(): self
    {
        if ($this->limits) {
            foreach ($this->limits as $limit) {
                $this->limits->removeElement($limit);
            }
        }

        return $this;
    }

    public function getDossierPaymentLink(): ?DossierPaymentLink
    {
        return $this->dossierPaymentLink;
    }

    public function setDossierPaymentLink(DossierPaymentLink $dossierPaymentLink): self
    {
        // set the owning side of the relation if necessary
        if ($dossierPaymentLink->getProduct() !== $this) {
            $dossierPaymentLink->setProduct($this);
        }

        $this->dossierPaymentLink = $dossierPaymentLink;

        return $this;
    }
}
