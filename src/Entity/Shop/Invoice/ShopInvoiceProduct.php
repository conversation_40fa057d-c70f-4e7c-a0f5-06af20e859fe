<?php

namespace MatGyver\Entity\Shop\Invoice;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'shop_invoices_products')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Invoice\ShopInvoiceProductRepository::class)]
class ShopInvoiceProduct
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'float')]
    private $quantity;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'float')]
    private $priceTaxExcl;

    #[ORM\Column(type: 'float')]
    private $vat;

    #[ORM\Column(type: 'float')]
    private $priceTaxIncl;

    #[ORM\Column(type: 'string', length: 10)]
    private $currency = 'EUR';

    #[ORM\Column(type: 'text')]
    private $attributes = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'invoice_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Invoice\ShopInvoice::class, inversedBy: 'invoiceProducts')]
    private $invoice;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getQuantity(): ?float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    public function getPriceTaxExcl(): ?float
    {
        return $this->priceTaxExcl;
    }

    public function setPriceTaxExcl(float $priceTaxExcl): self
    {
        $this->priceTaxExcl = $priceTaxExcl;

        return $this;
    }

    public function getPriceTaxIncl(): ?float
    {
        return $this->priceTaxIncl;
    }

    public function setPriceTaxIncl(float $priceTaxIncl): self
    {
        $this->priceTaxIncl = $priceTaxIncl;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getAttributes(): ?string
    {
        return $this->attributes;
    }

    public function setAttributes(string $attributes): self
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getInvoice(): ?ShopInvoice
    {
        return $this->invoice;
    }

    public function setInvoice(?ShopInvoice $invoice): self
    {
        $this->invoice = $invoice;

        return $this;
    }

}
