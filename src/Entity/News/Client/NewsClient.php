<?php

namespace MatGyver\Entity\News\Client;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\News\News;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use Gedmo\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'mg_news_clients')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\News\Client\NewsClientRepository::class)]
class NewsClient
{

    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'boolean')]
    private $viewed;

    #[ORM\Column(type: 'boolean')]
    private $readed;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'news_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\News\News::class)]
    private $news;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getViewed(): ?bool
    {
        return $this->viewed;
    }

    public function setViewed(bool $viewed): self
    {
        $this->viewed = $viewed;

        return $this;
    }

    public function getReaded(): ?bool
    {
        return $this->readed;
    }

    public function setReaded(bool $readed): self
    {
        $this->readed = $readed;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getNews(): ?News
    {
        return $this->news;
    }

    public function setNews(?News $news): self
    {
        $this->news = $news;

        return $this;
    }

}
