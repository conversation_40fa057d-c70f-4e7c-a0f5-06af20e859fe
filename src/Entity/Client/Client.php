<?php

namespace MatGyver\Entity\Client;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Company\Company;
use MatGyver\Entity\Config\Config;
use MatGyver\Entity\Game\GameLevelClient;
use MatGyver\Entity\Limit\Limit;
use MatGyver\Entity\Traits\SoftDeleteableEntity;
use MatGyver\Entity\User\User;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Limit\LimitClientsService;
use MatGyver\Services\Limit\LimitService;
use MatGyver\Services\Users\UsersService;

#[ORM\Table(name: 'mg_clients')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Client\ClientRepository::class)]
#[Gedmo\SoftDeleteable(fieldName: 'deletedAt', timeAware: false)]
class Client
{
    use SoftDeleteableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 100)]
    private $name;

    #[ORM\Column(type: 'string', length: 100)]
    private $uniqid;

    #[ORM\Column(type: 'integer')]
    private $maxusers;

    #[ORM\Column(type: 'boolean')]
    private $active = true;

    #[ORM\Column(type: 'boolean')]
    private $freemium = false;

    #[ORM\Column(type: 'boolean')]
    private $onPause = false;

    #[ORM\Column(type: 'string', length: 50)]
    private $subscription;

    #[ORM\Column(type: 'boolean')]
    private $recurring;

    #[ORM\Column(type: 'string', length: 50)]
    private $source = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $referer = '';

    #[ORM\Column(type: 'date')]
    private $dateEndSubscription;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateDesactivation;

    #[ORM\JoinColumn(nullable: true, name: 'partner_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Affiliation\Partner\AffiliationPartner::class)]
    private $affiliationPartner;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Config\Config::class, mappedBy: 'client', indexBy: 'name')]
    private $clientConfigs;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Company\Company::class, mappedBy: 'client')]
    private $companies;

    #[ORM\OneToOne(targetEntity: GameLevelClient::class, mappedBy: 'client', cascade: ['persist', 'remove'])]
    private $gameLevelClient;

    public function __construct()
    {
        $this->clientConfigs = new ArrayCollection();
        $this->companies = new ArrayCollection();
    }

    public function __toString(): string
    {
        return (string) $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getUniqid(): ?string
    {
        return $this->uniqid;
    }

    public function setUniqid(string $uniqid): self
    {
        $this->uniqid = $uniqid;

        return $this;
    }

    public function getMaxusers(): ?int
    {
        return $this->maxusers;
    }

    public function setMaxusers(int $maxusers): self
    {
        $this->maxusers = $maxusers;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getFreemium(): ?bool
    {
        return $this->freemium;
    }

    public function setFreemium(bool $freemium): self
    {
        $this->freemium = $freemium;

        return $this;
    }

    public function getOnPause(): ?bool
    {
        return $this->onPause;
    }

    public function setOnPause(bool $onPause): self
    {
        $this->onPause = $onPause;

        return $this;
    }

    public function getSubscription(): ?string
    {
        return $this->subscription;
    }

    public function setSubscription(string $subscription): self
    {
        $this->subscription = $subscription;

        return $this;
    }

    public function getRecurring(): ?bool
    {
        return $this->recurring;
    }

    public function setRecurring(bool $recurring): self
    {
        $this->recurring = $recurring;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getReferer(): ?string
    {
        return $this->referer;
    }

    public function setReferer(string $referer): self
    {
        $this->referer = $referer;

        return $this;
    }

    public function getDateEndSubscription(): ?\DateTimeInterface
    {
        return $this->dateEndSubscription;
    }

    public function setDateEndSubscription(\DateTimeInterface $dateEndSubscription): self
    {
        $this->dateEndSubscription = $dateEndSubscription;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateDesactivation(): ?\DateTimeInterface
    {
        return $this->dateDesactivation;
    }

    public function setDateDesactivation(?\DateTimeInterface $dateDesactivation): self
    {
        $this->dateDesactivation = $dateDesactivation;

        return $this;
    }

    public function getAffiliationPartner(): ?AffiliationPartner
    {
        return $this->affiliationPartner;
    }

    public function setAffiliationPartner(?AffiliationPartner $affiliationPartner): self
    {
        $this->affiliationPartner = $affiliationPartner;

        return $this;
    }

    /**
     * @return Collection|Config[]
     */
    public function getClientConfigs(): Collection
    {
        return $this->clientConfigs;
    }

    /**
     * @return string|null
     */
    public function getClientConfig(string $configName): ?string
    {
        foreach ($this->getClientConfigs() as $clientConfig) {
            if ($clientConfig->getName() == $configName) {
                return $clientConfig->getValue();
            }
        }

        return null;
    }

    /**
     * @return array
     */
    public function getClientConfigsAsArray(): array
    {
        $results = [];
        foreach ($this->getClientConfigs() as $clientConfig) {
            $results[$clientConfig->getName()] = $clientConfig->getValue();
        }
        return $results;
    }

    public function addClientConfig(Config $config): self
    {
        if (!$this->clientConfigs->contains($config)) {
            $this->clientConfigs[] = $config;
            $config->setClient($this);
        }

        return $this;
    }

    public function removeClientConfig(Config $config): self
    {
        if ($this->clientConfigs->contains($config)) {
            $this->clientConfigs->removeElement($config);
            // set the owning side to null (unless already changed)
            if ($config->getClient() === $this) {
                $config->setClient(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Company[]
     */
    public function getCompanies(): Collection
    {
        return $this->companies;
    }

    public function addCompany(Company $company): self
    {
        if (!$this->companies->contains($company)) {
            $this->companies[] = $company;
            $company->setClient($this);
        }

        return $this;
    }

    public function removeCompany(Company $company): self
    {
        if ($this->companies->contains($company)) {
            $this->companies->removeElement($company);
            // set the owning side to null (unless already changed)
            if ($company->getClient() === $this) {
                $company->setClient(null);
            }
        }

        return $this;
    }

    /**
     * @return User|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getMainAdmin(): ?User
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(UsersService::class)->getMainAdmin($this->getId());
    }

    public function getGameLevelClient(): ?GameLevelClient
    {
        return $this->gameLevelClient;
    }

    public function setGameLevelClient(GameLevelClient $gameLevelClient): self
    {
        // set the owning side of the relation if necessary
        if ($gameLevelClient->getClient() !== $this) {
            $gameLevelClient->setClient($this);
        }

        $this->gameLevelClient = $gameLevelClient;

        return $this;
    }

    /**
     * @param string $productReference
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function hasSubscription(string $productReference): bool
    {
        if (ENV !== ENV_PROD) {
            return true;
        }

        if ($this->getFreemium()) {
            return false;
        }

        $nbPoints = 0;
        $points = [
            ProductsEnum::REF_START => 1,
            ProductsEnum::REF_MEDIUM => 2,
            ProductsEnum::REF_PREMIUM => 3,
            ProductsEnum::REF_COMPLETE => 4,
            ProductsEnum::REF_JUDICIAIRE => 5,
        ];
        $requiredPoints = $points[$productReference];

        $container = ContainerBuilderService::getInstance();
        $activeSubscription = $container->get(ClientsSubscriptionsService::class)->getRepository()->getSubscriptionActiveByType(ProductsEnum::TYPE_SUBSCRIPTION, $this->getId());
        if ($activeSubscription and $activeSubscription->getProduct()) {
            $activeSubscriptionPermalink = $activeSubscription->getProduct()->getPermalink();
            foreach ($points as $productPermalink => $productPoints) {
                if (str_contains($activeSubscriptionPermalink, $productPermalink)) {
                    $nbPoints = $productPoints;
                    break;
                }
            }
        }

        return ($nbPoints >= $requiredPoints);
    }

    /**
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function isStart(): bool
    {
        return $this->hasSubscription(ProductsEnum::REF_START);
    }

    /**
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function isMedium(): bool
    {
        return $this->hasSubscription(ProductsEnum::REF_MEDIUM);
    }

    /**
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function isPremium(): bool
    {
        return $this->hasSubscription(ProductsEnum::REF_PREMIUM);
    }

    /**
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function isComplete(): bool
    {
        return $this->hasSubscription(ProductsEnum::REF_COMPLETE);
    }

    /**
     * @return bool
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function isJudiciaire(): bool
    {
        return $this->hasSubscription(ProductsEnum::REF_JUDICIAIRE);
    }

    public function getNbExperts(): int
    {
        $nbExperts = 1;
        $container = ContainerBuilderService::getInstance();
        $limit = $container->get(LimitService::class)->getRepository()->findOneBy(['reference' => Limit::LIMIT_EXPERTS]);
        if ($limit) {
            $clientLimit = $container->get(LimitClientsService::class)->getRepository()->findOneBy(['client' => $this, 'limit' => $limit]);
            if ($clientLimit) {
                $nbExperts = $clientLimit->getValue();
            }
        }

        return $nbExperts;
    }

    public function isInTrial(): bool
    {
        $container = ContainerBuilderService::getInstance();
        $activeSubscriptions = $container->get(ClientsSubscriptionsService::class)->getRepository()->findBy(['client' => $this, 'status' => 'active']);
        foreach ($activeSubscriptions as $activeSubscription) {
            if ($activeSubscription->isInTrial()) {
                return true;
            }
        }

        return false;
    }
}
