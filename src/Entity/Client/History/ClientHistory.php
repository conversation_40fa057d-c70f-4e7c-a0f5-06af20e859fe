<?php

namespace Mat<PERSON><PERSON>ver\Entity\Client\History;

use MatG<PERSON>ver\Entity\Traits\ClientEntity;
use MatG<PERSON><PERSON>\Entity\User\User;
use MatGyver\Enums\StatusesEnum;
use MatG<PERSON>ver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatG<PERSON>ver\Helpers\View\Table\Column;
use MatGyver\Repository\Client\History\ClientHistoryRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Services\Clients\ClientsCancelService;
use MatGyver\Services\Clients\ClientsErrorService;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\DI\ContainerBuilderService;

#[ORM\Table(name: 'mg_clients_histories')]
#[ORM\Entity(repositoryClass: ClientHistoryRepository::class)]
class ClientHistory
{
    const ACTION_SUBSCRIBE = 'subscribe';
    const ACTION_ORDER_ADD = 'order_add';
    const ACTION_ORDER_REFUND = 'order_refund';
    const ACTION_SUBSCRIPTION_ADD = 'subscription_add';
    const ACTION_SUBSCRIPTION_CANCEL = 'subscription_cancel';
    const ACTION_CLIENT_CANCEL = 'client_cancel';
    const ACTION_CLIENT_ERROR = 'client_error';
    const ACTION_ACTIVATE = 'activate';
    const ACTION_DISABLE = 'disable';
    const ACTION_PAUSE_ON = 'pause_on';
    const ACTION_PAUSE_OFF = 'pause_off';

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\ManyToOne(targetEntity: User::class)]
    private $user;

    #[ORM\Column(type: 'string', length: 50)]
    private $action;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $param;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getParam(): ?int
    {
        return $this->param;
    }

    public function setParam(?int $param): self
    {
        $this->param = $param;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return string
     */
    public function getIcon(): string
    {
        $icon = '';
        switch ($this->getAction()) {
            case self::ACTION_SUBSCRIBE:
                $icon = 'fas fa-user-plus';
                break;
            case self::ACTION_ACTIVATE:
                $icon = 'fas fa-check';
                break;
            case self::ACTION_DISABLE:
                $icon = 'fas fa-power-off';
                break;
            case self::ACTION_ORDER_ADD:
                $icon = 'fas fa-cart-plus';
                break;
            case self::ACTION_ORDER_REFUND:
                $icon = 'fas fa-sync';
                break;
            case self::ACTION_SUBSCRIPTION_ADD:
                $icon = 'fas fa-file-signature';
                break;
            case self::ACTION_CLIENT_CANCEL:
            case self::ACTION_CLIENT_ERROR:
            case self::ACTION_SUBSCRIPTION_CANCEL:
                $icon = 'fas fa-times';
                break;
            case self::ACTION_PAUSE_ON:
            case self::ACTION_PAUSE_OFF:
                $icon = 'fas fa-pause';
                break;
        }
        return $icon;
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        $label = 'success';
        switch ($this->getAction()) {
            case self::ACTION_DISABLE:
            case self::ACTION_ORDER_REFUND:
            case self::ACTION_SUBSCRIPTION_CANCEL:
                $label = 'danger';
                break;
            case self::ACTION_CLIENT_CANCEL:
            case self::ACTION_CLIENT_ERROR:
            case self::ACTION_PAUSE_ON:
                $label = 'warning';
                break;
            case self::ACTION_SUBSCRIBE:
                $label = 'info';
                break;
        }
        return $label;
    }

    public function getContent(): string
    {
        $content = '';
        $container = ContainerBuilderService::getInstance();
        if ($this->getAction() == self::ACTION_SUBSCRIBE) {
            $content = __('Inscription du client');
        }
        if ($this->getAction() == self::ACTION_ACTIVATE) {
            $content = __('Activation du client');
        }
        if ($this->getAction() == self::ACTION_DISABLE) {
            $content = __('Désactivation du client');
        }
        if ($this->getAction() == self::ACTION_PAUSE_ON) {
            $content = __('Mise en pause du client');
        }
        if ($this->getAction() == self::ACTION_PAUSE_OFF) {
            $content = __('Désactivation de la mise en pause du client');
        }
        if ($this->getAction() == self::ACTION_SUBSCRIPTION_ADD or $this->getAction() == self::ACTION_SUBSCRIPTION_CANCEL) {
            $content = __('Démarrage d\'un nouvel abonnement');
            if ($this->getAction() == self::ACTION_SUBSCRIPTION_CANCEL) {
                $content = __('Annulation d\'un abonnement');
            }
            if ($this->getParam()) {
                $subscription = $container->get(ClientsSubscriptionsService::class)->getRepository()->findWoClient($this->getParam());
                if ($subscription) {
                    $content .= '<span class="ml-4">';
                    if ($subscription->getProduct()) {
                        $content .= '<a href="' . Tools::makeLink('admin', 'shop', 'product/' . $subscription->getProduct()->getId()) . '">';
                    }
                    $content .= $subscription->getProductName();
                    if ($subscription->getProduct()) {
                        $content .= '</a>';
                    }
                    $content .= '</span>';
                }
            }
        }
        if ($this->getAction() == self::ACTION_ORDER_ADD or $this->getAction() == self::ACTION_ORDER_REFUND) {
            $content = __('Nouvelle transaction');
            if ($this->getAction() == self::ACTION_ORDER_REFUND) {
                $content = __('Remboursement d\'une transaction');
            }
            if ($this->getParam()) {
                $clientTransaction = $container->get(ClientsTransactionsService::class)->getRepository()->findWoClient($this->getParam());
                if ($clientTransaction and $clientTransaction->getTransaction()) {
                    try {
                        $clientTransaction->getTransaction()->getReference();
                        $content .= '<span class="ml-4">';
                        $content .= '<a href="' . Tools::makeLink('admin', 'shop', 'transaction/' . $clientTransaction->getTransaction()->getReference()) . '">';
                        $content .= $clientTransaction->getTransaction()->getProductName();
                        $content .= '</a>';
                        $content .= '</span>';
                        $content .= '<span class="ml-4">' . Number::formatAmount($clientTransaction->getTransaction()->getAmountTaxExcl(), $clientTransaction->getTransaction()->getCurrency()) . '</span>';
                    } catch (\Exception $e) {}
                }
            }
        }
        if ($this->getAction() == self::ACTION_ORDER_REFUND) {
            $content = __('Remboursement d\'une transaction');
            if ($this->getParam()) {
                $clientTransaction = $container->get(ClientsTransactionsService::class)->getRepository()->findWoClient($this->getParam());
                if ($clientTransaction and $clientTransaction->getTransaction()) {
                    try {
                        $clientTransaction->getTransaction()->getReference();
                        $content .= '<span class="ml-4">';
                        $content .= '<a href="' . Tools::makeLink('admin', 'shop', 'transaction/' . $clientTransaction->getTransaction()->getReference()) . '">';
                        $content .= $clientTransaction->getTransaction()->getProductName();
                        $content .= '</a>';
                        $content .= '</span>';
                        $content .= '<span class="ml-4">' . Number::formatAmount($clientTransaction->getTransaction()->getAmountTaxExcl(), $clientTransaction->getTransaction()->getCurrency()) . '</span>';
                    } catch (\Exception $e) {}
                }
            }
        }
        if ($this->getAction() == self::ACTION_CLIENT_CANCEL) {
            $content = __('Demande d\'annulation du compte');
            if ($this->getParam()) {
                $clientCancel = $container->get(ClientsCancelService::class)->getRepository()->findWoClient($this->getParam());
                if ($clientCancel) {
                    $reason = ClientsCancelService::getReason($clientCancel->getCancelReason());
                    if ($reason) {
                        $content .= '<span class="ml-4 mr-4">' . $reason . '</span>';
                    }

                    switch ($clientCancel->getStatus()) {
                        case StatusesEnum::STATUS_PENDING:
                            $content .= '<span class="label label-light-info label-inline">' . __('En attente') . '</span>';
                            break;
                        case StatusesEnum::STATUS_PROCESSED:
                            $content .= '<span class="label label-light-success label-inline">' . __('Traitée') . '</span>';
                            break;
                        default:
                            $content .= '<span class="label label-light-danger label-inline">' . $clientCancel->getStatus() . '</span>';
                            break;
                    }

                    if ($clientCancel->getReason()) {
                        $content .= '<br>' . stripslashes($clientCancel->getReason());
                    }
                }
            }
        }
        if ($this->getAction() == self::ACTION_CLIENT_ERROR) {
            $content = __('Séquence d\'erreur');
            if ($this->getParam()) {
                $clientError = $container->get(ClientsErrorService::class)->getRepository()->findWoClient($this->getParam());
                if ($clientError) {
                    $content .= '<a class="ml-4" href="' . Tools::makeLink('admin', 'shop', 'transaction/' . $clientError->getTransactionReference()) . '">#' . $clientError->getTransactionReference() . '</a>';
                    $content .= '<span class="ml-4 mr-4">' . $clientError->getProduct() . '</span>';

                    if ($clientError->getDesactive()) {
                        $content .= '<span class="label label-danger label-inline">' . __('Désactivé') . '</span>';
                    } elseif ($clientError->getSendRelance3()) {
                        $content .= '<span class="label label-danger">3</span>';
                    } elseif ($clientError->getSendRelance2()) {
                        $content .= '<span class="label label-warning">2</span>';
                    } else {
                        $content .= '<span class="label label-success">1</span>';
                    }
                }
            }
        }
        return $content;
    }
}
