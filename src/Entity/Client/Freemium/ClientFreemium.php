<?php

namespace MatGyver\Entity\Client\Freemium;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatG<PERSON>ver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_clients_freemium')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Client\Freemium\ClientFreemiumRepository::class)]
class ClientFreemium
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer')]
    private $userId;

    #[ORM\Column(type: 'string', length: 250)]
    private $name;

    #[ORM\Column(type: 'string', length: 250)]
    private $uniqid;

    #[ORM\Column(type: 'boolean')]
    private $freemium = true;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateSwitch = null;

    #[ORM\JoinColumn(nullable: true, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    #[ORM\JoinColumn(nullable: false, name: 'customer_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Customer\ShopCustomer::class)]
    private $customer;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getUniqid(): ?string
    {
        return $this->uniqid;
    }

    public function setUniqid(string $uniqid): self
    {
        $this->uniqid = $uniqid;

        return $this;
    }

    public function getFreemium(): ?bool
    {
        return $this->freemium;
    }

    public function setFreemium(bool $freemium): self
    {
        $this->freemium = $freemium;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateSwitch(): ?\DateTimeInterface
    {
        return $this->dateSwitch;
    }

    public function setDateSwitch(?\DateTimeInterface $dateSwitch): self
    {
        $this->dateSwitch = $dateSwitch;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getCustomer(): ?ShopCustomer
    {
        return $this->customer;
    }

    public function setCustomer(?ShopCustomer $customer): ClientFreemium
    {
        $this->customer = $customer;

        return $this;
    }
}
