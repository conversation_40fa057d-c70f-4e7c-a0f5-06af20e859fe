<?php

namespace MatGyver\Entity\Dossier\Expertise;

use MatG<PERSON>ver\Entity\Client\Client;
use <PERSON>G<PERSON>ver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\User\User;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseReportRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_dossiers_expertises_reports')]
#[ORM\Entity(repositoryClass: DossierExpertiseReportRepository::class)]
class DossierExpertiseReport
{
    const TYPE_FINAL = 'final';
    const TYPE_INFORMATION = 'information';
    const TYPE_PRELIMINARY = 'preliminary';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\OneToOne(targetEntity: DossierExpertise::class, inversedBy: 'expertiseReport', cascade: ['persist', 'remove'])]
    private $expertise;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\OneToOne(targetEntity: DossierDocument::class, cascade: ['persist'])]
    private $document = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private $expert = null;

    #[ORM\Column(type: 'string', length: 20)]
    private $type = self::TYPE_FINAL;

    #[ORM\Column(type: 'json')]
    private $recipients = [];

    #[ORM\Column(type: 'text')]
    private $technicalAnalyses = '';

    #[ORM\Column(type: 'text')]
    private $responsibilities = '';

    #[ORM\Column(type: 'text')]
    private $conclusions = '';

    #[ORM\Column(type: 'text')]
    private $observationsPreReport = '';

    #[ORM\Column(type: 'text')]
    private $observationsFinalReport = '';

    #[ORM\Column(type: 'text')]
    private $causes = '';

    #[ORM\Column(type: 'json')]
    private $causesPictures = [];

    #[ORM\Column(type: 'text')]
    private $consequences = '';

    #[ORM\Column(type: 'json')]
    private $consequencesPictures = [];

    #[ORM\Column(type: 'json')]
    private $pictures = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierExpertisePicture::class)]
    private $firstPagePicture = null;

    #[ORM\Column(type: 'json')]
    private $documents = [];

    #[ORM\Column(type: 'json')]
    private $appendices = [];

    #[ORM\Column(type: 'json')]
    private $suppliedDocuments = [];

    #[ORM\Column(type: 'boolean')]
    private $displayPrice = false;

    #[ORM\Column(type: 'boolean')]
    private $displayFees = false;

    #[ORM\Column(type: 'boolean')]
    private $displayVrade = false;

    #[ORM\Column(type: 'boolean')]
    private $displayDocuments = false;

    #[ORM\Column(type: 'boolean')]
    private $displayPictures = false;

    #[ORM\Column(type: 'boolean')]
    private $displayHistory = false;

    #[ORM\Column(type: 'boolean')]
    private $displayMandatePriority = false;

    #[ORM\Column(type: 'text')]
    private $salesConclusion = '';

    #[ORM\Column(type: 'float')]
    private $appraisedValueAmount = 0;

    #[ORM\Column(type: 'string', length: 20)]
    private $appraisedValueTax = '';

    #[ORM\Column(type: 'text')]
    private $appraisedValue = '';

    #[ORM\Column(type: 'json')]
    private $appraisedValuePictures = [];

    #[ORM\Column(type: 'text')]
    private $legalReserves = '';

    #[ORM\Column(type: 'boolean')]
    private $modified = true;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    public function setExpertise(DossierExpertise $expertise): self
    {
        $this->expertise = $expertise;

        return $this;
    }

    public function getDocument(): ?DossierDocument
    {
        return $this->document;
    }

    public function setDocument(?DossierDocument $document): self
    {
        $this->document = $document;

        return $this;
    }

    public function getAppendices(): ?array
    {
        return $this->appendices;
    }

    public function setAppendices(array $appendices): self
    {
        $this->appendices = $appendices;

        return $this;
    }

    public function getExpert(): ?User
    {
        return $this->expert;
    }

    public function setExpert(?User $expert): self
    {
        $this->expert = $expert;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getRecipients(): ?array
    {
        return $this->recipients;
    }

    public function setRecipients(array $recipients): self
    {
        $this->recipients = $recipients;

        return $this;
    }

    public function getTechnicalAnalyses(): ?string
    {
        return $this->technicalAnalyses;
    }

    public function setTechnicalAnalyses(string $technicalAnalyses): self
    {
        $this->technicalAnalyses = $technicalAnalyses;

        return $this;
    }

    public function getResponsibilities(): ?string
    {
        return $this->responsibilities;
    }

    public function setResponsibilities(string $responsibilities): self
    {
        $this->responsibilities = $responsibilities;

        return $this;
    }

    public function getConclusions(): ?string
    {
        return $this->conclusions;
    }

    public function setConclusions(string $conclusions): self
    {
        $this->conclusions = $conclusions;

        return $this;
    }

    public function getObservationsPreReport(): ?string
    {
        return $this->observationsPreReport;
    }

    public function setObservationsPreReport(?string $observationsPreReport): self
    {
        $this->observationsPreReport = $observationsPreReport;

        return $this;
    }

    public function getObservationsFinalReport(): ?string
    {
        return $this->observationsFinalReport;
    }

    public function setObservationsFinalReport(?string $observationsFinalReport): self
    {
        $this->observationsFinalReport = $observationsFinalReport;

        return $this;
    }

    public function getCauses(): ?string
    {
        return $this->causes;
    }

    public function setCauses(string $causes): self
    {
        $this->causes = $causes;

        return $this;
    }

    public function getCausesPictures(): ?array
    {
        return $this->causesPictures;
    }

    public function setCausesPictures(array $causesPictures): self
    {
        $this->causesPictures = $causesPictures;

        return $this;
    }

    public function getConsequences(): ?string
    {
        return $this->consequences;
    }

    public function setConsequences(string $consequences): self
    {
        $this->consequences = $consequences;

        return $this;
    }

    public function getConsequencesPictures(): ?array
    {
        return $this->consequencesPictures;
    }

    public function setConsequencesPictures(array $consequencesPictures): self
    {
        $this->consequencesPictures = $consequencesPictures;

        return $this;
    }

    public function getPictures(): ?array
    {
        return $this->pictures;
    }

    public function setPictures(?array $pictures): self
    {
        $this->pictures = $pictures;

        return $this;
    }

    public function getFirstPagePicture(): ?DossierExpertisePicture
    {
        return $this->firstPagePicture;
    }

    public function setFirstPagePicture(?DossierExpertisePicture $firstPagePicture = null): self
    {
        $this->firstPagePicture = $firstPagePicture;

        return $this;
    }

    public function getDocuments(): ?array
    {
        return $this->documents;
    }

    public function setDocuments(array $documents): self
    {
        $this->documents = $documents;

        return $this;
    }

    public function getSuppliedDocuments(): ?array
    {
        return $this->suppliedDocuments;
    }

    public function setSuppliedDocuments(array $suppliedDocuments): self
    {
        $this->suppliedDocuments = $suppliedDocuments;

        return $this;
    }

    public function getDisplayPrice(): ?bool
    {
        return $this->displayPrice;
    }

    public function setDisplayPrice(bool $displayPrice): self
    {
        $this->displayPrice = $displayPrice;

        return $this;
    }

    public function getDisplayFees(): ?bool
    {
        return $this->displayFees;
    }

    public function setDisplayFees(bool $displayFees): self
    {
        $this->displayFees = $displayFees;

        return $this;
    }

    public function getDisplayVrade(): ?bool
    {
        return $this->displayVrade;
    }

    public function setDisplayVrade(bool $displayVrade): self
    {
        $this->displayVrade = $displayVrade;

        return $this;
    }

    public function getDisplayDocuments(): ?bool
    {
        return $this->displayDocuments;
    }

    public function setDisplayDocuments(bool $displayDocuments): self
    {
        $this->displayDocuments = $displayDocuments;

        return $this;
    }

    public function getDisplayPictures(): ?bool
    {
        return $this->displayPictures;
    }

    public function setDisplayPictures(bool $displayPictures): self
    {
        $this->displayPictures = $displayPictures;

        return $this;
    }

    public function getDisplayHistory(): ?bool
    {
        return $this->displayHistory;
    }

    public function setDisplayHistory(bool $displayHistory): self
    {
        $this->displayHistory = $displayHistory;

        return $this;
    }

    public function getDisplayMandatePriority(): ?bool
    {
        return $this->displayMandatePriority;
    }

    public function setDisplayMandatePriority(bool $displayMandatePriority): self
    {
        $this->displayMandatePriority = $displayMandatePriority;

        return $this;
    }

    public function getSalesConclusion(): ?string
    {
        return $this->salesConclusion;
    }

    public function setSalesConclusion(string $salesConclusion): self
    {
        $this->salesConclusion = $salesConclusion;

        return $this;
    }

    public function getAppraisedValueAmount(): ?float
    {
        return $this->appraisedValueAmount;
    }

    public function setAppraisedValueAmount(float $appraisedValueAmount): self
    {
        $this->appraisedValueAmount = $appraisedValueAmount;

        return $this;
    }

    public function getAppraisedValueTax(): ?string
    {
        return $this->appraisedValueTax;
    }

    public function setAppraisedValueTax(string $appraisedValueTax): self
    {
        $this->appraisedValueTax = $appraisedValueTax;

        return $this;
    }

    public function getAppraisedValue(): ?string
    {
        return $this->appraisedValue;
    }

    public function setAppraisedValue(string $appraisedValue): self
    {
        $this->appraisedValue = $appraisedValue;

        return $this;
    }

    public function getAppraisedValuePictures(): ?array
    {
        return $this->appraisedValuePictures;
    }

    public function setAppraisedValuePictures(array $appraisedValuePictures): self
    {
        $this->appraisedValuePictures = $appraisedValuePictures;

        return $this;
    }

    public function getLegalReserves(): ?string
    {
        return $this->legalReserves;
    }

    public function setLegalReserves(string $legalReserves): self
    {
        $this->legalReserves = $legalReserves;

        return $this;
    }

    public function getModified(): ?bool
    {
        return $this->modified;
    }

    public function setModified(bool $modified): self
    {
        $this->modified = $modified;

        return $this;
    }
}
