<?php

namespace MatGyver\Entity\Dossier\Expertise;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Client\Client;
use MatGyver\Helpers\Assets;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseJudiciaireRepository;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireChronologyService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestionAnswerService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDiligenceService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDireService;

#[ORM\Table(name: 'px_dossiers_expertises_judiciaires')]
#[ORM\Entity(repositoryClass: DossierExpertiseJudiciaireRepository::class)]
class DossierExpertiseJudiciaire
{
    const ACKNOWLEDGEMENT_OF_RECEIPT_OF_REQUEST = 'ACKNOWLEDGEMENT_OF_RECEIPT_OF_REQUEST';
    const COURT_OF_APPEAL_RULING = 'COURT_OF_APPEAL_RULING';
    const JUDGEMENT = 'JUDGEMENT';
    const JUDGEMENT_CONTRADICTORY = 'JUDGEMENT_CONTRADICTORY';
    const PRELIMINARY_RULING = 'PRELIMINARY_RULING';
    const ORDER = 'ORDER';
    const ORDER_TO_EXTEND_TIME_LIMIT = 'ORDER_TO_EXTEND_TIME_LIMIT';
    const INTERIM_ORDER = 'INTERIM_ORDER';
    const INTERIM_CONSTRUCTION_ORDER = 'INTERIM_CONSTRUCTION_ORDER';
    const ORDER_FOR_ADDITIONAL_ADVANCE_PAYMENT = 'ORDER_FOR_ADDITIONAL_ADVANCE_PAYMENT';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: DossierExpertise::class)]
    private $expertise;

    #[ORM\Column(type: 'text')]
    private $introduction = '';

    #[ORM\Column(type: 'text')]
    private $chronologyIntroduction = '';

    #[ORM\Column(type: 'text')]
    private $circumstances = '';

    #[ORM\Column(type: 'text')]
    private $courtMission = '';

    #[ORM\Column(type: 'boolean')]
    private $courtOrderTranscriptAccept = false;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $courtOrderTranscriptAcceptDate = null;

    #[ORM\Column(type: 'string', length: 50)]
    private $orderType = '';

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $orderDate = null;

    #[ORM\Column(type: 'string', length: 255)]
    private $rgNumber = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $expertiseNumber = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $courtChamber = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $minuteNumber = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $portalisNumber = '';

    #[ORM\Column(type: 'text')]
    private $notes = '';

    #[ORM\Column(type: 'text')]
    private $declarationIndependence = '';

    #[ORM\Column(type: 'text')]
    private $damageCircumstances = '';

    #[ORM\Column(type: 'text')]
    private $dueDiligences = '';

    #[ORM\Column(type: 'text')]
    private $externalCompany = '';

    #[ORM\Column(type: 'string')]
    private $externalCompanyFile = '';

    #[ORM\Column(type: 'text')]
    private $upcomingOperations = '';

    #[ORM\Column(type: 'text')]
    private $remuneration = '';

    #[ORM\Column(type: 'string')]
    private $remunerationFile = '';

    #[ORM\Column(type: 'text')]
    private $contradictoryEndText = '';

    #[ORM\Column(type: 'date', nullable: true)]
    private $lastReportDate = null;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\OneToMany(targetEntity: DossierExpertiseJudiciaireDire::class, mappedBy: 'expertiseJudiciaire')]
    private $dires;

    #[ORM\OneToMany(targetEntity: DossierExpertiseJudiciaireCourtQuestion::class, mappedBy: 'expertiseJudiciaire')]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private $courtQuestions;

    #[ORM\OneToMany(targetEntity: DossierExpertiseJudiciaireFact::class, mappedBy: 'expertiseJudiciaire')]
    private $facts;

    #[ORM\OneToMany(targetEntity: DossierExpertiseJudiciaireDoubt::class, mappedBy: 'expertiseJudiciaire')]
    private $doubts;

    #[ORM\OneToMany(targetEntity: DossierExpertiseJudiciaireOpinion::class, mappedBy: 'expertiseJudiciaire')]
    private $opinions;

    public function __construct()
    {
        $this->dires = new ArrayCollection();
        $this->courtQuestions = new ArrayCollection();
        $this->facts = new ArrayCollection();
        $this->doubts = new ArrayCollection();
        $this->opinions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    public function setExpertise(?DossierExpertise $expertise): self
    {
        $this->expertise = $expertise;

        return $this;
    }

    public function getIntroduction(): ?string
    {
        return $this->introduction;
    }

    public function setIntroduction(string $introduction): self
    {
        $this->introduction = $introduction;

        return $this;
    }

    public function getChronologyIntroduction(): ?string
    {
        return $this->chronologyIntroduction;
    }

    public function setChronologyIntroduction(string $chronologyIntroduction): self
    {
        $this->chronologyIntroduction = $chronologyIntroduction;

        return $this;
    }

    public function getCircumstances(): ?string
    {
        return $this->circumstances;
    }

    public function setCircumstances(string $circumstances): self
    {
        $this->circumstances = $circumstances;

        return $this;
    }

    public function getCourtMission(): ?string
    {
        return $this->courtMission;
    }

    public function setCourtMission(string $courtMission): self
    {
        $this->courtMission = $courtMission;

        return $this;
    }

    public function getCourtOrderTranscriptAccept(): ?bool
    {
        return $this->courtOrderTranscriptAccept;
    }

    public function setCourtOrderTranscriptAccept(bool $courtOrderTranscriptAccept): self
    {
        $this->courtOrderTranscriptAccept = $courtOrderTranscriptAccept;

        return $this;
    }

    public function getCourtOrderTranscriptAcceptDate(): ?\DateTimeInterface
    {
        return $this->courtOrderTranscriptAcceptDate;
    }

    public function setCourtOrderTranscriptAcceptDate(?\DateTimeInterface $courtOrderTranscriptAcceptDate): self
    {
        $this->courtOrderTranscriptAcceptDate = $courtOrderTranscriptAcceptDate;

        return $this;
    }

    public function getOrderType(): ?string
    {
        return $this->orderType;
    }

    public function setOrderType(string $orderType): self
    {
        $this->orderType = $orderType;

        return $this;
    }

    public function getOrderDate(): ?\DateTimeInterface
    {
        return $this->orderDate;
    }

    public function setOrderDate(\DateTimeInterface $orderDate): self
    {
        $this->orderDate = $orderDate;

        return $this;
    }

    public function getRgNumber(): ?string
    {
        return $this->rgNumber;
    }

    public function setRgNumber(string $rgNumber): self
    {
        $this->rgNumber = $rgNumber;

        return $this;
    }

    public function getExpertiseNumber(): ?string
    {
        return $this->expertiseNumber;
    }

    public function setExpertiseNumber(string $expertiseNumber): self
    {
        $this->expertiseNumber = $expertiseNumber;

        return $this;
    }

    public function getCourtChamber(): ?string
    {
        return $this->courtChamber;
    }

    public function setCourtChamber(string $courtChamber): self
    {
        $this->courtChamber = $courtChamber;

        return $this;
    }

    public function getMinuteNumber(): ?string
    {
        return $this->minuteNumber;
    }

    public function setMinuteNumber(string $minuteNumber): self
    {
        $this->minuteNumber = $minuteNumber;

        return $this;
    }

    public function getPortalisNumber(): ?string
    {
        return $this->portalisNumber;
    }

    public function setPortalisNumber(string $portalisNumber): self
    {
        $this->portalisNumber = $portalisNumber;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getDeclarationIndependence(): ?string
    {
        return $this->declarationIndependence;
    }

    public function setDeclarationIndependence(string $declarationIndependence): self
    {
        $this->declarationIndependence = $declarationIndependence;

        return $this;
    }

    public function getDamageCircumstances(): ?string
    {
        return $this->damageCircumstances;
    }

    public function setDamageCircumstances(string $damageCircumstances): self
    {
        $this->damageCircumstances = $damageCircumstances;

        return $this;
    }

    public function getDueDiligences(): ?string
    {
        return $this->dueDiligences;
    }

    public function setDueDiligences(string $dueDiligences): self
    {
        $this->dueDiligences = $dueDiligences;

        return $this;
    }

    public function getExternalCompany(): ?string
    {
        return $this->externalCompany;
    }

    public function setExternalCompany(string $externalCompany): self
    {
        $this->externalCompany = $externalCompany;

        return $this;
    }

    public function getExternalCompanyFile(): ?string
    {
        return $this->externalCompanyFile;
    }

    public function setExternalCompanyFile(string $externalCompanyFile): self
    {
        $this->externalCompanyFile = $externalCompanyFile;

        return $this;
    }

    public function getExternalCompanyFileUrl(): ?string
    {
        return Assets::getMediaUrl($this->getExpertise()->getDossier()->getFolder() . $this->getExternalCompanyFile() . '?dossier_ref=' . $this->getExpertise()->getDossier()->getReference());
    }

    public function getUpcomingOperations(): ?string
    {
        return $this->upcomingOperations;
    }

    public function setUpcomingOperations(string $upcomingOperations): self
    {
        $this->upcomingOperations = $upcomingOperations;

        return $this;
    }

    public function getRemuneration(): ?string
    {
        return $this->remuneration;
    }

    public function setRemuneration(string $remuneration): self
    {
        $this->remuneration = $remuneration;

        return $this;
    }

    public function getRemunerationFile(): ?string
    {
        return $this->remunerationFile;
    }

    public function setRemunerationFile(string $remunerationFile): self
    {
        $this->remunerationFile = $remunerationFile;

        return $this;
    }

    public function getRemunerationFileUrl(): ?string
    {
        return Assets::getMediaUrl($this->getExpertise()->getDossier()->getFolder() . $this->getRemunerationFile() . '?dossier_ref=' . $this->getExpertise()->getDossier()->getReference());
    }

    public function getContradictoryEndText(): ?string
    {
        return $this->contradictoryEndText;
    }

    public function setContradictoryEndText(string $contradictoryEndText): self
    {
        $this->contradictoryEndText = $contradictoryEndText;

        return $this;
    }

    public function getLastReportDate(): ?\DateTimeInterface
    {
        return $this->lastReportDate;
    }

    public function setLastReportDate(\DateTimeInterface $lastReportDate): self
    {
        $this->lastReportDate = $lastReportDate;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection<int, DossierExpertiseJudiciaireDire>
     */
    public function getDires(): Collection
    {
        return $this->dires;
    }

    public function getDiresAnswers(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->getAnswers($this->getExpertise());
    }

    public function addDire(DossierExpertiseJudiciaireDire $dire): self
    {
        if (!$this->dires->contains($dire)) {
            $this->dires[] = $dire;
            $dire->setExpertiseJudiciaire($this);
        }

        return $this;
    }

    public function removeDire(DossierExpertiseJudiciaireDire $dire): self
    {
        if ($this->dires->removeElement($dire)) {
            // set the owning side to null (unless already changed)
            if ($dire->getExpertiseJudiciaire() === $this) {
                $dire->setExpertiseJudiciaire(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DossierExpertiseJudiciaireCourtQuestion>
     */
    public function getCourtQuestions(): Collection
    {
        return $this->courtQuestions;
    }

    public function getCourtQuestionsAnswers(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireCourtQuestionAnswerService::class)->getRepository()->findBy(['expertiseJudiciaire' => $this]);
    }

    public function addCourtQuestion(DossierExpertiseJudiciaireCourtQuestion $courtQuestion): self
    {
        if (!$this->courtQuestions->contains($courtQuestion)) {
            $this->courtQuestions[] = $courtQuestion;
            $courtQuestion->setExpertiseJudiciaire($this);
        }

        return $this;
    }

    public function removeCourtQuestion(DossierExpertiseJudiciaireCourtQuestion $courtQuestion): self
    {
        if ($this->courtQuestions->removeElement($courtQuestion)) {
            // set the owning side to null (unless already changed)
            if ($courtQuestion->getExpertiseJudiciaire() === $this) {
                $courtQuestion->setExpertiseJudiciaire(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DossierExpertiseJudiciaireFact>
     */
    public function getFacts(): Collection
    {
        return $this->facts;
    }

    public function addFact(DossierExpertiseJudiciaireFact $fact): self
    {
        if (!$this->facts->contains($fact)) {
            $this->facts[] = $fact;
            $fact->setExpertiseJudiciaire($this);
        }

        return $this;
    }

    public function removeFact(DossierExpertiseJudiciaireFact $fact): self
    {
        if ($this->facts->removeElement($fact)) {
            // set the owning side to null (unless already changed)
            if ($fact->getExpertiseJudiciaire() === $this) {
                $fact->setExpertiseJudiciaire(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DossierExpertiseJudiciaireDoubt>
     */
    public function getDoubts(): Collection
    {
        return $this->doubts;
    }

    public function addDoubt(DossierExpertiseJudiciaireDoubt $doubt): self
    {
        if (!$this->doubts->contains($doubt)) {
            $this->doubts[] = $doubt;
            $doubt->setExpertiseJudiciaire($this);
        }

        return $this;
    }

    public function removeDoubt(DossierExpertiseJudiciaireDoubt $doubt): self
    {
        if ($this->doubts->removeElement($doubt)) {
            // set the owning side to null (unless already changed)
            if ($doubt->getExpertiseJudiciaire() === $this) {
                $doubt->setExpertiseJudiciaire(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DossierExpertiseJudiciaireOpinion>
     */
    public function getOpinions(): Collection
    {
        return $this->opinions;
    }

    public function addOpinion(DossierExpertiseJudiciaireOpinion $opinion): self
    {
        if (!$this->opinions->contains($opinion)) {
            $this->opinions[] = $opinion;
            $opinion->setExpertiseJudiciaire($this);
        }

        return $this;
    }

    public function removeOpinion(DossierExpertiseJudiciaireOpinion $opinion): self
    {
        if ($this->opinions->removeElement($opinion)) {
            // set the owning side to null (unless already changed)
            if ($opinion->getExpertiseJudiciaire() === $this) {
                $opinion->setExpertiseJudiciaire(null);
            }
        }

        return $this;
    }

    public function getChronology(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findBy(['expertise' => $this->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY], ['position' => 'ASC']);
    }

    public function getDocumentsReceived(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findBy(['expertise' => $this->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_RECEIVED], ['position' => 'ASC']);
    }

    public function getDocumentsSent(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findBy(['expertise' => $this->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_SENT], ['position' => 'ASC']);
    }

    public function getDiligences(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertiseJudiciaireDiligenceService::class)->getRepository()->findBy(['expertise' => $this->getExpertise()]);
    }
}
