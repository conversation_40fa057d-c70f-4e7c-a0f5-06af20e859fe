<?php

namespace Mat<PERSON><PERSON>ver\Entity\Dossier;

use MatG<PERSON>ver\Entity\Address\Address;
use MatGyver\Entity\Client\Client;
use MatGyver\Repository\Dossier\DossierVehicleRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_dossiers_vehicles')]
#[ORM\Entity(repositoryClass: DossierVehicleRepository::class)]
class DossierVehicle
{
    const ENGINE_GASOLINE = 'gasoline';
    const ENGINE_DIESEL = 'diesel';
    const ENGINE_GPL = 'gpl';
    const ENGINE_ELECTRIC = 'electric';
    const ENGINE_HYBRID = 'hybrid';
    const ENGINE_BIOETHANOL = 'bioethanol';
    const ENGINE_HYDROGEN = 'hydrogen';
    const ENGINE_OTHER = 'other';

    const COLOR_WHITE = 'white';
    const COLOR_LIGHT_BLUE = 'light_blue';
    const COLOR_DARK_BLUE = 'dark_blue';
    const COLOR_LIGHT_GRAY = 'light_gray';
    const COLOR_DARK_GRAY = 'dark_gray';
    const COLOR_YELLOW = 'yellow';
    const COLOR_LIGHT_BROWN = 'light_brown';
    const COLOR_DARK_BROWN = 'dark_brown';
    const COLOR_BLACK = 'black';
    const COLOR_ORANGE = 'orange';
    const COLOR_PURPLE = 'purple';
    const COLOR_PINK = 'pink';
    const COLOR_LIGHT_RED = 'light_red';
    const COLOR_DARK_RED = 'dark_red';
    const COLOR_LIGHT_GREEN = 'light_green';
    const COLOR_DARK_GREEN = 'dark_green';
    const COLOR_VIOLET = 'violet';
    const COLOR_OTHER = 'other';

    const GEARBOX_MANUAL = 'manual';
    const GEARBOX_AUTOMATIC = 'automatic';
    const GEARBOX_HYBRID = 'hybrid';
    const GEARBOX_OTHER = 'other';

    const USE_TYPE_PRIVATE = 'private';
    const USE_TYPE_PROFESSIONAL = 'professional';

    const USE_PURPOSE_NOT_SET = 'not_set';
    const USE_PURPOSE_OTHER = 'other';
    const USE_PURPOSE_TAXI = 'taxi';
    const USE_PURPOSE_VSL = 'vsl';
    const USE_PURPOSE_AMBULANCE = 'ambulance';
    const USE_PURPOSE_DRIVING_SCHOOL = 'driving_school';

    const FEES_PER_DAY = 'day';
    const FEES_PER_MONTH = 'month';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\OneToOne(targetEntity: Dossier::class, inversedBy: 'vehicle', cascade: ['persist', 'remove'])]
    private $dossier;

    #[ORM\Column(type: 'string', length: 50)]
    private $registration;

    #[ORM\Column(type: 'string', length: 2)]
    private $registrationCountry = 'FR';

    #[ORM\Column(type: 'string', length: 50)]
    private $reference = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $serialNumber;

    #[ORM\Column(type: 'string', length: 20)]
    private $serialNumberValid = 'yes';

    #[ORM\Column(type: 'string', length: 255)]
    private $formuleNumber = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $insuranceCard = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $brand;

    #[ORM\Column(type: 'string', length: 50)]
    private $model;

    #[ORM\Column(type: 'string', length: 50)]
    private $engine;

    #[ORM\Column(type: 'string', length: 255)]
    private $type = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $vehicleType = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $bodywork = '';

    #[ORM\Column(type: 'integer')]
    private $power = 0;

    #[ORM\Column(type: 'string', length: 255)]
    private $engineCode = '';

    #[ORM\Column(type: 'string', length: 20)]
    private $gearbox = '';

    #[ORM\Column(type: 'integer')]
    private $seating = 0;

    #[ORM\Column(type: 'string', length: 20)]
    private $useType = self::USE_TYPE_PRIVATE;

    #[ORM\Column(type: 'string', length: 20)]
    private $usePurpose = self::USE_PURPOSE_OTHER;

    #[ORM\Column(type: 'string', length: 255)]
    private $usePurposeOther = '';

    #[ORM\Column(type: 'float')]
    private $vrade = 0;

    #[ORM\Column(type: 'string', length: 20)]
    private $vradeTax = '';

    #[ORM\Column(type: 'string', length: 10)]
    private $dismantled;

    #[ORM\Column(type: 'string', length: 10)]
    private $reassembled;

    #[ORM\Column(type: 'string', length: 10)]
    private $draining;

    #[ORM\Column(type: 'string')]
    private $drainingComment = '';

    #[ORM\Column(type: 'string', length: 10)]
    private $carStorage;

    #[ORM\Column(type: 'float')]
    private $carStorageFees = 0;

    #[ORM\Column(type: 'string', length: 20)]
    private $carStorageFeesType = self::FEES_PER_DAY;

    #[ORM\Column(type: 'date')]
    private $carStorageDate = null;

    #[ORM\Column(type: 'date')]
    private $carStorageDateEnd = null;

    #[ORM\Column(type: 'string')]
    private $carStorageInfos = '';

    #[ORM\Column(type: 'date', nullable: true)]
    private $buyDate;

    #[ORM\Column(type: 'float')]
    private $buyPrice;

    #[ORM\Column(type: 'string', length: 50)]
    private $buyPayment;

    #[ORM\Column(type: 'string')]
    private $buyPaymentComment = '';

    #[ORM\Column(type: 'integer')]
    private $buyMileage;

    #[ORM\Column(type: 'string')]
    private $buyWebsite = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $buyIntermediary = '';

    #[ORM\Column(type: 'string', length: 10)]
    private $buyTest = 'unsure';

    #[ORM\Column(type: 'integer')]
    private $buyTestDuration = 0;

    #[ORM\Column(type: 'integer')]
    private $buyTestMileage = 0;

    #[ORM\Column(type: 'date', nullable: true)]
    private $registrationDate;

    #[ORM\Column(type: 'string', length: 20)]
    private $color;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: Address::class)]
    private $immobilizationPlace;

    #[ORM\Column(type: 'string', length: 50)]
    private $immobilizationPlaceType = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $immobilizationPlaceName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $immobilizationEmail = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $immobilizationAddress = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $immobilizationZip = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $immobilizationCity = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $immobilizationTelephone = '';

    #[ORM\Column(type: 'text')]
    private $observations;

    #[ORM\Column(type: 'string', length: 10)]
    private $loan = 'unsure';

    #[ORM\Column(type: 'float')]
    private $loanAmount = 0;

    #[ORM\Column(type: 'date')]
    private $loanEndDate = null;

    #[ORM\Column(type: 'boolean')]
    private $loanInsurance = false;

    #[ORM\Column(type: 'text')]
    private $standardEquipments = '';

    #[ORM\Column(type: 'text')]
    private $accessoryEquipments = '';

    #[ORM\Column(type: 'text')]
    private $securityEquipments = '';

    #[ORM\Column(type: 'date')]
    private $dateFirstCirculation = null;

    #[ORM\Column(type: 'date')]
    private $dateTechnicalInspectionExpiry = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getRegistration(): ?string
    {
        return $this->registration;
    }

    public function setRegistration(string $registration): self
    {
        $this->registration = $registration;

        return $this;
    }

    public function getRegistrationCountry(): ?string
    {
        return $this->registrationCountry;
    }

    public function setRegistrationCountry(string $registrationCountry): self
    {
        $this->registrationCountry = $registrationCountry;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getSerialNumber(): ?string
    {
        return $this->serialNumber;
    }

    public function setSerialNumber(string $serialNumber): self
    {
        $this->serialNumber = $serialNumber;

        return $this;
    }

    public function getSerialNumberValid(): ?string
    {
        return $this->serialNumberValid;
    }

    public function setSerialNumberValid(string $serialNumberValid): self
    {
        $this->serialNumberValid = $serialNumberValid;

        return $this;
    }

    public function getFormuleNumber(): ?string
    {
        return $this->formuleNumber;
    }

    public function setFormuleNumber(string $formuleNumber): self
    {
        $this->formuleNumber = $formuleNumber;

        return $this;
    }

    public function getInsuranceCard(): ?string
    {
        return $this->insuranceCard;
    }

    public function setInsuranceCard(string $insuranceCard): self
    {
        $this->insuranceCard = $insuranceCard;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getModel(): ?string
    {
        return $this->model;
    }

    public function setModel(string $model): self
    {
        $this->model = $model;

        return $this;
    }

    public function getEngine(): ?string
    {
        return $this->engine;
    }

    public function setEngine(string $engine): self
    {
        $this->engine = $engine;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getVehicleType(): ?string
    {
        return $this->vehicleType;
    }

    public function setVehicleType(string $vehicleType): self
    {
        $this->vehicleType = $vehicleType;

        return $this;
    }

    public function getBodywork(): ?string
    {
        return $this->bodywork;
    }

    public function setBodywork(string $bodywork): self
    {
        $this->bodywork = $bodywork;

        return $this;
    }

    public function getPower(): ?int
    {
        return $this->power;
    }

    public function setPower(int $power): self
    {
        $this->power = $power;

        return $this;
    }

    public function getEngineCode(): ?string
    {
        return $this->engineCode;
    }

    public function setEngineCode(string $engineCode): self
    {
        $this->engineCode = $engineCode;

        return $this;
    }

    public function getGearbox(): ?string
    {
        return $this->gearbox;
    }

    public function setGearbox(string $gearbox): self
    {
        $this->gearbox = $gearbox;

        return $this;
    }

    public function getSeating(): ?string
    {
        return $this->seating;
    }

    public function setSeating(string $seating): self
    {
        $this->seating = $seating;

        return $this;
    }

    public function getUseType(): ?string
    {
        return $this->useType;
    }

    public function setUseType(string $useType): self
    {
        $this->useType = $useType;

        return $this;
    }

    public function getUsePurpose(): ?string
    {
        return $this->usePurpose;
    }

    public function setUsePurpose(string $usePurpose): self
    {
        $this->usePurpose = $usePurpose;

        return $this;
    }

    public function getUsePurposeOther(): ?string
    {
        return $this->usePurposeOther;
    }

    public function setUsePurposeOther(string $usePurposeOther): self
    {
        $this->usePurposeOther = $usePurposeOther;

        return $this;
    }

    public function getVrade(): ?float
    {
        return $this->vrade;
    }

    public function setVrade(float $vrade): self
    {
        $this->vrade = $vrade;

        return $this;
    }

    public function getVradeTax(): ?string
    {
        return $this->vradeTax;
    }

    public function setVradeTax(string $vradeTax): self
    {
        $this->vradeTax = $vradeTax;

        return $this;
    }

    public function getDismantled(): ?string
    {
        return $this->dismantled;
    }

    public function setDismantled(string $dismantled): self
    {
        $this->dismantled = $dismantled;

        return $this;
    }

    public function getReassembled(): ?string
    {
        return $this->reassembled;
    }

    public function setReassembled(?string $reassembled): self
    {
        $this->reassembled = $reassembled;

        return $this;
    }

    public function getDraining(): ?string
    {
        return $this->draining;
    }

    public function setDraining(?string $draining): self
    {
        $this->draining = $draining;

        return $this;
    }

    public function getDrainingComment(): ?string
    {
        return $this->drainingComment;
    }

    public function setDrainingComment(?string $drainingComment): self
    {
        $this->drainingComment = $drainingComment;

        return $this;
    }

    public function getCarStorage(): ?string
    {
        return $this->carStorage;
    }

    public function setCarStorage(?string $carStorage): self
    {
        $this->carStorage = $carStorage;

        return $this;
    }

    public function getCarStorageFees(): ?float
    {
        return $this->carStorageFees;
    }

    public function setCarStorageFees(float $carStorageFees): self
    {
        $this->carStorageFees = $carStorageFees;

        return $this;
    }

    public function getCarStorageFeesType(): ?string
    {
        return $this->carStorageFeesType;
    }

    public function setCarStorageFeesType(string $carStorageFeesType): self
    {
        $this->carStorageFeesType = $carStorageFeesType;

        return $this;
    }

    public function getCarStorageDate(): ?\DateTimeInterface
    {
        return $this->carStorageDate;
    }

    public function setCarStorageDate(?\DateTimeInterface $carStorageDate): self
    {
        $this->carStorageDate = $carStorageDate;

        return $this;
    }

    public function getCarStorageDateEnd(): ?\DateTimeInterface
    {
        return $this->carStorageDateEnd;
    }

    public function setCarStorageDateEnd(?\DateTimeInterface $carStorageDateEnd): self
    {
        $this->carStorageDateEnd = $carStorageDateEnd;

        return $this;
    }

    public function getCarStorageInfos(): ?string
    {
        return $this->carStorageInfos;
    }

    public function setCarStorageInfos(?string $carStorageInfos): self
    {
        $this->carStorageInfos = $carStorageInfos;

        return $this;
    }

    public function getBuyDate(): ?\DateTimeInterface
    {
        return $this->buyDate;
    }

    public function setBuyDate(?\DateTimeInterface $buyDate): self
    {
        $this->buyDate = $buyDate;

        return $this;
    }

    public function getBuyPrice(): ?float
    {
        return $this->buyPrice;
    }

    public function setBuyPrice(float $buyPrice): self
    {
        $this->buyPrice = $buyPrice;

        return $this;
    }

    public function getBuyPayment(): ?string
    {
        return $this->buyPayment;
    }

    public function setBuyPayment(string $buyPayment): self
    {
        $this->buyPayment = $buyPayment;

        return $this;
    }

    public function getBuyPaymentComment(): ?string
    {
        return $this->buyPaymentComment;
    }

    public function setBuyPaymentComment(string $buyPaymentComment): self
    {
        $this->buyPaymentComment = $buyPaymentComment;

        return $this;
    }

    public function getBuyMileage(): ?int
    {
        return $this->buyMileage;
    }

    public function setBuyMileage(int $buyMileage): self
    {
        $this->buyMileage = $buyMileage;

        return $this;
    }

    public function getBuyWebsite(): ?string
    {
        return $this->buyWebsite;
    }

    public function setBuyWebsite(string $buyWebsite): self
    {
        $this->buyWebsite = $buyWebsite;

        return $this;
    }

    public function getBuyIntermediary(): ?string
    {
        return $this->buyIntermediary;
    }

    public function setBuyIntermediary(string $buyIntermediary): self
    {
        $this->buyIntermediary = $buyIntermediary;

        return $this;
    }

    public function getBuyTest(): ?string
    {
        return $this->buyTest;
    }

    public function setBuyTest(string $buyTest): self
    {
        $this->buyTest = $buyTest;

        return $this;
    }

    public function getBuyTestDuration(): ?int
    {
        return $this->buyTestDuration;
    }

    public function setBuyTestDuration(int $buyTestDuration): self
    {
        $this->buyTestDuration = $buyTestDuration;

        return $this;
    }

    public function getBuyTestMileage(): ?int
    {
        return $this->buyTestMileage;
    }

    public function setBuyTestMileage(int $buyTestMileage): self
    {
        $this->buyTestMileage = $buyTestMileage;

        return $this;
    }

    public function getRegistrationDate(): ?\DateTimeInterface
    {
        return $this->registrationDate;
    }

    public function setRegistrationDate(?\DateTimeInterface $registrationDate): self
    {
        $this->registrationDate = $registrationDate;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getImmobilizationPlace(): ?Address
    {
        return $this->immobilizationPlace;
    }

    public function setImmobilizationPlace(?Address $immobilizationPlace): self
    {
        $this->immobilizationPlace = $immobilizationPlace;

        return $this;
    }

    public function getImmobilizationPlaceType(): ?string
    {
        return $this->immobilizationPlaceType;
    }

    public function setImmobilizationPlaceType(string $immobilizationPlaceType): self
    {
        $this->immobilizationPlaceType = $immobilizationPlaceType;

        return $this;
    }

    public function getImmobilizationPlaceName(): ?string
    {
        return $this->immobilizationPlaceName;
    }

    public function setImmobilizationPlaceName(string $immobilizationPlaceName): self
    {
        $this->immobilizationPlaceName = $immobilizationPlaceName;

        return $this;
    }

    public function getImmobilizationEmail(): ?string
    {
        return $this->immobilizationEmail;
    }

    public function setImmobilizationEmail(string $immobilizationEmail): self
    {
        $this->immobilizationEmail = $immobilizationEmail;

        return $this;
    }

    public function getImmobilizationAddress(): ?string
    {
        return $this->immobilizationAddress;
    }

    public function setImmobilizationAddress(string $immobilizationAddress): self
    {
        $this->immobilizationAddress = $immobilizationAddress;

        return $this;
    }

    public function getImmobilizationZip(): ?string
    {
        return $this->immobilizationZip;
    }

    public function setImmobilizationZip(string $immobilizationZip): self
    {
        $this->immobilizationZip = $immobilizationZip;

        return $this;
    }

    public function getImmobilizationCity(): ?string
    {
        return $this->immobilizationCity;
    }

    public function setImmobilizationCity(string $immobilizationCity): self
    {
        $this->immobilizationCity = $immobilizationCity;

        return $this;
    }

    public function getImmobilizationTelephone(): ?string
    {
        return $this->immobilizationTelephone;
    }

    public function setImmobilizationTelephone(string $immobilizationTelephone): self
    {
        $this->immobilizationTelephone = $immobilizationTelephone;

        return $this;
    }

    public function getObservations(): ?string
    {
        return $this->observations;
    }

    public function setObservations(string $observations): self
    {
        $this->observations = $observations;

        return $this;
    }

    public function getLoan(): ?string
    {
        return $this->loan;
    }

    public function setLoan(string $loan): self
    {
        $this->loan = $loan;

        return $this;
    }

    public function getLoanAmount(): ?float
    {
        return $this->loanAmount;
    }

    public function setLoanAmount(float $loanAmount): self
    {
        $this->loanAmount = $loanAmount;

        return $this;
    }

    public function getLoanEndDate(): ?\DateTimeInterface
    {
        return $this->loanEndDate;
    }

    public function setLoanEndDate(?\DateTimeInterface $loanEndDate): self
    {
        $this->loanEndDate = $loanEndDate;

        return $this;
    }

    public function getLoanInsurance(): ?bool
    {
        return $this->loanInsurance;
    }

    public function setLoanInsurance(bool $loanInsurance): self
    {
        $this->loanInsurance = $loanInsurance;

        return $this;
    }

    public function getStandardEquipments(): ?string
    {
        return $this->standardEquipments;
    }

    public function setStandardEquipments(string $standardEquipments): self
    {
        $this->standardEquipments = $standardEquipments;

        return $this;
    }

    public function getAccessoryEquipments(): ?string
    {
        return $this->accessoryEquipments;
    }

    public function setAccessoryEquipments(string $accessoryEquipments): self
    {
        $this->accessoryEquipments = $accessoryEquipments;

        return $this;
    }

    public function getSecurityEquipments(): ?string
    {
        return $this->securityEquipments;
    }

    public function setSecurityEquipments(string $securityEquipments): self
    {
        $this->securityEquipments = $securityEquipments;

        return $this;
    }

    public function getDateFirstCirculation(): ?\DateTimeInterface
    {
        return $this->dateFirstCirculation;
    }

    public function setDateFirstCirculation(?\DateTimeInterface $dateFirstCirculation): self
    {
        $this->dateFirstCirculation = $dateFirstCirculation;

        return $this;
    }

    public function getDateTechnicalInspectionExpiry(): ?\DateTimeInterface
    {
        return $this->dateTechnicalInspectionExpiry;
    }

    public function setDateTechnicalInspectionExpiry(?\DateTimeInterface $dateTechnicalInspectionExpiry): self
    {
        $this->dateTechnicalInspectionExpiry = $dateTechnicalInspectionExpiry;

        return $this;
    }
}
