<?php

namespace MatGyver\Entity\Dossier;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use MatGyver\Entity\Breakdown\Breakdown;
use MatGyver\Entity\Client\Client;
use MatGyver\Repository\Dossier\DossierBreakdownRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_dossiers_breakdowns')]
#[ORM\Entity(repositoryClass: DossierBreakdownRepository::class)]
class DossierBreakdown
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\OneToOne(targetEntity: Dossier::class, inversedBy: 'breakdown', cascade: ['persist', 'remove'])]
    private $dossier;

    #[ORM\Column(type: 'integer')]
    private $mileage = 0;

    #[ORM\Column(type: 'text')]
    private $description = '';

    #[ORM\Column(type: 'float')]
    private $towingCost = 0;

    #[ORM\Column(type: 'string')]
    private $towingServiceProvidedBy = '';

    #[ORM\Column(type: 'date', nullable: true)]
    private $towingDate = null;

    #[ORM\Column(type: 'integer')]
    private $towingMileage = 0;

    #[ORM\Column(type: 'float')]
    private $dismantlingCost = 0;

    #[ORM\Column(type: 'date', nullable: true)]
    private $dismantlingDate = null;

    #[ORM\Column(type: 'integer')]
    private $dismantlingMileage = 0;

    #[ORM\Column(type: 'string', length: 50)]
    private $incidentNumber = '';

    #[ORM\Column(type: 'text')]
    private $causeOfMalfunction = '';

    #[ORM\Column(type: 'text')]
    private $fuelReversal = '';

    #[ORM\Column(type: 'text')]
    private $fuelPollution = '';

    #[ORM\Column(type: 'date', nullable: true)]
    private $date = null;

    #[ORM\JoinTable(name: 'px_dossiers_breakdowns_origins')]
    #[ORM\JoinColumn(name: 'dossier_breakdown_id', referencedColumnName: 'id')]
    #[ORM\InverseJoinColumn(name: 'breakdown_id', referencedColumnName: 'id')]
    #[ORM\ManyToMany(targetEntity: \MatGyver\Entity\Breakdown\Breakdown::class)]
    private $origins;

    public function __construct()
    {
        $this->origins = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getMileage(): ?int
    {
        return $this->mileage;
    }

    public function setMileage(int $mileage): self
    {
        $this->mileage = $mileage;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getTowingCost(): ?float
    {
        return $this->towingCost;
    }

    public function setTowingCost(float $towingCost): self
    {
        $this->towingCost = $towingCost;

        return $this;
    }

    public function getTowingServiceProvidedBy(): ?string
    {
        return $this->towingServiceProvidedBy;
    }

    public function setTowingServiceProvidedBy(string $towingServiceProvidedBy): self
    {
        $this->towingServiceProvidedBy = $towingServiceProvidedBy;

        return $this;
    }

    public function getTowingDate(): ?\DateTimeInterface
    {
        return $this->towingDate;
    }

    public function setTowingDate(?\DateTimeInterface $towingDate): self
    {
        $this->towingDate = $towingDate;

        return $this;
    }

    public function getTowingMileage(): int
    {
        return $this->towingMileage;
    }

    public function setTowingMileage(int $towingMileage): self
    {
        $this->towingMileage = $towingMileage;

        return $this;
    }

    public function getDismantlingCost(): ?float
    {
        return $this->dismantlingCost;
    }

    public function setDismantlingCost(float $dismantlingCost): self
    {
        $this->dismantlingCost = $dismantlingCost;

        return $this;
    }

    public function getDismantlingDate(): ?\DateTimeInterface
    {
        return $this->dismantlingDate;
    }

    public function setDismantlingDate(?\DateTimeInterface $dismantlingDate): self
    {
        $this->dismantlingDate = $dismantlingDate;

        return $this;
    }

    public function getDismantlingMileage(): int
    {
        return $this->dismantlingMileage;
    }

    public function setDismantlingMileage(int $dismantlingMileage): self
    {
        $this->dismantlingMileage = $dismantlingMileage;

        return $this;
    }

    public function getIncidentNumber(): ?string
    {
        return $this->incidentNumber;
    }

    public function setIncidentNumber(string $incidentNumber): self
    {
        $this->incidentNumber = $incidentNumber;

        return $this;
    }

    public function getCauseOfMalfunction(): ?string
    {
        return $this->causeOfMalfunction;
    }

    public function setCauseOfMalfunction(string $causeOfMalfunction): self
    {
        $this->causeOfMalfunction = $causeOfMalfunction;

        return $this;
    }

    public function getFuelReversal(): ?string
    {
        return $this->fuelReversal;
    }

    public function setFuelReversal(string $fuelReversal): self
    {
        $this->fuelReversal = $fuelReversal;

        return $this;
    }

    public function getFuelPollution(): ?string
    {
        return $this->fuelPollution;
    }

    public function setFuelPollution(string $fuelPollution): self
    {
        $this->fuelPollution = $fuelPollution;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(?\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection|Breakdown[]
     */
    public function getOrigins(): Collection
    {
        return $this->origins;
    }

    public function addOrigin(Breakdown $breakdown): self
    {
        if (!$this->origins->contains($breakdown)) {
            $this->origins[] = $breakdown;
        }

        return $this;
    }

    public function removeOrigin(Breakdown $breakdown): self
    {
        if ($this->origins->contains($breakdown)) {
            $this->origins->removeElement($breakdown);
        }

        return $this;
    }
}
