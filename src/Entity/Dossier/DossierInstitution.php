<?php

namespace MatG<PERSON>ver\Entity\Dossier;

use <PERSON><PERSON><PERSON>ver\Entity\Address\Address;
use MatG<PERSON><PERSON>\Entity\Client\Client;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatG<PERSON>ver\Enums\ConfigEnum;
use MatGyver\Repository\Dossier\DossierInstitutionRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierInstitutionService;

#[ORM\Table(name: 'px_dossiers_institutions')]
#[ORM\Entity(repositoryClass: DossierInstitutionRepository::class)]
class DossierInstitution
{
    const TYPE_AGENT = 'agent';
    const TYPE_BAILIFF = 'bailiff';
    const TYPE_BODYBUILDER = 'bodybuilder';
    const TYPE_BODY_REPAIRER = 'body_repairer';
    const TYPE_BUYER = 'buyer';
    const TYPE_CLERK = 'clerk';
    const TYPE_CABINET = 'cabinet';
    const TYPE_CONTACT = 'contact';
    const TYPE_CONSULTANT = 'consultant';
    const TYPE_COURT_ASSISTANT = 'court_assistant';
    const TYPE_COURT_PRESIDENT = 'court_president';
    const TYPE_DIRECTOR = 'director';
    const TYPE_EXPERT = 'expert';
    const TYPE_EXPERT_CUSTOM = 'expert_custom';
    const TYPE_EXPERTISE_PLACE = 'expertise_place';
    const TYPE_IMPORTER = 'importer';
    const TYPE_INSURER = 'insurer';
    const TYPE_INTERMEDIARY = 'intermediary';
    const TYPE_FORENSIC_EXPERT = 'forensic_expert';
    const TYPE_INSURED = 'insured';
    const TYPE_INSURER_EXPERT = 'insurer_expert';
    const TYPE_LAWYER = 'lawyer';
    const TYPE_LESSOR = 'lessor';
    const TYPE_LESE = 'lese';
    const TYPE_MANAGER = 'manager';
    const TYPE_MANDATE = 'mandate';
    const TYPE_MANUFACTURER = 'manufacturer';
    const TYPE_MANUFACTURER2 = 'manufacturer2';
    const TYPE_OTHER = 'other';
    const TYPE_POSTULANT = 'postulant';
    const TYPE_REPAIRER = 'repairer';
    const TYPE_SELLER = 'seller';
    const TYPE_SINISTER = 'sinister';
    const TYPE_SPARE_PARTS_SELLER = 'spare_parts_seller';
    const TYPE_SPOUSE = 'spouse';
    const TYPE_SUBCONTRACTOR = 'subcontractor';
    const TYPE_TECHNICAL_INSPECTOR = 'technical_inspector';
    const TYPE_THIRD_PARTY = 'third_party';
    const TYPE_TOWING_COMPANY = 'towing_company';
    const TYPE_VEHICLE_OWNER = 'vehicle_owner';
    const TYPE_VEHICLE_OWNER2 = 'vehicle_owner2';
    const TYPE_VEHICLE_USER = 'vehicle_user';
    const TYPE_WAREHOUSE = 'warehouse';
    const TYPE_WITNESS = 'witness';
    const TYPE_WORKSHOP_MANAGER = 'workshop_manager';
    const TYPE_WORKSHOP_TEAM_LEADER = 'workshop_team_leader';
    const TYPE_WRECKER = 'wrecker';
    const TYPE_SAPITEUR = 'sapiteur';
    const TYPE_PRE_TRIAL_JUDGE = 'pre_trial_judge';
    const TYPE_VICE_PRESIDENT = 'vice_president';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Dossier::class, inversedBy: 'institutions')]
    private $dossier;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierExpertise::class)]
    private $expertise = null;

    #[ORM\Column(type: 'string', length: 50)]
    private $type;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: Address::class)]
    private $place = null;

    #[ORM\Column(type: 'string', length: 255)]
    private $firstName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $lastName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $address = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $address2 = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $zip = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $city = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $email = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $telephone = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $company = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $represent = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $reference = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $nameClient = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $emailClient = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $referenceClient = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $insuranceCompany = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $referenceCompany = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $expertRegistrationNumber = '';

    #[ORM\Column(type: 'boolean')]
    private $defendant = false;

    #[ORM\Column(type: 'integer')]
    private $defendantNumber = 0;

    #[ORM\Column(type: 'boolean')]
    private $demandeur = false;

    #[ORM\Column(type: 'boolean')]
    private $lawyer = false;

    #[ORM\Column(type: 'string', length: 255)]
    private $lawyerName = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $lawyerEmail = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $lawyerMandate = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $lawyerNumber = '';

    #[ORM\Column(type: 'boolean')]
    private $locked = false;

    #[ORM\Column(type: 'boolean')]
    private $contactEditable = false;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierInstitution::class)]
    private $parent = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    public function setExpertise(?DossierExpertise $expertise = null): self
    {
        $this->expertise = $expertise;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getPlace(): ?Address
    {
        return $this->place;
    }

    public function setPlace(?Address $place): self
    {
        $this->place = $place;

        if ($place !== null) {
            $this->address = '';
            $this->address2 = '';
            $this->zip = '';
            $this->city = '';
            $this->email = '';
            $this->telephone = '';
            $this->company = '';
        }

        return $this;
    }

    public function getName(?DossierExpertise $expertise = null): ?string
    {
        if ($this->getType() == self::TYPE_CONTACT or $this->getType() == self::TYPE_VEHICLE_OWNER) {
            $name = $this->getLastName() . ' ' . $this->getFirstName();
            if (trim($name)) {
                return $name;
            }
        }
        if ($this->getType() == self::TYPE_LAWYER) {
            $name = $this->getLastName() . ' ' . $this->getFirstName();
            if (trim($name)) {
                if (!str_contains($name, 'Maître')) {
                    return __('Maître %s', $name);
                }
                return $name;
            }
        }
        if ($this->getCompany()) {
            return $this->getCompany();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getName();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getName() : '';
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if ($expertise) {
                if ($expertise->isAtHome()) {
                    return __('Au domicile du propriétaire');
                }
                if ($expertise->isAtContactPlace()) {
                    return __('À l\'adresse du lésé');
                }
                if ($expertise->isAtMandatePlace()) {
                    return __('À l\'adresse du mandant');
                }
                if ($expertise->getCompany()) {
                    return $expertise->getCompany();
                }
            }
        }

        return $this->getFirstName($expertise) . ' ' . $this->getLastName($expertise);
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return string|null
     */
    public function getFirstName(?DossierExpertise $expertise = null): ?string
    {
        if ($this->firstName or $this->lastName) {
            return $this->firstName;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getDossier()->getContact() ? $this->getDossier()->getContact()->getFirstName() : '';
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getDossier()->getVehicleOwner() ? $this->getDossier()->getVehicleOwner()->getFirstName() : '';
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getFirstName() : '';
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getFirstName() : '';
        }

        return '';
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(?DossierExpertise $expertise = null): ?string
    {
        if ($this->firstName or $this->lastName) {
            return $this->lastName;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getDossier()->getContact() ? $this->getDossier()->getContact()->getLastName() : '';
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getDossier()->getVehicleOwner() ? $this->getDossier()->getVehicleOwner()->getLastName() : '';
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getLastName() : '';
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getLastName() : '';
        }

        return '';
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(?DossierExpertise $expertise = null): ?string
    {
        if ($this->email) {
            return $this->email;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getEmail();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getDossier()->getContact() ? $this->getDossier()->getContact()->getEmail() : '';
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getDossier()->getVehicleOwner() ? $this->getDossier()->getVehicleOwner()->getEmail() : '';
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getEmail() : $expertise->getEmail();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getEmail() : '';
        }

        return '';
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getAddress(?DossierExpertise $expertise = null): ?string
    {
        if ($this->address) {
            return $this->address;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getAddress();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ADDRESS);
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getVehicleOwnerConfig(ConfigEnum::ADDRESS);
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getAddress() : $expertise->getAddress();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getAddress() : '';
        }

        return '';
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getAddress2(?DossierExpertise $expertise = null): ?string
    {
        if ($this->address2) {
            return $this->address2;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getAddress2();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ADDRESS2);
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getVehicleOwnerConfig(ConfigEnum::ADDRESS2);
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getAddress2() : $expertise->getAddress2();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getAddress2() : '';
        }

        return '';
    }

    public function setAddress2(string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getZip(?DossierExpertise $expertise = null): ?string
    {
        if ($this->zip) {
            return $this->zip;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getZip();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ZIP);
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getVehicleOwnerConfig(ConfigEnum::ZIP);
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getZip() : $expertise->getZip();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getZip() : '';
        }

        return '';
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getCity(?DossierExpertise $expertise = null): ?string
    {
        if ($this->city) {
            return $this->city;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getCity();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::CITY);
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getVehicleOwnerConfig(ConfigEnum::CITY);
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getCity() : $expertise->getCity();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getCity() : '';
        }

        return '';
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getTelephone(?DossierExpertise $expertise = null): ?string
    {
        if ($this->telephone) {
            return $this->telephone;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getTelephone();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::TELEPHONE);
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getVehicleOwnerConfig(ConfigEnum::TELEPHONE);
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE and $this->getDossier()->getExpertise()) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getTelephone() : $expertise->getTelephone();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getTelephone() : '';
        }

        return '';
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getCompany(?DossierExpertise $expertise = null): ?string
    {
        if ($this->company) {
            return $this->company;
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getName();
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getDossier()->getContact() ? $this->getDossier()->getContact()->getCompany() : null;
        }
        if ($this->getType() == self::TYPE_VEHICLE_OWNER) {
            return $this->getDossier()->getVehicleOwner() ? $this->getDossier()->getVehicleOwner()->getCompany() : null;
        }
        if ($this->getType() == self::TYPE_EXPERTISE_PLACE) {
            $expertise = (!$expertise ? ($this->getExpertise() ?: $this->getDossier()->getExpertise())  : $expertise);
            if (!$expertise) {
                return '';
            }
            return $expertise->getPlace() ? $expertise->getPlace()->getCompany() : $expertise->getCompany();
        }
        if ($this->getType() == self::TYPE_MANDATE) {
            return $this->getDossier()->getMandate() ? $this->getDossier()->getMandate()->getCompany() : '';
        }

        return '';
    }

    public function setCompany(string $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getRepresent(): ?string
    {
        return $this->represent;
    }

    public function setRepresent(string $represent): self
    {
        $this->represent = $represent;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getNameClient(): ?string
    {
        return $this->nameClient;
    }

    public function setNameClient(string $nameClient): self
    {
        $this->nameClient = $nameClient;

        return $this;
    }

    public function getEmailClient(): ?string
    {
        return $this->emailClient;
    }

    public function setEmailClient(string $emailClient): self
    {
        $this->emailClient = $emailClient;

        return $this;
    }

    public function getReferenceClient(): ?string
    {
        return $this->referenceClient;
    }

    public function setReferenceClient(string $referenceClient): self
    {
        $this->referenceClient = $referenceClient;

        return $this;
    }

    public function getInsuranceCompany(): ?string
    {
        return $this->insuranceCompany;
    }

    public function setInsuranceCompany(string $insuranceCompany): self
    {
        $this->insuranceCompany = $insuranceCompany;

        return $this;
    }

    public function getReferenceCompany(): ?string
    {
        return $this->referenceCompany;
    }

    public function setReferenceCompany(string $referenceCompany): self
    {
        $this->referenceCompany = $referenceCompany;

        return $this;
    }

    public function getExpertRegistrationNumber(): ?string
    {
        if ($this->expertRegistrationNumber) {
            return $this->expertRegistrationNumber;
        }
        if ($this->getType() == self::TYPE_EXPERT and $this->getDossier()->getUser()) {
            return $this->getDossier()->getUser()->getUserConfig(ConfigEnum::EXPERT_NUMBER);
        }
        return $this->expertRegistrationNumber;
    }

    public function setExpertRegistrationNumber(string $expertRegistrationNumber): self
    {
        $this->expertRegistrationNumber = $expertRegistrationNumber;

        return $this;
    }

    public function isDefendant(): bool
    {
        return $this->defendant;
    }

    public function setDefendant(bool $defendant): self
    {
        $this->defendant = $defendant;

        return $this;
    }

    public function getDefendantNumber(): int
    {
        return $this->defendantNumber;
    }

    public function setDefendantNumber(int $defendantNumber): self
    {
        $this->defendantNumber = $defendantNumber;

        return $this;
    }

    public function isDemandeur(): bool
    {
        return $this->demandeur;
    }

    public function setDemandeur(bool $demandeur): self
    {
        $this->demandeur = $demandeur;

        return $this;
    }

    public function getLawyer(): ?bool
    {
        return $this->lawyer;
    }

    public function setLawyer(bool $lawyer): self
    {
        $this->lawyer = $lawyer;

        return $this;
    }

    public function getLawyerName(): ?string
    {
        return $this->lawyerName;
    }

    public function setLawyerName(string $lawyerName): self
    {
        $this->lawyerName = $lawyerName;

        return $this;
    }

    public function getLawyerEmail(): ?string
    {
        return $this->lawyerEmail;
    }

    public function setLawyerEmail(string $lawyerEmail): self
    {
        $this->lawyerEmail = $lawyerEmail;

        return $this;
    }

    public function getLawyerMandate(): ?string
    {
        return $this->lawyerMandate;
    }

    public function setLawyerMandate(string $lawyerMandate): self
    {
        $this->lawyerMandate = $lawyerMandate;

        return $this;
    }

    public function getLawyerNumber(): ?string
    {
        return $this->lawyerNumber;
    }

    public function setLawyerNumber(string $lawyerNumber): self
    {
        $this->lawyerNumber = $lawyerNumber;

        return $this;
    }

    public function getLocked(): ?bool
    {
        return $this->locked;
    }

    public function setLocked(bool $locked): self
    {
        $this->locked = $locked;

        return $this;
    }

    public function getContactEditable(): ?bool
    {
        return $this->contactEditable;
    }

    public function setContactEditable(bool $contactEditable): self
    {
        $this->contactEditable = $contactEditable;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @param string $type
     * @return string|null
     */
    public function getContactConfig(string $type): ?string
    {
        if (!$this->getDossier()->getContact()) {
            return null;
        }
        $contact = $this->getDossier()->getContact();
        if (!method_exists($contact, 'get' . ucfirst($type))) {
            return null;
        }
        return $contact->{'get' . ucfirst($type)}();
    }

    /**
     * @param string $type
     * @return string|null
     */
    public function getVehicleOwnerConfig(string $type): ?string
    {
        if (!$this->getDossier()->getVehicleOwner()) {
            return null;
        }
        $vehicleOwner = $this->getDossier()->getVehicleOwner();
        if (!method_exists($vehicleOwner, 'get' . ucfirst($type))) {
            return null;
        }
        return $vehicleOwner->{'get' . ucfirst($type)}();
    }

    public function getSiret(): ?string
    {
        if ($this->getPlace()) {
            return $this->getPlace()->getSiret();
        }
        return null;
    }

    public function getParent(): ?DossierInstitution
    {
        return $this->parent;
    }

    public function setParent(?DossierInstitution $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return DossierInstitution[]
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getChildren(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierInstitutionService::class)->getRepository()->findBy(['parent' => $this]);
    }
}
