<?php

namespace Mat<PERSON><PERSON>ver\Entity\Dossier;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON>G<PERSON><PERSON>\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Dossier\DossierPaymentLinkRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_dossiers_payments_links')]
#[ORM\Entity(repositoryClass: DossierPaymentLinkRepository::class)]
class DossierPaymentLink
{
    const STATUS_NOT_SENT = 'not_send';
    const STATUS_WAITING = 'waiting';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELED = 'canceled';
    const STATUS_ERROR = 'error';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Dossier::class, inversedBy: 'paymentsLinks')]
    private $dossier;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\OneToOne(targetEntity: ShopProduct::class, inversedBy: 'dossierPaymentLink', cascade: ['persist', 'remove'])]
    private $product;

    #[ORM\OneToOne(targetEntity: ShopTransaction::class, inversedBy: 'dossierPaymentLink', cascade: ['persist', 'remove'])]
    private $transaction = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\OneToOne(targetEntity: ShopInvoice::class)]
    private $invoice = null;

    #[ORM\Column(type: 'string')]
    private $title = '';

    #[ORM\Column(type: 'boolean')]
    private $unlockReport = false;

    #[ORM\Column(type: 'json')]
    private $documents = [];

    #[ORM\Column(type: 'string', length: 50)]
    private $permalink;

    #[ORM\Column(type: 'string', length: 20)]
    private $status = self::STATUS_WAITING;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\OneToOne(targetEntity: DossierPayment::class, mappedBy: 'paymentLink', cascade: ['persist'])]
    private $dossierPayment;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getInvoice(): ?ShopInvoice
    {
        return $this->invoice;
    }

    public function setInvoice(?ShopInvoice $invoice): self
    {
        $this->invoice = $invoice;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getUnlockReport(): ?bool
    {
        return $this->unlockReport;
    }

    public function setUnlockReport(bool $unlockReport): self
    {
        $this->unlockReport = $unlockReport;

        return $this;
    }

    public function getDocuments(): ?array
    {
        return $this->documents;
    }

    public function setDocuments(array $documents): self
    {
        $this->documents = $documents;

        return $this;
    }

    public function getPermalink(): ?string
    {
        return $this->permalink;
    }

    public function setPermalink(string $permalink): self
    {
        $this->permalink = $permalink;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDossierPayment(): ?DossierPayment
    {
        return $this->dossierPayment;
    }

    public function setDossierPayment(?DossierPayment $dossierPayment): self
    {
        $this->dossierPayment = $dossierPayment;

        return $this;
    }

    /**
     * @return string
     */
    public function getStatusName(): string
    {
        switch ($this->getStatus()) {
            case self::STATUS_NOT_SENT:
                return __('Non envoyé');
            case self::STATUS_WAITING:
                return __('Envoyé - En attente de paiement');
            case self::STATUS_PAID:
                return __('Payé');
            case self::STATUS_CANCELED:
                return __('Annulé');
            case self::STATUS_ERROR:
                return __('Erreur de paiement');
            default:
                return __('Inconnu');
        }
    }

    /**
     * @return string
     */
    public function getStatusLabel(): string
    {
        switch ($this->getStatus()) {
            case self::STATUS_NOT_SENT:
                $label = 'light-warning';
                break;
            case self::STATUS_CANCELED:
            case self::STATUS_ERROR:
                $label = 'light-danger';
                break;
            case self::STATUS_PAID:
                $label = 'success';
                break;
            default:
                $label = 'light-primary';
                break;
        }

        return $label;
    }

    /**
     * @return string
     */
    public function displayStatus(): string
    {
        $statusName = $this->getStatusName();
        $statusLabel = $this->getStatusLabel();
        return '<span class="label label-inline label-' . $statusLabel . '">' . $statusName . '</span>';
    }

    /*
     * @return string
     */
    public function getLink(): string
    {
        return Tools::makeLink('site', 'dossier', 'payment/' . $this->getPermalink() . '/' . $this->getDossier()->getReference());
    }
}
