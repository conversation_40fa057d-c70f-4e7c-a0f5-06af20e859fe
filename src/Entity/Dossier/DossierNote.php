<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier;

use <PERSON>trine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'px_dossiers_notes')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Dossier\DossierNoteRepository::class)]
class DossierNote
{
    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'dossier_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Dossier\Dossier::class)]
    private $dossier;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private $label;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDossier(): Dossier
    {
        return $this->dossier;
    }

    public function setDossier(Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
