<?php

namespace MatGyver\Entity\Help\Article;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Help\Note\HelpNote;
use MatGyver\Entity\Help\Universe\HelpUniverse;
use Gedmo\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'help_articles')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Help\Article\HelpArticleRepository::class)]
class HelpArticle
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 250)]
    private $title;

    #[ORM\Column(type: 'text')]
    private $description = '';

    #[ORM\Column(type: 'string', length: 250)]
    private $titleUs;

    #[ORM\Column(type: 'string', length: 250)]
    private $permalink;

    #[ORM\Column(type: 'string', length: 250)]
    private $route;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'text')]
    private $contentUs;

    #[ORM\Column(type: 'integer')]
    private $views;

    #[ORM\JoinColumn(nullable: false, name: 'universe_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Help\Universe\HelpUniverse::class, inversedBy: 'articles')]
    private $universe;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Help\Note\HelpNote::class, mappedBy: 'article')]
    private $notes;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function __construct()
    {
        $this->notes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getTitleUs(): ?string
    {
        return $this->titleUs;
    }

    public function setTitleUs(string $titleUs): self
    {
        $this->titleUs = $titleUs;

        return $this;
    }

    public function getPermalink(): ?string
    {
        return $this->permalink;
    }

    public function setPermalink(string $permalink): self
    {
        $this->permalink = $permalink;

        return $this;
    }

    public function getRoute(): ?string
    {
        return $this->route;
    }

    public function setRoute(string $route): self
    {
        $this->route = $route;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getContentUs(): ?string
    {
        return $this->contentUs;
    }

    public function setContentUs(string $contentUs): self
    {
        $this->contentUs = $contentUs;

        return $this;
    }

    public function getViews(): ?int
    {
        return $this->views;
    }

    public function setViews(int $views): self
    {
        $this->views = $views;

        return $this;
    }

    public function getActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getUniverse(): ?HelpUniverse
    {
        return $this->universe;
    }

    public function setUniverse(?HelpUniverse $universe): self
    {
        $this->universe = $universe;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection|HelpNote[]
     */
    public function getNotes(): Collection
    {
        return $this->notes;
    }

    public function addNote(HelpNote $note): self
    {
        if (!$this->notes->contains($note)) {
            $this->notes[] = $note;
            $note->setArticle($this);
        }

        return $this;
    }

    public function removeNote(HelpNote $note): self
    {
        if ($this->notes->contains($note)) {
            $this->notes->removeElement($note);
            // set the owning side to null (unless already changed)
            if ($note->getArticle() === $this) {
                $note->setArticle(null);
            }
        }

        return $this;
    }

    public function getCountNotes(): int
    {
        return count($this->notes);
    }

    public function getCountNotesByType(string $type): int
    {
        if (!$this->notes) {
            return 0;
        }

        $nbNotes = 0;
        foreach ($this->notes as $note) {
            if ($note->getType() == $type) {
                $nbNotes++;
            }
        }
        return $nbNotes;
    }

    /**
     * @return array
     */
    public static function getLangFormChoices(): array
    {
        return [
            'fr' => __('Français'),
            'us' => __('English')
        ];
    }
}
