<?php

namespace MatGyver\Entity\Help\Note;

use Doctrine\ORM\Mapping as ORM;
use MatGy<PERSON>\Entity\Help\Article\HelpArticle;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'help_notes')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Help\Note\HelpNoteRepository::class)]
class HelpNote
{
    const TYPE_HAPPY = 'happy';
    const TYPE_NEUTRAL = 'neutral';
    const TYPE_NOT_HAPPY = 'not_happy';

    const SMILEY_HAPPY = '&#128515;';
    const SMILEY_NEUTRAL = '&#128528;';
    const SMILEY_NOT_HAPPY = '&#128542;';

    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 20)]
    private $type;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'article_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Help\Article\HelpArticle::class, inversedBy: 'notes')]
    private $article;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getArticle(): ?HelpArticle
    {
        return $this->article;
    }

    public function setArticle(?HelpArticle $article): self
    {
        $this->article = $article;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
