<?php

namespace MatGyver\Entity\Application;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Universe\Universe;

#[ORM\Table(name: 'mg_applications')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Application\ApplicationRepository::class)]
class Application
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $applicationId;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Universe\Universe::class, mappedBy: 'application')]
    private $universes;

    public function __construct()
    {
        $this->universes = new ArrayCollection();
    }

    public function getApplicationId(): ?int
    {
        return $this->applicationId;
    }

    public function setApplicationId(int $applicationId): self
    {
        $this->applicationId = $applicationId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|Universe[]
     */
    public function getUniverses(): Collection
    {
        return $this->universes;
    }

    public function addUniverse(Universe $universe): self
    {
        if (!$this->universes->contains($universe)) {
            $this->universes[] = $universe;
            $universe->setApplication($this);
        }

        return $this;
    }

    public function removeUniverse(Universe $universe): self
    {
        if ($this->universes->contains($universe)) {
            $this->universes->removeElement($universe);
            // set the owning side to null (unless already changed)
            if ($universe->getApplication() === $this) {
                $universe->setApplication(null);
            }
        }

        return $this;
    }

}
