<?php

namespace MatGyver\Entity\User\Note;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use MatGyver\Entity\User\User;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'mg_users_notes')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\User\Note\UserNoteRepository::class)]
class UserNote
{
    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'admin_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $admin;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private $label;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAdmin(): ?User
    {
        return $this->admin;
    }

    public function setAdmin(?User $admin): self
    {
        $this->admin = $admin;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
