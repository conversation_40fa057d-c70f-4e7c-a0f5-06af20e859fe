<?php

namespace MatGyver\Entity\User;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ed<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Mat<PERSON>yver\Entity\Traits\SoftDeleteableEntity;
use MatGyver\Entity\User\Config\UserConfig;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\User\Role\UserRole;
use MatGyver\Enums\ConfigEnum;

#[ORM\Table(name: 'mg_users')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\User\UserRepository::class)]
#[Gedmo\SoftDeleteable(fieldName: 'deletedAt', timeAware: false)]
class User
{

    use SoftDeleteableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'boolean')]
    private $admin = false;

    #[ORM\Column(type: 'integer')]
    private $restricted = 0;

    #[ORM\Column(type: 'integer')]
    private $validated = 0;

    #[ORM\Column(type: 'string', length: 255)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 255)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 255)]
    private $email;

    #[ORM\Column(type: 'string', length: 255)]
    private $password;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $timestamp;

    #[ORM\Column(type: 'boolean')]
    private $newsletter = 1;

    #[ORM\Column(type: 'boolean')]
    private $rgpd;

    #[ORM\Column(type: 'boolean')]
    private $rgpdAff;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'string', length: 50)]
    private $randomId;

    #[ORM\Column(type: 'string', length: 255)]
    private $rememberMeToken = '';

    #[ORM\Column(type: 'string', length: 32, name: 'md5_email')]
    private $md5Email;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\User\Config\UserConfig::class, mappedBy: 'user', indexBy: 'name')]
    private $userConfigs;

    #[ORM\JoinColumn(nullable: false, name: 'client_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false, name: 'user_id', referencedColumnName: 'user_id')]
    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class, mappedBy: 'user')]
    private $transactions;

    #[ORM\JoinColumn(nullable: false, name: 'user_role_id', referencedColumnName: 'user_role_id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\Role\UserRole::class, fetch: 'EAGER')]
    private $userRole;

    public function __construct()
    {
        $this->userConfigs = new ArrayCollection();
        $this->transactions = new ArrayCollection();
    }

    /**
     * @return string
     */
    public function __toString(): string
    {
        if ($this->firstName !== null && $this->lastName !== null) {
            return $this->firstName . ' ' . $this->lastName;
        }

        return (string) $this->email;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getAdmin(): ?bool
    {
        return $this->admin;
    }

    public function setAdmin(bool $admin): self
    {
        $this->admin = $admin;

        return $this;
    }

    public function getRestricted(): ?int
    {
        return $this->restricted;
    }

    public function setRestricted(int $restricted): self
    {
        $this->restricted = $restricted;

        return $this;
    }

    public function getValidated(): ?int
    {
        return $this->validated;
    }

    public function setValidated(int $validated): self
    {
        $this->validated = $validated;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): self
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    public function getNewsletter(): ?bool
    {
        return $this->newsletter;
    }

    public function setNewsletter(bool $newsletter): self
    {
        $this->newsletter = $newsletter;

        return $this;
    }

    public function getRgpd(): ?bool
    {
        return $this->rgpd;
    }

    public function setRgpd(bool $rgpd): self
    {
        $this->rgpd = $rgpd;

        return $this;
    }

    public function getRgpdAff(): ?bool
    {
        return $this->rgpdAff;
    }

    public function setRgpdAff(bool $rgpdAff): self
    {
        $this->rgpdAff = $rgpdAff;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getMd5Email(): ?string
    {
        return $this->md5Email;
    }

    public function setMd5Email(string $md5Email): self
    {
        $this->md5Email = $md5Email;

        return $this;
    }

    /**
     * @return Collection|UserConfig[]
     */
    public function getUserConfigs(): Collection
    {
        return $this->userConfigs;
    }

    /**
     * @return array
     */
    public function getUserConfigsAsArray(): array
    {
        $results = [];
        foreach ($this->getUserConfigs() as $userConfig) {
            $results[$userConfig->getName()] = $userConfig->getValue();
        }
        return $results;
    }

    /**
     * @return string|null
     */
    public function getUserConfig(string $configName): ?string
    {
        foreach ($this->getUserConfigs() as $userConfig) {
            if ($userConfig->getName() == $configName) {
                return $userConfig->getValue();
            }
        }
        return null;
    }

    public function getExpertNumber(): ?string
    {
        return $this->getUserConfig(ConfigEnum::EXPERT_NUMBER);
    }

    public function addUserConfig(UserConfig $userConfig): self
    {
        if (!$this->userConfigs->contains($userConfig)) {
            $this->userConfigs[] = $userConfig;
            $userConfig->setUser($this);
        }

        return $this;
    }

    public function removeUserConfig(UserConfig $userConfig): self
    {
        if ($this->userConfigs->contains($userConfig)) {
            $this->userConfigs->removeElement($userConfig);
            // set the owning side to null (unless already changed)
            if ($userConfig->getUser() === $this) {
                $userConfig->setUser(null);
            }
        }

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getUserRole(): ?UserRole
    {
        return $this->userRole;
    }

    public function setUserRole(?UserRole $userRole): self
    {
        $this->userRole = $userRole;

        return $this;
    }

    public function getRandomId(): ?string
    {
        return $this->randomId;
    }

    public function setRandomId(string $randomId): self
    {
        $this->randomId = $randomId;

        return $this;
    }

    public function getRememberMeToken(): ?string
    {
        return $this->rememberMeToken;
    }

    public function setRememberMeToken(string $rememberMeToken): self
    {
        $this->rememberMeToken = $rememberMeToken;

        return $this;
    }

    /**
     * @return Collection
     */
    public function getTransactions(): Collection
    {
        return $this->transactions;
    }

    public function isExpert(): bool
    {
        if ($this->getUserRole()->getRole() and $this->getUserRole()->getRole()->getRoleId() == ROLE_EDITOR) {
            return true;
        }
        if ($this->getUserRole()->getRole() and $this->getUserRole()->getRole()->getRoleId() == ROLE_ADMIN) {
            return (bool) $this->getExpertNumber();
        }
        if (!$this->getUserRole()->getRole()) {
            return (bool) $this->getExpertNumber();
        }
        return false;
    }

    public function isManager(): bool
    {
        if (!$this->getUserRole()->getRole()) {
            return false;
        }
        if ($this->getUserRole()->getRole() and in_array($this->getUserRole()->getRole()->getRoleId(), [ROLE_SUPEREDITOR, ROLE_ADMIN])) {
            return true;
        }
        return false;
    }
}
