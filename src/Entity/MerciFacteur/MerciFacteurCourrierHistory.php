<?php

namespace <PERSON><PERSON><PERSON>ver\Entity\MerciFacteur;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatGyver\Repository\MerciFacteur\MerciFacteurCourrierHistoryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mf_courriers_history')]
#[ORM\Entity(repositoryClass: MerciFacteurCourrierHistoryRepository::class)]
class MerciFacteurCourrierHistory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: MerciFacteurCourrier::class)]
    private $courrier;

    #[ORM\Column(type: 'string', length: 50)]
    private $status = '';

    #[ORM\Column(type: 'text')]
    private $description;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getCourrier(): ?MerciFacteurCourrier
    {
        return $this->courrier;
    }

    public function setCourrier(?MerciFacteurCourrier $courrier): self
    {
        $this->courrier = $courrier;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
