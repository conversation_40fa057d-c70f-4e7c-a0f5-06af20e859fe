<?php

namespace MatG<PERSON>ver\Entity\MerciFacteur;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use MatG<PERSON>ver\Entity\Client\Client;
use Mat<PERSON><PERSON>ver\Entity\Dossier\Dossier;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\DossierConvocation;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Repository\MerciFacteur\MerciFacteurCourrierRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mf_courriers')]
#[ORM\Entity(repositoryClass: MerciFacteurCourrierRepository::class)]
class MerciFacteurCourrier
{
    const STATUS_NEW = 'new';
    const STATUS_PRINTED = 'printed';
    const STATUS_SENDED = 'sended';
    const STATUS_NEW_STATE = 'new-state';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_ERROR = 'error';
    const STATUS_PND = 'pnd';
    const STATUS_ARE = 'are';
    const STATUS_PDD = 'pdd';
    const STATUS_PRIS_EN_CHARGE = 'pris_en_charge';
    const STATUS_TRAITEMENT = 'traitement';
    const STATUS_ATTENTE_PRESENTATION = 'attente_presentation';
    const STATUS_DISTRIBUTION_EN_COURS = 'distribution_en_cours';
    const STATUS_PROBLEME_RESOLU = 'probleme_resolu';
    const STATUS_ATTENTE_AU_GUICHET = 'attente_au_guichet';
    const STATUS_DECLARATIF_RECEPTIONNE = 'declaratif_receptionne';

    const SEND_MODE_LRAR = 'lrar';
    const SEND_MODE_LRARE = 'lrare';
    const SEND_MODE_SUIVI = 'suivi';
    const SEND_MODE_NORMAL = 'normal';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Dossier::class)]
    private $dossier;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: IntegrationAccount::class)]
    private $account;

    #[ORM\ManyToOne(targetEntity: DossierExpertisePerson::class)]
    private $person;

    #[ORM\ManyToOne(targetEntity: DossierConvocation::class)]
    private $convocation;

    #[ORM\Column(type: 'string', length: 255)]
    private $address = '';

    #[ORM\Column(type: 'integer')]
    private $userId = 0;

    #[ORM\Column(type: 'integer')]
    private $expAddressId = 0;

    #[ORM\Column(type: 'integer')]
    private $destAddressId = 0;

    #[ORM\Column(type: 'integer')]
    private $courrierId = 0;

    #[ORM\Column(type: 'string', length: 50)]
    private $reference = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $sendMode = self::SEND_MODE_LRAR;

    #[ORM\Column(type: 'text')]
    private $additionalDocuments = '';

    #[ORM\Column(type: 'string')]
    private $pdd = '';

    #[ORM\Column(type: 'string')]
    private $are = '';

    #[ORM\Column(type: 'boolean')]
    private $sent;

    #[ORM\Column(type: 'boolean')]
    private $received = false;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierDocument::class)]
    private $deposit = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierDocument::class)]
    private $receipt = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: DossierDocument::class)]
    private $nonReceipt = null;

    #[ORM\Column(type: 'string', length: 50)]
    private $status = self::STATUS_NEW;

    #[ORM\Column(type: 'string', length: 255)]
    private $result;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\OneToMany(targetEntity: \MerciFacteurCourrierDocument::class, mappedBy: 'courrier', cascade: ['persist'], orphanRemoval: true)]
    private $documents;

    #[ORM\OneToMany(targetEntity: \MerciFacteurCourrierHistory::class, mappedBy: 'courrier', cascade: ['persist'], orphanRemoval: true)]
    private $histories;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->histories = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getPerson(): ?DossierExpertisePerson
    {
        return $this->person;
    }

    public function setPerson(?DossierExpertisePerson $person): self
    {
        $this->person = $person;

        return $this;
    }

    public function getConvocation(): ?DossierConvocation
    {
        return $this->convocation;
    }

    public function setConvocation(?DossierConvocation $convocation): self
    {
        $this->convocation = $convocation;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getExpAddressId(): ?int
    {
        return $this->expAddressId;
    }

    public function setExpAddressId(int $expAddressId): self
    {
        $this->expAddressId = $expAddressId;

        return $this;
    }

    public function getDestAddressId(): ?int
    {
        return $this->destAddressId;
    }

    public function setDestAddressId(int $destAddressId): self
    {
        $this->destAddressId = $destAddressId;

        return $this;
    }

    public function getCourrierId(): ?int
    {
        return $this->courrierId;
    }

    public function setCourrierId(int $courrierId): self
    {
        $this->courrierId = $courrierId;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getSendMode(): ?string
    {
        return $this->sendMode;
    }

    public function setSendMode(string $sendMode): self
    {
        $this->sendMode = $sendMode;

        return $this;
    }

    public function getAdditionalDocuments(): array
    {
        return ($this->additionalDocuments ? json_decode($this->additionalDocuments, true) : []);
    }

    public function setAdditionalDocuments(array $additionalDocuments): self
    {
        $this->additionalDocuments = json_encode($additionalDocuments);

        return $this;
    }

    public function getPdd(): ?string
    {
        return $this->pdd;
    }

    public function setPdd(string $pdd): self
    {
        $this->pdd = $pdd;

        return $this;
    }

    public function getAre(): ?string
    {
        return $this->are;
    }

    public function setAre(string $are): self
    {
        $this->are = $are;

        return $this;
    }

    public function getSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getReceived(): bool
    {
        return $this->received;
    }

    public function setReceived(bool $received): self
    {
        $this->received = $received;

        return $this;
    }

    public function getDeposit(): ?DossierDocument
    {
        return $this->deposit;
    }

    public function setDeposit(?DossierDocument $deposit): self
    {
        $this->deposit = $deposit;

        return $this;
    }

    public function getReceipt(): ?DossierDocument
    {
        return $this->receipt;
    }

    public function setReceipt(?DossierDocument $receipt): self
    {
        $this->receipt = $receipt;

        return $this;
    }

    public function getNonReceipt(): ?DossierDocument
    {
        return $this->nonReceipt;
    }

    public function setNonReceipt(?DossierDocument $nonReceipt): self
    {
        $this->nonReceipt = $nonReceipt;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(string $result): self
    {
        $this->result = $result;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return Collection|MerciFacteurCourrierDocument[]
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(MerciFacteurCourrierDocument $document): self
    {
        if (!$this->documents->contains($document)) {
            $this->documents[] = $document;
            $document->setCourrier($this);
        }

        return $this;
    }

    public function removeDocument(MerciFacteurCourrierDocument $document): self
    {
        if ($this->documents->contains($document)) {
            $this->documents->removeElement($document);
            // set the owning side to null (unless already changed)
            if ($document->getCourrier() === $this) {
                $document->setCourrier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|MerciFacteurCourrierHistory[]
     */
    public function getHistories(): Collection
    {
        return $this->histories;
    }

    public function addHistory(MerciFacteurCourrierHistory $history): self
    {
        if (!$this->histories->contains($history)) {
            $this->histories[] = $history;
            $history->setCourrier($this);
        }

        return $this;
    }

    public function removeHistory(MerciFacteurCourrierHistory $history): self
    {
        if ($this->histories->contains($history)) {
            $this->histories->removeElement($history);
            // set the owning side to null (unless already changed)
            if ($history->getCourrier() === $this) {
                $history->setCourrier(null);
            }
        }

        return $this;
    }
}
