<?php

namespace <PERSON>G<PERSON>ver\Entity\MerciFacteur;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON>G<PERSON>ver\Entity\Dossier\DossierDocument;
use MatGyver\Repository\MerciFacteur\MerciFacteurCourrierDocumentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mf_courriers_documents')]
#[ORM\Entity(repositoryClass: MerciFacteurCourrierDocumentRepository::class)]
class MerciFacteurCourrierDocument
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: MerciFacteurCourrier::class)]
    private $courrier;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: DossierDocument::class)]
    private $document;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getCourrier(): ?MerciFacteurCourrier
    {
        return $this->courrier;
    }

    public function setCourrier(?MerciFacteurCourrier $courrier): self
    {
        $this->courrier = $courrier;

        return $this;
    }

    public function getDocument(): ?DossierDocument
    {
        return $this->document;
    }

    public function setDocument(?DossierDocument $document): self
    {
        $this->document = $document;

        return $this;
    }
}
