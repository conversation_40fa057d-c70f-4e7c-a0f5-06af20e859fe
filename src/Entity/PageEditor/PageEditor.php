<?php

namespace MatGyver\Entity\PageEditor;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_pages_editor')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\PageEditor\PageEditorRepository::class)]
#[ORM\InheritanceType('SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'type', type: 'string')]
#[ORM\DiscriminatorMap(['email' => 'PageEditorEmail', 'user' => 'PageEditorUser', 'product' => 'PageEditorProductOrder', 'affiliation_inscription' => 'PageEditorAffiliationInscription', 'tos' => 'PageEditorTos', 'privacy' => 'PageEditorPrivacy', 'cgu' => 'PageEditorCgu', 'legal_notice' => 'PageEditorLegalNotice', 'dpa' => 'PageEditorDpa', 'example' => 'PageEditorExample', 'help_center' => 'PageEditorHelpCenter', 'report' => 'PageEditorReport'])]
abstract class PageEditor
{
    const TYPE_EMAIL = 'email';
    const TYPE_USER = 'user';
    const TYPE_PRODUCT = 'product';
    const TYPE_TOS = 'tos';
    const TYPE_PRIVACY = 'privacy';
    const TYPE_CGU = 'cgu';
    const TYPE_LEGAL_NOTICE = 'legal_notice';
    const TYPE_DPA = 'dpa';
    const TYPE_AFFILIATION_INSCRIPTION = 'affiliation_inscription';
    const TYPE_EXAMPLE = 'example';
    const TYPE_HELP_CENTER = 'help_center';
    const TYPE_HELP_ARTICLE = 'help_article';
    const TYPE_HELP_CATEGORY = 'help_category';
    const TYPE_REPORT = 'report';

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'text')]
    private $design;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $dateUpdate;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDesign(): ?string
    {
        return $this->design;
    }

    public function setDesign(string $design): self
    {
        $this->design = $design;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateUpdate(): ?\DateTimeInterface
    {
        return $this->dateUpdate;
    }

    public function setDateUpdate(\DateTimeInterface $dateUpdate): self
    {
        $this->dateUpdate = $dateUpdate;

        return $this;
    }

    /**
     * @return array
     */
    static public function getDiscriminatorMap(): array
    {
        return [
            'email' => 'MatGyver\Entity\PageEditor\PageEditorEmail',
            'user' => 'MatGyver\Entity\PageEditor\PageEditorUser',
            'product' => 'MatGyver\Entity\PageEditor\PageEditorProductOrder',
            'affiliation_inscription' => 'MatGyver\Entity\PageEditor\PageEditorAffiliationInscription',
            'tos' => 'MatGyver\Entity\PageEditor\PageEditorTos',
            'privacy' => 'MatGyver\Entity\PageEditor\PageEditorPrivacy',
            'cgu' => 'MatGyver\Entity\PageEditor\PageEditorCgu',
            'legal_notice' => 'MatGyver\Entity\PageEditor\PageEditorLegalNotice',
            'dpa' => 'MatGyver\Entity\PageEditor\PageEditorDpa',
            'example' => 'MatGyver\Entity\PageEditor\PageEditorExample',
            'help_center' => 'MatGyver\Entity\PageEditor\PageEditorHelpCenter',
            'report' => 'MatGyver\Entity\PageEditor\PageEditorReport',
        ];
    }
}
