<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Observation;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON><PERSON><PERSON><PERSON>\Repository\Observation\ObservationRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_observations')]
#[ORM\Entity(repositoryClass: ObservationRepository::class)]
class Observation
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
