<?php

namespace MatGyver\Entity\Intervention;

use MatGyver\Repository\Intervention\InterventionSubjectRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_interventions_subjects')]
#[ORM\Entity(repositoryClass: InterventionSubjectRepository::class)]
class InterventionSubject
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
