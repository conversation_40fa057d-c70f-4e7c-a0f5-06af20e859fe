<?php

namespace Mat<PERSON><PERSON><PERSON>\Entity\Intervention;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatG<PERSON>ver\Repository\Intervention\InterventionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'px_interventions')]
#[ORM\Entity(repositoryClass: InterventionRepository::class)]
class Intervention
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
