<?php
/**
 * Test script pour vérifier que les quantités flottantes fonctionnent correctement
 */

// Test des entités
echo "=== Test des entités ===\n";

// Test ShopInvoiceProduct
$invoiceProduct = new \MatGyver\Entity\Shop\Invoice\ShopInvoiceProduct();
$invoiceProduct->setQuantity(2.5);
echo "ShopInvoiceProduct quantity: " . $invoiceProduct->getQuantity() . " (type: " . gettype($invoiceProduct->getQuantity()) . ")\n";

// Test ShopQuoteProduct
$quoteProduct = new \MatGyver\Entity\Shop\Quote\ShopQuoteProduct();
$quoteProduct->setQuantity(1.75);
echo "ShopQuoteProduct quantity: " . $quoteProduct->getQuantity() . " (type: " . gettype($quoteProduct->getQuantity()) . ")\n";

// Test ShopCreditNoteProduct
$creditNoteProduct = new \MatGyver\Entity\Shop\CreditNote\ShopCreditNoteProduct();
$creditNoteProduct->setQuantity(0.5);
echo "ShopCreditNoteProduct quantity: " . $creditNoteProduct->getQuantity() . " (type: " . gettype($creditNoteProduct->getQuantity()) . ")\n";

// Test ShopTransactionProduct
$transactionProduct = new \MatGyver\Entity\Shop\Transaction\ShopTransactionProduct();
$transactionProduct->setQuantity(3.25);
echo "ShopTransactionProduct quantity: " . $transactionProduct->getQuantity() . " (type: " . gettype($transactionProduct->getQuantity()) . ")\n";

// Test ShopCartProduct
$cartProduct = new \MatGyver\Entity\Shop\Cart\ShopCartProduct();
$cartProduct->setQuantity(1.33);
echo "ShopCartProduct quantity: " . $cartProduct->getQuantity() . " (type: " . gettype($cartProduct->getQuantity()) . ")\n";

echo "\n=== Test des calculs JavaScript (simulation) ===\n";

// Simulation des calculs JavaScript
function simulateJSCalc($qty, $priceHT, $vat) {
    $qty = floatval($qty);
    $priceHT = floatval($priceHT);
    $vat = floatval($vat);
    
    $priceTTC = $priceHT * $qty * (1 + $vat / 100);
    
    return [
        'quantity' => $qty,
        'price_ht' => $priceHT,
        'vat' => $vat,
        'price_ttc' => round($priceTTC, 2)
    ];
}

$testCases = [
    ['qty' => 2.5, 'price_ht' => 10.00, 'vat' => 20],
    ['qty' => 1.75, 'price_ht' => 15.50, 'vat' => 5.5],
    ['qty' => 0.5, 'price_ht' => 100.00, 'vat' => 0],
    ['qty' => 3.33, 'price_ht' => 7.25, 'vat' => 20]
];

foreach ($testCases as $test) {
    $result = simulateJSCalc($test['qty'], $test['price_ht'], $test['vat']);
    echo "Qty: {$result['quantity']}, HT: {$result['price_ht']}€, TVA: {$result['vat']}% => TTC: {$result['price_ttc']}€\n";
}

echo "\n=== Tests terminés ===\n";
echo "Toutes les entités supportent maintenant les quantités flottantes.\n";
echo "Les calculs JavaScript ont été adaptés pour gérer les décimales.\n";
echo "N'oubliez pas d'exécuter la migration de base de données !\n";
?>
